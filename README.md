# remotion

[![NPM version](https://img.shields.io/npm/v/remotion.svg?style=flat)](https://npmjs.org/package/remotion)
[![NPM downloads](http://img.shields.io/npm/dm/remotion.svg?style=flat)](https://npmjs.org/package/remotion)

A react library developed with dumi

## Usage

TODO

## Options

TODO

## Development

```bash
# install dependencies
$ pnpm install

# develop library by docs demo
$ pnpm start

# build library source code
$ pnpm run build

# build library source code in watch mode
$ pnpm run build:watch

# build docs
$ pnpm run docs:build

# Locally preview the production build.
$ pnpm run docs:preview

# check your project for potential problems
$ pnpm run doctor
```

## LICENSE

MIT
