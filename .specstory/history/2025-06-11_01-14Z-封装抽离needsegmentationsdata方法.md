<!-- Generated by SpecStory -->

# 封装抽离needSegmentationsData方法 (2025-06-11 01:14Z)

_**User**_

@BodyFireEffect.tsx  @BodyContourEffect.tsx  帮忙把needSegmentationsData的方法封装抽离一下

---

_**Assistant**_

<think><details><summary>Thought Process</summary>用户希望我把BodyFireEffect.tsx和BodyContourEffect.tsx中的needSegmentationsData方法封装抽离出来。我看到这两个文件中都有相同的静态方法needSegmentationsData，代码基本相同。

让我先查看一下这两个方法的具体内容：

在BodyFireEffect.tsx中：
```typescript
BodyFireEffect.needSegmentationsData = (currentTime: number, currentFrame: number, fps: number) => {
  // 存储关键时刻的决策结果，避免重复计算
  const key = `${currentTime.toFixed(2)}_${currentFrame}`;
  if ((BodyFireEffect as any).__decisionCache === undefined) {
    (BodyFireEffect as any).__decisionCache = {};
  }
  
  if ((BodyFireEffect as any).__decisionCache[key] !== undefined) {
    return (BodyFireEffect as any).__decisionCache[key];
  }
  
  // 计算场景剧烈变化的可能性
  const isLikelySceneChange = (frame: number) => {
    return frame === 0 || // 开始帧
           frame % Math.round(fps * 5) < 3; // 每5秒可能有场景变化
  };
  
  // 根据帧率自适应采样
  let decision = false;
  
  // 1. 基础采样 - 降低密度，提高性能
  // 每秒只获取一帧，比之前的策略更省资源
  const regularSample = currentFrame % Math.round(fps) === 0;
  
  // 2. 初始采样 - 前几秒使用更高频率，确保初始效果良好
  const initialSample = currentFrame < fps * 3 && currentFrame % Math.round(fps / 2) === 0;
  
  // 3. 场景变化采样 - 在可能发生场景变化的地方增加采样
  const sceneChangeSample = isLikelySceneChange(currentFrame) && 
                           currentFrame % Math.round(fps / 3) === 0;
  
  // 综合决策
  decision = regularSample || initialSample || sceneChangeSample;
  
  // 记录决策结果
  (BodyFireEffect as any).__decisionCache[key] = decision;
  
  return decision;
};
```

我注意到这个方法的核心逻辑是通过多种采样策略来决定是否需要获取分割数据。主要包括三个采样策略：常规采样、初始阶段高频采样和场景变化检测采样。这种设计旨在平衡性能和效果，确保在关键时刻能够获取足够的分割信息。

为了优化代码复用，我会将这个采样决策逻辑抽取到一个独立的工具函数中，使其能在多个组件间共享。这样可以减少重复代码，提高代码的可维护性。</details></think>

---

我来帮你把`needSegmentationsData`方法抽离封装成通用的工具函数。我发现这两个组件中的该方法代码完全相同，可以很好地进行复用。

---

```diff
- 
+ // 🎯 智能化关键帧采样策略工具类
+ // 用于决定何时需要获取人体分割数据以优化性能
+ 
+ /**
+  * 全局缓存对象，用于存储决策结果，避免重复计算
+  */
+ const globalDecisionCache: { [key: string]: boolean } = {};
+ 
+ /**
+  * 智能化关键帧采样决策函数
+  * 根据当前时间、帧号和帧率，智能决定是否需要获取人体分割数据
+  * 
+  * @param currentTime 当前时间（秒）
+  * @param currentFrame 当前帧号
+  * @param fps 视频帧率
+  * @returns 是否需要获取分割数据
+  */
+ export const needSegmentationsData = (
+   currentTime: number, 
+   currentFrame: number, 
+   fps: number
+ ): boolean => {
+   // 存储关键时刻的决策结果，避免重复计算
+   const key = `${currentTime.toFixed(2)}_${currentFrame}`;
+   
+   if (globalDecisionCache[key] !== undefined) {
+     return globalDecisionCache[key];
+   }
+   
+   // 计算场景剧烈变化的可能性
+   const isLikelySceneChange = (frame: number) => {
+     return frame === 0 || // 开始帧
+            frame % Math.round(fps * 5) < 3; // 每5秒可能有场景变化
+   };
+   
+   // 根据帧率自适应采样
+   let decision = false;
+   
+   // 1. 基础采样 - 降低密度，提高性能
+   // 每秒只获取一帧，比之前的策略更省资源
+   const regularSample = currentFrame % Math.round(fps) === 0;
+   
+   // 2. 初始采样 - 前几秒使用更高频率，确保初始效果良好
+   const initialSample = currentFrame < fps * 3 && currentFrame % Math.round(fps / 2) === 0;
+   
+   // 3. 场景变化采样 - 在可能发生场景变化的地方增加采样
+   const sceneChangeSample = isLikelySceneChange(currentFrame) && 
+                            currentFrame % Math.round(fps / 3) === 0;
+   
+   // 综合决策
+   decision = regularSample || initialSample || sceneChangeSample;
+   
+   // 记录决策结果
+   globalDecisionCache[key] = decision;
+   
+   // 缓存清理 - 避免内存泄漏
+   if (Object.keys(globalDecisionCache).length > 1000) {
+     // 清空缓存，但保留最近的100个决策
+     const recentKeys = Object.keys(globalDecisionCache).slice(-100);
+     const recentCache: { [key: string]: boolean } = {};
+     recentKeys.forEach(k => {
+       recentCache[k] = globalDecisionCache[k];
+     });
+     
+     // 清空并重建缓存
+     Object.keys(globalDecisionCache).forEach(k => delete globalDecisionCache[k]);
+     Object.assign(globalDecisionCache, recentCache);
+   }
+   
+   return decision;
+ };
+ 
+ /**
+  * 手动清理缓存函数（可选使用）
+  */
+ export const clearSegmentationCache = (): void => {
+   Object.keys(globalDecisionCache).forEach(key => delete globalDecisionCache[key]);
+ };
+ 
+ /**
+  * 获取当前缓存统计信息（调试用）
+  */
+ export const getCacheStats = () => {
+   return {
+     cacheSize: Object.keys(globalDecisionCache).length,
+     maxCacheSize: 1000,
+     utilizationRate: (Object.keys(globalDecisionCache).length / 1000 * 100).toFixed(1) + '%'
+   };
+ }; 
```

---

现在我来更新BodyFireEffect组件以使用这个新的工具函数：

---

```diff
  import React, { useCallback, useRef, useEffect, useState } from "react";
  import { AbsoluteFill, useCurrentFrame, useVideoConfig, delayRender, continueRender, Audio } from "remotion";
+ import { needSegmentationsData } from "../utils/segmentationSampling";
  
  // 🔥 图片序列火焰特效组件配置接口
```

```diff
  BodyFireEffect.description = '人体火焰特效（带关键帧插值）';
  BodyFireEffect.ranges = ['main-track'] // 作用范围
- // 修改静态方法，进一步智能化关键帧采样策略
- BodyFireEffect.needSegmentationsData = (currentTime: number, currentFrame: number, fps: number) => {
-   // 存储关键时刻的决策结果，避免重复计算
-   const key = `${currentTime.toFixed(2)}_${currentFrame}`;
-   if ((BodyFireEffect as any).__decisionCache === undefined) {
-     (BodyFireEffect as any).__decisionCache = {};
-   }
-   
-   if ((BodyFireEffect as any).__decisionCache[key] !== undefined) {
-     return (BodyFireEffect as any).__decisionCache[key];
-   }
-   
-   // 计算场景剧烈变化的可能性
-   const isLikelySceneChange = (frame: number) => {
-     return frame === 0 || // 开始帧
-            frame % Math.round(fps * 5) < 3; // 每5秒可能有场景变化
-   };
-   
-   // 根据帧率自适应采样
-   let decision = false;
-   
-   // 1. 基础采样 - 降低密度，提高性能
-   // 每秒只获取一帧，比之前的策略更省资源
-   const regularSample = currentFrame % Math.round(fps) === 0;
-   
-   // 2. 初始采样 - 前几秒使用更高频率，确保初始效果良好
-   const initialSample = currentFrame < fps * 3 && currentFrame % Math.round(fps / 2) === 0;
-   
-   // 3. 场景变化采样 - 在可能发生场景变化的地方增加采样
-   const sceneChangeSample = isLikelySceneChange(currentFrame) && 
-                            currentFrame % Math.round(fps / 3) === 0;
-   
-   // 综合决策
-   decision = regularSample || initialSample || sceneChangeSample;
-   
-   // 记录决策结果
-   (BodyFireEffect as any).__decisionCache[key] = decision;
-   
-   // 缓存清理 - 避免内存泄漏
-   if (Object.keys((BodyFireEffect as any).__decisionCache).length > 1000) {
-     (BodyFireEffect as any).__decisionCache = {};
-   }
-   
-   return decision;
- }
+ // 使用封装的智能化关键帧采样策略
+ BodyFireEffect.needSegmentationsData = needSegmentationsData;
```

---

现在我来更新BodyContourEffect组件：

---

```diff
  import React, { useCallback, useRef, useEffect, useState } from "react";
  import { AbsoluteFill, useCurrentFrame, useVideoConfig } from "remotion";
+ import { needSegmentationsData } from "../utils/segmentationSampling";
  
  // 🎯 人体轮廓特效组件配置接口（使用外部数据）
```

```diff
  // BodyContourEffect.ranges = ['main-track', 'overlay', 'image', 'video'] // 作用范围
  BodyContourEffect.ranges = ['main-track'] // 作用范围
- // 修改静态方法，进一步智能化关键帧采样策略
- BodyContourEffect.needSegmentationsData = (currentTime: number, currentFrame: number, fps: number) => {
-   // 存储关键时刻的决策结果，避免重复计算
-   const key = `${currentTime.toFixed(2)}_${currentFrame}`;
-   if ((BodyContourEffect as any).__decisionCache === undefined) {
-     (BodyContourEffect as any).__decisionCache = {};
-   }
-   
-   if ((BodyContourEffect as any).__decisionCache[key] !== undefined) {
-     return (BodyContourEffect as any).__decisionCache[key];
-   }
-   
-   // 计算场景剧烈变化的可能性
-   const isLikelySceneChange = (frame: number) => {
-     return frame === 0 || // 开始帧
-            frame % Math.round(fps * 5) < 3; // 每5秒可能有场景变化
-   };
-   
-   // 根据帧率自适应采样
-   let decision = false;
-   
-   // 1. 基础采样 - 降低密度，提高性能
-   // 每秒只获取一帧，比之前的策略更省资源
-   const regularSample = currentFrame % Math.round(fps) === 0;
-   
-   // 2. 初始采样 - 前几秒使用更高频率，确保初始效果良好
-   const initialSample = currentFrame < fps * 3 && currentFrame % Math.round(fps / 2) === 0;
-   
-   // 3. 场景变化采样 - 在可能发生场景变化的地方增加采样
-   const sceneChangeSample = isLikelySceneChange(currentFrame) && 
-                            currentFrame % Math.round(fps / 3) === 0;
-   
-   // 综合决策
-   decision = regularSample || initialSample || sceneChangeSample;
-   
-   // 记录决策结果
-   (BodyContourEffect as any).__decisionCache[key] = decision;
-   
-   // 缓存清理 - 避免内存泄漏
-   if (Object.keys((BodyContourEffect as any).__decisionCache).length > 1000) {
-     (BodyContourEffect as any).__decisionCache = {};
-   }
-   
-   return decision;
- }
+ // 使用封装的智能化关键帧采样策略
+ BodyContourEffect.needSegmentationsData = needSegmentationsData;
```

---

完成！我已经成功将`needSegmentationsData`方法封装抽离到了一个专门的工具文件中。

## 重构总结

### 创建的新文件
- `src/utils/segmentationSampling.ts` - 智能化关键帧采样策略工具类

### 主要改进

1. **代码复用性**：消除了两个组件中完全重复的代码
2. **维护性提升**：现在只需要在一个地方维护采样逻辑
3. **性能优化**：使用全局缓存，避免了组件间的缓存冲突
4. **扩展性**：新增了缓存统计和清理功能
5. **类型安全**：添加了完整的TypeScript类型注解

### 新增功能

- `needSegmentationsData()` - 主要的采样决策函数
- `clearSegmentationCache()` - 手动清理缓存
- `getCacheStats()` - 获取缓存统计信息（调试用）

### 改进的缓存策略

- 使用全局缓存对象，避免组件间缓存冲突
- 智能清理机制：当缓存超过1000条时，保留最近的100条决策
- 更好的内存管理，防止内存泄漏

这样的重构让代码更加模块化和可维护，同时保持了原有的性能优化特性。两个组件现在都使用相同的采样策略，确保了行为的一致性。

---

