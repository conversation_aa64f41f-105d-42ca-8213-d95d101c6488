<!-- Generated by SpecStory -->

# 定义花字主色和自定义颜色 (2025-06-26 01:13Z)

_**User**_

@Flower300.tsx 我想给花字定义一个主色挂在组件的mainColor属性上，同时可自定义花字的主色，从style.color中取，默认色就是当前组件定义的颜色，请帮我修改一下

---

_**Assistant**_

<think><details><summary>Thought Process</summary>用户想要给Flower300组件添加一个mainColor属性，同时支持从style.color中自定义花字的主色。让我分析一下需求：

1. 给组件添加一个mainColor属性，挂在组件上
2. 支持从style.color中自定义花字主色
3. 默认色是当前组件定义的颜色

看当前代码，fillColor默认是"#00f5ff"，这应该就是主色。我需要：</details></think>

---

