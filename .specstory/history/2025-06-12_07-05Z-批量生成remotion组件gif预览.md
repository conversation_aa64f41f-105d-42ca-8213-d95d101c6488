<!-- Generated by SpecStory -->

# 批量生成remotion组件gif预览 (2025-06-12 07:05Z)

_**User**_

这个项目时remotion的渲染组件库，我想给这些组件生成预览的gif图，有没有好的批量处理方法

---

_**Assistant**_

<think><details><summary>Thought Process</summary>用户想为 Remotion 的渲染组件库生成预览的 GIF 图，需要批量处理。我需要先了解这个项目的结构，看看有哪些组件，然后提供一个批量生成 GIF 预览的方案。

首先我需要：
1. 探索项目结构，了解有哪些组件
2. 查看现有的 Remotion 配置
3. 提供批量生成 GIF 的方案

让我先从项目根目录开始探索。</details></think>

---

我来帮您为 Remotion 组件库生成预览 GIF 图的批量处理方案。首先让我探索一下项目结构，了解现有的组件情况。

---

<details>
            <summary>Listed current directory • **22** results</summary>
        
| Name |
|-------|
| 📁 `dist` |
| 📁 `.git` |
| 📁 `.dumi` |
| 📁 `src` |
| 📄 `package.json` |
| 📄 `.cursorindexingignore` |
| 📁 `.specstory` |
| 📁 `.cursor` |
| 📁 `node_modules` |
| 📁 `.husky` |
| 📄 `tsconfig.json` |
| 📁 `docs` |
| 📄 `.editorconfig` |
| 📄 `.eslintrc.js` |
| 📄 `.fatherrc.ts` |
| 📄 `.gitignore` |
| 📄 `.prettierignore` |
| 📄 `.prettierrc.js` |
| 📄 `.stylelintrc` |
| 📄 `LICENSE` |
| 📄 `README.md` |
| 📄 `.dumirc.ts` |

</details>

---

Read file: package.json

---

<details>
            <summary>Listed directory src • **9** results</summary>
        
| Name |
|-------|
| 📄 `index.ts` |
| 📁 `sticker` |
| 📁 `global-effects` |
| 📁 `utils` |
| 📁 `text` |
| 📁 `video` |
| 📁 `sence-effects` |
| 📁 `person-card` |
| 📁 `image` |

</details>

---

Read file: src/index.ts

---

<details>
            <summary>Listed directory docs • **2** results</summary>
        
| Name |
|-------|
| 📄 `guide.md` |
| 📄 `index.md` |

</details>

---

现在让我检查一下是否已经有 Remotion 配置文件：

---

<details>
            <summary>Searched codebase "remotion.config" • **0** results</summary>
        
No results found
</details>

---

<details>
            <summary>Searched codebase "video.config" • **0** results</summary>
        
No results found
</details>

---

<think><details><summary>Thought Process</summary>用户想为 Remotion 的渲染组件库生成预览的 GIF 图，需要批量处理。我需要先了解这个项目的结构，看看有哪些组件，然后提供一个批量生成 GIF 预览的方案。

首先我需要：
1. 探索项目结构，了解有哪些组件
2. 查看现有的 Remotion 配置
3. 提供批量生成 GIF 的方案

让我先从项目根目录开始探索。</details></think>

---

<details>
            <summary>Grep search for "remotion.config" • **0** files</summary>
        
No results found
</details>

---

<details>
            <summary>Searched codebase ".specstory" • **1** results</summary>
        
| File |
|------|
| `undefined` |

</details>

---

<think><details><summary>Thought Process</summary>用户想为 Remotion 的渲染组件库生成预览的 GIF 图，需要批量处理。我需要先了解这个项目的结构，看看有哪些组件，然后提供一个批量生成 GIF 预览的方案。

首先我需要：
1. 探索项目结构，了解有哪些组件
2. 查看现有的 Remotion 配置
3. 提供批量生成 GIF 的方案

让我先从项目根目录开始探索。</details></think>

---

<details>
            <summary>Listed directory .specstory • **1** results</summary>
        
| Name |
|-------|
| 📄 `.gitignore` |

</details>

---

现在让我看看具体的组件目录，了解组件的结构：

---

<details>
            <summary>Listed directory src/text • **6** results</summary>
        
| Name |
|-------|
| 📄 `BaseText.tsx` |
| 📁 `dynamic` |
| 📄 `index.md` |
| 📁 `effects` |
| 📁 `flowers` |
| 📄 `index.ts` |

</details>

---

<details>
            <summary>Listed directory src/text/effects • **3** results</summary>
        
| Name |
|-------|
| 📁 `textInEffects` |
| 📁 `textLoopEffects` |
| 📁 `textOutEffects` |

</details>

---

<details>
            <summary>Listed directory src/text/flowers • **2** results</summary>
        
| Name |
|-------|
| 📁 `decorative-text` |
| 📁 `advanced-text` |

</details>

---

<think><details><summary>Thought Process</summary>用户想为 Remotion 的渲染组件库生成预览的 GIF 图，需要批量处理。我需要先了解这个项目的结构，看看有哪些组件，然后提供一个批量生成 GIF 预览的方案。

首先我需要：
1. 探索项目结构，了解有哪些组件
2. 查看现有的 Remotion 配置
3. 提供批量生成 GIF 的方案

让我先从项目根目录开始探索。</details></think>

---

让我看看一个具体的组件目录，了解组件的文档结构：

---

<details>
            <summary>Listed directory src/text/effects/textInEffects • **5** results</summary>
        
| Name |
|-------|
| 📄 `StarJumpEffect.tsx` |
| 📄 `SlideInEffect.tsx` |
| 📄 `RotateScaleInEffect.tsx` |
| 📄 `FadeInEffect.tsx` |
| 📄 `index.ts` |

</details>

---

<think><details><summary>Thought Process</summary>用户想为 Remotion 的渲染组件库生成预览的 GIF 图，需要批量处理。我需要先了解这个项目的结构，看看有哪些组件，然后提供一个批量生成 GIF 预览的方案。

首先我需要：
1. 探索项目结构，了解有哪些组件
2. 查看现有的 Remotion 配置
3. 提供批量生成 GIF 的方案

让我先从项目根目录开始探索。</details></think>

---

Read file: src/text/effects/textInEffects/index.ts

---

<think><details><summary>Thought Process</summary>用户想为 Remotion 的渲染组件库生成预览的 GIF 图，需要批量处理。我需要先了解这个项目的结构，看看有哪些组件，然后提供一个批量生成 GIF 预览的方案。

首先我需要：
1. 探索项目结构，了解有哪些组件
2. 查看现有的 Remotion 配置
3. 提供批量生成 GIF 的方案

让我先从项目根目录开始探索。</details></think>

---

现在让我看看是否有已经存在的示例或预览文件：

---

<details>
            <summary>Grep search for "\.md$" • **0** files</summary>
        
No results found
</details>

---

<think><details><summary>Thought Process</summary>用户想为 Remotion 的渲染组件库生成预览的 GIF 图，需要批量处理。我需要先了解这个项目的结构，看看有哪些组件，然后提供一个批量生成 GIF 预览的方案。

首先我需要：
1. 探索项目结构，了解有哪些组件
2. 查看现有的 Remotion 配置
3. 提供批量生成 GIF 的方案

让我先从项目根目录开始探索。</details></think>

---

<details>
            <summary>Searched codebase "md" • **10** results</summary>
        
| File |
|------|
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |

</details>

---

<think><details><summary>Thought Process</summary>用户想为 Remotion 的渲染组件库生成预览的 GIF 图，需要批量处理。我需要先了解这个项目的结构，看看有哪些组件，然后提供一个批量生成 GIF 预览的方案。

首先我需要：
1. 探索项目结构，了解有哪些组件
2. 查看现有的 Remotion 配置
3. 提供批量生成 GIF 的方案

让我先从项目根目录开始探索。</details></think>

---

Read file: src/text/index.md

---

<think><details><summary>Thought Process</summary>了解项目结构后，我发现这是一个基于 Remotion 的组件库，包含：

1. 文本特效组件（入场、出场、循环特效）
2. 花字组件（高级文本、装饰性文本）
3. 图片组件
4. 视频组件
5. 个人卡片组件
6. 场景特效组件
7. 贴纸组件

现在我需要为用户创建一个批量生成 GIF 预览的解决方案。有几种方法：

1. 使用 Remotion CLI 直接渲染 GIF
2. 创建一个自动化脚本，为每个组件生成预览
3. 使用 Puppeteer 或类似工具截屏生成 GIF

让我为用户提供一个完整的批量处理方案。

首先我需要：
1. 创建一个脚本来遍历所有组件
2. 为每个组件创建一个预览文件
3. 使用 Remotion CLI 批量渲染 GIF
4. 可选：创建一个预览页面展示所有 GIF

让我开始创建这个方案。</details></think>

---

基于您的 Remotion 组件库项目结构，我来为您创建一个批量生成 GIF 预览的完整解决方案。

## 🎯 解决方案概述

我将创建一个自动化脚本，为您的所有组件生成 GIF 预览，包括：
- 文本特效组件（入场、出场、循环特效）
- 花字组件（高级文本、装饰性文本）
- 场景特效组件
- 贴纸组件等

## 📋 实现步骤

首先，让我创建批量生成 GIF 的核心脚本：

---

```diff
- 
+ const fs = require('fs');
+ const path = require('path');
+ const { execSync } = require('child_process');
+ 
+ // 组件配置
+ const componentGroups = {
+   FlowerTextComponents: {
+     title: '花字组件',
+     sampleText: '万强文字特效',
+     duration: 90, // 3秒 @30fps
+     dimensions: { width: 600, height: 200 }
+   },
+   TextLoopEffectsComponents: {
+     title: '文本循环特效',
+     sampleText: '循环动画效果',
+     duration: 180, // 6秒 @30fps
+     dimensions: { width: 600, height: 200 }
+   },
+   TextInEffectsComponents: {
+     title: '文本入场特效',
+     sampleText: '入场动画效果',
+     duration: 120, // 4秒 @30fps
+     dimensions: { width: 600, height: 200 }
+   },
+   TextOutEffectsComponents: {
+     title: '文本出场特效',
+     sampleText: '出场动画效果',
+     duration: 120, // 4秒 @30fps
+     dimensions: { width: 600, height: 200 }
+   },
+   DynamicSectionTextEffectsComponents: {
+     title: '动态文本特效',
+     sampleText: '动态文本效果',
+     duration: 150, // 5秒 @30fps
+     dimensions: { width: 600, height: 200 }
+   },
+   StickerComponents: {
+     title: '贴纸组件',
+     sampleText: '贴纸效果',
+     duration: 90, // 3秒 @30fps
+     dimensions: { width: 400, height: 400 }
+   },
+   SceneEffectsComponents: {
+     title: '场景特效',
+     sampleText: '场景动画',
+     duration: 120, // 4秒 @30fps
+     dimensions: { width: 800, height: 450 }
+   }
+ };
+ 
+ // 创建预览目录
+ const previewDir = path.join(__dirname, '../previews');
+ const gifDir = path.join(previewDir, 'gifs');
+ const compositionsDir = path.join(previewDir, 'compositions');
+ 
+ if (!fs.existsSync(previewDir)) fs.mkdirSync(previewDir, { recursive: true });
+ if (!fs.existsSync(gifDir)) fs.mkdirSync(gifDir, { recursive: true });
+ if (!fs.existsSync(compositionsDir)) fs.mkdirSync(compositionsDir, { recursive: true });
+ 
+ // 生成单个组件的预览文件
+ function generateComponentPreview(componentName, groupName, config) {
+   const previewContent = `
+ import React from 'react';
+ import { Composition } from 'remotion';
+ import { ${componentName} } from '../../src';
+ 
+ // 组件预览
+ const ${componentName}Preview = () => {
+   return (
+     <div style={{
+       display: 'flex',
+       justifyContent: 'center',
+       alignItems: 'center',
+       width: '100%',
+       height: '100%',
+       backgroundColor: '#000',
+       color: '#fff'
+     }}>
+       <${componentName}
+         text="${config.sampleText}"
+         style={{
+           fontSize: 36,
+           fontWeight: 'bold',
+           color: '#fff',
+           textAlign: 'center'
+         }}
+       />
+     </div>
+   );
+ };
+ 
+ // 导出 Composition
+ export default () => (
+   <Composition
+     id="${componentName}"
+     component={${componentName}Preview}
+     durationInFrames={${config.duration}}
+     fps={30}
+     width={${config.dimensions.width}}
+     height={${config.dimensions.height}}
+   />
+ );
+ `;
+ 
+   const filePath = path.join(compositionsDir, `${componentName}.tsx`);
+   fs.writeFileSync(filePath, previewContent);
+   return filePath;
+ }
+ 
+ // 生成主要的 Remotion 配置文件
+ function generateRemotionConfig() {
+   const configContent = `
+ import { Config } from '@remotion/cli/config';
+ 
+ Config.setImageFormat('jpeg');
+ Config.setPixelFormat('yuv420p');
+ Config.setCodec('h264');
+ Config.setConcurrency(2);
+ Config.setQuality(8);
+ 
+ export default {};
+ `;
+ 
+   fs.writeFileSync(path.join(__dirname, '../remotion.config.ts'), configContent);
+ }
+ 
+ // 生成主索引文件
+ function generateMainIndex(componentList) {
+   const imports = componentList.map(comp => 
+     `import ${comp.name}Composition from './compositions/${comp.name}';`
+   ).join('\n');
+ 
+   const compositions = componentList.map(comp => 
+     `      <${comp.name}Composition />`
+   ).join('\n');
+ 
+   const indexContent = `
+ import React from 'react';
+ import { registerRoot } from 'remotion';
+ ${imports}
+ 
+ const RemotionRoot = () => {
+   return (
+     <>
+ ${compositions}
+     </>
+   );
+ };
+ 
+ registerRoot(RemotionRoot);
+ `;
+ 
+   fs.writeFileSync(path.join(previewDir, 'index.tsx'), indexContent);
+ }
+ 
+ // 批量渲染 GIF
+ async function renderGifs(componentList) {
+   console.log('🎬 开始批量渲染 GIF...');
+   
+   for (const component of componentList) {
+     try {
+       console.log(`📹 渲染 ${component.name}...`);
+       
+       const outputPath = path.join(gifDir, `${component.name}.gif`);
+       const command = `npx remotion render ${component.name} ${outputPath} --image-format=gif --quality=8`;
+       
+       execSync(command, { 
+         cwd: path.join(__dirname, '../previews'),
+         stdio: 'inherit' 
+       });
+       
+       console.log(`✅ ${component.name} 渲染完成`);
+     } catch (error) {
+       console.error(`❌ ${component.name} 渲染失败:`, error.message);
+     }
+   }
+ }
+ 
+ // 主执行函数
+ async function main() {
+   console.log('🚀 开始生成组件预览...');
+   
+   // 生成 Remotion 配置
+   generateRemotionConfig();
+   
+   // 读取组件信息
+   const srcIndexPath = path.join(__dirname, '../src/index.ts');
+   const srcContent = fs.readFileSync(srcIndexPath, 'utf8');
+   
+   const componentList = [];
+   
+   // 解析每个组件组
+   Object.keys(componentGroups).forEach(groupName => {
+     const config = componentGroups[groupName];
+     
+     // 这里需要实际解析组件名称，暂时使用示例
+     // 您需要根据实际的组件导出结构调整这部分
+     const mockComponents = getMockComponentsForGroup(groupName);
+     
+     mockComponents.forEach(componentName => {
+       console.log(`📝 生成 ${componentName} 预览文件...`);
+       generateComponentPreview(componentName, groupName, config);
+       componentList.push({ name: componentName, group: groupName });
+     });
+   });
+   
+   // 生成主索引文件
+   generateMainIndex(componentList);
+   
+   // 渲染 GIF
+   await renderGifs(componentList);
+   
+   console.log('🎉 批量生成完成！');
+   console.log(`📂 GIF 文件保存在: ${gifDir}`);
+ }
+ 
+ // 临时函数：获取组件名称（需要根据实际情况调整）
+ function getMockComponentsForGroup(groupName) {
+   const mockData = {
+     TextInEffectsComponents: ['FadeInEffect', 'SlideInEffect', 'RotateScaleInEffect', 'StarJumpEffect'],
+     TextLoopEffectsComponents: ['MachineCodeEffect'],
+     FlowerTextComponents: ['MultiShadowText', 'DiamondText', 'SupremeGoldText'],
+     // 其他组件组...
+   };
+   
+   return mockData[groupName] || [];
+ }
+ 
+ // 运行主函数
+ if (require.main === module) {
+   main().catch(console.error);
+ }
+ 
+ module.exports = { main, generateComponentPreview }; 
```

---

现在让我创建一个更智能的组件解析脚本，自动从您的源代码中提取组件信息：

---

```diff
- 
+ const fs = require('fs');
+ const path = require('path');
+ 
+ // 解析组件信息的工具函数
+ class ComponentParser {
+   constructor(srcPath) {
+     this.srcPath = srcPath;
+     this.components = new Map();
+   }
+ 
+   // 解析指定目录下的所有组件
+   parseDirectory(dirPath, category = '') {
+     const fullPath = path.join(this.srcPath, dirPath);
+     
+     if (!fs.existsSync(fullPath)) {
+       console.warn(`目录不存在: ${fullPath}`);
+       return [];
+     }
+ 
+     const items = fs.readdirSync(fullPath);
+     const components = [];
+ 
+     for (const item of items) {
+       const itemPath = path.join(fullPath, item);
+       const stat = fs.statSync(itemPath);
+ 
+       if (stat.isDirectory()) {
+         // 递归解析子目录
+         components.push(...this.parseDirectory(path.join(dirPath, item), category));
+       } else if (item.endsWith('.tsx') && !item.startsWith('Base') && item !== 'index.tsx') {
+         // 解析组件文件
+         const componentInfo = this.parseComponentFile(itemPath, dirPath, category);
+         if (componentInfo) {
+           components.push(componentInfo);
+         }
+       }
+     }
+ 
+     return components;
+   }
+ 
+   // 解析单个组件文件
+   parseComponentFile(filePath, relativePath, category) {
+     try {
+       const content = fs.readFileSync(filePath, 'utf8');
+       const fileName = path.basename(filePath, '.tsx');
+       
+       // 检查是否是有效的 React 组件
+       const hasDefaultExport = /export\s+default\s+/.test(content);
+       const hasComponentDeclaration = new RegExp(`(const|function)\\s+${fileName}`, 'g').test(content);
+       
+       if (!hasDefaultExport && !hasComponentDeclaration) {
+         return null;
+       }
+ 
+       // 提取组件的参数接口
+       const propsPattern = /interface\s+(\w+Props)\s*{([^}]+)}/;
+       const propsMatch = content.match(propsPattern);
+       
+       let props = { text: 'string', style: 'React.CSSProperties' };
+       if (propsMatch) {
+         // 简单解析 props 接口
+         const propsContent = propsMatch[2];
+         const propLines = propsContent.split('\n').map(line => line.trim()).filter(line => line);
+         
+         props = {};
+         propLines.forEach(line => {
+           const propMatch = line.match(/(\w+)\??\s*:\s*([^;]+);?/);
+           if (propMatch) {
+             props[propMatch[1]] = propMatch[2];
+           }
+         });
+       }
+ 
+       // 判断组件类型
+       const componentType = this.determineComponentType(content, relativePath);
+       
+       return {
+         name: fileName,
+         filePath: path.relative(this.srcPath, filePath),
+         category: category || this.getCategoryFromPath(relativePath),
+         type: componentType,
+         props: props,
+         hasAnimation: this.hasAnimation(content),
+         description: this.extractDescription(content)
+       };
+     } catch (error) {
+       console.error(`解析组件文件失败 ${filePath}:`, error.message);
+       return null;
+     }
+   }
+ 
+   // 判断组件类型
+   determineComponentType(content, relativePath) {
+     if (relativePath.includes('textInEffects')) return 'textIn';
+     if (relativePath.includes('textOutEffects')) return 'textOut';
+     if (relativePath.includes('textLoopEffects')) return 'textLoop';
+     if (relativePath.includes('flowers')) return 'flower';
+     if (relativePath.includes('dynamic')) return 'dynamic';
+     if (relativePath.includes('sticker')) return 'sticker';
+     if (relativePath.includes('sence-effects')) return 'scene';
+     if (relativePath.includes('global-effects')) return 'globalEffect';
+     return 'other';
+   }
+ 
+   // 根据路径获取分类
+   getCategoryFromPath(relativePath) {
+     if (relativePath.includes('text/effects/textInEffects')) return '文本入场特效';
+     if (relativePath.includes('text/effects/textOutEffects')) return '文本出场特效';
+     if (relativePath.includes('text/effects/textLoopEffects')) return '文本循环特效';
+     if (relativePath.includes('text/flowers')) return '花字组件';
+     if (relativePath.includes('text/dynamic')) return '动态文本特效';
+     if (relativePath.includes('sticker')) return '贴纸组件';
+     if (relativePath.includes('sence-effects')) return '场景特效';
+     if (relativePath.includes('global-effects')) return '全局特效';
+     if (relativePath.includes('image')) return '图片组件';
+     if (relativePath.includes('video')) return '视频组件';
+     if (relativePath.includes('person-card')) return '个人卡片';
+     return '其他组件';
+   }
+ 
+   // 检查是否包含动画
+   hasAnimation(content) {
+     const animationKeywords = [
+       'useCurrentFrame',
+       'interpolate',
+       'spring',
+       'Easing',
+       'transform',
+       'transition',
+       'keyframes',
+       '@keyframes'
+     ];
+     
+     return animationKeywords.some(keyword => content.includes(keyword));
+   }
+ 
+   // 提取组件描述
+   extractDescription(content) {
+     // 尝试从注释中提取描述
+     const commentPattern = /\/\*\*\s*\n\s*\*\s*(.+?)\s*\n/;
+     const match = content.match(commentPattern);
+     return match ? match[1] : '';
+   }
+ 
+   // 解析所有组件
+   parseAllComponents() {
+     const componentPaths = [
+       'text/effects/textInEffects',
+       'text/effects/textOutEffects', 
+       'text/effects/textLoopEffects',
+       'text/flowers/advanced-text',
+       'text/flowers/decorative-text',
+       'text/dynamic/sections',
+       'sticker',
+       'sence-effects/in',
+       'sence-effects/out',
+       'global-effects',
+       'image',
+       'video',
+       'person-card'
+     ];
+ 
+     const allComponents = [];
+     
+     componentPaths.forEach(dirPath => {
+       console.log(`🔍 解析目录: ${dirPath}`);
+       const components = this.parseDirectory(dirPath);
+       allComponents.push(...components);
+       console.log(`   找到 ${components.length} 个组件`);
+     });
+ 
+     return allComponents;
+   }
+ 
+   // 生成组件配置
+   generateComponentConfig(components) {
+     const config = {};
+     
+     components.forEach(comp => {
+       const groupKey = this.getGroupKey(comp.type);
+       if (!config[groupKey]) {
+         config[groupKey] = {
+           title: comp.category,
+           components: [],
+           sampleText: this.getSampleText(comp.type),
+           duration: this.getDuration(comp.type),
+           dimensions: this.getDimensions(comp.type)
+         };
+       }
+       
+       config[groupKey].components.push({
+         name: comp.name,
+         description: comp.description,
+         props: comp.props,
+         hasAnimation: comp.hasAnimation
+       });
+     });
+ 
+     return config;
+   }
+ 
+   // 获取组件组键名
+   getGroupKey(type) {
+     const typeMap = {
+       'textIn': 'TextInEffectsComponents',
+       'textOut': 'TextOutEffectsComponents',
+       'textLoop': 'TextLoopEffectsComponents',
+       'flower': 'FlowerTextComponents',
+       'dynamic': 'DynamicSectionTextEffectsComponents',
+       'sticker': 'StickerComponents',
+       'scene': 'SceneEffectsComponents',
+       'globalEffect': 'VideoEffectsComponents',
+       'other': 'OtherComponents'
+     };
+     return typeMap[type] || 'OtherComponents';
+   }
+ 
+   // 获取示例文本
+   getSampleText(type) {
+     const textMap = {
+       'textIn': '入场动画效果',
+       'textOut': '出场动画效果', 
+       'textLoop': '循环动画效果',
+       'flower': '万强文字特效',
+       'dynamic': '动态文本效果',
+       'sticker': '贴纸效果',
+       'scene': '场景动画',
+       'globalEffect': '全局特效'
+     };
+     return textMap[type] || '组件预览';
+   }
+ 
+   // 获取动画时长
+   getDuration(type) {
+     const durationMap = {
+       'textIn': 120,    // 4秒
+       'textOut': 120,   // 4秒
+       'textLoop': 180,  // 6秒
+       'flower': 90,     // 3秒
+       'dynamic': 150,   // 5秒
+       'sticker': 90,    // 3秒
+       'scene': 120,     // 4秒
+       'globalEffect': 120
+     };
+     return durationMap[type] || 90;
+   }
+ 
+   // 获取画面尺寸
+   getDimensions(type) {
+     if (type === 'sticker') {
+       return { width: 400, height: 400 };
+     }
+     if (type === 'scene') {
+       return { width: 800, height: 450 };
+     }
+     return { width: 600, height: 200 };
+   }
+ }
+ 
+ // 导出解析器
+ module.exports = ComponentParser;
+ 
+ // 如果直接运行此脚本
+ if (require.main === module) {
+   const srcPath = path.join(__dirname, '../src');
+   const parser = new ComponentParser(srcPath);
+   
+   console.log('🚀 开始解析组件...');
+   const components = parser.parseAllComponents();
+   const config = parser.generateComponentConfig(components);
+   
+   console.log(`\n📊 解析结果:`);
+   console.log(`总共找到 ${components.length} 个组件`);
+   
+   Object.keys(config).forEach(groupKey => {
+     const group = config[groupKey];
+     console.log(`  ${group.title}: ${group.components.length} 个组件`);
+   });
+   
+   // 保存配置到文件
+   const configPath = path.join(__dirname, 'component-config.json');
+   fs.writeFileSync(configPath, JSON.stringify({ components, config }, null, 2));
+   console.log(`\n💾 配置已保存到: ${configPath}`);
+ } 
```

---

<think><details><summary>Thought Process</summary>了解项目结构后，我发现这是一个基于 Remotion 的组件库，包含：

1. 文本特效组件（入场、出场、循环特效）
2. 花字组件（高级文本、装饰性文本）
3. 图片组件
4. 视频组件
5. 个人卡片组件
6. 场景特效组件
7. 贴纸组件

现在我需要为用户创建一个批量生成 GIF 预览的解决方案。有几种方法：

1. 使用 Remotion CLI 直接渲染 GIF
2. 创建一个自动化脚本，为每个组件生成预览
3. 使用 Puppeteer 或类似工具截屏生成 GIF

让我为用户提供一个完整的批量处理方案。

首先我需要：
1. 创建一个脚本来遍历所有组件
2. 为每个组件创建一个预览文件
3. 使用 Remotion CLI 批量渲染 GIF
4. 可选：创建一个预览页面展示所有 GIF

让我开始创建这个方案。</details></think>

---

现在让我更新主要的预览生成脚本，使其能够使用组件解析器：

---

```diff
- 
+ const fs = require('fs');
+ const path = require('path');
+ const { execSync } = require('child_process');
+ const ComponentParser = require('./component-parser');
+ 
+ class PreviewGenerator {
+   constructor(srcPath) {
+     this.srcPath = srcPath;
+     this.parser = new ComponentParser(srcPath);
+     this.previewDir = path.join(__dirname, '../previews');
+     this.gifDir = path.join(this.previewDir, 'gifs');
+     this.compositionsDir = path.join(this.previewDir, 'compositions');
+     
+     this.ensureDirectories();
+   }
+ 
+   // 确保目录存在
+   ensureDirectories() {
+     [this.previewDir, this.gifDir, this.compositionsDir].forEach(dir => {
+       if (!fs.existsSync(dir)) {
+         fs.mkdirSync(dir, { recursive: true });
+       }
+     });
+   }
+ 
+   // 生成单个组件的预览 Composition
+   generateComponentComposition(component, config) {
+     const { name, props, type } = component;
+     const { sampleText, duration, dimensions } = config;
+ 
+     // 根据组件类型生成不同的预览代码
+     let componentJSX = '';
+     let additionalProps = '';
+ 
+     // 根据组件属性动态生成 props
+     if (props.text) {
+       additionalProps += `        text="${sampleText}"\n`;
+     }
+     if (props.style) {
+       additionalProps += `        style={{\n          fontSize: 36,\n          fontWeight: 'bold',\n          color: '#fff',\n          textAlign: 'center'\n        }}\n`;
+     }
+     if (props.durationFrames) {
+       additionalProps += `        durationFrames={${duration}}\n`;
+     }
+     if (props.speed && type === 'textLoop') {
+       additionalProps += `        speed={1}\n`;
+     }
+ 
+     // 特殊组件类型的额外配置
+     if (type === 'sticker') {
+       additionalProps = `        style={{\n          width: 200,\n          height: 200\n        }}\n`;
+     }
+ 
+     componentJSX = `      <${name}\n${additionalProps}      />`;
+ 
+     const compositionContent = `
+ import React from 'react';
+ import { Composition } from 'remotion';
+ import { ${name} } from '../../src';
+ 
+ // ${name} 预览组件
+ const ${name}Preview: React.FC = () => {
+   return (
+     <div style={{
+       display: 'flex',
+       justifyContent: 'center',
+       alignItems: 'center',
+       width: '100%',
+       height: '100%',
+       backgroundColor: '#000',
+       color: '#fff',
+       padding: 20
+     }}>
+ ${componentJSX}
+     </div>
+   );
+ };
+ 
+ // 导出 Composition
+ export const ${name}Composition: React.FC = () => (
+   <Composition
+     id="${name}"
+     component={${name}Preview}
+     durationInFrames={${duration}}
+     fps={30}
+     width={${dimensions.width}}
+     height={${dimensions.height}}
+   />
+ );
+ `;
+ 
+     const filePath = path.join(this.compositionsDir, `${name}.tsx`);
+     fs.writeFileSync(filePath, compositionContent);
+     console.log(`✅ 生成预览文件: ${name}.tsx`);
+     
+     return filePath;
+   }
+ 
+   // 生成 Remotion 配置文件
+   generateRemotionConfig() {
+     const configContent = `
+ import { Config } from '@remotion/cli/config';
+ 
+ // 设置渲染配置
+ Config.setImageFormat('jpeg');
+ Config.setPixelFormat('yuv420p');
+ Config.setCodec('h264');
+ Config.setConcurrency(1); // 降低并发以避免内存问题
+ Config.setQuality(8);
+ 
+ // GIF 特定配置
+ Config.setImageSequence(false);
+ 
+ export default {};
+ `;
+ 
+     fs.writeFileSync(path.join(this.previewDir, 'remotion.config.ts'), configContent);
+   }
+ 
+   // 生成主索引文件
+   generateMainIndex(componentList) {
+     const imports = componentList.map(comp => 
+       `import { ${comp.name}Composition } from './compositions/${comp.name}';`
+     ).join('\n');
+ 
+     const compositions = componentList.map(comp => 
+       `      <${comp.name}Composition />`
+     ).join('\n');
+ 
+     const indexContent = `
+ import React from 'react';
+ import { registerRoot } from 'remotion';
+ ${imports}
+ 
+ // Remotion 根组件
+ const RemotionRoot: React.FC = () => {
+   return (
+     <>
+ ${compositions}
+     </>
+   );
+ };
+ 
+ registerRoot(RemotionRoot);
+ `;
+ 
+     fs.writeFileSync(path.join(this.previewDir, 'index.tsx'), indexContent);
+   }
+ 
+   // 生成 package.json
+   generatePackageJson() {
+     const packageContent = {
+       name: "remotion-previews",
+       version: "1.0.0",
+       description: "Component previews for remotion-wanqiang",
+       scripts: {
+         "render": "remotion render",
+         "preview": "remotion preview",
+         "render-all": "node ../scripts/render-all-gifs.js"
+       },
+       dependencies: {
+         "remotion": "4.0.306",
+         "react": "^18.0.0",
+         "react-dom": "^18.0.0",
+         "remotion-wanqiang": "file:.."
+       },
+       devDependencies: {
+         "@types/react": "^18.0.0",
+         "@types/react-dom": "^18.0.0",
+         "typescript": "^5.0.0"
+       }
+     };
+ 
+     fs.writeFileSync(
+       path.join(this.previewDir, 'package.json'), 
+       JSON.stringify(packageContent, null, 2)
+     );
+   }
+ 
+   // 生成 TypeScript 配置
+   generateTsConfig() {
+     const tsConfigContent = {
+       compilerOptions: {
+         target: "es2018",
+         lib: ["dom", "dom.iterable", "esnext"],
+         allowJs: true,
+         skipLibCheck: true,
+         esModuleInterop: true,
+         allowSyntheticDefaultImports: true,
+         strict: true,
+         forceConsistentCasingInFileNames: true,
+         module: "esnext",
+         moduleResolution: "node",
+         resolveJsonModule: true,
+         isolatedModules: true,
+         noEmit: true,
+         jsx: "react-jsx"
+       },
+       include: [
+         "**/*"
+       ]
+     };
+ 
+     fs.writeFileSync(
+       path.join(this.previewDir, 'tsconfig.json'),
+       JSON.stringify(tsConfigContent, null, 2)
+     );
+   }
+ 
+   // 批量渲染 GIF
+   async renderGifs(componentList, options = {}) {
+     const { 
+       concurrent = false, 
+       quality = 8,
+       scale = 1,
+       frameRate = 30
+     } = options;
+ 
+     console.log('🎬 开始批量渲染 GIF...');
+     console.log(`📊 共需要渲染 ${componentList.length} 个组件`);
+ 
+     // 安装依赖（如果需要）
+     try {
+       console.log('📦 检查依赖...');
+       execSync('npm install', { 
+         cwd: this.previewDir,
+         stdio: 'inherit'
+       });
+     } catch (error) {
+       console.warn('⚠️ 依赖安装可能失败，继续渲染...');
+     }
+ 
+     const renderPromises = [];
+     const renderResults = [];
+ 
+     for (const [index, component] of componentList.entries()) {
+       const renderTask = async () => {
+         try {
+           console.log(`📹 [${index + 1}/${componentList.length}] 渲染 ${component.name}...`);
+           
+           const outputPath = path.join(this.gifDir, `${component.name}.gif`);
+           
+           // 构建渲染命令
+           const renderCommand = [
+             'npx remotion render',
+             component.name,
+             `"${outputPath}"`,
+             '--image-format=gif',
+             `--quality=${quality}`,
+             `--scale=${scale}`,
+             `--frame-range=0-${component.config.duration - 1}`,
+             '--overwrite'
+           ].join(' ');
+ 
+           console.log(`🔧 执行命令: ${renderCommand}`);
+           
+           execSync(renderCommand, { 
+             cwd: this.previewDir,
+             stdio: 'pipe'
+           });
+           
+           console.log(`✅ ${component.name} 渲染完成`);
+           return { name: component.name, success: true, path: outputPath };
+         } catch (error) {
+           console.error(`❌ ${component.name} 渲染失败:`, error.message);
+           return { name: component.name, success: false, error: error.message };
+         }
+       };
+ 
+       if (concurrent) {
+         renderPromises.push(renderTask());
+       } else {
+         const result = await renderTask();
+         renderResults.push(result);
+       }
+     }
+ 
+     // 如果是并发模式，等待所有任务完成
+     if (concurrent) {
+       const results = await Promise.all(renderPromises);
+       renderResults.push(...results);
+     }
+ 
+     return renderResults;
+   }
+ 
+   // 生成预览页面
+   generatePreviewPage(componentList, renderResults) {
+     const successfulRenders = renderResults.filter(r => r.success);
+     
+     let previewHTML = `
+ <!DOCTYPE html>
+ <html lang="zh-CN">
+ <head>
+     <meta charset="UTF-8">
+     <meta name="viewport" content="width=device-width, initial-scale=1.0">
+     <title>Remotion 万强组件库 - 预览</title>
+     <style>
+         body {
+             font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
+             margin: 0;
+             padding: 20px;
+             background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
+             min-height: 100vh;
+         }
+         .header {
+             text-align: center;
+             color: white;
+             margin-bottom: 40px;
+         }
+         .header h1 {
+             font-size: 2.5em;
+             margin-bottom: 10px;
+             text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
+         }
+         .stats {
+             display: flex;
+             justify-content: center;
+             gap: 30px;
+             margin-bottom: 40px;
+         }
+         .stat-item {
+             background: rgba(255,255,255,0.1);
+             padding: 15px 25px;
+             border-radius: 10px;
+             color: white;
+             text-align: center;
+             backdrop-filter: blur(10px);
+         }
+         .stat-number {
+             font-size: 2em;
+             font-weight: bold;
+             display: block;
+         }
+         .components-grid {
+             display: grid;
+             grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
+             gap: 20px;
+             max-width: 1400px;
+             margin: 0 auto;
+         }
+         .component-card {
+             background: rgba(255,255,255,0.95);
+             border-radius: 15px;
+             padding: 20px;
+             box-shadow: 0 8px 32px rgba(0,0,0,0.1);
+             backdrop-filter: blur(10px);
+             transition: transform 0.3s ease;
+         }
+         .component-card:hover {
+             transform: translateY(-5px);
+         }
+         .component-title {
+             font-size: 1.2em;
+             font-weight: bold;
+             margin-bottom: 10px;
+             color: #333;
+             display: flex;
+             align-items: center;
+             gap: 10px;
+         }
+         .component-category {
+             background: #667eea;
+             color: white;
+             padding: 4px 8px;
+             border-radius: 12px;
+             font-size: 0.8em;
+         }
+         .gif-container {
+             text-align: center;
+             margin: 15px 0;
+             background: #000;
+             border-radius: 10px;
+             overflow: hidden;
+         }
+         .gif-container img {
+             width: 100%;
+             height: auto;
+             display: block;
+         }
+         .component-info {
+             font-size: 0.9em;
+             color: #666;
+             margin-top: 10px;
+         }
+         .failed-render {
+             background: rgba(244,67,54,0.1);
+             border: 1px solid rgba(244,67,54,0.3);
+         }
+         .failed-render .component-title {
+             color: #d32f2f;
+         }
+         .error-message {
+             color: #d32f2f;
+             font-size: 0.85em;
+             margin-top: 10px;
+             padding: 10px;
+             background: rgba(244,67,54,0.05);
+             border-radius: 5px;
+         }
+     </style>
+ </head>
+ <body>
+     <div class="header">
+         <h1>🎬 Remotion 万强组件库</h1>
+         <p>自动生成的组件预览</p>
+     </div>
+     
+     <div class="stats">
+         <div class="stat-item">
+             <span class="stat-number">${componentList.length}</span>
+             <span>总组件数</span>
+         </div>
+         <div class="stat-item">
+             <span class="stat-number">${successfulRenders.length}</span>
+             <span>成功渲染</span>
+         </div>
+         <div class="stat-item">
+             <span class="stat-number">${renderResults.length - successfulRenders.length}</span>
+             <span>渲染失败</span>
+         </div>
+     </div>
+ 
+     <div class="components-grid">
+ `;
+ 
+     // 为每个组件生成卡片
+     renderResults.forEach(result => {
+       const component = componentList.find(c => c.name === result.name);
+       if (!component) return;
+ 
+       const cardClass = result.success ? 'component-card' : 'component-card failed-render';
+       const gifPath = result.success ? `./gifs/${result.name}.gif` : '';
+ 
+       previewHTML += `
+         <div class="${cardClass}">
+             <div class="component-title">
+                 ${result.name}
+                 <span class="component-category">${component.category}</span>
+             </div>
+             ${result.success ? `
+                 <div class="gif-container">
+                     <img src="${gifPath}" alt="${result.name} 预览" loading="lazy">
+                 </div>
+             ` : `
+                 <div class="error-message">
+                     渲染失败: ${result.error || '未知错误'}
+                 </div>
+             `}
+             <div class="component-info">
+                 <div><strong>类型:</strong> ${component.type}</div>
+                 <div><strong>动画:</strong> ${component.hasAnimation ? '是' : '否'}</div>
+                 ${component.description ? `<div><strong>描述:</strong> ${component.description}</div>` : ''}
+             </div>
+         </div>
+       `;
+     });
+ 
+     previewHTML += `
+     </div>
+     <div style="text-align: center; margin-top: 40px; color: white;">
+         <p>生成时间: ${new Date().toLocaleString('zh-CN')}</p>
+     </div>
+ </body>
+ </html>
+ `;
+ 
+     fs.writeFileSync(path.join(this.previewDir, 'index.html'), previewHTML);
+     console.log(`🌐 预览页面已生成: ${path.join(this.previewDir, 'index.html')}`);
+   }
+ 
+   // 主执行函数
+   async main(options = {}) {
+     console.log('🚀 开始生成组件预览...');
+     
+     // 解析所有组件
+     console.log('🔍 解析组件...');
+     const components = this.parser.parseAllComponents();
+     const config = this.parser.generateComponentConfig(components);
+     
+     console.log(`📊 找到 ${components.length} 个组件`);
+     Object.keys(config).forEach(groupKey => {
+       const group = config[groupKey];
+       console.log(`  ${group.title}: ${group.components.length} 个组件`);
+     });
+ 
+     if (components.length === 0) {
+       console.log('⚠️ 没有找到可渲染的组件');
+       return;
+     }
+ 
+     // 生成必要文件
+     console.log('📝 生成配置文件...');
+     this.generateRemotionConfig();
+     this.generatePackageJson();
+     this.generateTsConfig();
+ 
+     // 生成组件预览文件
+     console.log('📄 生成组件预览文件...');
+     const componentList = [];
+     
+     Object.keys(config).forEach(groupKey => {
+       const group = config[groupKey];
+       group.components.forEach(comp => {
+         this.generateComponentComposition(comp, group);
+         componentList.push({
+           name: comp.name,
+           category: group.title,
+           type: groupKey,
+           hasAnimation: comp.hasAnimation,
+           description: comp.description,
+           config: group
+         });
+       });
+     });
+ 
+     // 生成主索引文件
+     this.generateMainIndex(componentList);
+ 
+     // 渲染 GIF
+     const renderResults = await this.renderGifs(componentList, options);
+ 
+     // 生成预览页面
+     this.generatePreviewPage(componentList, renderResults);
+ 
+     console.log('🎉 批量生成完成！');
+     console.log(`📂 输出目录: ${this.previewDir}`);
+     console.log(`🌐 预览页面: ${path.join(this.previewDir, 'index.html')}`);
+     
+     return { componentList, renderResults };
+   }
+ }
+ 
+ // 如果直接运行此脚本
+ if (require.main === module) {
+   const srcPath = path.join(__dirname, '../src');
+   const generator = new PreviewGenerator(srcPath);
+   
+   // 解析命令行参数
+   const args = process.argv.slice(2);
+   const options = {
+     concurrent: args.includes('--concurrent'),
+     quality: parseInt(args.find(arg => arg.startsWith('--quality='))?.split('=')[1]) || 8,
+     scale: parseFloat(args.find(arg => arg.startsWith('--scale='))?.split('=')[1]) || 1
+   };
+ 
+   generator.main(options).catch(console.error);
+ }
+ 
+ module.exports = PreviewGenerator; 
```

---

<think><details><summary>Thought Process</summary>现在让我创建一个简化的执行脚本，方便用户使用：</details></think>

---

现在让我创建一个简化的执行脚本和使用说明：

---

```diff
- 
+ #!/usr/bin/env node
+ 
+ const PreviewGenerator = require('./generate-previews-enhanced');
+ const path = require('path');
+ 
+ // 显示使用说明
+ function showUsage() {
+   console.log(`
+ 🎬 Remotion 万强组件库 - GIF 预览生成器
+ ===========================================
+ 
+ 用法:
+   node scripts/run.js [选项]
+ 
+ 选项:
+   --help              显示此帮助信息
+   --parse-only        仅解析组件，不渲染 GIF
+   --concurrent        并发渲染（更快但占用更多资源）
+   --quality=<1-10>    GIF 质量，默认为 8
+   --scale=<0.1-2.0>   渲染缩放比例，默认为 1
+   --dry-run           预览将要执行的操作，不实际运行
+ 
+ 示例:
+   node scripts/run.js                    # 默认渲染所有组件
+   node scripts/run.js --concurrent       # 并发渲染，速度更快
+   node scripts/run.js --quality=6        # 设置 GIF 质量为 6
+   node scripts/run.js --parse-only       # 仅解析组件信息
+   node scripts/run.js --dry-run          # 预览操作
+ 
+ 输出:
+   📂 previews/                 # 预览目录
+   ├── gifs/                   # 生成的 GIF 文件
+   ├── compositions/           # 组件预览文件
+   ├── index.html             # 预览页面
+   └── remotion.config.ts     # Remotion 配置
+ 
+ `);
+ }
+ 
+ // 解析命令行参数
+ function parseArgs() {
+   const args = process.argv.slice(2);
+   
+   return {
+     help: args.includes('--help') || args.includes('-h'),
+     parseOnly: args.includes('--parse-only'),
+     concurrent: args.includes('--concurrent'),
+     dryRun: args.includes('--dry-run'),
+     quality: parseInt(args.find(arg => arg.startsWith('--quality='))?.split('=')[1]) || 8,
+     scale: parseFloat(args.find(arg => arg.startsWith('--scale='))?.split('=')[1]) || 1
+   };
+ }
+ 
+ // 主函数
+ async function main() {
+   const options = parseArgs();
+   
+   if (options.help) {
+     showUsage();
+     return;
+   }
+   
+   console.log('🚀 Remotion 万强组件库 - GIF 预览生成器');
+   console.log('==========================================\n');
+   
+   const srcPath = path.join(__dirname, '../src');
+   const generator = new PreviewGenerator(srcPath);
+   
+   if (options.parseOnly) {
+     console.log('🔍 仅解析组件信息...');
+     const components = generator.parser.parseAllComponents();
+     const config = generator.parser.generateComponentConfig(components);
+     
+     console.log(`\n📊 解析结果:`);
+     console.log(`总共找到 ${components.length} 个组件\n`);
+     
+     Object.keys(config).forEach(groupKey => {
+       const group = config[groupKey];
+       console.log(`📁 ${group.title}:`);
+       group.components.forEach(comp => {
+         console.log(`  ├── ${comp.name} ${comp.hasAnimation ? '🎬' : '📄'}`);
+       });
+       console.log('');
+     });
+     
+     return;
+   }
+   
+   if (options.dryRun) {
+     console.log('🔍 预览模式 - 将要执行的操作:');
+     console.log(`  ✅ 解析所有组件`);
+     console.log(`  ✅ 生成 Remotion 配置文件`);
+     console.log(`  ✅ 生成组件预览文件`);
+     console.log(`  ✅ 批量渲染 GIF (质量: ${options.quality}, 缩放: ${options.scale})`);
+     console.log(`  ✅ 生成预览页面`);
+     console.log(`  ${options.concurrent ? '⚡' : '🐌'} 渲染模式: ${options.concurrent ? '并发' : '顺序'}`);
+     console.log('\n使用 --help 查看更多选项');
+     return;
+   }
+   
+   try {
+     const result = await generator.main({
+       concurrent: options.concurrent,
+       quality: options.quality,
+       scale: options.scale
+     });
+     
+     console.log('\n✨ 生成完成统计:');
+     console.log(`📊 总组件数: ${result.componentList.length}`);
+     console.log(`✅ 成功渲染: ${result.renderResults.filter(r => r.success).length}`);
+     console.log(`❌ 渲染失败: ${result.renderResults.filter(r => !r.success).length}`);
+     
+     if (result.renderResults.some(r => !r.success)) {
+       console.log('\n❌ 失败的组件:');
+       result.renderResults.filter(r => !r.success).forEach(r => {
+         console.log(`  • ${r.name}: ${r.error}`);
+       });
+     }
+     
+     console.log(`\n🌐 预览页面: file://${path.resolve('./previews/index.html')}`);
+     
+   } catch (error) {
+     console.error('❌ 生成过程中发生错误:', error.message);
+     console.error('\n🔧 可能的解决方案:');
+     console.error('  1. 检查 src 目录中的组件文件是否正确');
+     console.error('  2. 确保所有依赖已正确安装');
+     console.error('  3. 尝试使用 --parse-only 检查组件解析是否正常');
+     console.error('  4. 检查系统内存是否充足');
+     process.exit(1);
+   }
+ }
+ 
+ // 运行主函数
+ main().catch(error => {
+   console.error('💥 未处理的错误:', error);
+   process.exit(1); 
```

---

```diff
    console.error('💥 未处理的错误:', error);
    process.exit(1);
+ }); 
```

---

<think><details><summary>Thought Process</summary>现在让我创建一个简化的执行脚本，方便用户使用：</details></think>

---

现在让我创建一个简化的使用说明文档：

---

```diff
- 
+ # 🎬 Remotion 万强组件库 - GIF 预览生成器
+ 
+ 这是一个自动化工具，用于为 Remotion 万强组件库生成 GIF 预览图。
+ 
+ ## 🚀 快速开始
+ 
+ ### 1. 一键运行
+ ```bash
+ # 生成所有组件的 GIF 预览
+ node scripts/run.js
+ ```
+ 
+ ### 2. 仅解析组件信息
+ ```bash
+ # 查看所有可用组件
+ node scripts/run.js --parse-only
+ ```
+ 
+ ### 3. 预览将要执行的操作
+ ```bash
+ # 干运行模式
+ node scripts/run.js --dry-run
+ ```
+ 
+ ## 📋 命令行选项
+ 
+ | 选项 | 说明 | 示例 |
+ |------|------|------|
+ | `--help` | 显示帮助信息 | `node scripts/run.js --help` |
+ | `--parse-only` | 仅解析组件，不渲染 GIF | `node scripts/run.js --parse-only` |
+ | `--concurrent` | 并发渲染（更快但占用更多资源） | `node scripts/run.js --concurrent` |
+ | `--quality=<1-10>` | 设置 GIF 质量（默认8） | `node scripts/run.js --quality=6` |
+ | `--scale=<0.1-2.0>` | 设置渲染缩放比例（默认1） | `node scripts/run.js --scale=0.5` |
+ | `--dry-run` | 预览操作，不实际执行 | `node scripts/run.js --dry-run` |
+ 
+ ## 📁 输出结构
+ 
+ 运行后会在项目根目录生成 `previews` 文件夹：
+ 
+ ```
+ previews/
+ ├── gifs/                    # 生成的 GIF 文件
+ │   ├── FadeInEffect.gif
+ │   ├── SlideInEffect.gif
+ │   └── ...
+ ├── compositions/            # 组件预览源文件
+ │   ├── FadeInEffect.tsx
+ │   ├── SlideInEffect.tsx
+ │   └── ...
+ ├── index.tsx               # Remotion 主入口
+ ├── index.html              # 预览页面
+ ├── package.json            # 依赖配置
+ ├── tsconfig.json           # TypeScript 配置
+ └── remotion.config.ts      # Remotion 配置
+ ```
+ 
+ ## 🎯 支持的组件类型
+ 
+ | 组件类型 | 说明 | 示例时长 |
+ |----------|------|----------|
+ | 🎭 **文本入场特效** | FadeInEffect, SlideInEffect 等 | 4秒 |
+ | 🎪 **文本出场特效** | FadeOutEffect, SlideOutEffect 等 | 4秒 |
+ | 🔄 **文本循环特效** | MachineCodeEffect 等 | 6秒 |
+ | 💎 **花字组件** | MultiShadowText, DiamondText 等 | 3秒 |
+ | ⚡ **动态文本特效** | 各种动态效果 | 5秒 |
+ | 🎪 **贴纸组件** | 各种贴纸效果 | 3秒 |
+ | 🌟 **场景特效** | 入场和出场场景效果 | 4秒 |
+ 
+ ## 🔧 高级用法
+ 
+ ### 并发渲染（推荐用于强大的机器）
+ ```bash
+ node scripts/run.js --concurrent --quality=8
+ ```
+ 
+ ### 低质量快速预览
+ ```bash
+ node scripts/run.js --quality=4 --scale=0.5
+ ```
+ 
+ ### 高质量渲染
+ ```bash
+ node scripts/run.js --quality=10 --scale=1.5
+ ```
+ 
+ ## 🎨 自定义配置
+ 
+ 你可以修改 `scripts/component-parser.js` 中的配置来调整：
+ 
+ - **示例文本**：为不同类型组件设置不同的示例文字
+ - **动画时长**：调整各类型组件的动画持续时间
+ - **画面尺寸**：设置不同组件的渲染尺寸
+ 
+ ```javascript
+ // 自定义示例文本
+ getSampleText(type) {
+   const textMap = {
+     'textIn': '你的自定义文本',
+     'flower': '万强文字特效',
+     // ...
+   };
+   return textMap[type] || '组件预览';
+ }
+ ```
+ 
+ ## 🐛 故障排除
+ 
+ ### 常见问题
+ 
+ **1. 组件解析失败**
+ ```bash
+ # 检查组件是否正确导出
+ node scripts/run.js --parse-only
+ ```
+ 
+ **2. 渲染失败**
+ ```bash
+ # 检查系统资源，尝试非并发模式
+ node scripts/run.js --quality=6
+ ```
+ 
+ **3. 内存不足**
+ ```bash
+ # 降低质量和缩放比例
+ node scripts/run.js --quality=4 --scale=0.5
+ ```
+ 
+ ### 系统要求
+ 
+ - **Node.js**: >= 16.0.0
+ - **内存**: 建议 8GB+ RAM
+ - **磁盘空间**: 预留 1GB+ 用于输出文件
+ - **FFmpeg**: 自动通过 Remotion 安装
+ 
+ ## 📊 性能优化
+ 
+ ### 渲染速度对比
+ 
+ | 模式 | 时间 | 资源占用 | 推荐场景 |
+ |------|------|----------|----------|
+ | 顺序渲染 | 慢 | 低 | 低配置机器 |
+ | 并发渲染 | 快 | 高 | 高配置机器 |
+ 
+ ### 质量设置建议
+ 
+ | 质量等级 | 文件大小 | 清晰度 | 用途 |
+ |----------|----------|--------|------|
+ | 1-3 | 小 | 低 | 快速预览 |
+ | 4-6 | 中 | 中 | 一般预览 |
+ | 7-8 | 大 | 高 | 正式展示 |
+ | 9-10 | 很大 | 很高 | 高质量展示 |
+ 
+ ## 🎉 预览页面功能
+ 
+ 生成的 `previews/index.html` 包含：
+ 
+ - 📊 **统计信息**：组件总数、成功/失败渲染数量
+ - 🎬 **动态预览**：所有成功渲染的 GIF 预览
+ - 🏷️ **分类标签**：按组件类型分类显示
+ - 📱 **响应式设计**：支持各种屏幕尺寸
+ - 🎨 **美观界面**：现代化的渐变背景和卡片设计
+ 
+ ## 🤝 贡献
+ 
+ 如果你想扩展这个工具：
+ 
+ 1. 修改 `component-parser.js` 添加新的组件类型支持
+ 2. 调整 `generate-previews-enhanced.js` 的渲染逻辑
+ 3. 优化 `run.js` 的命令行界面
+ 
+ ## 📝 注意事项
+ 
+ - 首次运行可能需要较长时间来安装依赖
+ - 大型项目建议使用 `--concurrent` 选项提高速度  
+ - 生成的 GIF 文件可能较大，注意磁盘空间
+ - 如果遇到组件导入错误，请检查组件的导出方式是否正确 
```

---

<think><details><summary>Thought Process</summary>现在让我创建一个简化的执行脚本，方便用户使用：</details></think>

---

最后，让我在项目的 package.json 中添加一个便捷的脚本命令：

---

Read file: package.json

---

<think><details><summary>Thought Process</summary>现在让我创建一个简化的执行脚本，方便用户使用：</details></think>

---

```diff
      "prepare": "husky install && dumi setup",
      "prepublishOnly": "father doctor && npm run build",
-     "start": "npm run dev"
+     "start": "npm run dev",
+     "preview:generate": "node scripts/run.js",
+     "preview:parse": "node scripts/run.js --parse-only",
+     "preview:fast": "node scripts/run.js --quality=4 --scale=0.5",
+     "preview:hq": "node scripts/run.js --quality=10 --concurrent"
    },
    "commitlint": {
```

---



---

