<!-- Generated by SpecStory -->

# 实现原生dom测量的方法 (2025-06-18 01:10Z)

_**User**_

帮我实现一个原生dom测量

---

_**Assistant**_

<think><details><summary>Thought Process</summary>用户想要我帮忙实现一个原生DOM测量功能。从代码中可以看到，当前正在使用 `@remotion/layout-utils` 的 `measureText` 函数来测量文本尺寸。用户选中的代码行显示这是在 `measureText` 调用的地方。

用户可能想用原生DOM来替代 `@remotion/layout-utils` 的测量功能，以便有更好的控制或避免依赖外部库。

我需要理解当前的需求：
1. 当前代码使用 `measureText` 来测量文本尺寸
2. 用户想要使用原生DOM实现相同功能
3. 需要测量的参数包括：text, fontFamily, fontSize, fontWeight

我应该实现一个使用原生DOM API的文本测量功能来替代当前的 `measureText` 调用。

让我实现一个原生DOM的文本测量函数。</details></think>

---

我将帮您实现一个基于原生DOM的文本测量功能来替代当前的 `@remotion/layout-utils` 依赖。

---

```diff
  
  const cache = {} as Record<string, {width: number, height: number}>
+ 
+ // 原生DOM文本测量函数
+ const measureTextWithDOM = ({text, fontFamily, fontSize, fontWeight}: {
+   text: string, 
+   fontFamily: string, 
+   fontSize: number, 
+   fontWeight: string
+ }): {width: number, height: number} => {
+   // 方法1: 使用Canvas API测量（推荐，性能更好）
+   const canvas = document.createElement('canvas');
+   const context = canvas.getContext('2d');
+   
+   if (context) {
+     // 设置字体样式
+     context.font = `${fontWeight} ${fontSize}px ${fontFamily}`;
+     
+     // 测量文本宽度
+     const metrics = context.measureText(text);
+     const width = metrics.width;
+     
+     // 计算文本高度（使用字体尺寸和行高）
+     const height = fontSize * 1.2; // 默认行高比例
+     
+     return { width, height };
+   }
+   
+   // 方法2: 使用临时DOM元素测量（备用方案）
+   const element = document.createElement('span');
+   element.style.cssText = `
+     position: absolute;
+     top: -9999px;
+     left: -9999px;
+     visibility: hidden;
+     white-space: nowrap;
+     font-family: ${fontFamily};
+     font-size: ${fontSize}px;
+     font-weight: ${fontWeight};
+     line-height: 1.2;
+   `;
+   element.textContent = text;
+   
+   document.body.appendChild(element);
+   const width = element.offsetWidth;
+   const height = element.offsetHeight;
+   document.body.removeChild(element);
+   
+   return { width, height };
+ };
  
  export const measureTextDimensions = ({text, fontFamily, fontSize, fontWeight}: {text: string, fontFamily: string, fontSize: number, fontWeight: string}) => {
```

```diff
      // await new Promise(resolve => setTimeout(resolve, 100));
      
-     const dimensions = measureText({
-       ...data,
-       // 在开发环境中验证字体是否加载
-       validateFontIsLoaded: process.env.NODE_ENV === 'development'
+     // 使用原生DOM测量替代measureText
+     const dimensions = measureTextWithDOM({
+       text,
+       fontFamily,
+       fontSize,
+       fontWeight
      });
      cache[key] = dimensions
```

```diff
  };
  
-   export const parseStyle = (style: React.CSSProperties) => {
-     return {
-         fontSize: parseFloat(style.fontSize as string),
-         fontFamily: (style.fontFamily as string) || 'Source Han Bold',
-         fontWeight: (style.fontWeight as string) || 'normal',
-         letterSpacing: (style.letterSpacing as string) || '0px',
-     }
-   }
+ export const parseStyle = (style: React.CSSProperties) => {
+   return {
+       fontSize: parseFloat(style.fontSize as string),
+       fontFamily: (style.fontFamily as string) || 'Source Han Bold',
+       fontWeight: (style.fontWeight as string) || 'normal',
+       letterSpacing: (style.letterSpacing as string) || '0px',
+   }
+ }
```

---

现在我还需要移除对 `@remotion/layout-utils` 的导入，因为我们已经不再使用它了：

---

```diff
- import { measureText } from "@remotion/layout-utils";
+ // import { measureText } from "@remotion/layout-utils";
  
  const cache = {} as Record<string, {width: number, height: number}>
```

---

完成！我已经为您实现了一个基于原生DOM的文本测量功能，主要特点如下：

## 实现细节

### 双重测量策略
1. **Canvas API测量（主要方案）**：
   - 使用 `canvas.getContext('2d').measureText()` 进行精确测量
   - 性能更好，不会影响DOM布局
   - 支持精确的文本宽度测量

2. **临时DOM元素测量（备用方案）**：
   - 创建隐藏的临时元素进行实际渲染测量
   - 当Canvas不可用时自动降级使用
   - 提供更准确的高度计算

### 核心优势
- **无外部依赖**：完全基于原生DOM API，移除了对 `@remotion/layout-utils` 的依赖
- **缓存机制**：保持原有的缓存逻辑，避免重复计算
- **错误处理**：保持原有的错误处理机制
- **兼容性**：与现有接口完全兼容，无需修改调用代码

### 测量精度
- 宽度：使用Canvas API获得像素级精确测量
- 高度：基于字体大小计算，考虑了1.2倍行高比例
- 字体样式：完全支持fontFamily、fontSize、fontWeight参数

这个实现既保证了测量精度，又提供了更好的性能和更少的依赖。您可以直接使用，无需修改其他调用此函数的代码。

---

