<!-- Generated by SpecStory -->

# 支持动画显示帧率配置 (2025-06-09 01:17Z)

_**User**_

@BodyContourEffect.tsx 我想支持一个当前动画显示帧率的配置，能帮我实现吗

---

_**Assistant**_

<think><details><summary>Thought Process</summary>用户想要在BodyContourEffect组件中添加一个配置选项来控制当前动画显示的帧率。我需要分析一下当前的代码，看看如何实现这个功能。

从代码中我可以看到：
1. 当前组件使用useCurrentFrame()和useVideoConfig()来获取当前帧和视频配置
2. 有一个针对关键帧插值系统的逻辑
3. 有调试信息显示功能

要实现动画显示帧率的配置，我需要：
1. 在BodyContourEffectProps接口中添加一个新的配置选项，比如animationFps
2. 修改渲染逻辑，使其根据配置的帧率来控制动画更新频率
3. 在调试信息中显示当前设置的动画帧率

实现思路：
- 添加animationFps参数，默认值可以是30或者使用视频的fps
- 使用这个参数来控制动画的更新频率，比如通过计算当前帧是否应该更新动画
- 可能需要修改特效的渲染逻辑，让它按照指定的帧率来更新

让我看看具体的实现方式：
1. 计算动画帧：const animationFrame = Math.floor(frame * animationFps / fps)
2. 只在动画帧变化时更新动画状态
3. 在调试信息中显示这个配置</details></think>

---

我来帮你为BodyContourEffect组件添加动画显示帧率的配置功能。

---

```diff
    /** 是否显示插值调试信息，默认false */
    showInterpolationDebug?: boolean;
+   /** 动画显示帧率，默认为视频帧率 */
+   animationFps?: number;
  }
  
```

```diff
    interpolationQuality = 3,
    keyframeWindowSize = 10,
-   showInterpolationDebug = false
+   showInterpolationDebug = false,
+   animationFps
  }: BodyContourEffectProps) => {
    const canvasRef = useRef<HTMLCanvasElement>(null);
```

---

```diff
    const { width, height, fps } = useVideoConfig();
    const frame = useCurrentFrame();
+   
+   // 🎯 动画帧率控制 - 如果未指定则使用视频帧率
+   const effectiveAnimationFps = animationFps || fps;
+   
+   // 🎯 计算动画帧号 - 用于控制动画更新频率
+   const animationFrame = Math.floor(frame * effectiveAnimationFps / fps);
+   const prevAnimationFrameRef = useRef<number>(-1);
+   
+   // 判断是否需要更新动画（只在动画帧变化时更新）
+   const shouldUpdateAnimation = animationFrame !== prevAnimationFrameRef.current;
+   
+   // 更新动画帧记录
+   useEffect(() => {
+     prevAnimationFrameRef.current = animationFrame;
+   }, [animationFrame]);
    
    // 关键帧插值系统状态
```

---

```diff
      Math.min(1, (currentTime - startTime) / duration) : 0;
  
+   // 🎯 基于动画帧率的特效进度计算
+   const animationTime = animationFrame / effectiveAnimationFps;
+   const animationEffectProgress = shouldShowEffect ? 
+     Math.min(1, (animationTime - startTime) / duration) : 0;
+ 
    const hexToRgb = (hex: string) => {
      const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
```

---

```diff
        const context = canvas.getContext('2d', { alpha: true }); // 确保使用透明背景
        if (!context) return;
+ 
+       // 🎯 如果动画帧率受限且当前帧不需要更新动画，跳过渲染
+       if (effectiveAnimationFps < fps && !shouldUpdateAnimation && lastFrameRenderedRef.current) {
+         return;
+       }
  
        try {
```

---

```diff
        
        // 绘制轮廓
-       drawSmoothContourPath(scaledPoints, contourColor, contourThickness, context, effectProgress);
+       drawSmoothContourPath(scaledPoints, contourColor, contourThickness, context, animationEffectProgress);
        
        if (scaleFactor > 1.0) {
-         addScaleEffects(canvasPoints, scaledPoints, contourColor, contourThickness, context, effectProgress);
+         addScaleEffects(canvasPoints, scaledPoints, contourColor, contourThickness, context, animationEffectProgress);
        }
        
        if (showDecorations) {
-         drawDecorations(scaledPoints, context, effectProgress);
+         drawDecorations(scaledPoints, context, animationEffectProgress);
        }
        
```

```diff
        return null;
      }
-   }, [interpolationQuality, scaleFactor, contourColor, contourThickness, effectProgress, showDecorations, showDebugInfo]);
+   }, [interpolationQuality, scaleFactor, contourColor, contourThickness, animationEffectProgress, showDecorations, showDebugInfo]);
  
    // 原有的轮廓绘制方法（为了保持向后兼容性）
```

```diff
            
            // 在缓冲区上绘制轮廓
-           drawSmoothContourPath(scaledPoints, color, thickness, bufferContext, effectProgress);
+           drawSmoothContourPath(scaledPoints, color, thickness, bufferContext, animationEffectProgress);
            
            if (scaleFactor > 1.0) {
-             addScaleEffects(canvasPoints, scaledPoints, color, thickness, bufferContext, effectProgress);
+             addScaleEffects(canvasPoints, scaledPoints, color, thickness, bufferContext, animationEffectProgress);
            }
  
            // 🎨 绘制装饰贴片
            if (showDecorations) {
-             drawDecorations(scaledPoints, bufferContext, effectProgress);
+             drawDecorations(scaledPoints, bufferContext, animationEffectProgress);
            }
            
```

```diff
            }
            
-           drawSmoothContourPath(scaledPoints, color, thickness, context, effectProgress);
+           drawSmoothContourPath(scaledPoints, color, thickness, context, animationEffectProgress);
            
            if (scaleFactor > 1.0) {
-             addScaleEffects(canvasPoints, scaledPoints, color, thickness, context, effectProgress);
+             addScaleEffects(canvasPoints, scaledPoints, color, thickness, context, animationEffectProgress);
            }
  
            if (showDecorations) {
-             drawDecorations(scaledPoints, context, effectProgress);
+             drawDecorations(scaledPoints, context, animationEffectProgress);
            }
          }
```

```diff
              
              // 绘制轮廓到缓冲区
-             drawSmoothContourPath(scaledPoints, contourColor, contourThickness, bufferContext, effectProgress);
+             drawSmoothContourPath(scaledPoints, contourColor, contourThickness, bufferContext, animationEffectProgress);
              
              if (scaleFactor > 1.0) {
-               addScaleEffects(contourPointsToRender, scaledPoints, contourColor, contourThickness, bufferContext, effectProgress);
+               addScaleEffects(contourPointsToRender, scaledPoints, contourColor, contourThickness, bufferContext, animationEffectProgress);
              }
              
              // 绘制装饰贴片
              if (showDecorations) {
-               drawDecorations(scaledPoints, bufferContext, effectProgress);
+               drawDecorations(scaledPoints, bufferContext, animationEffectProgress);
              }
              
```

```diff
      renderFrame();
    }, [
-     frame, shouldShowEffect, effectProgress, segmentations, currentVideoFrame, 
+     frame, shouldShowEffect, animationEffectProgress, segmentations, currentVideoFrame, 
      scaleFactor, contourThickness, contourColor, showDebugInfo, currentTime, 
      width, height, keyframes, manageKeyframes, extractKeyframeData, 
```

---

```diff
      width, height, keyframes, manageKeyframes, extractKeyframeData, 
      interpolateContourPoints, interpolationQuality, fps, showInterpolationDebug,
-     renderSegmentationDirectly
+     renderSegmentationDirectly, effectiveAnimationFps, shouldUpdateAnimation, animationFrame
    ]);
  
```

---

```diff
            <div>🎯 特效时间: {startTime}s - {(startTime + duration).toFixed(1)}s</div>
            <div>📊 特效状态: {shouldShowEffect ? `✅ 激活 (${(effectProgress * 100).toFixed(0)}%)` : '⏸️ 未激活'}</div>
+           <div>🎬 视频帧率: {fps}fps</div>
+           <div>🎨 动画帧率: {effectiveAnimationFps}fps</div>
+           <div>🎞️ 当前帧号: {frame} (动画帧: {animationFrame})</div>
+           <div>🔄 动画进度: {(animationEffectProgress * 100).toFixed(0)}%</div>
            <div>🎨 轮廓颜色: {contourColor}</div>
            <div>📏 轮廓粗细: {contourThickness}px</div>
```

```diff
            <div>🎨 装饰状态: {showDecorations ? `✅ ${decorationCount}个装饰` : '❌ 已关闭'}</div>
            <div>📊 装饰大小: {decorationSize}px</div>
-           <div>🎬 当前帧号: {frame}</div>
            <div>🔑 关键帧数: {keyframes.length}</div>
+           <div>⚡ 渲染优化: {effectiveAnimationFps < fps ? '✅ 帧率优化已启用' : '❌ 全帧率渲染'}</div>
          </div>
        )}
```

---

现在我已经成功为 `BodyContourEffect` 组件添加了动画显示帧率配置功能。让我总结一下实现的主要功能：

## 🎯 新增功能特性

### 1. **动画帧率配置**
- 新增 `animationFps` 参数，允许用户自定义动画显示帧率
- 如果未指定，默认使用视频的原始帧率
- 支持降低帧率以优化性能或创造特殊视觉效果

### 2. **智能渲染优化**
- 当动画帧率低于视频帧率时，自动跳过不必要的帧渲染
- 只在动画帧变化时更新动画状态，大幅提升性能
- 保持最后一帧的显示状态，避免闪烁

### 3. **精确的动画控制**
- 基于动画帧率计算特效进度，而不是视频帧率
- 确保动画效果在不同帧率下保持一致的时间表现
- 支持平滑的动画过渡和特效展示

### 4. **增强的调试信息**
- 显示当前视频帧率和动画帧率
- 显示当前帧号和对应的动画帧号
- 显示基于动画帧率的动画进度
- 指示是否启用了帧率优化

## 📋 使用示例

```tsx
// 使用默认帧率（视频帧率）
<BodyContourEffect 
  segmentations={segmentations}
  showDebugInfo={true}
/>

// 设置较低的动画帧率以优化性能
<BodyContourEffect 
  segmentations={segmentations}
  animationFps={15}  // 15fps动画
  showDebugInfo={true}
/>

// 设置较高的动画帧率以获得更流畅的效果
<BodyContourEffect 
  segmentations={segmentations}
  animationFps={60}  // 60fps动画
  showDebugInfo={true}
/>
```

## 🚀 性能优势

1. **资源节约**：当设置较低的动画帧率时，可以大幅减少CPU和GPU使用
2. **流畅体验**：在低性能设备上仍能保持稳定的动画效果
3. **灵活控制**：可根据不同场景需求调整动画质量和性能平衡

这个实现让你可以根据具体需求灵活控制动画的显示帧率，既能在需要时提供高质量的视觉效果，也能在性能受限的情况下进行优化。

---

