<!-- Generated by SpecStory -->

# Support for setting speed in AntiGravityEffect (2025-06-16 01:15Z)

_**User**_

@AntiGravityEffect.tsx 支持一下设置速率

---

_**Assistant**_

<think><details><summary>Thought Process</summary>用户要求为 AntiGravityEffect.tsx 组件支持速率设置。让我分析一下当前的代码：

1. 当前代码使用了 `frame * 0.1` 和 `frame * 0.05` 这些固定的速率值
2. 根据工作区规则，这是一个循环特效，需要支持 `speed?: number;` 参数
3. 需要将固定的速率值替换为可配置的 `speed` 参数

我需要：
1. 在接口中添加 `speed?: number;` 参数
2. 在组件中使用 `speed` 参数来控制动画速度
3. 设置合适的默认值

根据规则，循环特效需要支持 `speed` 参数。</details></think>

---

我来为 AntiGravityEffect 组件添加速率设置功能。根据循环特效的参数规范，我需要添加 `speed` 参数来控制动画速度。

---

```diff
    fontSize?: number;
    style?: React.CSSProperties;
+   speed?: number;
  }
  
```

```diff
    fontSize = 48,
    style = {},
+   speed = 1,
  }: AntiGravityEffectProps) {
    const frame = useCurrentFrame();
  
-   const floatY = interpolate(Math.sin(frame * 0.1), [-1, 1], [-20, 20]);
+   const floatY = interpolate(Math.sin(frame * 0.1 * speed), [-1, 1], [-20, 20]);
    const particles = Array.from({ length: 15 }, (_, i) => {
-     const y = interpolate(frame + i * 5, [0, 100], [100, -100], {
+     const y = interpolate(frame * speed + i * 5, [0, 100], [100, -100], {
        extrapolateRight: 'wrap',
      });
-     const x = Math.sin(frame * 0.05 + i) * 50;
+     const x = Math.sin(frame * 0.05 * speed + i) * 50;
      return { x, y };
    });
```

---

完美！我已经成功为 AntiGravityEffect 组件添加了速率控制功能：

## 主要改动：

1. **接口扩展**：在 `AntiGravityEffectProps` 中添加了 `speed?: number;` 参数（符合循环特效的参数规范）

2. **默认值设置**：设置 `speed = 1` 作为默认值，保持原有动画速度

3. **动画速度控制**：
   - 文字浮动效果：`frame * 0.1 * speed`
   - 粒子垂直运动：`frame * speed + i * 5`
   - 粒子水平摆动：`frame * 0.05 * speed + i`

## 使用方式：

```jsx
// 默认速度
<AntiGravityEffect text="反重力效果" />

// 2倍速度
<AntiGravityEffect text="快速反重力" speed={2} />

// 0.5倍速度（慢速）
<AntiGravityEffect text="慢速反重力" speed={0.5} />
```

现在用户可以通过调整 `speed` 参数来控制反重力效果的动画速度，数值越大动画越快，数值越小动画越慢。

---

