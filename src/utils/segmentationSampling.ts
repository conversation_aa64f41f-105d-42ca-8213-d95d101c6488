// 🎯 智能化关键帧采样策略工具类
// 用于决定何时需要获取人体分割数据以优化性能

/**
 * 全局缓存对象，用于存储决策结果，避免重复计算
 */
const globalDecisionCache: { [key: string]: boolean } = {};

/**
 * 智能化关键帧采样决策函数
 * 根据当前时间、帧号和帧率，智能决定是否需要获取人体分割数据
 * 
 * @param currentTime 当前时间（秒）
 * @param currentFrame 当前帧号
 * @param fps 视频帧率
 * @returns 是否需要获取分割数据
 */
export const needSegmentationsData = (
  currentTime: number, 
  currentFrame: number, 
  fps: number
): boolean => {
  // 存储关键时刻的决策结果，避免重复计算
  const key = `${currentTime.toFixed(2)}_${currentFrame}`;
  
  if (globalDecisionCache[key] !== undefined) {
    return globalDecisionCache[key];
  }
  
  // 计算场景剧烈变化的可能性
  const isLikelySceneChange = (frame: number) => {
    return frame === 0 || // 开始帧
           frame % Math.round(fps * 5) < 3; // 每5秒可能有场景变化
  };
  
  // 根据帧率自适应采样
  let decision = false;
  
  // 1. 基础采样 - 降低密度，提高性能
  // 每秒只获取一帧，比之前的策略更省资源
  const regularSample = currentFrame % Math.round(fps) === 0;
  
  // 2. 初始采样 - 前几秒使用更高频率，确保初始效果良好
  const initialSample = currentFrame < fps * 3 && currentFrame % Math.round(fps / 2) === 0;
  
  // 3. 场景变化采样 - 在可能发生场景变化的地方增加采样
  const sceneChangeSample = isLikelySceneChange(currentFrame) && 
                           currentFrame % Math.round(fps / 3) === 0;
  
  // 综合决策
  decision = regularSample || initialSample || sceneChangeSample;
  
  // 记录决策结果
  globalDecisionCache[key] = decision;
  
  // 缓存清理 - 避免内存泄漏
  if (Object.keys(globalDecisionCache).length > 1000) {
    // 清空缓存，但保留最近的100个决策
    const recentKeys = Object.keys(globalDecisionCache).slice(-100);
    const recentCache: { [key: string]: boolean } = {};
    recentKeys.forEach(k => {
      recentCache[k] = globalDecisionCache[k];
    });
    
    // 清空并重建缓存
    Object.keys(globalDecisionCache).forEach(k => delete globalDecisionCache[k]);
    Object.assign(globalDecisionCache, recentCache);
  }
  
  return decision;
};

/**
 * 手动清理缓存函数（可选使用）
 */
export const clearSegmentationCache = (): void => {
  Object.keys(globalDecisionCache).forEach(key => delete globalDecisionCache[key]);
};

/**
 * 获取当前缓存统计信息（调试用）
 */
export const getCacheStats = () => {
  return {
    cacheSize: Object.keys(globalDecisionCache).length,
    maxCacheSize: 1000,
    utilizationRate: (Object.keys(globalDecisionCache).length / 1000 * 100).toFixed(1) + '%'
  };
}; 