import React, { memo } from 'react';

interface Flower356Props {
  text?: string;
  style?: React.CSSProperties;
  fillColor?: string;
  strokeColor?: string;
  backgroundColor?: string;
}

export const Flower356: any = memo(({ 
  text = "花字", 
  style = {},
  fillColor = "#fd79a8",
  strokeColor = "#e84393", 
  backgroundColor = "transparent"
}: Flower356Props) => {
  const fontSize = style.fontSize || 48;
  const fontFamily = style.fontFamily || '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif';
  const fontWeight = style.fontWeight || 'bold';
  const letterSpacing = style.letterSpacing || 'normal';
  
  const textStyle: React.CSSProperties = {
    fontSize,
    fontFamily,
    fontWeight,
    letterSpacing,
    color: fillColor,
    WebkitTextStroke: `2px ${strokeColor}`,
    textShadow: `-4px 0px 0 #ff9a9e, -3px -2px 0 #ff9a9e, -3px -1px 0 #ff9a9e, -3px 0px 0 #ff9a9e, -3px 1px 0 #ff9a9e, -3px 2px 0 #ff9a9e, -2px -3px 0 #ff9a9e, -2px -2px 0 #ff9a9e, -2px -1px 0 #ff9a9e, -2px 0px 0 #ff9a9e, -2px 1px 0 #ff9a9e, -2px 2px 0 #ff9a9e, -2px 3px 0 #ff9a9e, -1px -3px 0 #ff9a9e, -1px -2px 0 #ff9a9e, -1px -1px 0 #ff9a9e, -1px 0px 0 #ff9a9e, -1px 1px 0 #ff9a9e, -1px 2px 0 #ff9a9e, -1px 3px 0 #ff9a9e, 0px -4px 0 #ff9a9e, 0px -3px 0 #ff9a9e, 0px -2px 0 #ff9a9e, 0px -1px 0 #ff9a9e, 0px 1px 0 #ff9a9e, 0px 2px 0 #ff9a9e, 0px 3px 0 #ff9a9e, 0px 4px 0 #ff9a9e, 1px -3px 0 #ff9a9e, 1px -2px 0 #ff9a9e, 1px -1px 0 #ff9a9e, 1px 0px 0 #ff9a9e, 1px 1px 0 #ff9a9e, 1px 2px 0 #ff9a9e, 1px 3px 0 #ff9a9e, 2px -3px 0 #ff9a9e, 2px -2px 0 #ff9a9e, 2px -1px 0 #ff9a9e, 2px 0px 0 #ff9a9e, 2px 1px 0 #ff9a9e, 2px 2px 0 #ff9a9e, 2px 3px 0 #ff9a9e, 3px -2px 0 #ff9a9e, 3px -1px 0 #ff9a9e, 3px 0px 0 #ff9a9e, 3px 1px 0 #ff9a9e, 3px 2px 0 #ff9a9e, 4px 0px 0 #ff9a9e`,
    filter: 'none',
    WebkitFontSmoothing: 'antialiased',
    MozOsxFontSmoothing: 'grayscale',
    textRendering: 'optimizeLegibility',
    display: 'inline-block',
    backgroundColor,
    ...style
  };

  return (
    <span style={textStyle}>
      {text}
    </span>
  );
});

Flower356.showName = '蜜桃双粉';
Flower356.description = '花字356 - 甜美蜜桃的双粉描边';
Flower356.key = 'Flower356'; 
Flower356.mainColor = '#fd79a8';