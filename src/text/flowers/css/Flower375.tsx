import React, { memo } from 'react';

interface Flower375Props {
  text?: string;
  style?: React.CSSProperties;
  fillColor?: string;
  strokeColor?: string;
  backgroundColor?: string;
}

export const Flower375: any = memo(({ 
  text = "花字", 
  style = {},
  fillColor = "#708090",
  strokeColor = "#2F4F4F", 
  backgroundColor = "transparent"
}: Flower375Props) => {
  const fontSize = style.fontSize || 48;
  const fontFamily = style.fontFamily || '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif';
  const fontWeight = style.fontWeight || 'bold';
  const letterSpacing = style.letterSpacing || 'normal';
  
  const textStyle: React.CSSProperties = {
    fontSize,
    fontFamily,
    fontWeight,
    letterSpacing,
    background: `radial-gradient(circle at 30% 20%, #696969, transparent), radial-gradient(circle at 70% 80%, #2F4F4F, transparent), radial-gradient(circle at 20% 70%, ${fillColor}, transparent), #778899`,
    WebkitBackgroundClip: 'text',
    WebkitTextFillColor: 'transparent',
    backgroundClip: 'text',
    WebkitTextStroke: `2px ${strokeColor}`,
    textShadow: '2px 2px 4px rgba(47, 79, 79, 0.4)',
    filter: 'none',
    WebkitFontSmoothing: 'antialiased',
    MozOsxFontSmoothing: 'grayscale',
    textRendering: 'optimizeLegibility',
    display: 'inline-block',
    backgroundColor,
    backgroundSize: 'cover',
    backgroundPosition: 'center',
    ...style
  };

  return (
    <span style={textStyle}>
      {text}
    </span>
  );
});

Flower375.showName = '岩石纹理';
Flower375.description = '花字375 - 粗糙岩石纹理效果';
Flower375.key = 'Flower375'; 
Flower375.mainColor = '#708090';