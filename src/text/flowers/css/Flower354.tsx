import React, { memo } from 'react';

interface Flower354Props {
  text?: string;
  style?: React.CSSProperties;
  fillColor?: string;
  strokeColor?: string;
  backgroundColor?: string;
}

export const Flower354: any = memo(({ 
  text = "花字", 
  style = {},
  fillColor = "#55a3ff",
  strokeColor = "#2d3436", 
  backgroundColor = "transparent"
}: Flower354Props) => {
  const fontSize = style.fontSize || 48;
  const fontFamily = style.fontFamily || '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif';
  const fontWeight = style.fontWeight || 'bold';
  const letterSpacing = style.letterSpacing || 'normal';
  
  const textStyle: React.CSSProperties = {
    fontSize,
    fontFamily,
    fontWeight,
    letterSpacing,
    color: fillColor,
    WebkitTextStroke: `2px ${strokeColor}`,
    textShadow: `-4px 0px 0 #00b894, -3px -2px 0 #00b894, -3px -1px 0 #00b894, -3px 0px 0 #00b894, -3px 1px 0 #00b894, -3px 2px 0 #00b894, -2px -3px 0 #00b894, -2px -2px 0 #00b894, -2px -1px 0 #00b894, -2px 0px 0 #00b894, -2px 1px 0 #00b894, -2px 2px 0 #00b894, -2px 3px 0 #00b894, -1px -3px 0 #00b894, -1px -2px 0 #00b894, -1px -1px 0 #00b894, -1px 0px 0 #00b894, -1px 1px 0 #00b894, -1px 2px 0 #00b894, -1px 3px 0 #00b894, 0px -4px 0 #00b894, 0px -3px 0 #00b894, 0px -2px 0 #00b894, 0px -1px 0 #00b894, 0px 1px 0 #00b894, 0px 2px 0 #00b894, 0px 3px 0 #00b894, 0px 4px 0 #00b894, 1px -3px 0 #00b894, 1px -2px 0 #00b894, 1px -1px 0 #00b894, 1px 0px 0 #00b894, 1px 1px 0 #00b894, 1px 2px 0 #00b894, 1px 3px 0 #00b894, 2px -3px 0 #00b894, 2px -2px 0 #00b894, 2px -1px 0 #00b894, 2px 0px 0 #00b894, 2px 1px 0 #00b894, 2px 2px 0 #00b894, 2px 3px 0 #00b894, 3px -2px 0 #00b894, 3px -1px 0 #00b894, 3px 0px 0 #00b894, 3px 1px 0 #00b894, 3px 2px 0 #00b894, 4px 0px 0 #00b894`,
    filter: 'none',
    WebkitFontSmoothing: 'antialiased',
    MozOsxFontSmoothing: 'grayscale',
    textRendering: 'optimizeLegibility',
    display: 'inline-block',
    backgroundColor,
    ...style
  };

  return (
    <span style={textStyle}>
      {text}
    </span>
  );
});

Flower354.showName = '青春双绿';
Flower354.description = '花字354 - 活力青春的双绿描边';
Flower354.key = 'Flower354'; 
Flower354.mainColor = '#55a3ff';