import React, { memo } from 'react';

interface Flower322Props {
  text?: string;
  style?: React.CSSProperties;
  fillColor?: string;
  strokeColor?: string;
  backgroundColor?: string;
}

export const Flower322: any = memo(({ 
  text = "花字", 
  style = {},
  fillColor = "#81ecec",
  strokeColor = "#00b894", 
  backgroundColor = "transparent"
}: Flower322Props) => {
  const fontSize = style.fontSize || 48;
  const fontFamily = style.fontFamily || '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif';
  const fontWeight = style.fontWeight || 'bold';
  const letterSpacing = style.letterSpacing || 'normal';
  
  const textStyle: React.CSSProperties = {
    fontSize,
    fontFamily,
    fontWeight,
    letterSpacing,
    color: fillColor,
    WebkitTextStroke: `2px ${strokeColor}`,
    textShadow: 'none',
    filter: 'none',
    WebkitFontSmoothing: 'antialiased',
    MozOsxFontSmoothing: 'grayscale',
    textRendering: 'optimizeLegibility',
    display: 'inline-block',
    backgroundColor,
    ...style
  };

  return (
    <span style={textStyle}>
      {text}
    </span>
  );
});

Flower322.showName = '清新薄荷';
Flower322.description = '花字322 - 清新自然的薄荷绿';
Flower322.key = 'Flower322'; 
Flower322.mainColor = '#81ecec';
