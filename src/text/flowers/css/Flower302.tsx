import React, { memo } from 'react';

interface Flower302Props {
  text?: string;
  style?: React.CSSProperties;
  fillColor?: string;
  strokeColor?: string;
  backgroundColor?: string;
}

export const Flower302: any = memo(({ 
  text = "花字", 
  style = {},
  fillColor = "#ff69b4",
  strokeColor = "#ff1493", 
  backgroundColor = "transparent"
}: Flower302Props) => {
  const fontSize = style.fontSize || 48;
  const fontFamily = style.fontFamily || '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif';
  const fontWeight = style.fontWeight || 'bold';
  const letterSpacing = style.letterSpacing || 'normal';
  
  const textStyle: React.CSSProperties = {
    fontSize,
    fontFamily,
    fontWeight,
    letterSpacing,
    background: `linear-gradient(to bottom, #ffc0cb, ${fillColor}, #ff1493)`,
    WebkitBackgroundClip: 'text',
    WebkitTextFillColor: 'transparent',
    backgroundClip: 'text',
    filter: `drop-shadow(0 0 15px ${fillColor}CC)`,
    WebkitFontSmoothing: 'antialiased',
    MozOsxFontSmoothing: 'grayscale',
    textRendering: 'optimizeLegibility',
    display: 'inline-block',
    backgroundColor,
    ...style
  };

  return (
    <span style={textStyle}>
      {text}
    </span>
  );
});

Flower302.showName = '糖果粉色渐变';
Flower302.description = '花字302 - 甜美可爱的糖果色彩';
Flower302.key = 'Flower302'; 
Flower302.mainColor = '#ff69b4';