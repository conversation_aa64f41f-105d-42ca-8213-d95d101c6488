import React, { memo } from 'react';

interface Flower357Props {
  text?: string;
  style?: React.CSSProperties;
  fillColor?: string;
  strokeColor?: string;
  backgroundColor?: string;
}

export const Flower357: any = memo(({ 
  text = "花字", 
  style = {},
  fillColor = "#00cec9",
  strokeColor = "#00b894", 
  backgroundColor = "transparent"
}: Flower357Props) => {
  const fontSize = style.fontSize || 48;
  const fontFamily = style.fontFamily || '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif';
  const fontWeight = style.fontWeight || 'bold';
  const letterSpacing = style.letterSpacing || 'normal';
  
  const textStyle: React.CSSProperties = {
    fontSize,
    fontFamily,
    fontWeight,
    letterSpacing,
    color: fillColor,
    WebkitTextStroke: `2px ${strokeColor}`,
    textShadow: `-4px 0px 0 #74b9ff, -3px -2px 0 #74b9ff, -3px -1px 0 #74b9ff, -3px 0px 0 #74b9ff, -3px 1px 0 #74b9ff, -3px 2px 0 #74b9ff, -2px -3px 0 #74b9ff, -2px -2px 0 #74b9ff, -2px -1px 0 #74b9ff, -2px 0px 0 #74b9ff, -2px 1px 0 #74b9ff, -2px 2px 0 #74b9ff, -2px 3px 0 #74b9ff, -1px -3px 0 #74b9ff, -1px -2px 0 #74b9ff, -1px -1px 0 #74b9ff, -1px 0px 0 #74b9ff, -1px 1px 0 #74b9ff, -1px 2px 0 #74b9ff, -1px 3px 0 #74b9ff, 0px -4px 0 #74b9ff, 0px -3px 0 #74b9ff, 0px -2px 0 #74b9ff, 0px -1px 0 #74b9ff, 0px 1px 0 #74b9ff, 0px 2px 0 #74b9ff, 0px 3px 0 #74b9ff, 0px 4px 0 #74b9ff, 1px -3px 0 #74b9ff, 1px -2px 0 #74b9ff, 1px -1px 0 #74b9ff, 1px 0px 0 #74b9ff, 1px 1px 0 #74b9ff, 1px 2px 0 #74b9ff, 1px 3px 0 #74b9ff, 2px -3px 0 #74b9ff, 2px -2px 0 #74b9ff, 2px -1px 0 #74b9ff, 2px 0px 0 #74b9ff, 2px 1px 0 #74b9ff, 2px 2px 0 #74b9ff, 2px 3px 0 #74b9ff, 3px -2px 0 #74b9ff, 3px -1px 0 #74b9ff, 3px 0px 0 #74b9ff, 3px 1px 0 #74b9ff, 3px 2px 0 #74b9ff, 4px 0px 0 #74b9ff`,
    filter: 'none',
    WebkitFontSmoothing: 'antialiased',
    MozOsxFontSmoothing: 'grayscale',
    textRendering: 'optimizeLegibility',
    display: 'inline-block',
    backgroundColor,
    ...style
  };

  return (
    <span style={textStyle}>
      {text}
    </span>
  );
});

Flower357.showName = '电子双光';
Flower357.description = '花字357 - 科技电子的双光效果';
Flower357.key = 'Flower357'; 
Flower357.mainColor = '#00cec9';