import React, { memo } from 'react';

interface Flower366Props {
  text?: string;
  style?: React.CSSProperties;
  fillColor?: string;
  strokeColor?: string;
  backgroundColor?: string;
}

export const Flower366: any = memo(({ 
  text = "花字", 
  style = {},
  fillColor = "#FF1493",
  strokeColor = "#C71585", 
  backgroundColor = "transparent"
}: Flower366Props) => {
  const fontSize = style.fontSize || 48;
  const fontFamily = style.fontFamily || '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif';
  const fontWeight = style.fontWeight || 'bold';
  const letterSpacing = style.letterSpacing || 'normal';
  
  const textStyle: React.CSSProperties = {
    fontSize,
    fontFamily,
    fontWeight,
    letterSpacing,
    background: `radial-gradient(circle at 25% 25%, #FF69B4 2px, transparent 2px), radial-gradient(circle at 75% 75%, ${fillColor} 2px, transparent 2px), linear-gradient(45deg, #FFB6C1, #FFC0CB)`,
    WebkitBackgroundClip: 'text',
    WebkitTextFillColor: 'transparent',
    backgroundClip: 'text',
    WebkitTextStroke: `2px ${strokeColor}`,
    textShadow: '2px 2px 4px rgba(255, 105, 180, 0.3)',
    filter: 'none',
    WebkitFontSmoothing: 'antialiased',
    MozOsxFontSmoothing: 'grayscale',
    textRendering: 'optimizeLegibility',
    display: 'inline-block',
    backgroundColor,
    backgroundSize: 'cover',
    backgroundPosition: 'center',
    ...style
  };

  return (
    <span style={textStyle}>
      {text}
    </span>
  );
});

Flower366.showName = '波点图案';
Flower366.description = '花字366 - 经典波点图案填充';
Flower366.key = 'Flower366'; 
Flower366.mainColor = '#FF1493';