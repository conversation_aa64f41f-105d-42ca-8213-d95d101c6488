import React, { memo } from 'react';

interface Flower350Props {
  text?: string;
  style?: React.CSSProperties;
  fillColor?: string;
  strokeColor?: string;
  backgroundColor?: string;
}

export const Flower350: any = memo(({ 
  text = "花字", 
  style = {},
  fillColor = "#fdcb6e",
  strokeColor = "#f39c12", 
  backgroundColor = "transparent"
}: Flower350Props) => {
  const fontSize = style.fontSize || 48;
  const fontFamily = style.fontFamily || '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif';
  const fontWeight = style.fontWeight || 'bold';
  const letterSpacing = style.letterSpacing || 'normal';
  
  const textStyle: React.CSSProperties = {
    fontSize,
    fontFamily,
    fontWeight,
    letterSpacing,
    color: fillColor,
    WebkitTextStroke: `2px ${strokeColor}`,
    textShadow: `-4px 0px 0 #00b894, -3px -2px 0 #00b894, -3px -1px 0 #00b894, -3px 0px 0 #00b894, -3px 1px 0 #00b894, -3px 2px 0 #00b894, -2px -3px 0 #00b894, -2px -2px 0 #00b894, -2px -1px 0 #00b894, -2px 0px 0 #00b894, -2px 1px 0 #00b894, -2px 2px 0 #00b894, -2px 3px 0 #00b894, -1px -3px 0 #00b894, -1px -2px 0 #00b894, -1px -1px 0 #00b894, -1px 0px 0 #00b894, -1px 1px 0 #00b894, -1px 2px 0 #00b894, -1px 3px 0 #00b894, 0px -4px 0 #00b894, 0px -3px 0 #00b894, 0px -2px 0 #00b894, 0px -1px 0 #00b894, 0px 1px 0 #00b894, 0px 2px 0 #00b894, 0px 3px 0 #00b894, 0px 4px 0 #00b894, 1px -3px 0 #00b894, 1px -2px 0 #00b894, 1px -1px 0 #00b894, 1px 0px 0 #00b894, 1px 1px 0 #00b894, 1px 2px 0 #00b894, 1px 3px 0 #00b894, 2px -3px 0 #00b894, 2px -2px 0 #00b894, 2px -1px 0 #00b894, 2px 0px 0 #00b894, 2px 1px 0 #00b894, 2px 2px 0 #00b894, 2px 3px 0 #00b894, 3px -2px 0 #00b894, 3px -1px 0 #00b894, 3px 0px 0 #00b894, 3px 1px 0 #00b894, 3px 2px 0 #00b894, 4px 0px 0 #00b894`,
    filter: 'none',
    WebkitFontSmoothing: 'antialiased',
    MozOsxFontSmoothing: 'grayscale',
    textRendering: 'optimizeLegibility',
    display: 'inline-block',
    backgroundColor,
    ...style
  };

  return (
    <span style={textStyle}>
      {text}
    </span>
  );
});

Flower350.showName = '柠檬双圈';
Flower350.description = '花字350 - 清新柠檬的双圈描边';
Flower350.key = 'Flower350'; 
Flower350.mainColor = '#fdcb6e';