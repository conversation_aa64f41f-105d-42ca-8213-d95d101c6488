import React, { memo } from 'react';

interface Flower307Props {
  text?: string;
  style?: React.CSSProperties;
  fillColor?: string;
  strokeColor?: string;
  backgroundColor?: string;
}

export const Flower307: any = memo(({ 
  text = "花字", 
  style = {},
  fillColor = "#50c878",
  strokeColor = "#228b22", 
  backgroundColor = "transparent"
}: Flower307Props) => {
  const fontSize = style.fontSize || 48;
  const fontFamily = style.fontFamily || '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif';
  const fontWeight = style.fontWeight || 'bold';
  const letterSpacing = style.letterSpacing || 'normal';
  
  const textStyle: React.CSSProperties = {
    fontSize,
    fontFamily,
    fontWeight,
    letterSpacing,
    color: fillColor,
    textShadow: 'none',
    filter: 'none',
    WebkitFontSmoothing: 'antialiased',
    MozOsxFontSmoothing: 'grayscale',
    textRendering: 'optimizeLegibility',
    display: 'inline-block',
    backgroundColor,
    ...style
  };

  return (
    <span style={textStyle}>
      {text}
    </span>
  );
});

Flower307.showName = '翡翠绿色光晕';
Flower307.description = '花字307 - 清新自然的翡翠绿';
Flower307.key = 'Flower307'; 
Flower307.mainColor = '#50c878';