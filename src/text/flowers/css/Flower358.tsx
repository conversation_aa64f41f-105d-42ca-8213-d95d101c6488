import React, { memo } from 'react';

interface Flower358Props {
  text?: string;
  style?: React.CSSProperties;
  fillColor?: string;
  strokeColor?: string;
  backgroundColor?: string;
}

export const Flower358: any = memo(({ 
  text = "花字", 
  style = {},
  fillColor = "#fdcb6e",
  strokeColor = "#e17055", 
  backgroundColor = "transparent"
}: Flower358Props) => {
  const fontSize = style.fontSize || 48;
  const fontFamily = style.fontFamily || '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif';
  const fontWeight = style.fontWeight || 'bold';
  const letterSpacing = style.letterSpacing || 'normal';
  
  const textStyle: React.CSSProperties = {
    fontSize,
    fontFamily,
    fontWeight,
    letterSpacing,
    color: fillColor,
    WebkitTextStroke: `2px ${strokeColor}`,
    textShadow: `-4px 0px 0 #fd79a8, -3px -2px 0 #fd79a8, -3px -1px 0 #fd79a8, -3px 0px 0 #fd79a8, -3px 1px 0 #fd79a8, -3px 2px 0 #fd79a8, -2px -3px 0 #fd79a8, -2px -2px 0 #fd79a8, -2px -1px 0 #fd79a8, -2px 0px 0 #fd79a8, -2px 1px 0 #fd79a8, -2px 2px 0 #fd79a8, -2px 3px 0 #fd79a8, -1px -3px 0 #fd79a8, -1px -2px 0 #fd79a8, -1px -1px 0 #fd79a8, -1px 0px 0 #fd79a8, -1px 1px 0 #fd79a8, -1px 2px 0 #fd79a8, -1px 3px 0 #fd79a8, 0px -4px 0 #fd79a8, 0px -3px 0 #fd79a8, 0px -2px 0 #fd79a8, 0px -1px 0 #fd79a8, 0px 1px 0 #fd79a8, 0px 2px 0 #fd79a8, 0px 3px 0 #fd79a8, 0px 4px 0 #fd79a8, 1px -3px 0 #fd79a8, 1px -2px 0 #fd79a8, 1px -1px 0 #fd79a8, 1px 0px 0 #fd79a8, 1px 1px 0 #fd79a8, 1px 2px 0 #fd79a8, 1px 3px 0 #fd79a8, 2px -3px 0 #fd79a8, 2px -2px 0 #fd79a8, 2px -1px 0 #fd79a8, 2px 0px 0 #fd79a8, 2px 1px 0 #fd79a8, 2px 2px 0 #fd79a8, 2px 3px 0 #fd79a8, 3px -2px 0 #fd79a8, 3px -1px 0 #fd79a8, 3px 0px 0 #fd79a8, 3px 1px 0 #fd79a8, 3px 2px 0 #fd79a8, 4px 0px 0 #fd79a8`,
    filter: 'none',
    WebkitFontSmoothing: 'antialiased',
    MozOsxFontSmoothing: 'grayscale',
    textRendering: 'optimizeLegibility',
    display: 'inline-block',
    backgroundColor,
    ...style
  };

  return (
    <span style={textStyle}>
      {text}
    </span>
  );
});

Flower358.showName = '日落双辉';
Flower358.description = '花字358 - 浪漫日落的双辉描边';
Flower358.key = 'Flower358'; 
Flower358.mainColor = '#fdcb6e';