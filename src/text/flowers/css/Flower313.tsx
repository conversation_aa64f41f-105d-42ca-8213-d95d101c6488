import React, { memo } from 'react';

interface Flower313Props {
  text?: string;
  style?: React.CSSProperties;
  fillColor?: string;
  strokeColor?: string;
  backgroundColor?: string;
}

export const Flower313: any = memo(({ 
  text = "花字", 
  style = {},
  fillColor = "#ffb7c5",
  strokeColor = "#ff69b4", 
  backgroundColor = "transparent"
}: Flower313Props) => {
  const fontSize = style.fontSize || 48;
  const fontFamily = style.fontFamily || '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif';
  const fontWeight = style.fontWeight || 'bold';
  const letterSpacing = style.letterSpacing || 'normal';
  
  const textStyle: React.CSSProperties = {
    fontSize,
    fontFamily,
    fontWeight,
    letterSpacing,
    color: fillColor,
    textShadow: `
      0 0 2px ${fillColor},
      0 0 4px #ff91a4,
      0 0 8px ${strokeColor},
      1px 1px 2px rgba(255, 182, 193, 0.3)
    `,
    filter: `drop-shadow(0 0 6px ${fillColor}80)`,
    WebkitFontSmoothing: 'antialiased',
    MozOsxFontSmoothing: 'grayscale',
    textRendering: 'optimizeLegibility',
    display: 'inline-block',
    backgroundColor,
    ...style
  };

  return (
    <span style={textStyle}>
      {text}
    </span>
  );
});

Flower313.showName = '樱花粉色';
Flower313.description = '花字313 - 浪漫优雅的樱花粉';
Flower313.key = 'Flower313'; 
Flower313.mainColor = '#ffb7c5';