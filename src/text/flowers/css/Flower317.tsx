import React, { memo } from 'react';

interface Flower317Props {
  text?: string;
  style?: React.CSSProperties;
  fillColor?: string;
  strokeColor?: string;
  backgroundColor?: string;
}

export const Flower317: any = memo(({ 
  text = "花字", 
  style = {},
  fillColor = "#663399",
  strokeColor = "#dda0dd", 
  backgroundColor = "transparent"
}: Flower317Props) => {
  const fontSize = style.fontSize || 48;
  const fontFamily = style.fontFamily || '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif';
  const fontWeight = style.fontWeight || 'bold';
  const letterSpacing = style.letterSpacing || 'normal';
  
  const textStyle: React.CSSProperties = {
    fontSize,
    fontFamily,
    fontWeight,
    letterSpacing,
    background: `linear-gradient(to bottom, #dda0dd, #9966cc, ${fillColor})`,WebkitBackgroundClip: 'text',
    WebkitTextFillColor: 'transparent',
    backgroundClip: 'text',filter: `drop-shadow(0 0 18px ${fillColor}CC)`,
    position: 'relative',
    WebkitFontSmoothing: 'antialiased',
    MozOsxFontSmoothing: 'grayscale',
    textRendering: 'optimizeLegibility',
    display: 'inline-block',
    backgroundColor,
    ...style
  };return (
    <span style={textStyle}>
      {text}
    </span>
  );
});

Flower317.showName = '星空紫银';
Flower317.description = '花字317 - 梦幻迷人的星空紫';
Flower317.key = 'Flower317'; 
Flower317.mainColor = '#663399';