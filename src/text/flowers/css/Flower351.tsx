import React, { memo } from 'react';

interface Flower351Props {
  text?: string;
  style?: React.CSSProperties;
  fillColor?: string;
  strokeColor?: string;
  backgroundColor?: string;
}

export const Flower351: any = memo(({ 
  text = "花字", 
  style = {},
  fillColor = "#fd79a8",
  strokeColor = "#e84393", 
  backgroundColor = "transparent"
}: Flower351Props) => {
  const fontSize = style.fontSize || 48;
  const fontFamily = style.fontFamily || '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif';
  const fontWeight = style.fontWeight || 'bold';
  const letterSpacing = style.letterSpacing || 'normal';
  
  const textStyle: React.CSSProperties = {
    fontSize,
    fontFamily,
    fontWeight,
    letterSpacing,
    color: fillColor,
    WebkitTextStroke: `2px ${strokeColor}`,
    textShadow: `-4px 0px 0 #f39c12, -3px -2px 0 #f39c12, -3px -1px 0 #f39c12, -3px 0px 0 #f39c12, -3px 1px 0 #f39c12, -3px 2px 0 #f39c12, -2px -3px 0 #f39c12, -2px -2px 0 #f39c12, -2px -1px 0 #f39c12, -2px 0px 0 #f39c12, -2px 1px 0 #f39c12, -2px 2px 0 #f39c12, -2px 3px 0 #f39c12, -1px -3px 0 #f39c12, -1px -2px 0 #f39c12, -1px -1px 0 #f39c12, -1px 0px 0 #f39c12, -1px 1px 0 #f39c12, -1px 2px 0 #f39c12, -1px 3px 0 #f39c12, 0px -4px 0 #f39c12, 0px -3px 0 #f39c12, 0px -2px 0 #f39c12, 0px -1px 0 #f39c12, 0px 1px 0 #f39c12, 0px 2px 0 #f39c12, 0px 3px 0 #f39c12, 0px 4px 0 #f39c12, 1px -3px 0 #f39c12, 1px -2px 0 #f39c12, 1px -1px 0 #f39c12, 1px 0px 0 #f39c12, 1px 1px 0 #f39c12, 1px 2px 0 #f39c12, 1px 3px 0 #f39c12, 2px -3px 0 #f39c12, 2px -2px 0 #f39c12, 2px -1px 0 #f39c12, 2px 0px 0 #f39c12, 2px 1px 0 #f39c12, 2px 2px 0 #f39c12, 2px 3px 0 #f39c12, 3px -2px 0 #f39c12, 3px -1px 0 #f39c12, 3px 0px 0 #f39c12, 3px 1px 0 #f39c12, 3px 2px 0 #f39c12, 4px 0px 0 #f39c12`,
    filter: 'none',
    WebkitFontSmoothing: 'antialiased',
    MozOsxFontSmoothing: 'grayscale',
    textRendering: 'optimizeLegibility',
    display: 'inline-block',
    backgroundColor,
    ...style
  };

  return (
    <span style={textStyle}>
      {text}
    </span>
  );
});

Flower351.showName = '玫瑰双金';
Flower351.description = '花字351 - 浪漫玫瑰的双金效果';
Flower351.key = 'Flower351'; 
Flower351.mainColor = '#fd79a8';