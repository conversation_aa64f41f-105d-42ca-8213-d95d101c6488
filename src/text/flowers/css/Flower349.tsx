import React, { memo } from 'react';

interface Flower349Props {
  text?: string;
  style?: React.CSSProperties;
  fillColor?: string;
  strokeColor?: string;
  backgroundColor?: string;
}

export const Flower349: any = memo(({ 
  text = "花字", 
  style = {},
  fillColor = "#a29bfe",
  strokeColor = "#6c5ce7", 
  backgroundColor = "transparent"
}: Flower349Props) => {
  const fontSize = style.fontSize || 48;
  const fontFamily = style.fontFamily || '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif';
  const fontWeight = style.fontWeight || 'bold';
  const letterSpacing = style.letterSpacing || 'normal';
  
  const textStyle: React.CSSProperties = {
    fontSize,
    fontFamily,
    fontWeight,
    letterSpacing,
    color: fillColor,
    WebkitTextStroke: `2px ${strokeColor}`,
    textShadow: `-4px 0px 0 #fd79a8, -3px -2px 0 #fd79a8, -3px -1px 0 #fd79a8, -3px 0px 0 #fd79a8, -3px 1px 0 #fd79a8, -3px 2px 0 #fd79a8, -2px -3px 0 #fd79a8, -2px -2px 0 #fd79a8, -2px -1px 0 #fd79a8, -2px 0px 0 #fd79a8, -2px 1px 0 #fd79a8, -2px 2px 0 #fd79a8, -2px 3px 0 #fd79a8, -1px -3px 0 #fd79a8, -1px -2px 0 #fd79a8, -1px -1px 0 #fd79a8, -1px 0px 0 #fd79a8, -1px 1px 0 #fd79a8, -1px 2px 0 #fd79a8, -1px 3px 0 #fd79a8, 0px -4px 0 #fd79a8, 0px -3px 0 #fd79a8, 0px -2px 0 #fd79a8, 0px -1px 0 #fd79a8, 0px 1px 0 #fd79a8, 0px 2px 0 #fd79a8, 0px 3px 0 #fd79a8, 0px 4px 0 #fd79a8, 1px -3px 0 #fd79a8, 1px -2px 0 #fd79a8, 1px -1px 0 #fd79a8, 1px 0px 0 #fd79a8, 1px 1px 0 #fd79a8, 1px 2px 0 #fd79a8, 1px 3px 0 #fd79a8, 2px -3px 0 #fd79a8, 2px -2px 0 #fd79a8, 2px -1px 0 #fd79a8, 2px 0px 0 #fd79a8, 2px 1px 0 #fd79a8, 2px 2px 0 #fd79a8, 2px 3px 0 #fd79a8, 3px -2px 0 #fd79a8, 3px -1px 0 #fd79a8, 3px 0px 0 #fd79a8, 3px 1px 0 #fd79a8, 3px 2px 0 #fd79a8, 4px 0px 0 #fd79a8`,
    filter: 'none',
    WebkitFontSmoothing: 'antialiased',
    MozOsxFontSmoothing: 'grayscale',
    textRendering: 'optimizeLegibility',
    display: 'inline-block',
    backgroundColor,
    ...style
  };

  return (
    <span style={textStyle}>
      {text}
    </span>
  );
});

Flower349.showName = '薰衣草双影';
Flower349.description = '花字349 - 优雅薰衣草双影效果';
Flower349.key = 'Flower349'; 
Flower349.mainColor = '#a29bfe';