import React, { memo } from 'react';

interface Flower345Props {
  text?: string;
  style?: React.CSSProperties;
  fillColor?: string;
  strokeColor?: string;
  backgroundColor?: string;
}

export const Flower345: any = memo(({ 
  text = "花字", 
  style = {},
  fillColor = "#6c5ce7",
  strokeColor = "#5f3dc4", 
  backgroundColor = "transparent"
}: Flower345Props) => {
  const fontSize = style.fontSize || 48;
  const fontFamily = style.fontFamily || '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif';
  const fontWeight = style.fontWeight || 'bold';
  const letterSpacing = style.letterSpacing || 'normal';
  
  const textStyle: React.CSSProperties = {
    fontSize,
    fontFamily,
    fontWeight,
    letterSpacing,
    color: fillColor,
    WebkitTextStroke: `2px ${strokeColor}`,
    textShadow: `-4px 0px 0 #74b9ff, -3px -2px 0 #74b9ff, -3px -1px 0 #74b9ff, -3px 0px 0 #74b9ff, -3px 1px 0 #74b9ff, -3px 2px 0 #74b9ff, -2px -3px 0 #74b9ff, -2px -2px 0 #74b9ff, -2px -1px 0 #74b9ff, -2px 0px 0 #74b9ff, -2px 1px 0 #74b9ff, -2px 2px 0 #74b9ff, -2px 3px 0 #74b9ff, -1px -3px 0 #74b9ff, -1px -2px 0 #74b9ff, -1px -1px 0 #74b9ff, -1px 0px 0 #74b9ff, -1px 1px 0 #74b9ff, -1px 2px 0 #74b9ff, -1px 3px 0 #74b9ff, 0px -4px 0 #74b9ff, 0px -3px 0 #74b9ff, 0px -2px 0 #74b9ff, 0px -1px 0 #74b9ff, 0px 1px 0 #74b9ff, 0px 2px 0 #74b9ff, 0px 3px 0 #74b9ff, 0px 4px 0 #74b9ff, 1px -3px 0 #74b9ff, 1px -2px 0 #74b9ff, 1px -1px 0 #74b9ff, 1px 0px 0 #74b9ff, 1px 1px 0 #74b9ff, 1px 2px 0 #74b9ff, 1px 3px 0 #74b9ff, 2px -3px 0 #74b9ff, 2px -2px 0 #74b9ff, 2px -1px 0 #74b9ff, 2px 0px 0 #74b9ff, 2px 1px 0 #74b9ff, 2px 2px 0 #74b9ff, 2px 3px 0 #74b9ff, 3px -2px 0 #74b9ff, 3px -1px 0 #74b9ff, 3px 0px 0 #74b9ff, 3px 1px 0 #74b9ff, 3px 2px 0 #74b9ff, 4px 0px 0 #74b9ff`,
    filter: 'none',
    WebkitFontSmoothing: 'antialiased',
    MozOsxFontSmoothing: 'grayscale',
    textRendering: 'optimizeLegibility',
    display: 'inline-block',
    backgroundColor,
    ...style
  };

  return (
    <span style={textStyle}>
      {text}
    </span>
  );
});

Flower345.showName = '海洋双波';
Flower345.description = '花字345 - 深邃海洋的双波效果';
Flower345.key = 'Flower345'; 
Flower345.mainColor = '#6c5ce7';