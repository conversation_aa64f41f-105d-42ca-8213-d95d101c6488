import React, { memo } from 'react';

interface Flower344Props {
  text?: string;
  style?: React.CSSProperties;
  fillColor?: string;
  strokeColor?: string;
  backgroundColor?: string;
}

export const Flower344: any = memo(({ 
  text = "花字", 
  style = {},
  fillColor = "#fd79a8",
  strokeColor = "#e84393", 
  backgroundColor = "transparent"
}: Flower344Props) => {
  const fontSize = style.fontSize || 48;
  const fontFamily = style.fontFamily || '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif';
  const fontWeight = style.fontWeight || 'bold';
  const letterSpacing = style.letterSpacing || 'normal';
  
  const textStyle: React.CSSProperties = {
    fontSize,
    fontFamily,
    fontWeight,
    letterSpacing,
    color: fillColor,
    WebkitTextStroke: `2px ${strokeColor}`,
    textShadow: `-4px 0px 0 #6c5ce7, -3px -2px 0 #6c5ce7, -3px -1px 0 #6c5ce7, -3px 0px 0 #6c5ce7, -3px 1px 0 #6c5ce7, -3px 2px 0 #6c5ce7, -2px -3px 0 #6c5ce7, -2px -2px 0 #6c5ce7, -2px -1px 0 #6c5ce7, -2px 0px 0 #6c5ce7, -2px 1px 0 #6c5ce7, -2px 2px 0 #6c5ce7, -2px 3px 0 #6c5ce7, -1px -3px 0 #6c5ce7, -1px -2px 0 #6c5ce7, -1px -1px 0 #6c5ce7, -1px 0px 0 #6c5ce7, -1px 1px 0 #6c5ce7, -1px 2px 0 #6c5ce7, -1px 3px 0 #6c5ce7, 0px -4px 0 #6c5ce7, 0px -3px 0 #6c5ce7, 0px -2px 0 #6c5ce7, 0px -1px 0 #6c5ce7, 0px 1px 0 #6c5ce7, 0px 2px 0 #6c5ce7, 0px 3px 0 #6c5ce7, 0px 4px 0 #6c5ce7, 1px -3px 0 #6c5ce7, 1px -2px 0 #6c5ce7, 1px -1px 0 #6c5ce7, 1px 0px 0 #6c5ce7, 1px 1px 0 #6c5ce7, 1px 2px 0 #6c5ce7, 1px 3px 0 #6c5ce7, 2px -3px 0 #6c5ce7, 2px -2px 0 #6c5ce7, 2px -1px 0 #6c5ce7, 2px 0px 0 #6c5ce7, 2px 1px 0 #6c5ce7, 2px 2px 0 #6c5ce7, 2px 3px 0 #6c5ce7, 3px -2px 0 #6c5ce7, 3px -1px 0 #6c5ce7, 3px 0px 0 #6c5ce7, 3px 1px 0 #6c5ce7, 3px 2px 0 #6c5ce7, 4px 0px 0 #6c5ce7`,
    filter: 'none',
    WebkitFontSmoothing: 'antialiased',
    MozOsxFontSmoothing: 'grayscale',
    textRendering: 'optimizeLegibility',
    display: 'inline-block',
    backgroundColor,
    ...style
  };

  return (
    <span style={textStyle}>
      {text}
    </span>
  );
});

Flower344.showName = '梦幻双色';
Flower344.description = '花字344 - 梦幻浪漫的双色描边';
Flower344.key = 'Flower344'; 
Flower344.mainColor = '#fd79a8';