import React, { memo } from 'react';

interface Flower346Props {
  text?: string;
  style?: React.CSSProperties;
  fillColor?: string;
  strokeColor?: string;
  backgroundColor?: string;
}

export const Flower346: any = memo(({ 
  text = "花字", 
  style = {},
  fillColor = "#00b894",
  strokeColor = "#00a085", 
  backgroundColor = "transparent"
}: Flower346Props) => {
  const fontSize = style.fontSize || 48;
  const fontFamily = style.fontFamily || '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif';
  const fontWeight = style.fontWeight || 'bold';
  const letterSpacing = style.letterSpacing || 'normal';
  
  const textStyle: React.CSSProperties = {
    fontSize,
    fontFamily,
    fontWeight,
    letterSpacing,
    color: fillColor,
    WebkitTextStroke: `2px ${strokeColor}`,
    textShadow: `-4px 0px 0 #81ecec, -3px -2px 0 #81ecec, -3px -1px 0 #81ecec, -3px 0px 0 #81ecec, -3px 1px 0 #81ecec, -3px 2px 0 #81ecec, -2px -3px 0 #81ecec, -2px -2px 0 #81ecec, -2px -1px 0 #81ecec, -2px 0px 0 #81ecec, -2px 1px 0 #81ecec, -2px 2px 0 #81ecec, -2px 3px 0 #81ecec, -1px -3px 0 #81ecec, -1px -2px 0 #81ecec, -1px -1px 0 #81ecec, -1px 0px 0 #81ecec, -1px 1px 0 #81ecec, -1px 2px 0 #81ecec, -1px 3px 0 #81ecec, 0px -4px 0 #81ecec, 0px -3px 0 #81ecec, 0px -2px 0 #81ecec, 0px -1px 0 #81ecec, 0px 1px 0 #81ecec, 0px 2px 0 #81ecec, 0px 3px 0 #81ecec, 0px 4px 0 #81ecec, 1px -3px 0 #81ecec, 1px -2px 0 #81ecec, 1px -1px 0 #81ecec, 1px 0px 0 #81ecec, 1px 1px 0 #81ecec, 1px 2px 0 #81ecec, 1px 3px 0 #81ecec, 2px -3px 0 #81ecec, 2px -2px 0 #81ecec, 2px -1px 0 #81ecec, 2px 0px 0 #81ecec, 2px 1px 0 #81ecec, 2px 2px 0 #81ecec, 2px 3px 0 #81ecec, 3px -2px 0 #81ecec, 3px -1px 0 #81ecec, 3px 0px 0 #81ecec, 3px 1px 0 #81ecec, 3px 2px 0 #81ecec, 4px 0px 0 #81ecec`,
    filter: 'none',
    WebkitFontSmoothing: 'antialiased',
    MozOsxFontSmoothing: 'grayscale',
    textRendering: 'optimizeLegibility',
    display: 'inline-block',
    backgroundColor,
    ...style
  };

  return (
    <span style={textStyle}>
      {text}
    </span>
  );
});

Flower346.showName = '森林双环';
Flower346.description = '花字346 - 自然森林的双环描边';
Flower346.key = 'Flower346'; 
Flower346.mainColor = '#00b894';