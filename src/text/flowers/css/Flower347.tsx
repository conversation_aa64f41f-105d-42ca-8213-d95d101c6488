import React, { memo } from 'react';

interface Flower347Props {
  text?: string;
  style?: React.CSSProperties;
  fillColor?: string;
  strokeColor?: string;
  backgroundColor?: string;
}

export const Flower347: any = memo(({ 
  text = "花字", 
  style = {},
  fillColor = "#e17055",
  strokeColor = "#d63031", 
  backgroundColor = "transparent"
}: Flower347Props) => {
  const fontSize = style.fontSize || 48;
  const fontFamily = style.fontFamily || '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif';
  const fontWeight = style.fontWeight || 'bold';
  const letterSpacing = style.letterSpacing || 'normal';
  
  const textStyle: React.CSSProperties = {
    fontSize,
    fontFamily,
    fontWeight,
    letterSpacing,
    color: fillColor,
    WebkitTextStroke: `2px ${strokeColor}`,
    textShadow: `-4px 0px 0 #fdcb6e, -3px -2px 0 #fdcb6e, -3px -1px 0 #fdcb6e, -3px 0px 0 #fdcb6e, -3px 1px 0 #fdcb6e, -3px 2px 0 #fdcb6e, -2px -3px 0 #fdcb6e, -2px -2px 0 #fdcb6e, -2px -1px 0 #fdcb6e, -2px 0px 0 #fdcb6e, -2px 1px 0 #fdcb6e, -2px 2px 0 #fdcb6e, -2px 3px 0 #fdcb6e, -1px -3px 0 #fdcb6e, -1px -2px 0 #fdcb6e, -1px -1px 0 #fdcb6e, -1px 0px 0 #fdcb6e, -1px 1px 0 #fdcb6e, -1px 2px 0 #fdcb6e, -1px 3px 0 #fdcb6e, 0px -4px 0 #fdcb6e, 0px -3px 0 #fdcb6e, 0px -2px 0 #fdcb6e, 0px -1px 0 #fdcb6e, 0px 1px 0 #fdcb6e, 0px 2px 0 #fdcb6e, 0px 3px 0 #fdcb6e, 0px 4px 0 #fdcb6e, 1px -3px 0 #fdcb6e, 1px -2px 0 #fdcb6e, 1px -1px 0 #fdcb6e, 1px 0px 0 #fdcb6e, 1px 1px 0 #fdcb6e, 1px 2px 0 #fdcb6e, 1px 3px 0 #fdcb6e, 2px -3px 0 #fdcb6e, 2px -2px 0 #fdcb6e, 2px -1px 0 #fdcb6e, 2px 0px 0 #fdcb6e, 2px 1px 0 #fdcb6e, 2px 2px 0 #fdcb6e, 2px 3px 0 #fdcb6e, 3px -2px 0 #fdcb6e, 3px -1px 0 #fdcb6e, 3px 0px 0 #fdcb6e, 3px 1px 0 #fdcb6e, 3px 2px 0 #fdcb6e, 4px 0px 0 #fdcb6e`,
    filter: 'none',
    WebkitFontSmoothing: 'antialiased',
    MozOsxFontSmoothing: 'grayscale',
    textRendering: 'optimizeLegibility',
    display: 'inline-block',
    backgroundColor,
    ...style
  };

  return (
    <span style={textStyle}>
      {text}
    </span>
  );
});

Flower347.showName = '珊瑚双层';
Flower347.description = '花字347 - 活力珊瑚的双层效果';
Flower347.key = 'Flower347'; 
Flower347.mainColor = '#e17055';