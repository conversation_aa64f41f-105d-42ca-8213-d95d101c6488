import React, { memo } from 'react';

interface Flower367Props {
  text?: string;
  style?: React.CSSProperties;
  fillColor?: string;
  strokeColor?: string;
  backgroundColor?: string;
}

export const Flower367: any = memo(({ 
  text = "花字", 
  style = {},
  fillColor = "#004e92",
  strokeColor = "#191970", 
  backgroundColor = "transparent"
}: Flower367Props) => {
  const fontSize = style.fontSize || 48;
  const fontFamily = style.fontFamily || '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif';
  const fontWeight = style.fontWeight || 'bold';
  const letterSpacing = style.letterSpacing || 'normal';
  
  const textStyle: React.CSSProperties = {
    fontSize,
    fontFamily,
    fontWeight,
    letterSpacing,
    background: `radial-gradient(2px 2px at 20px 30px, #FFFFFF, transparent), radial-gradient(2px 2px at 40px 70px, #FFFFFF, transparent), radial-gradient(1px 1px at 90px 40px, #FFFFFF, transparent), radial-gradient(1px 1px at 130px 80px, #FFFFFF, transparent), linear-gradient(45deg, #000428, ${fillColor})`,
    WebkitBackgroundClip: 'text',
    WebkitTextFillColor: 'transparent',
    backgroundClip: 'text',
    WebkitTextStroke: `2px ${strokeColor}`,
    textShadow: '2px 2px 4px rgba(0, 4, 40, 0.5)',
    filter: 'none',
    WebkitFontSmoothing: 'antialiased',
    MozOsxFontSmoothing: 'grayscale',
    textRendering: 'optimizeLegibility',
    display: 'inline-block',
    backgroundColor,
    backgroundSize: 'cover',
    backgroundPosition: 'center',
    ...style
  };

  return (
    <span style={textStyle}>
      {text}
    </span>
  );
});

Flower367.showName = '星空背景';
Flower367.description = '花字367 - 神秘星空图案效果';
Flower367.key = 'Flower367'; 
Flower367.mainColor = '#004e92';