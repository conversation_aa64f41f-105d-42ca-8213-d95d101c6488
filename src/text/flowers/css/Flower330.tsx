import React, { memo } from 'react';

interface Flower330Props {
  text?: string;
  style?: React.CSSProperties;
  fillColor?: string;
  strokeColor?: string;
  backgroundColor?: string;
}

export const Flower330: any = memo(({ 
  text = "花字", 
  style = {},
  fillColor = "#00b894",
  strokeColor = "#00a085", 
  backgroundColor = "transparent"
}: Flower330Props) => {
  const fontSize = style.fontSize || 48;
  const fontFamily = style.fontFamily || '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif';
  const fontWeight = style.fontWeight || 'bold';
  const letterSpacing = style.letterSpacing || 'normal';
  
  const textStyle: React.CSSProperties = {
    fontSize,
    fontFamily,
    fontWeight,
    letterSpacing,
    color: fillColor,
    WebkitTextStroke: `2px ${strokeColor}`,
    textShadow: 'none',
    filter: 'none',
    WebkitFontSmoothing: 'antialiased',
    MozOsxFontSmoothing: 'grayscale',
    textRendering: 'optimizeLegibility',
    display: 'inline-block',
    backgroundColor,
    ...style
  };

  return (
    <span style={textStyle}>
      {text}
    </span>
  );
});

Flower330.showName = '柠檬黄绿';
Flower330.description = '花字330 - 清新活力的柠檬色';
Flower330.key = 'Flower330'; 
Flower330.mainColor = '#00b894';