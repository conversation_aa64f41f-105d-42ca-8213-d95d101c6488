import React, { memo } from 'react';

interface Flower376Props {
  text?: string;
  style?: React.CSSProperties;
  fillColor?: string;
  strokeColor?: string;
  backgroundColor?: string;
}

export const Flower376: any = memo(({ 
  text = "花字", 
  style = {},
  fillColor = "#00FFFF",
  strokeColor = "#000000", 
  backgroundColor = "transparent"
}: Flower376Props) => {
  const fontSize = style.fontSize || 48;
  const fontFamily = style.fontFamily || '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif';
  const fontWeight = style.fontWeight || 'bold';
  const letterSpacing = style.letterSpacing || 'normal';
  
  const textStyle: React.CSSProperties = {
    fontSize,
    fontFamily,
    fontWeight,
    letterSpacing,
    background: `linear-gradient(45deg, ${fillColor}, #FF00FF, #FFFF00, ${fillColor})`,
    WebkitBackgroundClip: 'text',
    WebkitTextFillColor: 'transparent',
    backgroundClip: 'text',
    WebkitTextStroke: `2px ${strokeColor}`,
    textShadow: '0 0 10px #00FFFF, 0 0 20px #FF00FF, 0 0 30px #FFFF00',
    filter: 'none',
    WebkitFontSmoothing: 'antialiased',
    MozOsxFontSmoothing: 'grayscale',
    textRendering: 'optimizeLegibility',
    display: 'inline-block',
    backgroundColor,
    backgroundSize: 'cover',
    backgroundPosition: 'center',
    ...style
  };

  return (
    <span style={textStyle}>
      {text}
    </span>
  );
});

Flower376.showName = '霓虹光效';
Flower376.description = '花字376 - 炫酷霓虹光效果';
Flower376.key = 'Flower376'; 
Flower376.mainColor = '#00FFFF';