import React, { memo } from 'react';

interface Flower352Props {
  text?: string;
  style?: React.CSSProperties;
  fillColor?: string;
  strokeColor?: string;
  backgroundColor?: string;
}

export const Flower352: any = memo(({ 
  text = "花字", 
  style = {},
  fillColor = "#b2bec3",
  strokeColor = "#636e72", 
  backgroundColor = "transparent"
}: Flower352Props) => {
  const fontSize = style.fontSize || 48;
  const fontFamily = style.fontFamily || '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif';
  const fontWeight = style.fontWeight || 'bold';
  const letterSpacing = style.letterSpacing || 'normal';
  
  const textStyle: React.CSSProperties = {
    fontSize,
    fontFamily,
    fontWeight,
    letterSpacing,
    color: fillColor,
    WebkitTextStroke: `2px ${strokeColor}`,
    textShadow: `-4px 0px 0 #74b9ff, -3px -2px 0 #74b9ff, -3px -1px 0 #74b9ff, -3px 0px 0 #74b9ff, -3px 1px 0 #74b9ff, -3px 2px 0 #74b9ff, -2px -3px 0 #74b9ff, -2px -2px 0 #74b9ff, -2px -1px 0 #74b9ff, -2px 0px 0 #74b9ff, -2px 1px 0 #74b9ff, -2px 2px 0 #74b9ff, -2px 3px 0 #74b9ff, -1px -3px 0 #74b9ff, -1px -2px 0 #74b9ff, -1px -1px 0 #74b9ff, -1px 0px 0 #74b9ff, -1px 1px 0 #74b9ff, -1px 2px 0 #74b9ff, -1px 3px 0 #74b9ff, 0px -4px 0 #74b9ff, 0px -3px 0 #74b9ff, 0px -2px 0 #74b9ff, 0px -1px 0 #74b9ff, 0px 1px 0 #74b9ff, 0px 2px 0 #74b9ff, 0px 3px 0 #74b9ff, 0px 4px 0 #74b9ff, 1px -3px 0 #74b9ff, 1px -2px 0 #74b9ff, 1px -1px 0 #74b9ff, 1px 0px 0 #74b9ff, 1px 1px 0 #74b9ff, 1px 2px 0 #74b9ff, 1px 3px 0 #74b9ff, 2px -3px 0 #74b9ff, 2px -2px 0 #74b9ff, 2px -1px 0 #74b9ff, 2px 0px 0 #74b9ff, 2px 1px 0 #74b9ff, 2px 2px 0 #74b9ff, 2px 3px 0 #74b9ff, 3px -2px 0 #74b9ff, 3px -1px 0 #74b9ff, 3px 0px 0 #74b9ff, 3px 1px 0 #74b9ff, 3px 2px 0 #74b9ff, 4px 0px 0 #74b9ff`,
    filter: 'none',
    WebkitFontSmoothing: 'antialiased',
    MozOsxFontSmoothing: 'grayscale',
    textRendering: 'optimizeLegibility',
    display: 'inline-block',
    backgroundColor,
    ...style
  };

  return (
    <span style={textStyle}>
      {text}
    </span>
  );
});

Flower352.showName = '科技双线';
Flower352.description = '花字352 - 现代科技的双线描边';
Flower352.key = 'Flower352'; 
Flower352.mainColor = '#b2bec3';