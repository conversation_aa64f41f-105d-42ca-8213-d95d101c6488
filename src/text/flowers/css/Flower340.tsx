import React, { memo } from 'react';

interface Flower340Props {
  text?: string;
  style?: React.CSSProperties;
  fillColor?: string;
  strokeColor?: string;
  backgroundColor?: string;
}

export const Flower340: any = memo(({ 
  text = "花字", 
  style = {},
  fillColor = "#ff9a9e",
  strokeColor = "#ff6b6b", 
  backgroundColor = "transparent"
}: Flower340Props) => {
  const fontSize = style.fontSize || 48;
  const fontFamily = style.fontFamily || '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif';
  const fontWeight = style.fontWeight || 'bold';
  const letterSpacing = style.letterSpacing || 'normal';
  
  const textStyle: React.CSSProperties = {
    fontSize,
    fontFamily,
    fontWeight,
    letterSpacing,
    color: fillColor,
    WebkitTextStroke: `2px ${strokeColor}`,
    textShadow: `-4px 0px 0 #4ecdc4, -3px -2px 0 #4ecdc4, -3px -1px 0 #4ecdc4, -3px 0px 0 #4ecdc4, -3px 1px 0 #4ecdc4, -3px 2px 0 #4ecdc4, -2px -3px 0 #4ecdc4, -2px -2px 0 #4ecdc4, -2px -1px 0 #4ecdc4, -2px 0px 0 #4ecdc4, -2px 1px 0 #4ecdc4, -2px 2px 0 #4ecdc4, -2px 3px 0 #4ecdc4, -1px -3px 0 #4ecdc4, -1px -2px 0 #4ecdc4, -1px -1px 0 #4ecdc4, -1px 0px 0 #4ecdc4, -1px 1px 0 #4ecdc4, -1px 2px 0 #4ecdc4, -1px 3px 0 #4ecdc4, 0px -4px 0 #4ecdc4, 0px -3px 0 #4ecdc4, 0px -2px 0 #4ecdc4, 0px -1px 0 #4ecdc4, 0px 1px 0 #4ecdc4, 0px 2px 0 #4ecdc4, 0px 3px 0 #4ecdc4, 0px 4px 0 #4ecdc4, 1px -3px 0 #4ecdc4, 1px -2px 0 #4ecdc4, 1px -1px 0 #4ecdc4, 1px 0px 0 #4ecdc4, 1px 1px 0 #4ecdc4, 1px 2px 0 #4ecdc4, 1px 3px 0 #4ecdc4, 2px -3px 0 #4ecdc4, 2px -2px 0 #4ecdc4, 2px -1px 0 #4ecdc4, 2px 0px 0 #4ecdc4, 2px 1px 0 #4ecdc4, 2px 2px 0 #4ecdc4, 2px 3px 0 #4ecdc4, 3px -2px 0 #4ecdc4, 3px -1px 0 #4ecdc4, 3px 0px 0 #4ecdc4, 3px 1px 0 #4ecdc4, 3px 2px 0 #4ecdc4, 4px 0px 0 #4ecdc4`,
    filter: 'none',
    WebkitFontSmoothing: 'antialiased',
    MozOsxFontSmoothing: 'grayscale',
    textRendering: 'optimizeLegibility',
    display: 'inline-block',
    backgroundColor,
    ...style
  };

  return (
    <span style={textStyle}>
      {text}
    </span>
  );
});

Flower340.showName = '卡通彩虹';
Flower340.description = '花字340 - 活泼可爱的卡通彩虹';
Flower340.key = 'Flower340'; 
Flower340.mainColor = '#ff9a9e';