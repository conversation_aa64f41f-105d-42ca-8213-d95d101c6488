import React, { memo } from 'react';

interface Flower335Props {
  text?: string;
  style?: React.CSSProperties;
  fillColor?: string;
  strokeColor?: string;
  backgroundColor?: string;
}

export const Flower335: any = memo(({ 
  text = "花字", 
  style = {},
  fillColor = "#6c5ce7",
  strokeColor = "#5f3dc4", 
  backgroundColor = "transparent"
}: Flower335Props) => {
  const fontSize = style.fontSize || 48;
  const fontFamily = style.fontFamily || '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif';
  const fontWeight = style.fontWeight || 'bold';
  const letterSpacing = style.letterSpacing || 'normal';
  
  const textStyle: React.CSSProperties = {
    fontSize,
    fontFamily,
    fontWeight,
    letterSpacing,
    color: fillColor,
    WebkitTextStroke: `2px ${strokeColor}`,
    textShadow: 'none',
    filter: 'none',
    WebkitFontSmoothing: 'antialiased',
    MozOsxFontSmoothing: 'grayscale',
    textRendering: 'optimizeLegibility',
    display: 'inline-block',
    backgroundColor,
    ...style
  };

  return (
    <span style={textStyle}>
      {text}
    </span>
  );
});

Flower335.showName = '暮光紫蓝';
Flower335.description = '花字335 - 神秘梦幻的暮光色';
Flower335.key = 'Flower335'; 
Flower335.mainColor = '#6c5ce7';