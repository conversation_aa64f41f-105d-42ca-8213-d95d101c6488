import React, { memo } from 'react';

interface Flower378Props {
  text?: string;
  style?: React.CSSProperties;
  fillColor?: string;
  strokeColor?: string;
  backgroundColor?: string;
}

export const Flower378: any = memo(({ 
  text = "花字", 
  style = {},
  fillColor = "#8B4513",
  strokeColor = "#800000", 
  backgroundColor = "transparent"
}: Flower378Props) => {
  const fontSize = style.fontSize || 48;
  const fontFamily = style.fontFamily || '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif';
  const fontWeight = style.fontWeight || 'bold';
  const letterSpacing = style.letterSpacing || 'normal';
  
  const textStyle: React.CSSProperties = {
    fontSize,
    fontFamily,
    fontWeight,
    letterSpacing,
    background: `radial-gradient(circle at 20% 30%, #B22222, transparent), radial-gradient(circle at 70% 60%, #CD853F, transparent), radial-gradient(circle at 40% 80%, ${fillColor}, transparent), #A0522D`,
    WebkitBackgroundClip: 'text',
    WebkitTextFillColor: 'transparent',
    backgroundClip: 'text',
    WebkitTextStroke: `2px ${strokeColor}`,
    textShadow: '2px 2px 4px rgba(178, 34, 34, 0.4)',
    filter: 'none',
    WebkitFontSmoothing: 'antialiased',
    MozOsxFontSmoothing: 'grayscale',
    textRendering: 'optimizeLegibility',
    display: 'inline-block',
    backgroundColor,
    backgroundSize: 'cover',
    backgroundPosition: 'center',
    ...style
  };

  return (
    <span style={textStyle}>
      {text}
    </span>
  );
});

Flower378.showName = '铁锈斑驳';
Flower378.description = '花字378 - 古旧铁锈斑驳效果';
Flower378.key = 'Flower378'; 
Flower378.mainColor = '#8B4513';