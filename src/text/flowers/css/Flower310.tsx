import React, { memo } from 'react';

interface Flower310Props {
  text?: string;
  style?: React.CSSProperties;
  fillColor?: string;
  strokeColor?: string;
  backgroundColor?: string;
}

export const Flower310: any = memo(({ 
  text = "花字", 
  style = {},
  fillColor = "#00ffff",
  strokeColor = "#008b8b", 
  backgroundColor = "transparent"
}: Flower310Props) => {
  const fontSize = style.fontSize || 48;
  const fontFamily = style.fontFamily || '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif';
  const fontWeight = style.fontWeight || 'bold';
  const letterSpacing = style.letterSpacing || 'normal';
  
  const textStyle: React.CSSProperties = {
    fontSize,
    fontFamily,
    fontWeight,
    letterSpacing,
    color: fillColor,
    textShadow: `
      0 0 5px ${fillColor},
      0 0 10px ${fillColor},
      0 0 20px ${strokeColor},
      0 0 40px ${strokeColor}
    `,
    filter: `drop-shadow(0 0 15px ${fillColor}CC)`,
    position: 'relative',
    WebkitFontSmoothing: 'antialiased',
    MozOsxFontSmoothing: 'grayscale',
    textRendering: 'optimizeLegibility',
    display: 'inline-block',
    backgroundColor,
    ...style
  };

  const glowStyle: React.CSSProperties = {
    position: 'absolute',
    top: '-5px',
    left: '-5px',
    right: '-5px',
    bottom: '-5px',
    background: `linear-gradient(45deg, transparent, ${fillColor}, transparent)`,
    zIndex: -1,
    filter: 'blur(10px)',
    opacity: 0.3,
    borderRadius: '50%'
  };

  return (
    <span style={textStyle}>
      <span style={glowStyle}></span>
      {text}
    </span>
  );
});

Flower310.showName = '科技青色';
Flower310.description = '花字310 - 未来感科技青色光效';
Flower310.key = 'Flower310'; 
Flower310.mainColor = '#00ffff';