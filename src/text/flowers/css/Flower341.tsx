import React, { memo } from 'react';

interface Flower341Props {
  text?: string;
  style?: React.CSSProperties;
  fillColor?: string;
  strokeColor?: string;
  backgroundColor?: string;
}

export const Flower341: any = memo(({ 
  text = "花字", 
  style = {},
  fillColor = "#74b9ff",
  strokeColor = "#0984e3", 
  backgroundColor = "transparent"
}: Flower341Props) => {
  const fontSize = style.fontSize || 48;
  const fontFamily = style.fontFamily || '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif';
  const fontWeight = style.fontWeight || 'bold';
  const letterSpacing = style.letterSpacing || 'normal';
  
  const textStyle: React.CSSProperties = {
    fontSize,
    fontFamily,
    fontWeight,
    letterSpacing,
    color: fillColor,
    WebkitTextStroke: `2px ${strokeColor}`,
    textShadow: `-4px 0px 0 #00cec9, -3px -2px 0 #00cec9, -3px -1px 0 #00cec9, -3px 0px 0 #00cec9, -3px 1px 0 #00cec9, -3px 2px 0 #00cec9, -2px -3px 0 #00cec9, -2px -2px 0 #00cec9, -2px -1px 0 #00cec9, -2px 0px 0 #00cec9, -2px 1px 0 #00cec9, -2px 2px 0 #00cec9, -2px 3px 0 #00cec9, -1px -3px 0 #00cec9, -1px -2px 0 #00cec9, -1px -1px 0 #00cec9, -1px 0px 0 #00cec9, -1px 1px 0 #00cec9, -1px 2px 0 #00cec9, -1px 3px 0 #00cec9, 0px -4px 0 #00cec9, 0px -3px 0 #00cec9, 0px -2px 0 #00cec9, 0px -1px 0 #00cec9, 0px 1px 0 #00cec9, 0px 2px 0 #00cec9, 0px 3px 0 #00cec9, 0px 4px 0 #00cec9, 1px -3px 0 #00cec9, 1px -2px 0 #00cec9, 1px -1px 0 #00cec9, 1px 0px 0 #00cec9, 1px 1px 0 #00cec9, 1px 2px 0 #00cec9, 1px 3px 0 #00cec9, 2px -3px 0 #00cec9, 2px -2px 0 #00cec9, 2px -1px 0 #00cec9, 2px 0px 0 #00cec9, 2px 1px 0 #00cec9, 2px 2px 0 #00cec9, 2px 3px 0 #00cec9, 3px -2px 0 #00cec9, 3px -1px 0 #00cec9, 3px 0px 0 #00cec9, 3px 1px 0 #00cec9, 3px 2px 0 #00cec9, 4px 0px 0 #00cec9`,
    filter: 'none',
    WebkitFontSmoothing: 'antialiased',
    MozOsxFontSmoothing: 'grayscale',
    textRendering: 'optimizeLegibility',
    display: 'inline-block',
    backgroundColor,
    ...style
  };

  return (
    <span style={textStyle}>
      {text}
    </span>
  );
});

Flower341.showName = '科技光环';
Flower341.description = '花字341 - 未来科技双重光环';
Flower341.key = 'Flower341'; 
Flower341.mainColor = '#74b9ff';