import React, { memo } from 'react';

interface Flower355Props {
  text?: string;
  style?: React.CSSProperties;
  fillColor?: string;
  strokeColor?: string;
  backgroundColor?: string;
}

export const Flower355: any = memo(({ 
  text = "花字", 
  style = {},
  fillColor = "#6c5ce7",
  strokeColor = "#5f3dc4", 
  backgroundColor = "transparent"
}: Flower355Props) => {
  const fontSize = style.fontSize || 48;
  const fontFamily = style.fontFamily || '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif';
  const fontWeight = style.fontWeight || 'bold';
  const letterSpacing = style.letterSpacing || 'normal';
  
  const textStyle: React.CSSProperties = {
    fontSize,
    fontFamily,
    fontWeight,
    letterSpacing,
    color: fillColor,
    WebkitTextStroke: `2px ${strokeColor}`,
    textShadow: `-4px 0px 0 #a29bfe, -3px -2px 0 #a29bfe, -3px -1px 0 #a29bfe, -3px 0px 0 #a29bfe, -3px 1px 0 #a29bfe, -3px 2px 0 #a29bfe, -2px -3px 0 #a29bfe, -2px -2px 0 #a29bfe, -2px -1px 0 #a29bfe, -2px 0px 0 #a29bfe, -2px 1px 0 #a29bfe, -2px 2px 0 #a29bfe, -2px 3px 0 #a29bfe, -1px -3px 0 #a29bfe, -1px -2px 0 #a29bfe, -1px -1px 0 #a29bfe, -1px 0px 0 #a29bfe, -1px 1px 0 #a29bfe, -1px 2px 0 #a29bfe, -1px 3px 0 #a29bfe, 0px -4px 0 #a29bfe, 0px -3px 0 #a29bfe, 0px -2px 0 #a29bfe, 0px -1px 0 #a29bfe, 0px 1px 0 #a29bfe, 0px 2px 0 #a29bfe, 0px 3px 0 #a29bfe, 0px 4px 0 #a29bfe, 1px -3px 0 #a29bfe, 1px -2px 0 #a29bfe, 1px -1px 0 #a29bfe, 1px 0px 0 #a29bfe, 1px 1px 0 #a29bfe, 1px 2px 0 #a29bfe, 1px 3px 0 #a29bfe, 2px -3px 0 #a29bfe, 2px -2px 0 #a29bfe, 2px -1px 0 #a29bfe, 2px 0px 0 #a29bfe, 2px 1px 0 #a29bfe, 2px 2px 0 #a29bfe, 2px 3px 0 #a29bfe, 3px -2px 0 #a29bfe, 3px -1px 0 #a29bfe, 3px 0px 0 #a29bfe, 3px 1px 0 #a29bfe, 3px 2px 0 #a29bfe, 4px 0px 0 #a29bfe`,
    filter: 'none',
    WebkitFontSmoothing: 'antialiased',
    MozOsxFontSmoothing: 'grayscale',
    textRendering: 'optimizeLegibility',
    display: 'inline-block',
    backgroundColor,
    ...style
  };

  return (
    <span style={textStyle}>
      {text}
    </span>
  );
});

Flower355.showName = '暮光双影';
Flower355.description = '花字355 - 神秘暮光的双影效果';
Flower355.key = 'Flower355'; 
Flower355.mainColor = '#6c5ce7';