import React, { memo } from 'react';

interface Flower320Props {
  text?: string;
  style?: React.CSSProperties;
  fillColor?: string;
  strokeColor?: string;
  backgroundColor?: string;
}

export const Flower320: any = memo(({ 
  text = "花字", 
  style = {},
  fillColor = "#ff9a9e",
  strokeColor = "#ff6b6b", 
  backgroundColor = "transparent"
}: Flower320Props) => {
  const fontSize = style.fontSize || 48;
  const fontFamily = style.fontFamily || '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif';
  const fontWeight = style.fontWeight || 'bold';
  const letterSpacing = style.letterSpacing || 'normal';
  
  const textStyle: React.CSSProperties = {
    fontSize,
    fontFamily,
    fontWeight,
    letterSpacing,
    color: fillColor,
    WebkitTextStroke: `2px ${strokeColor}`,
    textShadow: 'none',
    filter: 'none',
    WebkitFontSmoothing: 'antialiased',
    MozOsxFontSmoothing: 'grayscale',
    textRendering: 'optimizeLegibility',
    display: 'inline-block',
    backgroundColor,
    ...style
  };

  return (
    <span style={textStyle}>
      {text}
    </span>
  );
});

Flower320.showName = '卡通粉橙';
Flower320.description = '花字320 - 可爱活泼的卡通风格';
Flower320.key = 'Flower320'; 
Flower320.mainColor = '#ff9a9e';
