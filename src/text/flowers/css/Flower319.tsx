import React, { memo } from 'react';

interface Flower319Props {
  text?: string;
  style?: React.CSSProperties;
  fillColor?: string;
  strokeColor?: string;
  backgroundColor?: string;
}

export const Flower319: any = memo(({ 
  text = "花字", 
  style = {},
  fillColor = "#6495ed",
  strokeColor = "#ffffff", 
  backgroundColor = "transparent"
}: Flower319Props) => {
  const fontSize = style.fontSize || 48;
  const fontFamily = style.fontFamily || '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif';
  const fontWeight = style.fontWeight || 'bold';
  const letterSpacing = style.letterSpacing || 'normal';
  
  const textStyle: React.CSSProperties = {
    fontSize,
    fontFamily,
    fontWeight,
    letterSpacing,
    background: `linear-gradient(to bottom, #87ceeb, #6a5acd, ${fillColor})`,
    WebkitBackgroundClip: 'text',
    WebkitTextFillColor: 'transparent',
    backgroundClip: 'text',
    filter: 'drop-shadow(0 0 20px rgba(255, 255, 255, 0.4))',
    WebkitFontSmoothing: 'antialiased',
    MozOsxFontSmoothing: 'grayscale',
    textRendering: 'optimizeLegibility',
    display: 'inline-block',
    backgroundColor,
    ...style
  };

  return (
    <span style={textStyle}>
      {text}
    </span>
  );
});

Flower319.showName = '蓝紫极光';
Flower319.description = '花字319 - 神秘优雅的蓝紫色调';
Flower319.key = 'Flower319'; 
Flower319.mainColor = '#6495ed';