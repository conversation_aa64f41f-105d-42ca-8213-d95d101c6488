import React, { memo } from 'react';

interface Flower315Props {
  text?: string;
  style?: React.CSSProperties;
  fillColor?: string;
  strokeColor?: string;
  backgroundColor?: string;
}

export const Flower315: any = memo(({ 
  text = "花字", 
  style = {},
  fillColor = "#228b22",
  strokeColor = "#006400", 
  backgroundColor = "transparent"
}: Flower315Props) => {
  const fontSize = style.fontSize || 48;
  const fontFamily = style.fontFamily || '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif';
  const fontWeight = style.fontWeight || 'bold';
  const letterSpacing = style.letterSpacing || 'normal';
  
  const textStyle: React.CSSProperties = {
    fontSize,
    fontFamily,
    fontWeight,
    letterSpacing,
    background: `linear-gradient(to bottom, #90ee90, #32cd32, ${fillColor})`,WebkitBackgroundClip: 'text',
    WebkitTextFillColor: 'transparent',
    backgroundClip: 'text',filter: `drop-shadow(0 0 16px ${fillColor}CC)`,
    WebkitFontSmoothing: 'antialiased',
    MozOsxFontSmoothing: 'grayscale',
    textRendering: 'optimizeLegibility',
    display: 'inline-block',
    backgroundColor,
    ...style
  };return (
    <span style={textStyle}>
      {text}
    </span>
  );
});

Flower315.showName = '森林绿色';
Flower315.description = '花字315 - 生机盎然的森林绿';
Flower315.key = 'Flower315'; 
Flower315.mainColor = '#228b22';