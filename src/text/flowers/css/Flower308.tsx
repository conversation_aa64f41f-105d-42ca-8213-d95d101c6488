import React, { memo } from 'react';

interface Flower308Props {
  text?: string;
  style?: React.CSSProperties;
  fillColor?: string;
  strokeColor?: string;
  backgroundColor?: string;
}

export const Flower308: any = memo(({ 
  text = "花字", 
  style = {},
  fillColor = "#ff6b35",
  strokeColor = "#ffffff", 
  backgroundColor = "transparent"
}: Flower308Props) => {
  const fontSize = style.fontSize || 48;
  const fontFamily = style.fontFamily || '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif';
  const fontWeight = style.fontWeight || 'bold';
  const letterSpacing = style.letterSpacing || 'normal';
  
  const textStyle: React.CSSProperties = {
    fontSize,
    fontFamily,
    fontWeight,
    letterSpacing,
    background: `linear-gradient(to bottom, #ff6b6b, #ff8e53, ${fillColor})`,
    WebkitBackgroundClip: 'text',
    WebkitTextFillColor: 'transparent',
    backgroundClip: 'text',
    filter: 'drop-shadow(0 0 15px rgba(255, 255, 255, 0.3))',
    WebkitFontSmoothing: 'antialiased',
    MozOsxFontSmoothing: 'grayscale',
    textRendering: 'optimizeLegibility',
    display: 'inline-block',
    backgroundColor,
    ...style
  };

  return (
    <span style={textStyle}>
      {text}
    </span>
  );
});

Flower308.showName = '温暖渐变';
Flower308.description = '花字308 - 温暖舒适的橙红色调';
Flower308.key = 'Flower308'; 
Flower308.mainColor = '#ff6b35'