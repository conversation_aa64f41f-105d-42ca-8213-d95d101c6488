import React, { memo } from 'react';

interface Flower304Props {
  text?: string;
  style?: React.CSSProperties;
  fillColor?: string;
  strokeColor?: string;
  backgroundColor?: string;
}

export const Flower304: any = memo(({ 
  text = "花字", 
  style = {},
  fillColor = "#ff4500",
  strokeColor = "#ff0000", 
  backgroundColor = "transparent"
}: Flower304Props) => {
  const fontSize = style.fontSize || 48;
  const fontFamily = style.fontFamily || '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif';
  const fontWeight = style.fontWeight || 'bold';
  const letterSpacing = style.letterSpacing || 'normal';
  
  const textStyle: React.CSSProperties = {
    fontSize,
    fontFamily,
    fontWeight,
    letterSpacing,
    background: `linear-gradient(to bottom, #ffa500, #ff6347, ${fillColor})`,WebkitBackgroundClip: 'text',
    WebkitTextFillColor: 'transparent',
    backgroundClip: 'text',filter: `drop-shadow(0 0 20px ${fillColor}CC)`,
    WebkitFontSmoothing: 'antialiased',
    MozOsxFontSmoothing: 'grayscale',
    textRendering: 'optimizeLegibility',
    display: 'inline-block',
    backgroundColor,
    ...style
  };return (
    <span style={textStyle}>
      {text}
    </span>
  );
});

Flower304.showName = '火焰红橙渐变';
Flower304.description = '花字304 - 热情奔放的火焰色彩';
Flower304.key = 'Flower304'; 
Flower304.mainColor = '#ff4500';