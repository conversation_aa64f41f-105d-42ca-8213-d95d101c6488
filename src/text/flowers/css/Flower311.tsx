import React, { memo } from 'react';

interface Flower311Props {
  text?: string;
  style?: React.CSSProperties;
  fillColor?: string;
  strokeColor?: string;
  backgroundColor?: string;
}

export const Flower311: any = memo(({ 
  text = "花字", 
  style = {},
  fillColor = "#ff6b6b",
  strokeColor = "#ff69b4", 
  backgroundColor = "transparent"
}: Flower311Props) => {
  const fontSize = style.fontSize || 48;
  const fontFamily = style.fontFamily || '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif';
  const fontWeight = style.fontWeight || 'bold';
  const letterSpacing = style.letterSpacing || 'normal';
  
  const textStyle: React.CSSProperties = {
    fontSize,
    fontFamily,
    fontWeight,
    letterSpacing,
    background: `linear-gradient(to bottom, #ffb347, #ff8c69, ${fillColor})`,WebkitBackgroundClip: 'text',
    WebkitTextFillColor: 'transparent',
    backgroundClip: 'text',filter: `drop-shadow(0 0 18px ${'#ff6b35'}CC)`,
    WebkitFontSmoothing: 'antialiased',
    MozOsxFontSmoothing: 'grayscale',
    textRendering: 'optimizeLegibility',
    display: 'inline-block',
    backgroundColor,
    ...style
  };return (
    <span style={textStyle}>
      {text}
    </span>
  );
});

Flower311.showName = '夕阳橙粉';
Flower311.description = '花字311 - 温暖浪漫的夕阳色彩';
Flower311.key = 'Flower311'; 
Flower311.mainColor = '#ff6b6b';