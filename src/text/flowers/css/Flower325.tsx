import React, { memo } from 'react';

interface Flower325Props {
  text?: string;
  style?: React.CSSProperties;
  fillColor?: string;
  strokeColor?: string;
  backgroundColor?: string;
}

export const Flower325: any = memo(({ 
  text = "花字", 
  style = {},
  fillColor = "#6c5ce7",
  strokeColor = "#5f3dc4", 
  backgroundColor = "transparent"
}: Flower325Props) => {
  const fontSize = style.fontSize || 48;
  const fontFamily = style.fontFamily || '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif';
  const fontWeight = style.fontWeight || 'bold';
  const letterSpacing = style.letterSpacing || 'normal';
  
  const textStyle: React.CSSProperties = {
    fontSize,
    fontFamily,
    fontWeight,
    letterSpacing,
    color: fillColor,
    WebkitTextStroke: `2px ${strokeColor}`,
    textShadow: 'none',
    filter: 'none',
    WebkitFontSmoothing: 'antialiased',
    MozOsxFontSmoothing: 'grayscale',
    textRendering: 'optimizeLegibility',
    display: 'inline-block',
    backgroundColor,
    ...style
  };

  return (
    <span style={textStyle}>
      {text}
    </span>
  );
});

Flower325.showName = '海洋青蓝';
Flower325.description = '花字325 - 深邃宁静的海洋蓝';
Flower325.key = 'Flower325'; 
Flower325.mainColor = '#6c5ce7';
