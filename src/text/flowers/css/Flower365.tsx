import React, { memo } from 'react';

interface Flower365Props {
  text?: string;
  style?: React.CSSProperties;
  fillColor?: string;
  strokeColor?: string;
  backgroundColor?: string;
}

export const Flower365: any = memo(({ 
  text = "花字", 
  style = {},
  fillColor = "transparent",
  strokeColor = "#333333", 
  backgroundColor = "transparent"
}: Flower365Props) => {
  const fontSize = style.fontSize || 48;
  const fontFamily = style.fontFamily || '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif';
  const fontWeight = style.fontWeight || 'bold';
  const letterSpacing = style.letterSpacing || 'normal';
  
  const textStyle: React.CSSProperties = {
    fontSize,
    fontFamily,
    fontWeight,
    letterSpacing,
    background: `repeating-linear-gradient(45deg, #FF0000 0px, #FF7F00 10px, #FFFF00 20px, #00FF00 30px, #0000FF 40px, #4B0082 50px, #9400D3 60px)`,
    WebkitBackgroundClip: 'text',
    WebkitTextFillColor: 'transparent',
    backgroundClip: 'text',
    WebkitTextStroke: `2px ${strokeColor}`,
    textShadow: '2px 2px 4px rgba(0, 0, 0, 0.2)',
    filter: 'none',
    WebkitFontSmoothing: 'antialiased',
    MozOsxFontSmoothing: 'grayscale',
    textRendering: 'optimizeLegibility',
    display: 'inline-block',
    backgroundColor,
    backgroundSize: 'cover',
    backgroundPosition: 'center',
    ...style
  };

  return (
    <span style={textStyle}>
      {text}
    </span>
  );
});

Flower365.showName = '彩虹条纹';
Flower365.description = '花字365 - 活力彩虹条纹图案';
Flower365.key = 'Flower365'; 
