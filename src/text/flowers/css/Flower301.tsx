import React, { memo } from 'react';

interface Flower301Props {
  text?: string;
  style?: React.CSSProperties;
  fillColor?: string;
  strokeColor?: string;
  backgroundColor?: string;
}

export const Flower301: any = memo(({ 
  text = "花字", 
  style = {},
  fillColor = "#8b4513",
  strokeColor = "#8b4513", 
  backgroundColor = "transparent"
}: Flower301Props) => {
  const fontSize = style.fontSize || 48;
  const fontFamily = style.fontFamily || '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif';
  const fontWeight = style.fontWeight || 'bold';
  const letterSpacing = style.letterSpacing || 'normal';
  
  const textStyle: React.CSSProperties = {
    fontSize,
    fontFamily,
    fontWeight,
    letterSpacing,
    background: `linear-gradient(to bottom, #cd853f, #b87333, ${fillColor})`,WebkitBackgroundClip: 'text',
    WebkitTextFillColor: 'transparent',
    backgroundClip: 'text',
    filter: 'drop-shadow(2px 2px 4px rgba(139, 69, 19, 0.6))',
    WebkitFontSmoothing: 'antialiased',
    MozOsxFontSmoothing: 'grayscale',
    textRendering: 'optimizeLegibility',
    display: 'inline-block',
    position: 'relative',
    backgroundColor,
    ...style
  };

  const shadowStyle: React.CSSProperties = {
    position: 'absolute',
    top: '2px',
    left: '2px',
    zIndex: -1,
    background: `linear-gradient(to bottom, #cd853f, #b87333, ${fillColor})`,
    WebkitBackgroundClip: 'text',
    WebkitTextFillColor: 'transparent',
    backgroundClip: 'text',
    fontSize,
    fontFamily,
    fontWeight,
    letterSpacing
  };

  return (
    <span style={textStyle}>
      <span style={shadowStyle}>
        {text}
      </span>
      {text}
    </span>
  );
});

Flower301.showName = '金属铜色质感';
Flower301.description = '花字301 - 古典豪华的金属铜色';
Flower301.key = 'Flower301'; 
Flower301.mainColor = '#8b4513';