import React, { memo } from 'react';

interface Flower372Props {
  text?: string;
  style?: React.CSSProperties;
  fillColor?: string;
  strokeColor?: string;
  backgroundColor?: string;
}

export const Flower372: any = memo(({ 
  text = "花字", 
  style = {},
  fillColor = "rgba(255, 20, 147, 0.8)",
  strokeColor = "#FF1493", 
  backgroundColor = "transparent"
}: Flower372Props) => {
  const fontSize = style.fontSize || 48;
  const fontFamily = style.fontFamily || '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif';
  const fontWeight = style.fontWeight || 'bold';
  const letterSpacing = style.letterSpacing || 'normal';
  
  const textStyle: React.CSSProperties = {
    fontSize,
    fontFamily,
    fontWeight,
    letterSpacing,
    background: `linear-gradient(45deg, ${fillColor}, rgba(255, 105, 180, 0.8), rgba(255, 182, 193, 0.8))`,
    WebkitBackgroundClip: 'text',
    WebkitTextFillColor: 'transparent',
    backgroundClip: 'text',
    WebkitTextStroke: `2px ${strokeColor}`,
    textShadow: '2px 2px 8px rgba(255, 20, 147, 0.4), inset 0 0 10px rgba(255, 255, 255, 0.3)',
    filter: 'none',
    WebkitFontSmoothing: 'antialiased',
    MozOsxFontSmoothing: 'grayscale',
    textRendering: 'optimizeLegibility',
    display: 'inline-block',
    backgroundColor,
    backgroundSize: 'cover',
    backgroundPosition: 'center',
    ...style
  };

  return (
    <span style={textStyle}>
      {text}
    </span>
  );
});

Flower372.showName = '果冻质感';
Flower372.description = '花字372 - 半透明果冻质感';
Flower372.key = 'Flower372'; 
Flower372.mainColor = 'rgba(255, 20, 147, 0.8)';