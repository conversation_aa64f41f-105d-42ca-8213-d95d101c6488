import React, { memo } from 'react';

interface Flower309Props {
  text?: string;
  style?: React.CSSProperties;
  fillColor?: string;
  strokeColor?: string;
  backgroundColor?: string;
}

export const Flower309: any = memo(({ 
  text = "花字", 
  style = {},
  fillColor = "#b0e0e6",
  strokeColor = "#4682b4", 
  backgroundColor = "transparent"
}: Flower309Props) => {
  const fontSize = style.fontSize || 48;
  const fontFamily = style.fontFamily || '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif';
  const fontWeight = style.fontWeight || 'bold';
  const letterSpacing = style.letterSpacing || 'normal';
  
  const textStyle: React.CSSProperties = {
    fontSize,
    fontFamily,
    fontWeight,
    letterSpacing,
    color: fillColor,
    textShadow: `
      0 0 3px #87ceeb,
      0 0 6px #87ceeb,
      0 0 12px ${strokeColor},
      1px 1px 0 #ffffff,
      2px 2px 0 #e0f6ff
    `,
    filter: `drop-shadow(0 0 10px ${fillColor}CC)`,WebkitFontSmoothing: 'antialiased',
    MozOsxFontSmoothing: 'grayscale',
    textRendering: 'optimizeLegibility',
    display: 'inline-block',
    backgroundColor,
    ...style
  };return (
    <span style={textStyle}>
      {text}
    </span>
  );
});

Flower309.showName = '冰霜蓝白';
Flower309.description = '花字309 - 清冷纯净的冰霜质感';
Flower309.key = 'Flower309'; 
Flower309.mainColor = '#b0e0e6';