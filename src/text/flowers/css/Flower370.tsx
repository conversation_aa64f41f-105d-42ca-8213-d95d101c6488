import React, { memo } from 'react';

interface Flower370Props {
  text?: string;
  style?: React.CSSProperties;
  fillColor?: string;
  strokeColor?: string;
  backgroundColor?: string;
}

export const Flower370: any = memo(({ 
  text = "花字", 
  style = {},
  fillColor = "#2F4F4F",
  strokeColor = "#2F4F4F", 
  backgroundColor = "transparent"
}: Flower370Props) => {
  const fontSize = style.fontSize || 48;
  const fontFamily = style.fontFamily || '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif';
  const fontWeight = style.fontWeight || 'bold';
  const letterSpacing = style.letterSpacing || 'normal';
  
  const textStyle: React.CSSProperties = {
    fontSize,
    fontFamily,
    fontWeight,
    letterSpacing,
    background: `radial-gradient(ellipse at top left, #228B22, transparent), radial-gradient(ellipse at top right, #8B4513, transparent), radial-gradient(ellipse at bottom left, ${fillColor}, transparent), radial-gradient(ellipse at bottom right, #556B2F, transparent), #6B8E23`,
    WebkitBackgroundClip: 'text',
    WebkitTextFillColor: 'transparent',
    backgroundClip: 'text',
    WebkitTextStroke: `2px ${strokeColor}`,
    textShadow: '2px 2px 4px rgba(107, 142, 35, 0.4)',
    filter: 'none',
    WebkitFontSmoothing: 'antialiased',
    MozOsxFontSmoothing: 'grayscale',
    textRendering: 'optimizeLegibility',
    display: 'inline-block',
    backgroundColor,
    backgroundSize: 'cover',
    backgroundPosition: 'center',
    ...style
  };

  return (
    <span style={textStyle}>
      {text}
    </span>
  );
});

Flower370.showName = '迷彩图案';
Flower370.description = '花字370 - 军事迷彩图案效果';
Flower370.key = 'Flower370'; 
Flower370.mainColor = '#2F4F4F';