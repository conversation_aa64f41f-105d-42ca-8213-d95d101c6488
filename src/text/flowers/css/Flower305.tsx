import React, { memo } from 'react';

interface Flower305Props {
  text?: string;
  style?: React.CSSProperties;
  fillColor?: string;
  strokeColor?: string;
  backgroundColor?: string;
}

export const Flower305: any = memo(({ 
  text = "花字", 
  style = {},
  fillColor = "#9400d3",
  strokeColor = "#8a2be2", 
  backgroundColor = "transparent"
}: Flower305Props) => {
  const fontSize = style.fontSize || 48;
  const fontFamily = style.fontFamily || '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif';
  const fontWeight = style.fontWeight || 'bold';
  const letterSpacing = style.letterSpacing || 'normal';
  
  const textStyle: React.CSSProperties = {
    fontSize,
    fontFamily,
    fontWeight,
    letterSpacing,
    color: fillColor,
    textShadow: 'none',
    filter: 'none',
    WebkitFontSmoothing: 'antialiased',
    MozOsxFontSmoothing: 'grayscale',
    textRendering: 'optimizeLegibility',
    display: 'inline-block',
    backgroundColor,
    ...style
  };

  return (
    <span style={textStyle}>
      {text}
    </span>
  );
});

Flower305.showName = '电光紫色';
Flower305.description = '花字305 - 神秘魅力的电光紫';
Flower305.key = 'Flower305'; 
Flower305.mainColor = '#9400d3';