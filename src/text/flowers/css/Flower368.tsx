import React, { memo } from 'react';

interface Flower368Props {
  text?: string;
  style?: React.CSSProperties;
  fillColor?: string;
  strokeColor?: string;
  backgroundColor?: string;
}

export const Flower368: any = memo(({ 
  text = "花字", 
  style = {},
  fillColor = "#FF4500",
  strokeColor = "#8B0000", 
  backgroundColor = "transparent"
}: Flower368Props) => {
  const fontSize = style.fontSize || 48;
  const fontFamily = style.fontFamily || '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif';
  const fontWeight = style.fontWeight || 'bold';
  const letterSpacing = style.letterSpacing || 'normal';
  
  const textStyle: React.CSSProperties = {
    fontSize,
    fontFamily,
    fontWeight,
    letterSpacing,
    background: `linear-gradient(0deg, ${fillColor}, #FF6347, #FF8C00, #FFD700, #FFFF00)`,
    WebkitBackgroundClip: 'text',
    WebkitTextFillColor: 'transparent',
    backgroundClip: 'text',
    WebkitTextStroke: `2px ${strokeColor}`,
    textShadow: '2px 2px 8px rgba(255, 69, 0, 0.6)',
    filter: 'none',
    WebkitFontSmoothing: 'antialiased',
    MozOsxFontSmoothing: 'grayscale',
    textRendering: 'optimizeLegibility',
    display: 'inline-block',
    backgroundColor,
    backgroundSize: 'cover',
    backgroundPosition: 'center',
    ...style
  };

  return (
    <span style={textStyle}>
      {text}
    </span>
  );
});

Flower368.showName = '火焰效果';
Flower368.description = '花字368 - 炽热火焰材质效果';
Flower368.key = 'Flower368'; 
Flower368.mainColor = '#FF4500';