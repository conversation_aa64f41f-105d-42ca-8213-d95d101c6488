import React, { memo } from 'react';

interface Flower306Props {
  text?: string;
  style?: React.CSSProperties;
  fillColor?: string;
  strokeColor?: string;
  backgroundColor?: string;
}

export const Flower306: any = memo(({ 
  text = "花字", 
  style = {},
  fillColor = "#ffd700",
  strokeColor = "#b8860b", 
  backgroundColor = "transparent"
}: Flower306Props) => {
  const fontSize = style.fontSize || 48;
  const fontFamily = style.fontFamily || '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif';
  const fontWeight = style.fontWeight || 'bold';
  const letterSpacing = style.letterSpacing || 'normal';
  
  const textStyle: React.CSSProperties = {
    fontSize,
    fontFamily,
    fontWeight,
    letterSpacing,
    background: `linear-gradient(to bottom, #ffed4e, ${fillColor}, #b8860b)`,WebkitBackgroundClip: 'text',
    WebkitTextFillColor: 'transparent',
    backgroundClip: 'text',filter: 'drop-shadow(2px 2px 8px rgba(255, 215, 0, 0.6))',
    WebkitFontSmoothing: 'antialiased',
    MozOsxFontSmoothing: 'grayscale',
    textRendering: 'optimizeLegibility',
    display: 'inline-block',
    position: 'relative',
    backgroundColor,
    ...style
  };return (
    <span style={textStyle}>
      {text}
    </span>
  );
});

Flower306.showName = '黄金豪华质感';
Flower306.description = '花字306 - 奢华典雅的黄金效果';
Flower306.key = 'Flower306'; 
Flower306.mainColor = '#ffd700';