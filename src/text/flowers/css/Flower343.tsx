import React, { memo } from 'react';

interface Flower343Props {
  text?: string;
  style?: React.CSSProperties;
  fillColor?: string;
  strokeColor?: string;
  backgroundColor?: string;
}

export const Flower343: any = memo(({ 
  text = "花字", 
  style = {},
  fillColor = "#fdcb6e",
  strokeColor = "#e17055", 
  backgroundColor = "transparent"
}: Flower343Props) => {
  const fontSize = style.fontSize || 48;
  const fontFamily = style.fontFamily || '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif';
  const fontWeight = style.fontWeight || 'bold';
  const letterSpacing = style.letterSpacing || 'normal';
  
  const textStyle: React.CSSProperties = {
    fontSize,
    fontFamily,
    fontWeight,
    letterSpacing,
    color: fillColor,
    WebkitTextStroke: `2px ${strokeColor}`,
    textShadow: `-4px 0px 0 #d63031, -3px -2px 0 #d63031, -3px -1px 0 #d63031, -3px 0px 0 #d63031, -3px 1px 0 #d63031, -3px 2px 0 #d63031, -2px -3px 0 #d63031, -2px -2px 0 #d63031, -2px -1px 0 #d63031, -2px 0px 0 #d63031, -2px 1px 0 #d63031, -2px 2px 0 #d63031, -2px 3px 0 #d63031, -1px -3px 0 #d63031, -1px -2px 0 #d63031, -1px -1px 0 #d63031, -1px 0px 0 #d63031, -1px 1px 0 #d63031, -1px 2px 0 #d63031, -1px 3px 0 #d63031, 0px -4px 0 #d63031, 0px -3px 0 #d63031, 0px -2px 0 #d63031, 0px -1px 0 #d63031, 0px 1px 0 #d63031, 0px 2px 0 #d63031, 0px 3px 0 #d63031, 0px 4px 0 #d63031, 1px -3px 0 #d63031, 1px -2px 0 #d63031, 1px -1px 0 #d63031, 1px 0px 0 #d63031, 1px 1px 0 #d63031, 1px 2px 0 #d63031, 1px 3px 0 #d63031, 2px -3px 0 #d63031, 2px -2px 0 #d63031, 2px -1px 0 #d63031, 2px 0px 0 #d63031, 2px 1px 0 #d63031, 2px 2px 0 #d63031, 2px 3px 0 #d63031, 3px -2px 0 #d63031, 3px -1px 0 #d63031, 3px 0px 0 #d63031, 3px 1px 0 #d63031, 3px 2px 0 #d63031, 4px 0px 0 #d63031`,
    filter: 'none',
    WebkitFontSmoothing: 'antialiased',
    MozOsxFontSmoothing: 'grayscale',
    textRendering: 'optimizeLegibility',
    display: 'inline-block',
    backgroundColor,
    ...style
  };

  return (
    <span style={textStyle}>
      {text}
    </span>
  );
});

Flower343.showName = '阳光双圈';
Flower343.description = '花字343 - 温暖明亮的双圈效果';
Flower343.key = 'Flower343'; 
Flower343.mainColor = '#fdcb6e';