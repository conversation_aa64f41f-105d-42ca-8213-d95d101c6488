import React, { memo } from 'react';

interface Flower300Props {
  text?: string;
  style?: React.CSSProperties;
  fillColor?: string;
  strokeColor?: string;
  backgroundColor?: string;
}

export const Flower300: any = memo(({ 
  text = "花字", 
  style = {},
  fillColor = "#00f5ff",
  strokeColor = "#0080ff", 
  backgroundColor = "transparent"
}: Flower300Props) => {
  const fontSize = style.fontSize || 48;
  const fontFamily = style.fontFamily || '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif';
  const fontWeight = style.fontWeight || 'bold';
  const letterSpacing = style.letterSpacing || 'normal';
  
  const textStyle: React.CSSProperties = {
    fontSize,
    fontFamily,
    fontWeight,
    letterSpacing,
    color: fillColor,
    textShadow: 'none',
    filter: 'none',
    WebkitFontSmoothing: 'antialiased',
    MozOsxFontSmoothing: 'grayscale',
    textRendering: 'optimizeLegibility',
    display: 'inline-block',
    backgroundColor,
    ...style
  };

  return (
    <span style={textStyle}>
      {text}
    </span>
  );
});

Flower300.showName = '霓虹蓝色发光';
Flower300.description = '花字300 - 科幻感十足的蓝色霓虹效果';
Flower300.key = 'Flower300';
Flower300.mainColor = '#00f5ff'; // 组件的主色定义 