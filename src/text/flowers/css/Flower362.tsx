import React, { memo } from 'react';

interface Flower362Props {
  text?: string;
  style?: React.CSSProperties;
  fillColor?: string;
  strokeColor?: string;
  backgroundColor?: string;
}

export const Flower362: any = memo(({ 
  text = "花字", 
  style = {},
  fillColor = "#B87333",
  strokeColor = "#8B4513", 
  backgroundColor = "transparent"
}: Flower362Props) => {
  const fontSize = style.fontSize || 48;
  const fontFamily = style.fontFamily || '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif';
  const fontWeight = style.fontWeight || 'bold';
  const letterSpacing = style.letterSpacing || 'normal';
  
  const textStyle: React.CSSProperties = {
    fontSize,
    fontFamily,
    fontWeight,
    letterSpacing,
    background: `linear-gradient(45deg, ${fillColor}, #CD853F, #D2691E, ${fillColor})`,
    WebkitBackgroundClip: 'text',
    WebkitTextFillColor: 'transparent',
    backgroundClip: 'text',
    WebkitTextStroke: `2px ${strokeColor}`,
    textShadow: '2px 2px 4px rgba(184, 115, 51, 0.3)',
    filter: 'none',
    WebkitFontSmoothing: 'antialiased',
    MozOsxFontSmoothing: 'grayscale',
    textRendering: 'optimizeLegibility',
    display: 'inline-block',
    backgroundColor,
    backgroundSize: 'cover',
    backgroundPosition: 'center',
    ...style
  };

  return (
    <span style={textStyle}>
      {text}
    </span>
  );
});

Flower362.showName = '铜色古典';
Flower362.description = '花字362 - 复古铜色金属效果';
Flower362.key = 'Flower362'; 
Flower362.mainColor = '#B87333';