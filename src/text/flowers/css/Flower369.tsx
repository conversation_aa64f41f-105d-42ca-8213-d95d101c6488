import React, { memo } from 'react';

interface Flower369Props {
  text?: string;
  style?: React.CSSProperties;
  fillColor?: string;
  strokeColor?: string;
  backgroundColor?: string;
}

export const Flower369: any = memo(({ 
  text = "花字", 
  style = {},
  fillColor = "#4682B4",
  strokeColor = "#4682B4", 
  backgroundColor = "transparent"
}: Flower369Props) => {
  const fontSize = style.fontSize || 48;
  const fontFamily = style.fontFamily || '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif';
  const fontWeight = style.fontWeight || 'bold';
  const letterSpacing = style.letterSpacing || 'normal';
  
  const textStyle: React.CSSProperties = {
    fontSize,
    fontFamily,
    fontWeight,
    letterSpacing,
    background: `linear-gradient(45deg, #87CEEB 25%, transparent 25%), linear-gradient(-45deg, #87CEEB 25%, transparent 25%), linear-gradient(45deg, transparent 75%, ${fillColor} 75%), linear-gradient(-45deg, transparent 75%, ${fillColor} 75%), linear-gradient(to bottom, #B0E0E6, #87CEEB)`,
    WebkitBackgroundClip: 'text',
    WebkitTextFillColor: 'transparent',
    backgroundClip: 'text',
    WebkitTextStroke: `2px ${strokeColor}`,
    textShadow: '2px 2px 4px rgba(135, 206, 235, 0.3)',
    filter: 'none',
    WebkitFontSmoothing: 'antialiased',
    MozOsxFontSmoothing: 'grayscale',
    textRendering: 'optimizeLegibility',
    display: 'inline-block',
    backgroundColor,
    backgroundSize: 'cover',
    backgroundPosition: 'center',
    ...style
  };

  return (
    <span style={textStyle}>
      {text}
    </span>
  );
});

Flower369.showName = '水波纹理';
Flower369.description = '花字369 - 清澈水波纹理效果';
Flower369.key = 'Flower369'; 
Flower369.mainColor = '#4682B4';