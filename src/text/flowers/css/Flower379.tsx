import React, { memo } from 'react';

interface Flower379Props {
  text?: string;
  style?: React.CSSProperties;
  fillColor?: string;
  strokeColor?: string;
  backgroundColor?: string;
}

export const Flower379: any = memo(({ 
  text = "花字", 
  style = {},
  fillColor = "#FF6B6B",
  strokeColor = "#4B0082", 
  backgroundColor = "transparent"
}: Flower379Props) => {
  const fontSize = style.fontSize || 48;
  const fontFamily = style.fontFamily || '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif';
  const fontWeight = style.fontWeight || 'bold';
  const letterSpacing = style.letterSpacing || 'normal';
  
  const textStyle: React.CSSProperties = {
    fontSize,
    fontFamily,
    fontWeight,
    letterSpacing,
    background: `conic-gradient(from 45deg, ${fillColor}, #4ECDC4, #45B7D1, #96CEB4, #FFEAA7, #DDA0DD, ${fillColor})`,
    WebkitBackgroundClip: 'text',
    WebkitTextFillColor: 'transparent',
    backgroundClip: 'text',
    WebkitTextStroke: `2px ${strokeColor}`,
    textShadow: '2px 2px 8px rgba(0, 0, 0, 0.3), 0 0 15px rgba(255, 255, 255, 0.4)',
    filter: 'none',
    WebkitFontSmoothing: 'antialiased',
    MozOsxFontSmoothing: 'grayscale',
    textRendering: 'optimizeLegibility',
    display: 'inline-block',
    backgroundColor,
    backgroundSize: 'cover',
    backgroundPosition: 'center',
    ...style
  };

  return (
    <span style={textStyle}>
      {text}
    </span>
  );
});

Flower379.showName = '宝石光泽';
Flower379.description = '花字379 - 多彩宝石光泽效果';
Flower379.key = 'Flower379'; 
Flower379.mainColor = '#FF6B6B';