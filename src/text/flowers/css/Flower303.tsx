import React, { memo } from 'react';

interface Flower303Props {
  text?: string;
  style?: React.CSSProperties;
  fillColor?: string;
  strokeColor?: string;
  backgroundColor?: string;
}

export const Flower303: any = memo(({ 
  text = "花字", 
  style = {},
  fillColor = "rgba(255, 255, 255, 0.9)",
  strokeColor = "rgba(255, 255, 255, 0.2)", 
  backgroundColor = "rgba(255, 255, 255, 0.1)"
}: Flower303Props) => {
  const fontSize = style.fontSize || 48;
  const fontFamily = style.fontFamily || '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif';
  const fontWeight = style.fontWeight || 'bold';
  const letterSpacing = style.letterSpacing || 'normal';
  
  const textStyle: React.CSSProperties = {
    fontSize,
    fontFamily,
    fontWeight,
    letterSpacing,
    color: fillColor,
    backdropFilter: 'blur(10px)',
    WebkitBackdropFilter: 'blur(10px)',
    textShadow: `
      0 1px 0 rgba(255, 255, 255, 0.8),
      0 2px 4px rgba(0, 0, 0, 0.3)
    `,
    border: `1px solid ${strokeColor}`,
    borderRadius: '12px',
    padding: '0.3em 0.6em',
    background: backgroundColor,
    boxShadow: `
      0 8px 32px rgba(0, 0, 0, 0.1),
      inset 0 1px 0 rgba(255, 255, 255, 0.2)
    `,
    WebkitFontSmoothing: 'antialiased',
    MozOsxFontSmoothing: 'grayscale',
    textRendering: 'optimizeLegibility',
    display: 'inline-block',
    ...style
  };

  return (
    <span style={textStyle}>
      {text}
    </span>
  );
});

Flower303.showName = '磨砂玻璃效果';
Flower303.description = '花字303 - 现代简约的磨砂玻璃质感';
Flower303.key = 'Flower303'; 
Flower303.mainColor = 'rgba(255, 255, 255, 0.9)';