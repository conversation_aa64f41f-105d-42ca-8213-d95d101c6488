import React, { memo } from 'react';

interface Flower377Props {
  text?: string;
  style?: React.CSSProperties;
  fillColor?: string;
  strokeColor?: string;
  backgroundColor?: string;
}

export const Flower377: any = memo(({ 
  text = "花字", 
  style = {},
  fillColor = "#F4A460",
  strokeColor = "#8B7355", 
  backgroundColor = "transparent"
}: Flower377Props) => {
  const fontSize = style.fontSize || 48;
  const fontFamily = style.fontFamily || '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif';
  const fontWeight = style.fontWeight || 'bold';
  const letterSpacing = style.letterSpacing || 'normal';
  
  const textStyle: React.CSSProperties = {
    fontSize,
    fontFamily,
    fontWeight,
    letterSpacing,
    background: `radial-gradient(ellipse 2px 1px at 2px 1px, #DEB887, transparent), radial-gradient(ellipse 2px 1px at 8px 5px, ${fillColor}, transparent), radial-gradient(ellipse 2px 1px at 15px 3px, #D2B48C, transparent), #CD853F`,
    WebkitBackgroundClip: 'text',
    WebkitTextFillColor: 'transparent',
    backgroundClip: 'text',
    WebkitTextStroke: `2px ${strokeColor}`,
    textShadow: '2px 2px 4px rgba(205, 133, 63, 0.3)',
    filter: 'none',
    WebkitFontSmoothing: 'antialiased',
    MozOsxFontSmoothing: 'grayscale',
    textRendering: 'optimizeLegibility',
    display: 'inline-block',
    backgroundColor,
    backgroundSize: 'cover',
    backgroundPosition: 'center',
    ...style
  };

  return (
    <span style={textStyle}>
      {text}
    </span>
  );
});

Flower377.showName = '毛皮质感';
Flower377.description = '花字377 - 柔软毛皮质感效果';
Flower377.key = 'Flower377'; 
Flower377.mainColor = '#F4A460';