import React, { memo } from 'react';

interface Flower316Props {
  text?: string;
  style?: React.CSSProperties;
  fillColor?: string;
  strokeColor?: string;
  backgroundColor?: string;
}

export const Flower316: any = memo(({ 
  text = "花字", 
  style = {},
  fillColor = "#dc143c",
  strokeColor = "#b22222", 
  backgroundColor = "transparent"
}: Flower316Props) => {
  const fontSize = style.fontSize || 48;
  const fontFamily = style.fontFamily || '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif';
  const fontWeight = style.fontWeight || 'bold';
  const letterSpacing = style.letterSpacing || 'normal';
  
  const textStyle: React.CSSProperties = {
    fontSize,
    fontFamily,
    fontWeight,
    letterSpacing,
    background: `linear-gradient(to bottom, #ff6347, #ff4500, ${fillColor})`,WebkitBackgroundClip: 'text',
    WebkitTextFillColor: 'transparent',
    backgroundClip: 'text',filter: `drop-shadow(0 0 20px ${fillColor}CC)`,
    WebkitFontSmoothing: 'antialiased',
    MozOsxFontSmoothing: 'grayscale',
    textRendering: 'optimizeLegibility',
    display: 'inline-block',
    backgroundColor,
    ...style
  };return (
    <span style={textStyle}>
      {text}
    </span>
  );
});

Flower316.showName = '岩浆红色';
Flower316.description = '花字316 - 炽热强烈的岩浆红';
Flower316.key = 'Flower316'; 
Flower316.mainColor = '#dc143c';