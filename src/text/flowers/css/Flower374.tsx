import React, { memo } from 'react';

interface Flower374Props {
  text?: string;
  style?: React.CSSProperties;
  fillColor?: string;
  strokeColor?: string;
  backgroundColor?: string;
}

export const Flower374: any = memo(({ 
  text = "花字", 
  style = {},
  fillColor = "#E0E0E0",
  strokeColor = "#C0C0C0", 
  backgroundColor = "transparent"
}: Flower374Props) => {
  const fontSize = style.fontSize || 48;
  const fontFamily = style.fontFamily || '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif';
  const fontWeight = style.fontWeight || 'bold';
  const letterSpacing = style.letterSpacing || 'normal';
  
  const textStyle: React.CSSProperties = {
    fontSize,
    fontFamily,
    fontWeight,
    letterSpacing,
    background: `conic-gradient(from 0deg, ${fillColor}, #FFFFFF, #F5F5F5, ${fillColor}, #FFFFFF, #F5F5F5, ${fillColor})`,
    WebkitBackgroundClip: 'text',
    WebkitTextFillColor: 'transparent',
    backgroundClip: 'text',
    WebkitTextStroke: `2px ${strokeColor}`,
    textShadow: '2px 2px 6px rgba(0, 0, 0, 0.2), 0 0 20px rgba(255, 255, 255, 0.5)',
    filter: 'none',
    WebkitFontSmoothing: 'antialiased',
    MozOsxFontSmoothing: 'grayscale',
    textRendering: 'optimizeLegibility',
    display: 'inline-block',
    backgroundColor,
    backgroundSize: 'cover',
    backgroundPosition: 'center',
    ...style
  };

  return (
    <span style={textStyle}>
      {text}
    </span>
  );
});

Flower374.showName = '钻石切面';
Flower374.description = '花字374 - 璀璨钻石切面效果';
Flower374.key = 'Flower374'; 
Flower374.mainColor = '#E0E0E0';
