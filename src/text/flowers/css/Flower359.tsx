import React, { memo } from 'react';

interface Flower359Props {
  text?: string;
  style?: React.CSSProperties;
  fillColor?: string;
  strokeColor?: string;
  backgroundColor?: string;
}

export const Flower359: any = memo(({ 
  text = "花字", 
  style = {},
  fillColor = "#74b9ff",
  strokeColor = "#0984e3", 
  backgroundColor = "transparent"
}: Flower359Props) => {
  const fontSize = style.fontSize || 48;
  const fontFamily = style.fontFamily || '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif';
  const fontWeight = style.fontWeight || 'bold';
  const letterSpacing = style.letterSpacing || 'normal';
  
  const textStyle: React.CSSProperties = {
    fontSize,
    fontFamily,
    fontWeight,
    letterSpacing,
    color: fillColor,
    WebkitTextStroke: `2px ${strokeColor}`,
    textShadow: `-4px 0px 0 #6c5ce7, -3px -2px 0 #6c5ce7, -3px -1px 0 #6c5ce7, -3px 0px 0 #6c5ce7, -3px 1px 0 #6c5ce7, -3px 2px 0 #6c5ce7, -2px -3px 0 #6c5ce7, -2px -2px 0 #6c5ce7, -2px -1px 0 #6c5ce7, -2px 0px 0 #6c5ce7, -2px 1px 0 #6c5ce7, -2px 2px 0 #6c5ce7, -2px 3px 0 #6c5ce7, -1px -3px 0 #6c5ce7, -1px -2px 0 #6c5ce7, -1px -1px 0 #6c5ce7, -1px 0px 0 #6c5ce7, -1px 1px 0 #6c5ce7, -1px 2px 0 #6c5ce7, -1px 3px 0 #6c5ce7, 0px -4px 0 #6c5ce7, 0px -3px 0 #6c5ce7, 0px -2px 0 #6c5ce7, 0px -1px 0 #6c5ce7, 0px 1px 0 #6c5ce7, 0px 2px 0 #6c5ce7, 0px 3px 0 #6c5ce7, 0px 4px 0 #6c5ce7, 1px -3px 0 #6c5ce7, 1px -2px 0 #6c5ce7, 1px -1px 0 #6c5ce7, 1px 0px 0 #6c5ce7, 1px 1px 0 #6c5ce7, 1px 2px 0 #6c5ce7, 1px 3px 0 #6c5ce7, 2px -3px 0 #6c5ce7, 2px -2px 0 #6c5ce7, 2px -1px 0 #6c5ce7, 2px 0px 0 #6c5ce7, 2px 1px 0 #6c5ce7, 2px 2px 0 #6c5ce7, 2px 3px 0 #6c5ce7, 3px -2px 0 #6c5ce7, 3px -1px 0 #6c5ce7, 3px 0px 0 #6c5ce7, 3px 1px 0 #6c5ce7, 3px 2px 0 #6c5ce7, 4px 0px 0 #6c5ce7`,
    filter: 'none',
    WebkitFontSmoothing: 'antialiased',
    MozOsxFontSmoothing: 'grayscale',
    textRendering: 'optimizeLegibility',
    display: 'inline-block',
    backgroundColor,
    ...style
  };

  return (
    <span style={textStyle}>
      {text}
    </span>
  );
});

Flower359.showName = '深海双蓝';
Flower359.description = '花字359 - 神秘深海的双蓝效果';
Flower359.key = 'Flower359'; 
Flower359.mainColor = '#74b9ff';