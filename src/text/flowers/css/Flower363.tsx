import React, { memo } from 'react';

interface Flower363Props {
  text?: string;
  style?: React.CSSProperties;
  fillColor?: string;
  strokeColor?: string;
  backgroundColor?: string;
}

export const Flower363: any = memo(({ 
  text = "花字", 
  style = {},
  fillColor = "#8B4513",
  strokeColor = "#654321", 
  backgroundColor = "transparent"
}: Flower363Props) => {
  const fontSize = style.fontSize || 48;
  const fontFamily = style.fontFamily || '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif';
  const fontWeight = style.fontWeight || 'bold';
  const letterSpacing = style.letterSpacing || 'normal';
  
  const textStyle: React.CSSProperties = {
    fontSize,
    fontFamily,
    fontWeight,
    letterSpacing,
    background: `repeating-linear-gradient(90deg, ${fillColor} 0px, #A0522D 10px, #CD853F 20px, ${fillColor} 30px)`,
    WebkitBackgroundClip: 'text',
    WebkitTextFillColor: 'transparent',
    backgroundClip: 'text',
    WebkitTextStroke: `2px ${strokeColor}`,
    textShadow: '2px 2px 4px rgba(139, 69, 19, 0.3)',
    filter: 'none',
    WebkitFontSmoothing: 'antialiased',
    MozOsxFontSmoothing: 'grayscale',
    textRendering: 'optimizeLegibility',
    display: 'inline-block',
    backgroundColor,
    backgroundSize: 'cover',
    backgroundPosition: 'center',
    ...style
  };

  return (
    <span style={textStyle}>
      {text}
    </span>
  );
});

Flower363.showName = '木纹质感';
Flower363.description = '花字363 - 自然木纹材质效果';
Flower363.key = 'Flower363'; 
Flower363.mainColor = '#8B4513';