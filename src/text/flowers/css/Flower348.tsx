import React, { memo } from 'react';

interface Flower348Props {
  text?: string;
  style?: React.CSSProperties;
  fillColor?: string;
  strokeColor?: string;
  backgroundColor?: string;
}

export const Flower348: any = memo(({ 
  text = "花字", 
  style = {},
  fillColor = "#74b9ff",
  strokeColor = "#0984e3", 
  backgroundColor = "transparent"
}: Flower348Props) => {
  const fontSize = style.fontSize || 48;
  const fontFamily = style.fontFamily || '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif';
  const fontWeight = style.fontWeight || 'bold';
  const letterSpacing = style.letterSpacing || 'normal';
  
  const textStyle: React.CSSProperties = {
    fontSize,
    fontFamily,
    fontWeight,
    letterSpacing,
    color: fillColor,
    WebkitTextStroke: `2px ${strokeColor}`,
    textShadow: `-4px 0px 0 #a29bfe, -3px -2px 0 #a29bfe, -3px -1px 0 #a29bfe, -3px 0px 0 #a29bfe, -3px 1px 0 #a29bfe, -3px 2px 0 #a29bfe, -2px -3px 0 #a29bfe, -2px -2px 0 #a29bfe, -2px -1px 0 #a29bfe, -2px 0px 0 #a29bfe, -2px 1px 0 #a29bfe, -2px 2px 0 #a29bfe, -2px 3px 0 #a29bfe, -1px -3px 0 #a29bfe, -1px -2px 0 #a29bfe, -1px -1px 0 #a29bfe, -1px 0px 0 #a29bfe, -1px 1px 0 #a29bfe, -1px 2px 0 #a29bfe, -1px 3px 0 #a29bfe, 0px -4px 0 #a29bfe, 0px -3px 0 #a29bfe, 0px -2px 0 #a29bfe, 0px -1px 0 #a29bfe, 0px 1px 0 #a29bfe, 0px 2px 0 #a29bfe, 0px 3px 0 #a29bfe, 0px 4px 0 #a29bfe, 1px -3px 0 #a29bfe, 1px -2px 0 #a29bfe, 1px -1px 0 #a29bfe, 1px 0px 0 #a29bfe, 1px 1px 0 #a29bfe, 1px 2px 0 #a29bfe, 1px 3px 0 #a29bfe, 2px -3px 0 #a29bfe, 2px -2px 0 #a29bfe, 2px -1px 0 #a29bfe, 2px 0px 0 #a29bfe, 2px 1px 0 #a29bfe, 2px 2px 0 #a29bfe, 2px 3px 0 #a29bfe, 3px -2px 0 #a29bfe, 3px -1px 0 #a29bfe, 3px 0px 0 #a29bfe, 3px 1px 0 #a29bfe, 3px 2px 0 #a29bfe, 4px 0px 0 #a29bfe`,
    filter: 'none',
    WebkitFontSmoothing: 'antialiased',
    MozOsxFontSmoothing: 'grayscale',
    textRendering: 'optimizeLegibility',
    display: 'inline-block',
    backgroundColor,
    ...style
  };

  return (
    <span style={textStyle}>
      {text}
    </span>
  );
});

Flower348.showName = '天空双翼';
Flower348.description = '花字348 - 清澈天空的双翼描边';
Flower348.key = 'Flower348'; 
Flower348.mainColor = '#74b9ff';