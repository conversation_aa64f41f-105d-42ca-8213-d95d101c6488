import React, { memo } from 'react';

interface Flower328Props {
  text?: string;
  style?: React.CSSProperties;
  fillColor?: string;
  strokeColor?: string;
  backgroundColor?: string;
}

export const Flower328: any = memo(({ 
  text = "花字", 
  style = {},
  fillColor = "#74b9ff",
  strokeColor = "#0984e3", 
  backgroundColor = "transparent"
}: Flower328Props) => {
  const fontSize = style.fontSize || 48;
  const fontFamily = style.fontFamily || '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif';
  const fontWeight = style.fontWeight || 'bold';
  const letterSpacing = style.letterSpacing || 'normal';
  
  const textStyle: React.CSSProperties = {
    fontSize,
    fontFamily,
    fontWeight,
    letterSpacing,
    color: fillColor,
    WebkitTextStroke: `2px ${strokeColor}`,
    textShadow: 'none',
    filter: 'none',
    WebkitFontSmoothing: 'antialiased',
    MozOsxFontSmoothing: 'grayscale',
    textRendering: 'optimizeLegibility',
    display: 'inline-block',
    backgroundColor,
    ...style
  };

  return (
    <span style={textStyle}>
      {text}
    </span>
  );
});

Flower328.showName = '天空淡蓝';
Flower328.description = '花字328 - 清澈透明的天空蓝';
Flower328.key = 'Flower328'; 
Flower328.mainColor = '#74b9ff';