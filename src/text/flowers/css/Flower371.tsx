import React, { memo } from 'react';

interface Flower371Props {
  text?: string;
  style?: React.CSSProperties;
  fillColor?: string;
  strokeColor?: string;
  backgroundColor?: string;
}

export const Flower371: any = memo(({ 
  text = "花字", 
  style = {},
  fillColor = "#A0522D",
  strokeColor = "#654321", 
  backgroundColor = "transparent"
}: Flower371Props) => {
  const fontSize = style.fontSize || 48;
  const fontFamily = style.fontFamily || '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif';
  const fontWeight = style.fontWeight || 'bold';
  const letterSpacing = style.letterSpacing || 'normal';
  
  const textStyle: React.CSSProperties = {
    fontSize,
    fontFamily,
    fontWeight,
    letterSpacing,
    background: `repeating-linear-gradient(45deg, #8B4513 0px, #8B4513 10px, #CD853F 10px, #CD853F 20px), repeating-linear-gradient(-45deg, ${fillColor} 0px, ${fillColor} 10px, transparent 10px, transparent 20px)`,
    WebkitBackgroundClip: 'text',
    WebkitTextFillColor: 'transparent',
    backgroundClip: 'text',
    WebkitTextStroke: `2px ${strokeColor}`,
    textShadow: '2px 2px 4px rgba(139, 69, 19, 0.3)',
    filter: 'none',
    WebkitFontSmoothing: 'antialiased',
    MozOsxFontSmoothing: 'grayscale',
    textRendering: 'optimizeLegibility',
    display: 'inline-block',
    backgroundColor,
    backgroundSize: 'cover',
    backgroundPosition: 'center',
    ...style
  };

  return (
    <span style={textStyle}>
      {text}
    </span>
  );
});

Flower371.showName = '格子布纹';
Flower371.description = '花字371 - 经典格子布纹理';
Flower371.key = 'Flower371'; 
Flower371.mainColor = '#A0522D';