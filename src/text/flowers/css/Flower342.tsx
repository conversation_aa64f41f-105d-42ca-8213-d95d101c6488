import React, { memo } from 'react';

interface Flower342Props {
  text?: string;
  style?: React.CSSProperties;
  fillColor?: string;
  strokeColor?: string;
  backgroundColor?: string;
}

export const Flower342: any = memo(({ 
  text = "花字", 
  style = {},
  fillColor = "#81ecec",
  strokeColor = "#00b894", 
  backgroundColor = "transparent"
}: Flower342Props) => {
  const fontSize = style.fontSize || 48;
  const fontFamily = style.fontFamily || '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif';
  const fontWeight = style.fontWeight || 'bold';
  const letterSpacing = style.letterSpacing || 'normal';
  
  const textStyle: React.CSSProperties = {
    fontSize,
    fontFamily,
    fontWeight,
    letterSpacing,
    color: fillColor,
    WebkitTextStroke: `2px ${strokeColor}`,
    textShadow: `-4px 0px 0 #55a3ff, -3px -2px 0 #55a3ff, -3px -1px 0 #55a3ff, -3px 0px 0 #55a3ff, -3px 1px 0 #55a3ff, -3px 2px 0 #55a3ff, -2px -3px 0 #55a3ff, -2px -2px 0 #55a3ff, -2px -1px 0 #55a3ff, -2px 0px 0 #55a3ff, -2px 1px 0 #55a3ff, -2px 2px 0 #55a3ff, -2px 3px 0 #55a3ff, -1px -3px 0 #55a3ff, -1px -2px 0 #55a3ff, -1px -1px 0 #55a3ff, -1px 0px 0 #55a3ff, -1px 1px 0 #55a3ff, -1px 2px 0 #55a3ff, -1px 3px 0 #55a3ff, 0px -4px 0 #55a3ff, 0px -3px 0 #55a3ff, 0px -2px 0 #55a3ff, 0px -1px 0 #55a3ff, 0px 1px 0 #55a3ff, 0px 2px 0 #55a3ff, 0px 3px 0 #55a3ff, 0px 4px 0 #55a3ff, 1px -3px 0 #55a3ff, 1px -2px 0 #55a3ff, 1px -1px 0 #55a3ff, 1px 0px 0 #55a3ff, 1px 1px 0 #55a3ff, 1px 2px 0 #55a3ff, 1px 3px 0 #55a3ff, 2px -3px 0 #55a3ff, 2px -2px 0 #55a3ff, 2px -1px 0 #55a3ff, 2px 0px 0 #55a3ff, 2px 1px 0 #55a3ff, 2px 2px 0 #55a3ff, 2px 3px 0 #55a3ff, 3px -2px 0 #55a3ff, 3px -1px 0 #55a3ff, 3px 0px 0 #55a3ff, 3px 1px 0 #55a3ff, 3px 2px 0 #55a3ff, 4px 0px 0 #55a3ff`,
    filter: 'none',
    WebkitFontSmoothing: 'antialiased',
    MozOsxFontSmoothing: 'grayscale',
    textRendering: 'optimizeLegibility',
    display: 'inline-block',
    backgroundColor,
    ...style
  };

  return (
    <span style={textStyle}>
      {text}
    </span>
  );
});

Flower342.showName = '清新双层';
Flower342.description = '花字342 - 清新自然的双层效果';
Flower342.key = 'Flower342'; 
Flower342.mainColor = '#81ecec';
