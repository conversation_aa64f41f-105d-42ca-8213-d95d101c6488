import React, { memo } from 'react';

interface Flower314Props {
  text?: string;
  style?: React.CSSProperties;
  fillColor?: string;
  strokeColor?: string;
  backgroundColor?: string;
}

export const Flower314: any = memo(({ 
  text = "花字", 
  style = {},
  fillColor = "#c0c0c0",
  strokeColor = "#ffffff", 
  backgroundColor = "transparent"
}: Flower314Props) => {
  const fontSize = style.fontSize || 48;
  const fontFamily = style.fontFamily || '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif';
  const fontWeight = style.fontWeight || 'bold';
  const letterSpacing = style.letterSpacing || 'normal';
  
  const textStyle: React.CSSProperties = {
    fontSize,
    fontFamily,
    fontWeight,
    letterSpacing,
    background: `linear-gradient(to bottom, #ffffff, #e5e5e5, ${fillColor})`,
    WebkitBackgroundClip: 'text',
    WebkitTextFillColor: 'transparent',
    backgroundClip: 'text',
    filter: `drop-shadow(0 0 15px ${fillColor}CC)`,
    position: 'relative',
    WebkitFontSmoothing: 'antialiased',
    MozOsxFontSmoothing: 'grayscale',
    textRendering: 'optimizeLegibility',
    display: 'inline-block',
    backgroundColor,
    ...style
  };

  return (
    <span style={textStyle}>
      {text}
    </span>
  );
});

Flower314.showName = '钻石银白';
Flower314.description = '花字314 - 璀璨夺目的钻石银光';
Flower314.key = 'Flower314'; 
Flower314.mainColor = '#c0c0c0';