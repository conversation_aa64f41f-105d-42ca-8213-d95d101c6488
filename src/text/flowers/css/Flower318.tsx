import React, { memo } from 'react';

interface Flower318Props {
  text?: string;
  style?: React.CSSProperties;
  fillColor?: string;
  strokeColor?: string;
  backgroundColor?: string;
}

export const Flower318: any = memo(({ 
  text = "花字", 
  style = {},
  fillColor = "#ff8c69",
  strokeColor = "#ffd700", 
  backgroundColor = "transparent"
}: Flower318Props) => {
  const fontSize = style.fontSize || 48;
  const fontFamily = style.fontFamily || '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif';
  const fontWeight = style.fontWeight || 'bold';
  const letterSpacing = style.letterSpacing || 'normal';
  
  const textStyle: React.CSSProperties = {
    fontSize,
    fontFamily,
    fontWeight,
    letterSpacing,
    background: `linear-gradient(to bottom, #ffcba4, #ffa07a, ${fillColor})`,WebkitBackgroundClip: 'text',
    WebkitTextFillColor: 'transparent',
    backgroundClip: 'text',filter: `drop-shadow(0 0 14px ${'#ffcba4'}CC)`,
    WebkitFontSmoothing: 'antialiased',
    MozOsxFontSmoothing: 'grayscale',
    textRendering: 'optimizeLegibility',
    display: 'inline-block',
    backgroundColor,
    ...style
  };return (
    <span style={textStyle}>
      {text}
    </span>
  );
});

Flower318.showName = '蜜桃橙色';
Flower318.description = '花字318 - 温柔甜美的蜜桃色';
Flower318.key = 'Flower318'; 
Flower318.mainColor = '#ff8c69';