import React, { memo } from 'react';

interface Flower364Props {
  text?: string;
  style?: React.CSSProperties;
  fillColor?: string;
  strokeColor?: string;
  backgroundColor?: string;
}

export const Flower364: any = memo(({ 
  text = "花字", 
  style = {},
  fillColor = "#E6E6FA",
  strokeColor = "#D3D3D3", 
  backgroundColor = "transparent"
}: Flower364Props) => {
  const fontSize = style.fontSize || 48;
  const fontFamily = style.fontFamily || '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif';
  const fontWeight = style.fontWeight || 'bold';
  const letterSpacing = style.letterSpacing || 'normal';
  
  const textStyle: React.CSSProperties = {
    fontSize,
    fontFamily,
    fontWeight,
    letterSpacing,
    background: `linear-gradient(45deg, #F5F5DC 0%, ${fillColor} 25%, #F0F8FF 50%, #F5F5DC 75%, ${fillColor} 100%)`,
    WebkitBackgroundClip: 'text',
    WebkitTextFillColor: 'transparent',
    backgroundClip: 'text',
    WebkitTextStroke: `2px ${strokeColor}`,
    textShadow: '2px 2px 4px rgba(0, 0, 0, 0.1)',
    filter: 'none',
    WebkitFontSmoothing: 'antialiased',
    MozOsxFontSmoothing: 'grayscale',
    textRendering: 'optimizeLegibility',
    display: 'inline-block',
    backgroundColor,
    backgroundSize: 'cover',
    backgroundPosition: 'center',
    ...style
  };

  return (
    <span style={textStyle}>
      {text}
    </span>
  );
});

Flower364.showName = '大理石纹';
Flower364.description = '花字364 - 优雅大理石纹理';
Flower364.key = 'Flower364'; 
Flower364.mainColor = '#E6E6FA';