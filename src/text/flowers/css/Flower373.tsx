import React, { memo } from 'react';

interface Flower373Props {
  text?: string;
  style?: React.CSSProperties;
  fillColor?: string;
  strokeColor?: string;
  backgroundColor?: string;
}

export const Flower373: any = memo(({ 
  text = "花字", 
  style = {},
  fillColor = "#654321",
  strokeColor = "#2F1B14", 
  backgroundColor = "transparent"
}: Flower373Props) => {
  const fontSize = style.fontSize || 48;
  const fontFamily = style.fontFamily || '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif';
  const fontWeight = style.fontWeight || 'bold';
  const letterSpacing = style.letterSpacing || 'normal';
  
  const textStyle: React.CSSProperties = {
    fontSize,
    fontFamily,
    fontWeight,
    letterSpacing,
    background: `radial-gradient(ellipse at center, #8B4513 1px, transparent 1px), radial-gradient(ellipse at center, #A0522D 1px, transparent 1px), linear-gradient(45deg, ${fillColor}, #8B4513)`,
    WebkitBackgroundClip: 'text',
    WebkitTextFillColor: 'transparent',
    backgroundClip: 'text',
    WebkitTextStroke: `2px ${strokeColor}`,
    textShadow: '2px 2px 4px rgba(101, 67, 33, 0.4)',
    filter: 'none',
    WebkitFontSmoothing: 'antialiased',
    MozOsxFontSmoothing: 'grayscale',
    textRendering: 'optimizeLegibility',
    display: 'inline-block',
    backgroundColor,
    backgroundSize: 'cover',
    backgroundPosition: 'center',
    ...style
  };

  return (
    <span style={textStyle}>
      {text}
    </span>
  );
});

Flower373.showName = '皮革纹理';
Flower373.description = '花字373 - 真实皮革纹理效果';
Flower373.key = 'Flower373'; 
Flower373.mainColor = '#654321';