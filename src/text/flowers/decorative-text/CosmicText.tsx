import React from 'react';
import { TextEffectProps } from './type';

export function CosmicText({ style, text, className, ...props }: TextEffectProps) {
  return (
    <span
      style={{
        fontFamily: 'Arial, sans-serif',
        fontWeight: 'bold',
        background:
          'radial-gradient(circle, #000428, #004e92, #009ffd, #00d2ff)',
        WebkitBackgroundClip: 'text',
        WebkitTextFillColor: 'transparent',
        backgroundClip: 'text',
        textAlign: 'center',
        letterSpacing: '3px',
        textShadow: `
          0 0 10px #00d2ff,
          0 0 20px #009ffd,
          0 0 30px #004e92,
          2px 2px 4px rgba(0, 212, 255, 0.6)
        `,
        filter:
          'drop-shadow(4px 4px 8px rgba(0, 78, 146, 0.7)) brightness(1.2)',
        ...(style || {}),
      }}
      className={className}
      {...props}
    >
      {text}
    </span>
  );
}

CosmicText.key = 'CosmicText';
CosmicText.description = 'cosmic text';
CosmicText.showName = '宇宙';

export default CosmicText;
