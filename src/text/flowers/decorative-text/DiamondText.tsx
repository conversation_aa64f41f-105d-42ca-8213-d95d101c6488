import React from 'react';
import { TextEffectProps } from './type';

interface DiamondTextProps {
  text: string;
  fontSize?: number;
}

export function DiamondText({ style, text, className, ...props }: TextEffectProps) {
  return (
    <span
      style={{
        fontFamily: 'Arial Black, sans-serif',
        fontWeight: '900',
        background:
          'linear-gradient(45deg, #ffffff, #e8e8e8, #ffffff, #f0f0f0, #ffffff)',
        WebkitBackgroundClip: 'text',
        WebkitTextFillColor: 'transparent',
        backgroundClip: 'text',
        textAlign: 'center',
        letterSpacing: '3px',
        WebkitTextStroke: '1px #cccccc',
        textShadow: `
          0 0 10px #ffffff,
          0 0 20px #e0e0e0,
          0 0 30px #f8f8f8,
          2px 2px 4px rgba(200, 200, 200, 0.8)
        `,
        filter:
          'drop-shadow(4px 4px 8px rgba(0, 0, 0, 0.3)) brightness(1.4) contrast(1.2)',
        ...(style || {}),
      }}
      className={className}
      {...props}
    >
      {text}
    </span>
  );
}

DiamondText.key = 'DiamondText';
DiamondText.description = 'diamond text';
DiamondText.showName = '钻石';

export default DiamondText;
