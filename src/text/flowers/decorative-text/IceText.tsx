import React from 'react';
import { TextEffectProps } from './type';

export function IceText({ style, text, className, ...props }: TextEffectProps) {
  return (
    <span
      style={{
        fontFamily: 'Arial, sans-serif',
        fontWeight: 'bold',
        background:
          'linear-gradient(135deg, #b3e5fc, #e1f5fe, #ffffff, #f0f8ff, #e0f6ff)',
        WebkitBackgroundClip: 'text',
        WebkitTextFillColor: 'transparent',
        backgroundClip: 'text',
        textAlign: 'center',
        letterSpacing: '4px',
        WebkitTextStroke: '1px #87ceeb',
        textShadow: `
          0 0 10px #87ceeb,
          0 0 20px #b0e0e6,
          0 0 30px #ffffff,
          2px 2px 4px rgba(135, 206, 235, 0.6)
        `,
        filter:
          'drop-shadow(4px 4px 8px rgba(173, 216, 230, 0.7)) brightness(1.3)',
        ...(style || {}),
      }}
      className={className}
      {...props}
    >
      {text}
    </span>
  );
}

IceText.key = 'IceText';
IceText.description = 'ice text';
IceText.showName = '冰';

export default IceText;
