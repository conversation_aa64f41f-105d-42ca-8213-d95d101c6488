import React from 'react';
import { TextEffectProps } from './type';

export function NeonGlowText({ style, text, className, ...props }: TextEffectProps) {
  return (
    <span
      style={{
        fontFamily: 'Arial, sans-serif',
        fontWeight: 'bold',
        color: '#fff',
        textAlign: 'center',
        letterSpacing: '4px',
        textShadow: `
          0 0 5px #ff00ff,
          0 0 10px #ff00ff,
          0 0 15px #ff00ff,
          0 0 20px #ff00ff,
          0 0 35px #ff00ff,
          0 0 40px #ff00ff,
          0 0 50px #ff00ff,
          0 0 75px #ff00ff
        `,
        WebkitTextStroke: '1px #ff00ff',
        filter: 'brightness(1.2) contrast(1.3)',
        background: 'linear-gradient(45deg, #ff00ff, #00ffff, #ff00ff)',
        WebkitBackgroundClip: 'text',
        WebkitTextFillColor: 'transparent',
        backgroundClip: 'text',
        ...(style || {}),
      }}
      className={className}
      {...props}
    >
      {text}
    </span>
  );
}

NeonGlowText.key = 'NeonGlowText';
NeonGlowText.description = 'neon glow text';
NeonGlowText.showName = '霓虹灯';

export default NeonGlowText;
