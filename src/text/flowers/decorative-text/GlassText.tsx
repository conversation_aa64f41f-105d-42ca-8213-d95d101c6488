import React from 'react';
import { TextEffectProps } from './type';

export function GlassText({ style, text, className, ...props }: TextEffectProps) {
  return (
    <span
      style={{
        fontFamily: 'Arial, sans-serif',
        fontWeight: 'bold',
        color: 'rgba(255, 255, 255, 0.1)',
        textAlign: 'center',
        letterSpacing: '2px',
        WebkitTextStroke: '2px rgba(255, 255, 255, 0.3)',
        textShadow: `
          0 0 10px rgba(255, 255, 255, 0.8),
          0 0 20px rgba(173, 216, 230, 0.6),
          0 2px 4px rgba(255, 255, 255, 0.9),
          0 -2px 4px rgba(255, 255, 255, 0.7)
        `,
        background:
          'linear-gradient(135deg, rgba(255, 255, 255, 0.4), rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.2))',
        WebkitBackgroundClip: 'text',
        backgroundClip: 'text',
        filter: 'blur(0.5px) brightness(1.5)',
        ...(style || {}),
      }}
      className={className}
      {...props}
    >
      {text}
    </span>
  );
}

GlassText.key = 'GlassText';
GlassText.description = 'glass text';
GlassText.showName = '玻璃';

export default GlassText;
