import React from 'react';
import { TextEffectProps } from './type';

export function GoldLuxuryText({ style, text, className, ...props }: TextEffectProps) {
  return (
    <span
      style={{
        fontFamily: 'Times New Roman, serif',
        fontWeight: '900',
        background:
          'linear-gradient(45deg, #ffd700, #ffed4e, #fff700, #ffd700, #ffb300, #ff8c00)',
        backgroundSize: '300% 300%',
        WebkitBackgroundClip: 'text',
        WebkitTextFillColor: 'transparent',
        backgroundClip: 'text',
        textAlign: 'center',
        letterSpacing: '4px',
        WebkitTextStroke: '2px #b8860b',
        textShadow: `
          0 0 10px #ffd700,
          0 0 20px #ffb300,
          0 0 30px #ff8c00,
          2px 2px 4px rgba(184, 134, 11, 0.8),
          4px 4px 8px rgba(139, 69, 19, 0.6),
          6px 6px 12px rgba(0, 0, 0, 0.4)
        `,
        filter:
          'drop-shadow(8px 8px 16px rgba(0, 0, 0, 0.7)) contrast(1.2) brightness(1.1)',
        ...(style || {}),
      }}
      className={className}
      {...props}
    >
      {text}
    </span>
  );
}

GoldLuxuryText.key = 'GoldLuxuryText';
GoldLuxuryText.description = 'gold luxury text';
GoldLuxuryText.showName = '金色豪华';

export default GoldLuxuryText;
