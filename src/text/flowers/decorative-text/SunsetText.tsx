import React from 'react';
import { TextEffectProps } from './type';

export function SunsetText({ style, text, className, ...props }: TextEffectProps) {
  return (
    <span
      style={{
        fontFamily: 'Arial, sans-serif',
        fontWeight: 'bold',
        background:
          'linear-gradient(45deg, #ff4500, #ff6347, #ff7f50, #ffa500, #ffd700)',
        WebkitBackgroundClip: 'text',
        WebkitTextFillColor: 'transparent',
        backgroundClip: 'text',
        textAlign: 'center',
        letterSpacing: '2px',
        textShadow: `0 0 20px #ff4500, 2px 2px 4px rgba(255, 69, 0, 0.7)`,
        filter: 'drop-shadow(4px 4px 8px rgba(255, 165, 0, 0.6))',
        ...(style || {}),
      }}
      className={className}
      {...props}
    >
      {text}
    </span>
  );
}

SunsetText.key = 'SunsetText';
SunsetText.description = 'sunset text';
SunsetText.showName = '日落';

export default SunsetText;
