import React from 'react';
import { TextEffectProps } from './type';

export function MultiStrokeText({ style, text, className, ...props }: TextEffectProps) {
  return (
    <span
      style={{
        fontFamily: 'Arial Black, sans-serif',
        fontWeight: '900',
        color: '#ffffff',
        textAlign: 'center',
        letterSpacing: '3px',
        WebkitTextStroke: `
          8px #ff0080,
          6px #00ff80,
          4px #8000ff,
          2px #ff8000
        `,
        textShadow: `
          0 0 10px #ff0080,
          0 0 20px #00ff80,
          0 0 30px #8000ff,
          3px 3px 0px #ff8000,
          6px 6px 0px #ff0080,
          9px 9px 0px #00ff80
        `,
        filter: 'drop-shadow(4px 4px 8px rgba(0, 0, 0, 0.5))',
        ...(style || {}),
      }}
      className={className}
      {...props}
    >
      {text}
    </span>
  );
}

MultiStrokeText.key = 'MultiStrokeText';
MultiStrokeText.description = 'multi stroke text';
MultiStrokeText.showName = '多重描边';

export default MultiStrokeText;
