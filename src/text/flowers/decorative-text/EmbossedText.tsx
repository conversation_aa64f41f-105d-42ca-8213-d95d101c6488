import React from 'react';
import { TextEffectProps } from './type';

export function EmbossedText({ style, text, className, ...props }: TextEffectProps) {
  return (
    <span
      style={{
        fontFamily: 'Arial Black, sans-serif',
        fontWeight: '900',
        color: '#8B7355',
        textAlign: 'center',
        letterSpacing: '3px',
        textShadow: `
          1px 1px 0px #D4C4AA,
          2px 2px 0px #BFB09B,
          3px 3px 0px #AAA08C,
          4px 4px 0px #96907D,
          5px 5px 0px #82806E,
          -1px -1px 0px #5A5142,
          -2px -2px 0px #4A4236,
          -3px -3px 0px #3A332A,
          6px 6px 20px rgba(0, 0, 0, 0.4)
        `,
        filter: 'drop-shadow(8px 8px 16px rgba(0, 0, 0, 0.3))',
        background: 'linear-gradient(45deg, #D4C4AA, #8B7355, #D4C4AA)',
        WebkitBackgroundClip: 'text',
        WebkitTextFillColor: 'transparent',
        backgroundClip: 'text',
        ...(style || {}),
      }}
      className={className}
      {...props}
    >
      {text}
    </span>
  );
}

EmbossedText.key = 'EmbossedText';
EmbossedText.description = 'embossed text';
EmbossedText.showName = '浮雕';

export default EmbossedText;
