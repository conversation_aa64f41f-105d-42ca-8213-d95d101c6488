import React from 'react';
import { TextEffectProps } from './type';

export function CrystalText({ style, text, className, ...props }: TextEffectProps) {
  return (
    <span
      style={{
        fontFamily: 'Arial, sans-serif',
        fontWeight: 'bold',
        color: 'transparent',
        textAlign: 'center',
        letterSpacing: '3px',
        WebkitTextStroke: '2px rgba(255, 255, 255, 0.8)',
        textShadow: `
          0 0 10px rgba(255, 255, 255, 0.9),
          0 0 20px rgba(135, 206, 250, 0.7),
          0 0 30px rgba(173, 216, 230, 0.5),
          inset 0 0 40px rgba(255, 255, 255, 0.3)
        `,
        background:
          'linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(135, 206, 250, 0.3), rgba(255, 255, 255, 0.1))',
        WebkitBackgroundClip: 'text',
        backgroundClip: 'text',
        filter:
          'drop-shadow(0 0 20px rgba(255, 255, 255, 0.6)) brightness(1.3)',
        ...(style || {}),
      }}
      className={className}
      {...props}
    >
      {text}
    </span>
  );
}

CrystalText.key = 'CrystalText';
CrystalText.description = 'crystal text';
CrystalText.showName = '水晶';

export default CrystalText;
