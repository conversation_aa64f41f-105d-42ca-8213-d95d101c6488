import React from 'react';
import { TextEffectProps } from './type';

export function FireGradientText({ style, text, className, ...props }: TextEffectProps) {
  return (
    <span
      style={{
        fontFamily: 'Arial Black, sans-serif',
        fontWeight: '900',
        background:
          'linear-gradient(45deg, #ff0000, #ff4500, #ff6600, #ff8c00, #ffa500, #ffff00)',
        WebkitBackgroundClip: 'text',
        WebkitTextFillColor: 'transparent',
        backgroundClip: 'text',
        textAlign: 'center',
        letterSpacing: '3px',
        textShadow: `
          0 0 10px #ff0000,
          0 0 20px #ff4500,
          0 0 30px #ff6600,
          0 0 40px #ff8c00
        `,
        filter:
          'drop-shadow(4px 4px 8px rgba(255, 69, 0, 0.8)) brightness(1.2)',
        ...(style || {}),
      }}
      className={className}
      {...props}
    >
      {text}
    </span>
  );
}

FireGradientText.key = 'FireGradientText';
FireGradientText.description = 'fire gradient text';
FireGradientText.showName = '火焰渐变';

export default FireGradientText;
