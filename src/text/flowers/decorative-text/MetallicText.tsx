import React from 'react';
import { TextEffectProps } from './type';

export function MetallicText({ style, text, className, ...props }: TextEffectProps) {
  return (
    <span
      style={{
        fontFamily: 'Arial Black, sans-serif',
        fontWeight: '900',
        background:
          'linear-gradient(135deg, #c0c0c0, #e8e8e8, #a0a0a0, #d3d3d3, #808080, #b8b8b8)',
        WebkitBackgroundClip: 'text',
        WebkitTextFillColor: 'transparent',
        backgroundClip: 'text',
        textAlign: 'center',
        letterSpacing: '2px',
        WebkitTextStroke: '1px #666666',
        textShadow: `
          2px 2px 4px rgba(0, 0, 0, 0.5),
          4px 4px 8px rgba(0, 0, 0, 0.3),
          6px 6px 12px rgba(0, 0, 0, 0.2)
        `,
        filter: 'drop-shadow(8px 8px 16px rgba(0, 0, 0, 0.4)) contrast(1.3)',
        ...(style || {}),
      }}
      className={className}
      {...props}
    >
      {text}
    </span>
  );
}

MetallicText.key = 'MetallicText';
MetallicText.description = 'metallic text';
MetallicText.showName = '金属';

export default MetallicText;
