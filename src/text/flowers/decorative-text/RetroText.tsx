import React from 'react';
import { TextEffectProps } from './type';

export function RetroText({ style, text, className, ...props }: TextEffectProps) {
  return (
    <span
      style={{
        fontFamily: 'Courier New, monospace',
        fontWeight: 'bold',
        background:
          'linear-gradient(45deg, #ff6b35, #f7931e, #ffd100, #ffb347)',
        WebkitBackgroundClip: 'text',
        WebkitTextFillColor: 'transparent',
        backgroundClip: 'text',
        textAlign: 'center',
        letterSpacing: '4px',
        WebkitTextStroke: '2px #8B4513',
        textShadow: `
          3px 3px 0px #8B4513,
          6px 6px 0px #A0522D,
          9px 9px 0px #CD853F,
          12px 12px 20px rgba(0, 0, 0, 0.5)
        `,
        filter: 'drop-shadow(8px 8px 16px rgba(139, 69, 19, 0.6))',
        ...(style || {}),
      }}
      className={className}
      {...props}
    >
      {text}
    </span>
  );
}

RetroText.key = 'RetroText';
RetroText.description = 'retro text';
RetroText.showName = '复古';

export default RetroText;
