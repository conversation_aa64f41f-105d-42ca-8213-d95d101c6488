import React from 'react';
import { TextEffectProps } from './type';

export function PrismText({ style, text, className, ...props }: TextEffectProps) {
  return (
    <span
      style={{
        fontFamily: 'Arial, sans-serif',
        fontWeight: 'bold',
        background:
          'conic-gradient(from 0deg, #ff0000, #ff7f00, #ffff00, #00ff00, #0000ff, #8f00ff, #ff0000)',
        WebkitBackgroundClip: 'text',
        WebkitTextFillColor: 'transparent',
        backgroundClip: 'text',
        textAlign: 'center',
        letterSpacing: '3px',
        textShadow: `
          0 0 20px rgba(255, 255, 255, 0.8),
          2px 0 0 #ff0000,
          4px 0 0 #00ff00,
          6px 0 0 #0000ff
        `,
        filter: 'drop-shadow(4px 4px 8px rgba(0, 0, 0, 0.4)) hue-rotate(0deg)',
        ...(style || {}),
      }}
      className={className}
      {...props}
    >
      {text}
    </span>
  );
}

PrismText.key = 'PrismText';
PrismText.description = 'prism text';
PrismText.showName = '棱镜';

export default PrismText;
