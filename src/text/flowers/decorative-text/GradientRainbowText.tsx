import React from 'react';
import { TextEffectProps } from './type';

export function GradientRainbowText({ style, text, className, ...props }: TextEffectProps) {
  return (
    <span
      style={{
        fontFamily: 'Arial Black, sans-serif',
        fontWeight: '900',
        background:
          'linear-gradient(45deg, #ff0000, #ff7f00, #ffff00, #00ff00, #0000ff, #8f00ff, #ff0080)',
        backgroundSize: '400% 400%',
        WebkitBackgroundClip: 'text',
        WebkitTextFillColor: 'transparent',
        backgroundClip: 'text',
        textAlign: 'center',
        letterSpacing: '2px',
        textShadow: '0 0 30px rgba(255, 255, 255, 0.5)',
        filter: 'drop-shadow(2px 2px 4px rgba(0, 0, 0, 0.3))',
        ...(style || {}),
      }}
      className={className}
      {...props}
    >
      {text}
    </span>
  );
}

GradientRainbowText.key = 'GradientRainbowText';
GradientRainbowText.description = 'gradient rainbow text';
GradientRainbowText.showName = '彩虹渐变';

export default GradientRainbowText;
