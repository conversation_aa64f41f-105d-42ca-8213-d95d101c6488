import React from 'react';
import { TextEffectProps } from './type';

export function RoyalText({ style, text, className, ...props }: TextEffectProps) {
  return (
    <span
      style={{
        fontFamily: 'Times New Roman, serif',
        fontWeight: 'bold',
        background:
          'linear-gradient(45deg, #4b0082, #663399, #800080, #9932cc, #ba55d3)',
        WebkitBackgroundClip: 'text',
        WebkitTextFillColor: 'transparent',
        backgroundClip: 'text',
        textAlign: 'center',
        letterSpacing: '3px',
        WebkitTextStroke: '1px #4b0082',
        textShadow: `0 0 20px #4b0082, 2px 2px 4px rgba(75, 0, 130, 0.8)`,
        filter: 'drop-shadow(4px 4px 8px rgba(128, 0, 128, 0.7))',
        ...(style || {}),
      }}
      className={className}
      {...props}
    >
      {text}
    </span>
  );
}

RoyalText.key = 'RoyalText';
RoyalText.description = 'royal text';
RoyalText.showName = '皇家';

export default RoyalText;
