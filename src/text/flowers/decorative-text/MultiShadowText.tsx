import React from 'react';
import { TextEffectProps } from './type';

export function MultiShadowText({ style, text, className, ...props }: TextEffectProps) {
  return (
    <span
      style={{
        fontFamily: 'Arial Black, sans-serif',
        fontWeight: '900',
        color: '#ffffff',
        textAlign: 'center',
        letterSpacing: '2px',
        textShadow: `
          1px 1px 0px #ff6b6b,
          2px 2px 0px #feca57,
          3px 3px 0px #48dbfb,
          4px 4px 0px #ff9ff3,
          5px 5px 0px #54a0ff,
          6px 6px 0px #5f27cd,
          7px 7px 0px #00d2d3,
          8px 8px 0px #ff6348,
          9px 9px 0px #2ed573,
          10px 10px 0px #ffa502,
          11px 11px 20px rgba(0, 0, 0, 0.8),
          12px 12px 25px rgba(0, 0, 0, 0.6),
          13px 13px 30px rgba(0, 0, 0, 0.4)
        `,
        filter: 'drop-shadow(15px 15px 35px rgba(0, 0, 0, 0.8))',
        ...(style || {}),
      }}
      className={className}
      {...props}
    >
      {text}
    </span>
  );
}

MultiShadowText.key = 'MultiShadowText';
MultiShadowText.description = 'multi shadow text';
MultiShadowText.showName = '多重阴影';

export default MultiShadowText;
