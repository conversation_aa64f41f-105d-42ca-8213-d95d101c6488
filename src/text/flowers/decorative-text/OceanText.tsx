import React from 'react';
import { TextEffectProps } from './type';

export function OceanText({ style, text, className, ...props }: TextEffectProps) {
  return (
    <span
      style={{
        fontFamily: 'Arial, sans-serif',
        fontWeight: 'bold',
        background:
          'linear-gradient(135deg, #003366, #004080, #0066cc, #3399ff, #66ccff)',
        WebkitBackgroundClip: 'text',
        WebkitTextFillColor: 'transparent',
        backgroundClip: 'text',
        textAlign: 'center',
        letterSpacing: '2px',
        textShadow: `0 0 20px #0066cc, 2px 2px 4px rgba(0, 102, 204, 0.7)`,
        filter: 'drop-shadow(4px 4px 8px rgba(0, 51, 102, 0.6))',
        ...(style || {}),
      }}
      className={className}
      {...props}
    >
      {text}
    </span>
  );
}

OceanText.key = 'OceanText';
OceanText.description = 'ocean text';
OceanText.showName = '海洋';

export default OceanText;
