import React from 'react';
import { TextEffectProps } from './type';

export function LavenderText({ style, text, className, ...props }: TextEffectProps) {
  return (
    <span
      style={{
        fontFamily: 'Georgia, serif',
        fontWeight: 'bold',
        background:
          'linear-gradient(135deg, #e6e6fa, #dda0dd, #da70d6, #ba55d3, #9370db)',
        WebkitBackgroundClip: 'text',
        WebkitTextFillColor: 'transparent',
        backgroundClip: 'text',
        textAlign: 'center',
        letterSpacing: '2px',
        textShadow: `
          0 0 15px #e6e6fa,
          0 0 25px #dda0dd,
          2px 2px 4px rgba(186, 85, 211, 0.6)
        `,
        filter: 'drop-shadow(4px 4px 8px rgba(147, 112, 219, 0.5))',
        ...(style || {}),
      }}
      className={className}
      {...props}
    >
      {text}
    </span>
  );
}

LavenderText.key = 'LavenderText';
LavenderText.description = 'lavender text';
LavenderText.showName = '薰衣草';

export default LavenderText;
