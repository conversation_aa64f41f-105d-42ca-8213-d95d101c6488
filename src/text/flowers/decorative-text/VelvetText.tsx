import React from 'react';
import { TextEffectProps } from './type';

export function VelvetText({ style, text, className, ...props }: TextEffectProps) {
  return (
    <span
      style={{
        fontFamily: 'Georgia, serif',
        fontWeight: 'bold',
        color: '#8B0000',
        textAlign: 'center',
        letterSpacing: '2px',
        textShadow: `
          1px 1px 2px #4B0000,
          2px 2px 4px #2B0000,
          3px 3px 6px #1B0000,
          0 0 20px #FF6B6B
        `,
        background:
          'linear-gradient(45deg, #8B0000, #DC143C, #B22222, #CD5C5C)',
        WebkitBackgroundClip: 'text',
        WebkitTextFillColor: 'transparent',
        backgroundClip: 'text',
        filter: 'drop-shadow(4px 4px 8px rgba(139, 0, 0, 0.6))',
        ...(style || {}),
      }}
      className={className}
      {...props}
    >
      {text}
    </span>
  );
}

VelvetText.key = 'VelvetText';
VelvetText.description = 'velvet text';
VelvetText.showName = '天鹅绒';

export default VelvetText;
