import React from 'react';
import { TextEffectProps } from './type';

export default function ElementalForceText({ text, style, className, ...props }: TextEffectProps) {
  return (
    <span
      style={{
        fontFamily: 'Arial Black, sans-serif',
        fontWeight: '900',
        background:
          'linear-gradient(135deg, #ff4500, #00ff7f, #1e90ff, #ffd700)',
        backgroundSize: '300% 300%',
        WebkitBackgroundClip: 'text',
        WebkitTextFillColor: 'transparent',
        backgroundClip: 'text',
        textAlign: 'center',
        letterSpacing: '3px',
        WebkitTextStroke: `6px #2f4f4f, 3px #696969`,
        textShadow: `0 0 15px #ff4500, 0 0 25px #00ff7f, 3px 3px 0px #2f4f4f, 6px 6px 20px rgba(0, 0, 0, 0.7)`,
        filter: `drop-shadow(8px 8px 16px rgba(0, 0, 0, 0.7)) brightness(1.3)`,
        ...(style || {}),
      }}
      className={className}
      {...props}
    >
      {text}
    </span>
  );
}

ElementalForceText.key = 'ElementalForceText';
ElementalForceText.description = 'ElementalForceText';
ElementalForceText.showName = '元素之力';
