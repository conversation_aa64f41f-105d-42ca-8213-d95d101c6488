import React from 'react';
import { TextEffectProps } from './type';

export default function InfernoBlastText({ text, style, className, ...props }: TextEffectProps) {
  return (
    <span
      style={{
        fontFamily: 'Arial Black, sans-serif',
        fontWeight: '900',
        background:
          'radial-gradient(circle, #ff0000, #ff4500, #ffa500, #ffff00, #ff8c00)',
        WebkitBackgroundClip: 'text',
        WebkitTextFillColor: 'transparent',
        backgroundClip: 'text',
        textAlign: 'center',
        letterSpacing: '4px',
        WebkitTextStroke: `7px #8b0000, 4px #dc143c, 1px #ff6347`,
        textShadow: `
          0 0 15px #ff0000, 0 0 25px #ff4500, 0 0 35px #ffa500,
          2px 2px 0px #8b0000, 4px 4px 0px #dc143c, 6px 6px 0px #ff6347,
          8px 8px 0px #ff4500, 10px 10px 30px rgba(0, 0, 0, 0.9)
        `,
        filter: `drop-shadow(12px 12px 24px rgba(0, 0, 0, 0.9)) brightness(1.4) contrast(1.5)`,
        transform: 'perspective(700px) rotateY(5deg) scale(1.1)',
        ...(style || {}),
      }}
      className={className}
      {...props}
    >
      {text}
    </span>
  );
}

InfernoBlastText.key = 'InfernoBlastText';
InfernoBlastText.description = 'InfernoBlastText';
InfernoBlastText.showName = '地狱爆炸';
