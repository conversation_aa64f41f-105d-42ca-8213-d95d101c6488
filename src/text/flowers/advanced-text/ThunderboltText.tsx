import React from 'react';

import { TextEffectProps } from './type';

export default function ThunderboltText({ text, style, className, ...props }: TextEffectProps) {
  return (
    <span
      style={{
        fontFamily: 'Arial Black, sans-serif',
        fontWeight: '900',
        background:
          'linear-gradient(45deg, #ffff00, #ffffff, #00ffff, #4169e1)',
        WebkitBackgroundClip: 'text',
        WebkitTextFillColor: 'transparent',
        backgroundClip: 'text',
        textAlign: 'center',
        letterSpacing: '3px',
        WebkitTextStroke: `5px #000080, 2px #4169e1`,
        textShadow: `0 0 15px #ffff00, 0 0 25px #00ffff, 3px 3px 0px #000080, 6px 6px 20px rgba(0, 0, 0, 0.8)`,
        filter: `drop-shadow(8px 8px 16px rgba(0, 0, 0, 0.7)) brightness(1.5)`,
        ...(style || {}),
      }}
      className={className}
      {...props}
    >
      {text}
    </span>
  );
}

ThunderboltText.key = 'ThunderboltText';
ThunderboltText.description = 'ThunderboltText';
ThunderboltText.showName = '雷霆';
