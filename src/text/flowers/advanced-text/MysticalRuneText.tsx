import React from 'react';
import { TextEffectProps } from './type';

export default function MysticalRuneText({ text, style, className, ...props }: TextEffectProps) {
  return (
    <span
      style={{
        fontFamily: 'Times New Roman, serif',
        fontWeight: 'bold',
        background:
          'linear-gradient(45deg, #4b0082, #8a2be2, #9370db, #dda0dd)',
        WebkitBackgroundClip: 'text',
        WebkitTextFillColor: 'transparent',
        backgroundClip: 'text',
        textAlign: 'center',
        letterSpacing: '5px',
        WebkitTextStroke: `5px #2e003e, 2px #4b0082`,
        textShadow: `0 0 20px #8a2be2, 0 0 30px #9370db, 3px 3px 0px #2e003e, 6px 6px 20px rgba(0, 0, 0, 0.7)`,
        filter: `drop-shadow(8px 8px 16px rgba(0, 0, 0, 0.8)) brightness(1.3)`,
        ...(style || {}),
      }}
      className={className}
      {...props}
    >
      {text}
    </span>
  );
}

MysticalRuneText.key = 'MysticalRuneText';
MysticalRuneText.description = 'MysticalRuneText';
MysticalRuneText.showName = '神秘符文';
