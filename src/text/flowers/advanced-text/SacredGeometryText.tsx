import React from 'react';
import { TextEffectProps } from './type';

export default function SacredGeometryText({ text, style, className, ...props }: TextEffectProps) {
  return (
    <span
      style={{
        fontFamily: 'Arial, sans-serif',
        fontWeight: 'bold',
        background:
          'conic-gradient(from 45deg, #ffd700, #ffffff, #4169e1, #ffd700)',
        WebkitBackgroundClip: 'text',
        WebkitTextFillColor: 'transparent',
        backgroundClip: 'text',
        textAlign: 'center',
        letterSpacing: '4px',
        WebkitTextStroke: `5px #191970, 2px #4169e1`,
        textShadow: `0 0 15px #ffd700, 0 0 25px #ffffff, 3px 3px 0px #191970, 6px 6px 20px rgba(0, 0, 0, 0.6)`,
        filter: `drop-shadow(8px 8px 16px rgba(0, 0, 0, 0.5)) brightness(1.2)`,
        ...(style || {}),
      }}
      className={className}
      {...props}
    >
      {text}
    </span>
  );
}

SacredGeometryText.key = 'SacredGeometryText';
SacredGeometryText.description = 'SacredGeometryText';
SacredGeometryText.showName = '神圣几何';
