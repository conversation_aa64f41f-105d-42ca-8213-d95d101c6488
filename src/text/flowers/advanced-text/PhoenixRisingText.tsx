import React from 'react';
import { TextEffectProps } from './type';

export default function PhoenixRisingText({ text, style, className, ...props }: TextEffectProps) {
  return (
    <span
      style={{
        fontFamily: 'Arial, sans-serif',
        fontWeight: 'bold',
        background:
          'conic-gradient(from 0deg, #ff0000, #ff4500, #ffa500, #ffff00, #ff6347, #ff0000)',
        WebkitBackgroundClip: 'text',
        WebkitTextFillColor: 'transparent',
        backgroundClip: 'text',
        textAlign: 'center',
        letterSpacing: '4px',
        WebkitTextStroke: `6px #8b0000, 3px #dc143c`,
        textShadow: `0 0 20px #ff4500, 3px 3px 0px #8b0000, 6px 6px 25px rgba(0, 0, 0, 0.8)`,
        filter: `drop-shadow(10px 10px 20px rgba(0, 0, 0, 0.8)) brightness(1.3)`,
        transform: 'perspective(500px) rotateX(5deg)',
        ...(style || {}),
      }}
      className={className}
      {...props}
    >
      {text}
    </span>
  );
}

PhoenixRisingText.key = 'PhoenixRisingText';
PhoenixRisingText.description = 'PhoenixRisingText';
PhoenixRisingText.showName = '凤凰升起';
