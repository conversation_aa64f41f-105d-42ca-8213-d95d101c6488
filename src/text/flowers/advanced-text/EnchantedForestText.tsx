import React from 'react';
import { TextEffectProps } from './type';

export default function EnchantedForestText({ text, style, className, ...props }: TextEffectProps) {
  return (
    <span
      style={{
        fontFamily: 'Georgia, serif',
        fontWeight: 'bold',
        background:
          'linear-gradient(45deg, #228b22, #32cd32, #98fb98, #00ff7f, #90ee90)',
        backgroundSize: '300% 300%',
        WebkitBackgroundClip: 'text',
        WebkitTextFillColor: 'transparent',
        backgroundClip: 'text',
        textAlign: 'center',
        letterSpacing: '3px',
        WebkitTextStroke: `4px #006400, 2px #228b22`,
        textShadow: `0 0 15px #32cd32, 0 0 25px #00ff7f, 2px 2px 0px #006400, 4px 4px 15px rgba(0, 0, 0, 0.6)`,
        filter: `drop-shadow(6px 6px 12px rgba(0, 0, 0, 0.5)) brightness(1.2)`,
        ...(style || {}),
      }}
      className={className}
      {...props}
    >
      {text}
    </span>
  );
}

EnchantedForestText.key = 'EnchantedForestText';
EnchantedForestText.description = 'EnchantedForestText';
EnchantedForestText.showName = '魔法森林';
