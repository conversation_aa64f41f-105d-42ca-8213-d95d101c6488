import React from 'react';
import { TextEffectProps } from './type';

export default function MythicalCrystalText({ text, style, className, ...props }: TextEffectProps) {
  return (
    <span
      style={{
        fontFamily: 'Arial, sans-serif',
        fontWeight: 'bold',
        background:
          'linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(135, 206, 250, 0.8), rgba(255, 255, 255, 0.7), rgba(173, 216, 230, 0.6))',
        WebkitBackgroundClip: 'text',
        WebkitTextFillColor: 'transparent',
        backgroundClip: 'text',
        textAlign: 'center',
        letterSpacing: '3px',
        WebkitTextStroke: `4px rgba(255, 255, 255, 0.8), 2px rgba(135, 206, 250, 0.9)`,
        textShadow: `
          0 0 10px rgba(255, 255, 255, 0.9), 0 0 20px rgba(135, 206, 250, 0.8),
          0 0 30px rgba(173, 216, 230, 0.7), 2px 2px 4px rgba(255, 255, 255, 0.9),
          4px 4px 8px rgba(135, 206, 250, 0.6), 6px 6px 12px rgba(0, 0, 0, 0.3)
        `,
        filter: `drop-shadow(6px 6px 12px rgba(0, 0, 0, 0.4)) brightness(1.5) contrast(1.2)`,
        ...(style || {}),
      }}
      className={className}
      {...props}
    >
      {text}
    </span>
  );
}

MythicalCrystalText.key = 'MythicalCrystalText';
MythicalCrystalText.description = 'MythicalCrystalText';
MythicalCrystalText.showName = '神话水晶';
