import React from 'react';
import { TextEffectProps } from './type';

export default function CelestialGloryText({ text, style, className, ...props }: TextEffectProps) {
  return (
    <span
      style={{
        fontFamily: 'Times New Roman, serif',
        fontSize: `64px`,
        fontWeight: 'bold',
        background:
          'radial-gradient(circle, #ffffff, #ffd700, #ffb347, #daa520)',
        WebkitBackgroundClip: 'text',
        WebkitTextFillColor: 'transparent',
        backgroundClip: 'text',
        textAlign: 'center',
        letterSpacing: '5px',
        WebkitTextStroke: `4px #b8860b, 2px #daa520`,
        textShadow: `0 0 30px #ffffff, 0 0 40px #ffd700, 3px 3px 0px #b8860b, 6px 6px 20px rgba(0, 0, 0, 0.5)`,
        filter: `drop-shadow(8px 8px 16px rgba(0, 0, 0, 0.6)) brightness(1.4)`,
        ...(style || {}),
      }}
      className={className}
      {...props}
    >
      {text}
    </span>
  );
}

CelestialGloryText.key = 'CelestialGloryText';
CelestialGloryText.description = 'unnatural text';
CelestialGloryText.showName = '天国荣誉';

CelestialGloryText.getOutSideData = async () => {
  const response = await fetch('https://api.example.com/data');
  const data = await response.json();
  return data;
};
