import React from 'react';
import { TextEffectProps } from './type';

export default function GalaxyNebulaText({ text, style, className, ...props }: TextEffectProps) {
  return (
    <span
      style={{
        fontFamily: 'Arial, sans-serif',
        fontWeight: 'bold',
        background:
          'radial-gradient(circle, #000428, #004e92, #009ffd, #00d2ff, #ffffff)',
        WebkitBackgroundClip: 'text',
        WebkitTextFillColor: 'transparent',
        backgroundClip: 'text',
        textAlign: 'center',
        letterSpacing: '4px',
        WebkitTextStroke: `6px #191970, 3px #4169e1, 1px #00d2ff`,
        textShadow: `
          0 0 10px #00d2ff, 0 0 20px #009ffd, 0 0 30px #004e92, 0 0 40px #000428,
          2px 2px 0px #191970, 4px 4px 0px #4169e1, 6px 6px 0px #6495ed,
          8px 8px 25px rgba(0, 0, 0, 0.9)
        `,
        filter: `drop-shadow(8px 8px 16px rgba(0, 0, 0, 0.8)) brightness(1.4)`,
        ...(style || {}),
      }}
      className={className}
      {...props}
    >
      {text}
    </span>
  );
}

GalaxyNebulaText.key = 'GalaxyNebulaText';
GalaxyNebulaText.description = 'GalaxyNebulaText';
GalaxyNebulaText.showName = '银河星云';
