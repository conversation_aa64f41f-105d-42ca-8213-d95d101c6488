import React from 'react';

import { TextEffectProps } from './type';

export default function TitaniumArmorText({ text, style, className, ...props }: TextEffectProps) {
  return (
    <span
      style={{
        fontFamily: 'Arial Black, sans-serif',
        fontWeight: '900',
        background:
          'linear-gradient(135deg, #c0c0c0, #e8e8e8, #a0a0a0, #d3d3d3)',
        WebkitBackgroundClip: 'text',
        WebkitTextFillColor: 'transparent',
        backgroundClip: 'text',
        textAlign: 'center',
        letterSpacing: '4px',
        WebkitTextStroke: `6px #2f4f4f, 3px #696969`,
        textShadow: `0 0 15px #c0c0c0, 3px 3px 0px #2f4f4f, 6px 6px 20px rgba(0, 0, 0, 0.8)`,
        filter: `drop-shadow(8px 8px 16px rgba(0, 0, 0, 0.7)) contrast(1.3)`,
        ...(style || {}),
      }}
      className={className}
      {...props}
    >
      {text}
    </span>
  );
}

TitaniumArmorText.key = 'TitaniumArmorText';
TitaniumArmorText.description = 'TitaniumArmorText';
TitaniumArmorText.showName = '钛合金装甲';
