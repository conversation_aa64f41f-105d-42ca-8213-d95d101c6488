import React from 'react';

import { TextEffectProps } from './type';

export default function UltimateRainbowText({ text, style, className, ...props }: TextEffectProps) {
  return (
    <span
      style={{
        fontFamily: 'Arial Black, sans-serif',
        fontWeight: '900',
        background:
          'conic-gradient(from 0deg, #ff0000, #ff7f00, #ffff00, #00ff00, #0000ff, #8f00ff, #ff0000)',
        WebkitBackgroundClip: 'text',
        WebkitTextFillColor: 'transparent',
        backgroundClip: 'text',
        textAlign: 'center',
        letterSpacing: '3px',
        WebkitTextStroke: `8px #000000, 4px #333333`,
        textShadow: `0 0 20px #ff0000, 0 0 30px #ffff00, 3px 3px 0px #000000, 6px 6px 25px rgba(0, 0, 0, 0.9)`,
        filter: `drop-shadow(12px 12px 24px rgba(0, 0, 0, 0.9)) brightness(1.4)`,
        ...(style || {}),
      }}
      className={className}
      {...props}
    >
      {text}
    </span>
  );
}

UltimateRainbowText.key = 'UltimateRainbowText';
UltimateRainbowText.description = 'UltimateRainbowText';
UltimateRainbowText.showName = '终极彩虹';
