import React from 'react';
import { TextEffectProps } from './type';

export default function DragonFireText({ text, style, className, ...props }: TextEffectProps) {
  return (
    <span
      style={{
        fontFamily: 'Arial Black, sans-serif',
        fontWeight: '900',
        background:
          'linear-gradient(45deg, #ff0000, #ff4500, #ff6600, #ff8c00, #ffa500, #ffff00, #ff0000)',
        backgroundSize: '400% 400%',
        WebkitBackgroundClip: 'text',
        WebkitTextFillColor: 'transparent',
        backgroundClip: 'text',
        textAlign: 'center',
        letterSpacing: '3px',
        WebkitTextStroke: `8px #8b0000, 5px #cd5c5c, 2px #ff6347`,
        textShadow: `
          0 0 10px #ff0000, 0 0 20px #ff4500, 0 0 30px #ff6600, 0 0 40px #ff8c00,
          1px 1px 0px #8b0000, 3px 3px 0px #cd5c5c, 5px 5px 0px #ff6347,
          7px 7px 0px #ff4500, 9px 9px 20px rgba(0, 0, 0, 0.8)
        `,
        filter: `drop-shadow(10px 10px 20px rgba(0, 0, 0, 0.9)) brightness(1.3) contrast(1.4)`,
        transform: 'perspective(600px) rotateX(-10deg) scale(1.05)',
        ...(style || {}),
      }}
      className={className}
      {...props}
    >
      {text}
    </span>
  );
}

DragonFireText.key = 'DragonFireText';
DragonFireText.description = 'DragonFire text';
DragonFireText.showName = '龙火';
