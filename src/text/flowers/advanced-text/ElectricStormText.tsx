import React from 'react';
import { TextEffectProps } from './type';

export default function ElectricStormText({ text, style, className, ...props }: TextEffectProps) {
  return (
    <span
      style={{
        fontFamily: 'Arial Black, sans-serif',
        fontWeight: '900',
        background:
          'linear-gradient(45deg, #00ffff, #0080ff, #4169e1, #1e90ff, #00bfff, #87ceeb, #00ffff)',
        backgroundSize: '300% 300%',
        WebkitBackgroundClip: 'text',
        WebkitTextFillColor: 'transparent',
        backgroundClip: 'text',
        textAlign: 'center',
        letterSpacing: '3px',
        WebkitTextStroke: `
          5px #000080,
          3px #0000cd,
          1px #00ffff
        `,
        textShadow: `
          0 0 5px #00ffff,
          0 0 10px #0080ff,
          0 0 15px #4169e1,
          0 0 20px #00ffff,
          0 0 25px #0080ff,
          0 0 30px #4169e1,
          0 0 35px #00ffff,
          0 0 40px #0080ff,
          2px 0 0 #00ffff,
          4px 0 0 #0080ff,
          6px 0 0 #4169e1,
          -2px 0 0 #00ffff,
          -4px 0 0 #0080ff,
          -6px 0 0 #4169e1,
          2px 2px 0px #000080,
          4px 4px 0px #000040,
          6px 6px 0px #000020,
          8px 8px 25px rgba(0, 0, 0, 0.9),
          10px 10px 30px rgba(0, 0, 0, 0.7)
        `,
        filter: `
          drop-shadow(8px 8px 16px rgba(0, 0, 0, 0.8))
          brightness(1.4)
          contrast(1.5)
          saturate(1.3)
          hue-rotate(10deg)
        `,
        transform: 'perspective(800px) rotateY(-5deg)',
        ...(style || {}),
      }}
      className={className}
      {...props}
    >
      {text}
    </span>
  );
}

ElectricStormText.key = 'ElectricStormText';
ElectricStormText.description = 'unnatural text';
ElectricStormText.showName = '电风暴';
