import React from 'react';
import { TextEffectProps } from './type';

export default function SupremeGoldText({ text, style, className, ...props }: TextEffectProps) {
  return (
    <span
      style={{
        fontFamily: 'Times New Roman, serif',
        fontWeight: '900',
        background:
          'linear-gradient(45deg, #ffd700, #ffed4e, #fff700, #ffd700, #ffb300, #ff8c00, #ffd700)',
        backgroundSize: '400% 400%',
        WebkitBackgroundClip: 'text',
        WebkitTextFillColor: 'transparent',
        backgroundClip: 'text',
        textAlign: 'center',
        letterSpacing: '4px',
        WebkitTextStroke: `
          6px #b8860b,
          4px #daa520,
          2px #ffd700
        `,
        textShadow: `
          0 0 5px #ffd700,
          0 0 10px #ffb300,
          0 0 15px #ff8c00,
          0 0 20px #ffd700,
          0 0 35px #ffb300,
          0 0 40px #ff8c00,
          1px 1px 0px #b8860b,
          2px 2px 0px #daa520,
          3px 3px 0px #ffd700,
          4px 4px 0px #ffb300,
          5px 5px 0px #ff8c00,
          6px 6px 0px #b8860b,
          7px 7px 0px #8b7355,
          8px 8px 0px #654321,
          9px 9px 20px rgba(0, 0, 0, 0.8),
          10px 10px 25px rgba(0, 0, 0, 0.6),
          11px 11px 30px rgba(0, 0, 0, 0.4)
        `,
        filter: `
          drop-shadow(12px 12px 24px rgba(0, 0, 0, 0.8))
          brightness(1.3)
          contrast(1.4)
          saturate(1.2)
        `,
        transform: 'perspective(500px) rotateX(10deg)',
        ...(style || {}),
      }}
      className={className}
      {...props}
    >
      {text}
    </span>
  );
}

SupremeGoldText.key = 'SupremeGoldText';
SupremeGoldText.description = 'SupremeGoldText';
SupremeGoldText.showName = '至尊黄金';
