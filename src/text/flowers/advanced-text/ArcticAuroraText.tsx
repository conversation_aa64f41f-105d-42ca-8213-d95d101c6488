import React from 'react';
import { TextEffectProps } from './type';

export default function ArcticAuroraText({ text, style, className, ...props }: TextEffectProps) {
  return (
    <span
      style={{
        fontFamily: 'Arial, sans-serif',
        fontSize: `64px`,
        fontWeight: 'bold',
        background:
          'linear-gradient(45deg, #00ff7f, #40e0d0, #9370db, #ff1493, #00bfff, #7fffd4)',
        backgroundSize: '400% 400%',
        WebkitBackgroundClip: 'text',
        WebkitTextFillColor: 'transparent',
        backgroundClip: 'text',
        textAlign: 'center',
        letterSpacing: '3px',
        WebkitTextStroke: `5px #2f4f4f, 2px #40e0d0`,
        textShadow: `
          0 0 10px #00ff7f, 0 0 20px #40e0d0, 0 0 30px #9370db,
          2px 2px 0px #2f4f4f, 4px 4px 0px #20b2aa, 6px 6px 20px rgba(0, 0, 0, 0.7)
        `,
        filter: `drop-shadow(8px 8px 16px rgba(0, 0, 0, 0.6)) brightness(1.3)`,
        ...(style || {}),
      }}
      className={className}
      {...props}
    >
      {text}
    </span>
  );
}

ArcticAuroraText.key = 'ArcticAuroraText';
ArcticAuroraText.description = 'natural text';
ArcticAuroraText.showName = '北极光';
