import React from 'react';
import { TextEffectProps } from './type';

export default function MoltenLavaText({ text, style, className, ...props }: TextEffectProps) {
  return (
    <span
      style={{
        fontFamily: 'Arial Black, sans-serif',
        fontWeight: '900',
        background:
          'linear-gradient(90deg, #ff4500, #ff0000, #8b0000, #ff6347, #dc143c)',
        WebkitBackgroundClip: 'text',
        WebkitTextFillColor: 'transparent',
        backgroundClip: 'text',
        textAlign: 'center',
        letterSpacing: '4px',
        WebkitTextStroke: `6px #8b0000, 3px #a0522d`,
        textShadow: `0 0 20px #ff4500, 3px 3px 0px #8b0000, 6px 6px 15px rgba(0, 0, 0, 0.8)`,
        filter: `drop-shadow(8px 8px 16px rgba(0, 0, 0, 0.8)) brightness(1.2)`,
        ...(style || {}),
      }}
      className={className}
      {...props}
    >
      {text}
    </span>
  );
}

MoltenLavaText.key = 'MoltenLavaText';
MoltenLavaText.description = 'MoltenLavaText';
MoltenLavaText.showName = '熔岩';
