import React from 'react';
import { TextEffectProps } from './type';

export default function CosmicExplosionText({ text, style, className, ...props }: TextEffectProps) {
  return (
    <span
      style={{
        fontFamily: 'Arial, sans-serif',
        fontSize: `64px`,
        fontWeight: 'bold',
        background:
          'radial-gradient(circle, #ffffff, #ffff00, #ff6347, #8a2be2, #000000)',
        WebkitBackgroundClip: 'text',
        WebkitTextFillColor: 'transparent',
        backgroundClip: 'text',
        textAlign: 'center',
        letterSpacing: '4px',
        WebkitTextStroke: `5px #000000, 2px #8a2be2`,
        textShadow: `0 0 20px #ffffff, 0 0 30px #ffff00, 3px 3px 0px #000000, 6px 6px 25px rgba(0, 0, 0, 0.8)`,
        filter: `drop-shadow(10px 10px 20px rgba(0, 0, 0, 0.9)) brightness(1.4)`,
        transform: 'scale(1.1)',
        ...(style || {}),
      }}
      className={className}
      {...props}
    >
      {text}
    </span>
  );
}
CosmicExplosionText.key = 'CosmicExplosionText';
CosmicExplosionText.description = 'unnatural text';
CosmicExplosionText.showName = '宇宙爆炸';
