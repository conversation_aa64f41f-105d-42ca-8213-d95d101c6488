import React from 'react';
import { TextEffectProps } from './type';

export default function QuantumEnergyText({ text, style, className, ...props }: TextEffectProps) {
  return (
    <span
      style={{
        fontFamily: 'Arial, sans-serif',
        fontWeight: 'bold',
        background:
          'linear-gradient(90deg, #00ffff, #ff00ff, #ffff00, #00ff00, #ff0000)',
        backgroundSize: '200% 200%',
        WebkitBackgroundClip: 'text',
        WebkitTextFillColor: 'transparent',
        backgroundClip: 'text',
        textAlign: 'center',
        letterSpacing: '3px',
        WebkitTextStroke: `4px #000080, 2px #800080`,
        textShadow: `0 0 10px #00ffff, 0 0 20px #ff00ff, 2px 2px 0px #000080, 4px 4px 15px rgba(0, 0, 0, 0.7)`,
        filter: `drop-shadow(6px 6px 12px rgba(0, 0, 0, 0.6)) hue-rotate(45deg)`,
        ...(style || {}),
      }}
      className={className}
      {...props}
    >
      {text}
    </span>
  );
}

QuantumEnergyText.key = 'QuantumEnergyText';
QuantumEnergyText.description = 'QuantumEnergyText';
QuantumEnergyText.showName = '量子能量';
