import React from 'react';
import { TextEffectProps } from './type';

export default function StarburstText({ text, style, className, ...props }: TextEffectProps) {
  return (
    <span
      style={{
        fontFamily: 'Arial, sans-serif',
        fontWeight: 'bold',
        background:
          'radial-gradient(circle, #ffffff, #ffd700, #ffb347, #ff6347)',
        WebkitBackgroundClip: 'text',
        WebkitTextFillColor: 'transparent',
        backgroundClip: 'text',
        textAlign: 'center',
        letterSpacing: '4px',
        WebkitTextStroke: `4px #b8860b, 2px #daa520`,
        textShadow: `0 0 25px #ffffff, 0 0 35px #ffd700, 3px 3px 0px #b8860b, 6px 6px 25px rgba(0, 0, 0, 0.6)`,
        filter: `drop-shadow(8px 8px 16px rgba(0, 0, 0, 0.6)) brightness(1.5)`,
        transform: 'scale(1.05)',
        ...(style || {}),
      }}
      className={className}
      {...props}
    >
      {text}
    </span>
  );
}

StarburstText.key = 'StarburstText';
StarburstText.description = 'StarburstText';
StarburstText.showName = '星爆';
