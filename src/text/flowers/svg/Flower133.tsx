import React, { memo, useMemo } from 'react';
import { measureTextDimensions, parseStyle, measureTextBaselineOffset } from './util';

interface Flower133Props {
  text?: string | any;
  style?: React.CSSProperties;
  fillColor?: string;
  strokeColor?: string;
  backgroundColor?: string;
}

export const Flower133: any = memo(
  ({
    text = '花字',
    style = {},
    fillColor = '#FF69B4',
    strokeColor = '#FFFFFF',
    backgroundColor = '#DC143C',
  }: Flower133Props) => {
    const { fontSize, fontFamily, fontWeight, letterSpacing, fontStyle } =
      parseStyle(style);

    let realText = (typeof text === 'string' ? text : text?.props?.text) || '';

    const textDimensions = useMemo(
      () => measureTextDimensions({ text: realText, fontFamily, fontSize, fontWeight, fontStyle }),
      [realText, fontFamily, fontSize, fontWeight, fontStyle],
    );

    // 为星光效果增加padding
    const padding = fontSize * 0.25;
    const svgWidth = textDimensions.width + padding * 2;
    const svgHeight = textDimensions.height + padding * 2;

    const centerX = svgWidth / 2;
    const centerY = svgHeight / 2;

    // 清晰的描边设置
    const outerStrokeWidth = fontSize * 0.17;
    const middleStrokeWidth = fontSize * 0.10;
    const innerStrokeWidth = fontSize * 0.05;

    const baselineOffset = measureTextBaselineOffset({text: realText, fontFamily, fontSize, fontWeight}) || textDimensions.height * 0.35;
    const textY = centerY + baselineOffset;

    const gradientId = `rose133_${Math.random().toString(36).substr(2, 9)}`;
    const sparkleId = `sparkle133_${Math.random().toString(36).substr(2, 9)}`;

    // 星光装饰位置
    const starPositions = [
      { x: centerX - textDimensions.width * 0.4, y: centerY - textDimensions.height * 0.3, size: 0.08 },
      { x: centerX + textDimensions.width * 0.4, y: centerY - textDimensions.height * 0.3, size: 0.06 },
      { x: centerX - textDimensions.width * 0.3, y: centerY + textDimensions.height * 0.3, size: 0.05 },
      { x: centerX + textDimensions.width * 0.3, y: centerY + textDimensions.height * 0.3, size: 0.07 },
    ];

    return (
      <svg
        style={{ display: 'inline' }}
        width={svgWidth}
        height={svgHeight}
        viewBox={`0 0 ${svgWidth} ${svgHeight}`}
        fontFamily={fontFamily}
        fontSize={fontSize - outerStrokeWidth / 2}
        fontWeight={fontWeight}
        letterSpacing={letterSpacing}
      >
        <defs>
          {/* 清晰的垂直玫瑰渐变 */}
          <linearGradient id={gradientId} x1="0%" y1="0%" x2="0%" y2="100%">
            <stop offset="0%" style={{stopColor: "#FFF0F5", stopOpacity: 1}} />
            <stop offset="25%" style={{stopColor: "#FFB6C1", stopOpacity: 1}} />
            <stop offset="50%" style={{stopColor: "#FF69B4", stopOpacity: 1}} />
            <stop offset="75%" style={{stopColor: "#FF1493", stopOpacity: 1}} />
            <stop offset="100%" style={{stopColor: "#DC143C", stopOpacity: 1}} />
          </linearGradient>
          {/* 星光闪烁滤镜 */}
          <filter id={sparkleId}>
            <feGaussianBlur stdDeviation="0.8" result="blur"/>
            <feSpecularLighting result="specOut" in="blur" specularConstant="1.5" specularExponent="15" lightingColor="white">
              <fePointLight x="50" y="30" z="200"/>
            </feSpecularLighting>
            <feComposite in="SourceGraphic" in2="specOut" operator="arithmetic" k1="0" k2="1" k3="0.8" k4="0"/>
          </filter>
        </defs>
        
        {/* 星光装饰 */}
        {starPositions.map((star, index) => (
          <g key={index}>
            <polygon
              points={`${star.x},${star.y - fontSize * star.size} ${star.x + fontSize * star.size * 0.3},${star.y - fontSize * star.size * 0.3} ${star.x + fontSize * star.size},${star.y} ${star.x + fontSize * star.size * 0.3},${star.y + fontSize * star.size * 0.3} ${star.x},${star.y + fontSize * star.size} ${star.x - fontSize * star.size * 0.3},${star.y + fontSize * star.size * 0.3} ${star.x - fontSize * star.size},${star.y} ${star.x - fontSize * star.size * 0.3},${star.y - fontSize * star.size * 0.3}`}
              fill="#FFB6C1"
              opacity="0.6"
            />
          </g>
        ))}
        
        {/* 外层深红描边 - 确保清晰度 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style133-text"
          fill="none"
          stroke="#8B0000"
          strokeWidth={outerStrokeWidth}
          strokeLinejoin="round"
          strokeLinecap="round"
        >
          {realText}
        </text>
        
        {/* 中层红色描边 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style133-text"
          fill="none"
          stroke="#DC143C"
          strokeWidth={middleStrokeWidth}
          strokeLinejoin="round"
          strokeLinecap="round"
        >
          {realText}
        </text>
        
        {/* 内层白色描边 - 增强清晰度 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style133-text"
          fill="none"
          stroke="#FFFFFF"
          strokeWidth={innerStrokeWidth}
          strokeLinejoin="round"
          strokeLinecap="round"
        >
          {realText}
        </text>
        
        {/* 主体玫瑰渐变填充 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style133-text"
          fill={`url(#${gradientId})`}
          filter={`url(#${sparkleId})`}
        >
          {realText}
        </text>
      </svg>
    );
  },
);

Flower133.showName = '玫瑰星光';
Flower133.description = '玫瑰星光清晰字幕特效';
Flower133.key = 'Flower133';
