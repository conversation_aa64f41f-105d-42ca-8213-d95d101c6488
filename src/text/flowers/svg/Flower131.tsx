import React, { memo, useMemo } from 'react';
import { measureTextDimensions, parseStyle, measureTextBaselineOffset } from './util';

interface Flower131Props {
  text?: string | any;
  style?: React.CSSProperties;
  fillColor?: string;
  strokeColor?: string;
  backgroundColor?: string;
}

export const Flower131: any = memo(
  ({
    text = '花字',
    style = {},
    fillColor = '#FFD700',
    strokeColor = '#FFFFFF',
    backgroundColor = '#FF4500',
  }: Flower131Props) => {
    const { fontSize, fontFamily, fontWeight, letterSpacing, fontStyle } =
      parseStyle(style);

    let realText = (typeof text === 'string' ? text : text?.props?.text) || '';

    const textDimensions = useMemo(
      () => measureTextDimensions({ text: realText, fontFamily, fontSize, fontWeight, fontStyle }),
      [realText, fontFamily, fontSize, fontWeight, fontStyle],
    );

    // 为发光效果增加适当padding
    const padding = fontSize * 0.2;
    const svgWidth = textDimensions.width + padding * 2;
    const svgHeight = textDimensions.height + padding * 2;

    const centerX = svgWidth / 2;
    const centerY = svgHeight / 2;

    // 更粗的描边确保清晰度
    const outerStrokeWidth = fontSize * 0.18;
    const middleStrokeWidth = fontSize * 0.12;
    const innerStrokeWidth = fontSize * 0.06;

    const baselineOffset = measureTextBaselineOffset({text: realText, fontFamily, fontSize, fontWeight}) || textDimensions.height * 0.35;
    const textY = centerY + baselineOffset;

    const gradientId = `golden131_${Math.random().toString(36).substr(2, 9)}`;
    const glowId = `glow131_${Math.random().toString(36).substr(2, 9)}`;

    return (
      <svg
        style={{ display: 'inline' }}
        width={svgWidth}
        height={svgHeight}
        viewBox={`0 0 ${svgWidth} ${svgHeight}`}
        fontFamily={fontFamily}
        fontSize={fontSize - outerStrokeWidth / 2}
        fontWeight={fontWeight}
        letterSpacing={letterSpacing}
      >
        <defs>
          {/* 清晰的垂直金色渐变 */}
          <linearGradient id={gradientId} x1="0%" y1="0%" x2="0%" y2="100%">
            <stop offset="0%" style={{stopColor: "#FFFEF7", stopOpacity: 1}} />
            <stop offset="20%" style={{stopColor: "#FFD700", stopOpacity: 1}} />
            <stop offset="50%" style={{stopColor: "#FFA500", stopOpacity: 1}} />
            <stop offset="80%" style={{stopColor: "#FF8C00", stopOpacity: 1}} />
            <stop offset="100%" style={{stopColor: "#FF6347", stopOpacity: 1}} />
          </linearGradient>
          {/* 柔和发光滤镜 */}
          <filter id={glowId}>
            <feGaussianBlur stdDeviation="1.5" result="coloredBlur"/>
            <feMerge>
              <feMergeNode in="coloredBlur"/>
              <feMergeNode in="SourceGraphic"/>
            </feMerge>
          </filter>
        </defs>
        
        {/* 最外层深色描边 - 确保清晰度 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style131-text"
          fill="none"
          stroke="#8B0000"
          strokeWidth={outerStrokeWidth}
          strokeLinejoin="round"
          strokeLinecap="round"
        >
          {realText}
        </text>
        
        {/* 中层橙色描边 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style131-text"
          fill="none"
          stroke="#FF4500"
          strokeWidth={middleStrokeWidth}
          strokeLinejoin="round"
          strokeLinecap="round"
        >
          {realText}
        </text>
        
        {/* 内层白色描边 - 增强清晰度 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style131-text"
          fill="none"
          stroke="#FFFFFF"
          strokeWidth={innerStrokeWidth}
          strokeLinejoin="round"
          strokeLinecap="round"
        >
          {realText}
        </text>
        
        {/* 主体金色渐变填充 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style131-text"
          fill={`url(#${gradientId})`}
          filter={`url(#${glowId})`}
        >
          {realText}
        </text>
      </svg>
    );
  },
);

Flower131.showName = '黄金发光';
Flower131.description = '黄金发光清晰字幕特效';
Flower131.key = 'Flower131';
