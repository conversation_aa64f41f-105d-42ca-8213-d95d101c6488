import React, { memo, useMemo } from 'react';
import { measureTextDimensions, parseStyle } from './util';

interface Flower046Props {
  text?: string | any;
  style?: React.CSSProperties;
  fillColor?: string;
  strokeColor?: string;
  backgroundColor?: string;
}

export const Flower046: any = memo(
  ({
    text = '花字',
    style = {},
    fillColor = '#E91E63',
    strokeColor = '#C2185B',
    backgroundColor = '#880E4F',
  }: Flower046Props) => {
    const { fontSize, fontFamily, fontWeight, letterSpacing } =
      parseStyle(style);
    let realText = (typeof text === 'string' ? text : text?.props?.text) || '';
    const textDimensions = useMemo(
      () =>
        measureTextDimensions({
          text: realText,
          fontFamily,
          fontSize,
          fontWeight,
        }),
      [realText, fontFamily, fontSize, fontWeight],
    );

    const svgWidth = textDimensions.width;
    const svgHeight = textDimensions.height;

    const centerX = svgWidth / 2;
    const centerY = svgHeight / 2;

    const outerStrokeWidth = fontSize * 0.1;
    const innerStrokeWidth = fontSize * 0.05;

    const baselineOffset = textDimensions.height * 0.35;
    const textY = centerY + baselineOffset;

    const gradientId = `roseGold46_${Math.random().toString(36).substr(2, 9)}`;

    return (
      <svg
        style={{ display: 'inline' }}
        width={svgWidth}
        height={svgHeight}
        viewBox={`0 0 ${svgWidth} ${svgHeight}`}
        fontFamily={fontFamily}
        fontSize={fontSize}
        fontWeight={fontWeight}
        letterSpacing={letterSpacing}
      >
        <defs>
          <linearGradient id={gradientId} x1="0%" y1="0%" x2="0%" y2="100%">
            <stop
              offset="0%"
              style={{ stopColor: '#F8BBD9', stopOpacity: 1 }}
            />
            <stop
              offset="30%"
              style={{ stopColor: '#E91E63', stopOpacity: 1 }}
            />
            <stop
              offset="70%"
              style={{ stopColor: '#C2185B', stopOpacity: 1 }}
            />
            <stop
              offset="100%"
              style={{ stopColor: '#880E4F', stopOpacity: 1 }}
            />
          </linearGradient>
        </defs>
        {/* 外层深玫红描边 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style46-text"
          fill="none"
          stroke={backgroundColor}
          strokeWidth={outerStrokeWidth}
          strokeLinejoin="round"
          strokeLinecap="round"
        >
          {realText}
        </text>
        {/* 内层玫红描边 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style46-text"
          fill="none"
          stroke={strokeColor}
          strokeWidth={innerStrokeWidth}
          strokeLinejoin="round"
          strokeLinecap="round"
        >
          {realText}
        </text>
        {/* 主体玫瑰金垂直渐变填充 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style46-text"
          fill={`url(#${gradientId})`}
        >
          {realText}
        </text>
      </svg>
    );
  },
);

Flower046.showName = '玫瑰金垂直渐变';
Flower046.description = '玫瑰金垂直渐变花字';
Flower046.key = 'Flower046';
