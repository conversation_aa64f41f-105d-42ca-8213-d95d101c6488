import React, { memo, useMemo } from 'react';
import { measureTextDimensions, parseStyle } from './util';

interface Flower038Props {
  text?: string | any;
  style?: React.CSSProperties;
  fillColor?: string;
  strokeColor?: string;
  backgroundColor?: string;
}

export const Flower038: any = memo(
  ({
    text = '花字',
    style = {},
    fillColor = '#191970',
    strokeColor = '#000080',
    backgroundColor = '#00008B',
  }: Flower038Props) => {
    const { fontSize, fontFamily, fontWeight, letterSpacing } =
      parseStyle(style);
    let realText = (typeof text === 'string' ? text : text?.props?.text) || '';
    const textDimensions = useMemo(
      () =>
        measureTextDimensions({
          text: realText,
          fontFamily,
          fontSize,
          fontWeight,
        }),
      [realText, fontFamily, fontSize, fontWeight],
    );

    const svgWidth = textDimensions.width;
    const svgHeight = textDimensions.height;

    const centerX = svgWidth / 2;
    const centerY = svgHeight / 2;

    const outerStrokeWidth = fontSize * 0.08;
    const innerStrokeWidth = fontSize * 0.04;

    const baselineOffset = textDimensions.height * 0.35;
    const textY = centerY + baselineOffset;

    const highlightId = `darkBlueHighlight38_${Math.random()
      .toString(36)
      .substr(2, 9)}`;

    return (
      <svg
        style={{ display: 'inline' }}
        width={svgWidth}
        height={svgHeight}
        viewBox={`0 0 ${svgWidth} ${svgHeight}`}
        fontFamily={fontFamily}
        fontSize={fontSize}
        fontWeight={fontWeight}
        letterSpacing={letterSpacing}
      >
        <defs>
          <filter id={highlightId}>
            <feDropShadow
              dx="-1"
              dy="-1"
              stdDeviation="1"
              floodColor="#87CEEB"
              floodOpacity="0.8"
            />
          </filter>
        </defs>
        {/* 外层深蓝描边 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style38-text"
          fill="none"
          stroke={backgroundColor}
          strokeWidth={outerStrokeWidth}
          strokeLinejoin="round"
          strokeLinecap="round"
        >
          {realText}
        </text>
        {/* 内层海军蓝描边 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style38-text"
          fill="none"
          stroke={strokeColor}
          strokeWidth={innerStrokeWidth}
          strokeLinejoin="round"
          strokeLinecap="round"
        >
          {realText}
        </text>
        {/* 主体深蓝高光填充 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style38-text"
          fill={fillColor}
          filter={`url(#${highlightId})`}
        >
          {realText}
        </text>
      </svg>
    );
  },
);

Flower038.showName = '深蓝高光';
Flower038.description = '深蓝高光效果花字';
Flower038.key = 'Flower038';
