import React, { memo, useMemo } from 'react';
import { measureTextDimensions, parseStyle } from './util';

interface Flower057Props {
  text?: string|any;
  style?: React.CSSProperties;
  fillColor?: string;
  strokeColor?: string;
  backgroundColor?: string;
}

export const Flower057: any = memo(({ 
  text = "花字", 
  style = {},
  fillColor = "#E6E6FA",
  strokeColor = "#9370DB", 
  backgroundColor = "#663399"
}: Flower057Props) => {
  const {fontSize, fontFamily, fontWeight, letterSpacing} = parseStyle(style);
    let realText = (typeof text === 'string' ? text : text?.props?.text) || '';
      const textDimensions = useMemo(
        () =>
          measureTextDimensions({
            text: realText,
            fontFamily,
            fontSize,
            fontWeight,
          }),
        [realText, fontFamily, fontSize, fontWeight],
      );
  
  const svgWidth = textDimensions.width
  const svgHeight = textDimensions.height
  
  const centerX = svgWidth / 2;
  const centerY = svgHeight / 2;
  
  const outerStrokeWidth = fontSize * 0.10;
  const innerStrokeWidth = fontSize * 0.05;
  
  const baselineOffset = textDimensions.height * 0.35;
  const textY = centerY + baselineOffset;
  
  const gradientId = `lavender57_${Math.random().toString(36).substr(2, 9)}`;
  
  return <svg style={{display: 'inline'}} 
    width={svgWidth} height={svgHeight} 
    viewBox={`0 0 ${svgWidth} ${svgHeight}`}
    fontFamily={fontFamily} fontSize={fontSize} fontWeight={fontWeight} letterSpacing={letterSpacing}
  >
    <defs>
        <linearGradient id={gradientId} x1="0%" y1="0%" x2="0%" y2="100%">
            <stop offset="0%" style={{stopColor:"#F8F8FF", stopOpacity:1}} />
            <stop offset="30%" style={{stopColor:"#E6E6FA", stopOpacity:1}} />
            <stop offset="70%" style={{stopColor:"#9370DB", stopOpacity:1}} />
            <stop offset="100%" style={{stopColor:"#663399", stopOpacity:1}} />
        </linearGradient>
    </defs>
    {/* 外层深紫描边 */}
    <text x={centerX} y={textY} textAnchor="middle" className="svg-text style57-text"             
            fill="none" stroke={backgroundColor} strokeWidth={outerStrokeWidth}
            strokeLinejoin="round" strokeLinecap="round">{realText}</text>
    {/* 内层中紫描边 */}
    <text x={centerX} y={textY} textAnchor="middle" className="svg-text style57-text"             
            fill="none" stroke={strokeColor} strokeWidth={innerStrokeWidth}
            strokeLinejoin="round" strokeLinecap="round">{realText}</text>
    {/* 主体薰衣草紫垂直渐变填充 */}
    <text x={centerX} y={textY} textAnchor="middle" className="svg-text style57-text"             
            fill={`url(#${gradientId})`}>{realText}</text>
    </svg>
});

Flower057.showName = '薰衣草紫垂直渐变';
Flower057.description = '薰衣草紫垂直渐变花字';
Flower057.key = 'Flower057'; 