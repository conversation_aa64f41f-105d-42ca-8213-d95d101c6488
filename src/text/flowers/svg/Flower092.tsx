import React, { memo, useMemo } from 'react';
import { measureTextDimensions, parseStyle } from './util';

interface Flower092Props {
  text?: string | any;
  style?: React.CSSProperties;
  fillColor?: string;
  strokeColor?: string;
  backgroundColor?: string;
}

export const Flower092: any = memo(({ 
  text = "花字", 
  style = {},
  fillColor = "#F472B6",
  strokeColor = "#EC4899", 
  backgroundColor = "#BE185D"
}: Flower092Props) => {
  const {fontSize, fontFamily, fontWeight, letterSpacing} = parseStyle(style);

  let realText = (typeof text === 'string' ? text : text?.props?.text) || '';

  const textDimensions = useMemo(() => measureTextDimensions({text: realText, fontFamily, fontSize, fontWeight}), [realText, fontFamily, fontSize, fontWeight]);
  
  const svgWidth = textDimensions.width
  const svgHeight = textDimensions.height
  
  const centerX = svgWidth / 2;
  const centerY = svgHeight / 2;
  
  const outerStrokeWidth = fontSize * 0.10;
  const innerStrokeWidth = fontSize * 0.05;
  
  const baselineOffset = textDimensions.height * 0.35;
  const textY = centerY + baselineOffset;
  
  const gradientId = `coralPink92_${Math.random().toString(36).substr(2, 9)}`;
  
  return <svg style={{display: 'inline'}} 
    width={svgWidth} height={svgHeight} 
    viewBox={`0 0 ${svgWidth} ${svgHeight}`}
    fontFamily={fontFamily} fontSize={fontSize} fontWeight={fontWeight} letterSpacing={letterSpacing}
  >
    <defs>
        <linearGradient id={gradientId} x1="0%" y1="0%" x2="0%" y2="100%">
            <stop offset="0%" style={{stopColor:"#FBCFE8", stopOpacity:1}} />
            <stop offset="50%" style={{stopColor:"#F472B6", stopOpacity:1}} />
            <stop offset="100%" style={{stopColor:"#EC4899", stopOpacity:1}} />
        </linearGradient>
    </defs>
    {/* 外层深粉描边 */}
    <text x={centerX} y={textY} textAnchor="middle" className="svg-text style92-text"             
            fill="none" stroke={backgroundColor} strokeWidth={outerStrokeWidth}
            strokeLinejoin="round" strokeLinecap="round">{realText}</text>
    {/* 内层粉色描边 */}
    <text x={centerX} y={textY} textAnchor="middle" className="svg-text style92-text"             
            fill="none" stroke={strokeColor} strokeWidth={innerStrokeWidth}
            strokeLinejoin="round" strokeLinecap="round">{realText}</text>
    {/* 主体珊瑚粉渐变填充 */}
    <text x={centerX} y={textY} textAnchor="middle" className="svg-text style92-text"             
            fill={`url(#${gradientId})`}>{realText}</text>
    </svg>
});

Flower092.showName = '珊瑚粉渐变';
Flower092.description = '珊瑚粉色系自然渐变花字';
Flower092.key = 'Flower092'; 