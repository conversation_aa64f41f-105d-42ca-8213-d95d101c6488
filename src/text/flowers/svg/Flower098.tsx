import React, { memo, useMemo } from 'react';
import { measureTextDimensions, parseStyle } from './util';

interface Flower098Props {
  text?: string | any;
  style?: React.CSSProperties;
  fillColor?: string;
  strokeColor?: string;
  backgroundColor?: string;
}

export const Flower098: any = memo(({ 
  text = "花字", 
  style = {},
  fillColor = "#0E7490",
  strokeColor = "#0891B2", 
  backgroundColor = "#164E63"
}: Flower098Props) => {
  const {fontSize, fontFamily, fontWeight, letterSpacing} = parseStyle(style);

  let realText = (typeof text === 'string' ? text : text?.props?.text) || '';

  const textDimensions = useMemo(() => measureTextDimensions({text: realText, fontFamily, fontSize, fontWeight}), [realText, fontFamily, fontSize, fontWeight]);
  
  const svgWidth = textDimensions.width
  const svgHeight = textDimensions.height
  
  const centerX = svgWidth / 2;
  const centerY = svgHeight / 2;
  
  const outerStrokeWidth = fontSize * 0.10;
  const innerStrokeWidth = fontSize * 0.05;
  
  const baselineOffset = textDimensions.height * 0.35;
  const textY = centerY + baselineOffset;
  
  const glowId = `deepCyan98_${Math.random().toString(36).substr(2, 9)}`;
  
  return <svg style={{display: 'inline'}} 
    width={svgWidth} height={svgHeight} 
    viewBox={`0 0 ${svgWidth} ${svgHeight}`}
    fontFamily={fontFamily} fontSize={fontSize} fontWeight={fontWeight} letterSpacing={letterSpacing}
  >
    <defs>
        <filter id={glowId}>
            <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
            <feMerge> 
                <feMergeNode in="coloredBlur"/>
                <feMergeNode in="SourceGraphic"/>
            </feMerge>
        </filter>
    </defs>
    {/* 外层深青描边 */}
    <text x={centerX} y={textY} textAnchor="middle" className="svg-text style98-text"             
            fill="none" stroke={backgroundColor} strokeWidth={outerStrokeWidth}
            strokeLinejoin="round" strokeLinecap="round">{realText}</text>
    {/* 内层青色描边 */}
    <text x={centerX} y={textY} textAnchor="middle" className="svg-text style98-text"             
            fill="none" stroke={strokeColor} strokeWidth={innerStrokeWidth}
            strokeLinejoin="round" strokeLinecap="round">{realText}</text>
    {/* 主体深青发光填充 */}
    <text x={centerX} y={textY} textAnchor="middle" className="svg-text style98-text"             
            fill={fillColor} filter={`url(#${glowId})`}>{realText}</text>
    </svg>
});

Flower098.showName = '深青纯色';
Flower098.description = '深青纯色发光花字';
Flower098.key = 'Flower098'; 