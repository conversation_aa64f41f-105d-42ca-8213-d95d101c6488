import React, { memo, useMemo } from 'react';
import { measureTextDimensions, parseStyle } from './util';

interface Flower021Props {
  text?: string|any;
  style?: React.CSSProperties;
  fillColor?: string;
  strokeColor?: string;
  backgroundColor?: string;
}

export const Flower021: any = memo(({ 
  text = "花字", 
  style = {},
  fillColor = "#00FFFF",
  strokeColor = "#008B8B", 
  backgroundColor = "#2F4F4F"
}: Flower021Props) => {
  const {fontSize, fontFamily, fontWeight, letterSpacing} = parseStyle(style);
      let realText = (typeof text === 'string' ? text : text?.props?.text) || '';
      const textDimensions = useMemo(
        () =>
          measureTextDimensions({
            text: realText,
            fontFamily,
            fontSize,
            fontWeight,
          }),
        [realText, fontFamily, fontSize, fontWeight],
      );
  
  const svgWidth = textDimensions.width
  const svgHeight = textDimensions.height
  
  const centerX = svgWidth / 2;
  const centerY = svgHeight / 2;
  
  const outerStrokeWidth = fontSize * 0.09;
  const innerStrokeWidth = fontSize * 0.045;
  
  const baselineOffset = textDimensions.height * 0.35;
  const textY = centerY + baselineOffset;
  
  const gradientId = `cyan21_${Math.random().toString(36).substr(2, 9)}`;
  const filterId = `fresh21_${Math.random().toString(36).substr(2, 9)}`;
  
  return <svg style={{display: 'inline'}} 
    width={svgWidth} height={svgHeight} 
    viewBox={`0 0 ${svgWidth} ${svgHeight}`}
    fontFamily={fontFamily} fontSize={fontSize} fontWeight={fontWeight} letterSpacing={letterSpacing}
  > 
    <defs>
        <linearGradient id={gradientId} x1="0%" y1="0%" x2="0%" y2="100%">
            <stop offset="0%" style={{stopColor:"#E0FFFF", stopOpacity:1}} />
            <stop offset="30%" style={{stopColor:"#AFEEEE", stopOpacity:1}} />
            <stop offset="70%" style={{stopColor:"#00FFFF", stopOpacity:1}} />
            <stop offset="100%" style={{stopColor:"#008B8B", stopOpacity:1}} />
        </linearGradient>
        <filter id={filterId}>
            <feGaussianBlur stdDeviation="2.5" result="coloredBlur"/>
            <feMerge> 
                <feMergeNode in="coloredBlur"/>
                <feMergeNode in="SourceGraphic"/>
            </feMerge>
        </filter>
    </defs>
    {/* 外层深青灰描边 */}
    <text x={centerX} y={textY} textAnchor="middle" className="svg-text style21-text"             
            fill="none" stroke={backgroundColor} strokeWidth={outerStrokeWidth}
            strokeLinejoin="round" strokeLinecap="round">{realText}</text>
    {/* 内层深青描边 */}
    <text x={centerX} y={textY} textAnchor="middle" className="svg-text style21-text"             
            fill="none" stroke={strokeColor} strokeWidth={innerStrokeWidth}
            strokeLinejoin="round" strokeLinecap="round">{realText}</text>
    {/* 主体青色垂直渐变填充 */}
    <text x={centerX} y={textY} textAnchor="middle" className="svg-text style21-text"             
            fill={`url(#${gradientId})`} filter={`url(#${filterId})`}>{realText}</text>
    </svg>
});

Flower021.showName = '青色垂直渐变';
Flower021.description = '青色垂直渐变花字';
Flower021.key = 'Flower021'; 