import React, { memo, useMemo } from 'react';
import { measureTextDimensions, parseStyle, measureTextBaselineOffset } from './util';

interface Flower122Props {
  text?: string | any;
  style?: React.CSSProperties;
  fillColor?: string;
  strokeColor?: string;
  backgroundColor?: string;
}

export const Flower122: any = memo(
  ({
    text = '花字',
    style = {},
    fillColor = '#FF6347',
    strokeColor = '#FF7F50',
    backgroundColor = '#CD5C5C',
  }: Flower122Props) => {
    const { fontSize, fontFamily, fontWeight, letterSpacing, fontStyle } =
      parseStyle(style);

    let realText = (typeof text === 'string' ? text : text?.props?.text) || '';

    const textDimensions = useMemo(
      () => measureTextDimensions({ text: realText, fontFamily, fontSize, fontWeight, fontStyle }),
      [realText, fontFamily, fontSize, fontWeight, fontStyle],
    );

    const svgWidth = textDimensions.width;
    const svgHeight = textDimensions.height;

    const centerX = svgWidth / 2;
    const centerY = svgHeight / 2;

    const outerStrokeWidth = fontSize * 0.18;
    const innerStrokeWidth = fontSize * 0.09;

    const baselineOffset = measureTextBaselineOffset({text: realText, fontFamily, fontSize, fontWeight}) || textDimensions.height * 0.35;
    const textY = centerY + baselineOffset;

    const gradientId = `sunset122_${Math.random().toString(36).substr(2, 9)}`;
    const sunsetId = `sunsetGlow122_${Math.random().toString(36).substr(2, 9)}`;

    return (
      <svg
        style={{ display: 'inline' }}
        width={svgWidth}
        height={svgHeight}
        viewBox={`0 0 ${svgWidth} ${svgHeight}`}
        fontFamily={fontFamily}
        fontSize={fontSize - outerStrokeWidth / 2}
        fontWeight={fontWeight}
        letterSpacing={letterSpacing}
      >
        <defs>
          <linearGradient id={gradientId} x1="0%" y1="0%" x2="0%" y2="100%">
            <stop offset="0%" style={{stopColor: "#FFFFE0", stopOpacity: 1}} />
            <stop offset="20%" style={{stopColor: "#FFE4B5", stopOpacity: 1}} />
            <stop offset="40%" style={{stopColor: "#FFA07A", stopOpacity: 1}} />
            <stop offset="60%" style={{stopColor: "#FF7F50", stopOpacity: 1}} />
            <stop offset="80%" style={{stopColor: "#FF6347", stopOpacity: 1}} />
            <stop offset="100%" style={{stopColor: "#CD5C5C", stopOpacity: 1}} />
          </linearGradient>
          <filter id={sunsetId}>
            <feGaussianBlur stdDeviation="2.5" result="blur"/>
            <feFlood floodColor="#FF4500" floodOpacity="0.7"/>
            <feComposite in="SourceGraphic" in2="blur" operator="multiply"/>
            <feGaussianBlur stdDeviation="1" result="innerGlow"/>
            <feMerge>
              <feMergeNode in="blur"/>
              <feMergeNode in="innerGlow"/>
              <feMergeNode in="SourceGraphic"/>
            </feMerge>
          </filter>
        </defs>
        {/* 外层深红描边 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style122-text"
          fill="none"
          stroke="#8B0000"
          strokeWidth={outerStrokeWidth}
          strokeLinejoin="round"
          strokeLinecap="round"
        >
          {realText}
        </text>
        {/* 内层印度红描边 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style122-text"
          fill="none"
          stroke={backgroundColor}
          strokeWidth={innerStrokeWidth}
          strokeLinejoin="round"
          strokeLinecap="round"
        >
          {realText}
        </text>
        {/* 主体夕阳渐变填充 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style122-text"
          fill={`url(#${gradientId})`}
          filter={`url(#${sunsetId})`}
        >
          {realText}
        </text>
      </svg>
    );
  },
);

Flower122.showName = '夕阳西下';
Flower122.description = '夕阳西下暖色渐变发光效果花字';
Flower122.key = 'Flower122';
