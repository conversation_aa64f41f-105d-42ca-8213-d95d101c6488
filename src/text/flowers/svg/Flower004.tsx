import React, { memo, useMemo } from 'react';
import { measureTextDimensions, parseStyle } from './util';

interface Flower004Props {
  text?: string | any;
  style?: React.CSSProperties;
  fillColor?: string;
  strokeColor?: string;
  backgroundColor?: string;
}

export const Flower004: any = memo(
  ({
    text = '花字',
    style = {},
    fillColor = '#DA70D6',
    strokeColor = '#C0C0C0',
    backgroundColor = '#4B0082',
  }: Flower004Props) => {
    // 从style中获取字体属性
    const { fontSize, fontFamily, fontWeight, letterSpacing } =
      parseStyle(style);

    let realText = (typeof text === 'string' ? text : text?.props?.text) || '';
    const textDimensions = useMemo(
      () =>
        measureTextDimensions({
          text: realText,
          fontFamily,
          fontSize,
          fontWeight,
        }),
      [realText, fontFamily, fontSize, fontWeight],
    );

    // 计算SVG尺寸，添加合适的padding
    const svgWidth = textDimensions.width;
    const svgHeight = textDimensions.height;

    // 计算文本居中位置
    const centerX = svgWidth / 2;
    const centerY = svgHeight / 2;

    // 计算描边宽度，根据字体大小动态调整
    const outerStrokeWidth = fontSize * 0.1;
    const innerStrokeWidth = fontSize * 0.04;

    // 计算dominantBaseline的偏移量，确保文本垂直居中
    const baselineOffset = textDimensions.height * 0.35;
    const textY = centerY + baselineOffset;

    // 生成唯一ID避免冲突
    const gradientId = `purpleGrad-${Math.random().toString(36).substr(2, 9)}`;
    const filterId = `purpleGlow-${Math.random().toString(36).substr(2, 9)}`;

    return (
      <svg
        style={{ display: 'inline' }}
        width={svgWidth}
        height={svgHeight}
        viewBox={`0 0 ${svgWidth} ${svgHeight}`}
        fontFamily={fontFamily}
        fontSize={fontSize}
        fontWeight={fontWeight}
        letterSpacing={letterSpacing}
      >
        <defs>
          <radialGradient id={gradientId} cx="50%" cy="50%" r="50%">
            <stop
              offset="0%"
              style={{ stopColor: '#DA70D6', stopOpacity: 1 }}
            />
            <stop
              offset="100%"
              style={{ stopColor: '#9932CC', stopOpacity: 1 }}
            />
          </radialGradient>
          <filter id={filterId}>
            <feGaussianBlur stdDeviation="2.5" result="coloredBlur" />
            <feMerge>
              <feMergeNode in="coloredBlur" />
              <feMergeNode in="SourceGraphic" />
            </feMerge>
          </filter>
        </defs>
        {/* 外层深紫描边 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style4-text"
          fill="none"
          stroke={backgroundColor}
          strokeWidth={outerStrokeWidth}
          strokeLinejoin="round"
          strokeLinecap="round"
        >
          {realText}
        </text>
        {/* 内层银色描边 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style4-text"
          fill="none"
          stroke={strokeColor}
          strokeWidth={innerStrokeWidth}
          strokeLinejoin="round"
          strokeLinecap="round"
        >
          {realText}
        </text>
        {/* 主体紫色径向渐变填充 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style4-text"
          fill={`url(#${gradientId})`}
          filter={`url(#${filterId})`}
        >
          {realText}
        </text>
      </svg>
    );
  },
);

Flower004.showName = '紫色梦幻';
Flower004.description = '紫色径向渐变梦幻花字';
Flower004.key = 'Flower004';
