import React, { memo, useMemo } from 'react';
import { measureTextDimensions, parseStyle } from './util';

interface Flower087Props {
  text?: string | any;
  style?: React.CSSProperties;
  fillColor?: string;
  strokeColor?: string;
  backgroundColor?: string;
}

export const Flower087: any = memo(({ 
  text = "花字", 
  style = {},
  fillColor = "#881337",
  strokeColor = "#9F1239", 
  backgroundColor = "#7F1D1D"
}: Flower087Props) => {
  const {fontSize, fontFamily, fontWeight, letterSpacing} = parseStyle(style);

  let realText = (typeof text === 'string' ? text : text?.props?.text) || '';

  const textDimensions = useMemo(() => measureTextDimensions({text: realText, fontFamily, fontSize, fontWeight}), [realText, fontFamily, fontSize, fontWeight]);
  
  const svgWidth = textDimensions.width
  const svgHeight = textDimensions.height
  
  const centerX = svgWidth / 2;
  const centerY = svgHeight / 2;
  
  const outerStrokeWidth = fontSize * 0.08;
  const innerStrokeWidth = fontSize * 0.04;
  
  const baselineOffset = textDimensions.height * 0.35;
  const textY = centerY + baselineOffset;
  
  const shadowId = `wineRed87_${Math.random().toString(36).substr(2, 9)}`;
  
  return <svg style={{display: 'inline'}} 
    width={svgWidth} height={svgHeight} 
    viewBox={`0 0 ${svgWidth} ${svgHeight}`}
    fontFamily={fontFamily} fontSize={fontSize} fontWeight={fontWeight} letterSpacing={letterSpacing}
  >
    <defs>
        <filter id={shadowId}>
            <feDropShadow dx="2" dy="2" stdDeviation="1" floodColor="#BE123C" floodOpacity="0.6"/>
        </filter>
    </defs>
    {/* 外层深红描边 */}
    <text x={centerX} y={textY} textAnchor="middle" className="svg-text style87-text"             
            fill="none" stroke={backgroundColor} strokeWidth={outerStrokeWidth}
            strokeLinejoin="round" strokeLinecap="round">{realText}</text>
    {/* 内层红色描边 */}
    <text x={centerX} y={textY} textAnchor="middle" className="svg-text style87-text"             
            fill="none" stroke={strokeColor} strokeWidth={innerStrokeWidth}
            strokeLinejoin="round" strokeLinecap="round">{realText}</text>
    {/* 主体酒红阴影填充 */}
    <text x={centerX} y={textY} textAnchor="middle" className="svg-text style87-text"             
            fill={fillColor} filter={`url(#${shadowId})`}>{realText}</text>
    </svg>
});

Flower087.showName = '酒红纯色';
Flower087.description = '深酒红纯色阴影花字';
Flower087.key = 'Flower087'; 