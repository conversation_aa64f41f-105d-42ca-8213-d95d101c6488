import React, { memo, useMemo } from 'react';
import { measureTextDimensions, parseStyle } from './util';

interface Flower027Props {
  text?: string|any;
  style?: React.CSSProperties;
  fillColor?: string;
  strokeColor?: string;
  backgroundColor?: string;
}

export const Flower027: any = memo(({ 
  text = "花字", 
  style = {},
  fillColor = "#DC143C",
  strokeColor = "#8B0000", 
  backgroundColor = "#2F0000"
}: Flower027Props) => {
  const {fontSize, fontFamily, fontWeight, letterSpacing} = parseStyle(style);
       let realText = (typeof text === 'string' ? text : text?.props?.text) || '';
       const textDimensions = useMemo(
         () =>
           measureTextDimensions({
             text: realText,
             fontFamily,
             fontSize,
             fontWeight,
           }),
         [realText, fontFamily, fontSize, fontWeight],
       );
  
  const svgWidth = textDimensions.width
  const svgHeight = textDimensions.height
  
  const centerX = svgWidth / 2;
  const centerY = svgHeight / 2;
  
  const outerStrokeWidth = fontSize * 0.12;
  const innerStrokeWidth = fontSize * 0.06;
  
  const baselineOffset = textDimensions.height * 0.35;
  const textY = centerY + baselineOffset;
  
  const filterId = `redGlow27_${Math.random().toString(36).substr(2, 9)}`;
  
  return <svg style={{display: 'inline'}} 
    width={svgWidth} height={svgHeight} 
    viewBox={`0 0 ${svgWidth} ${svgHeight}`}
    fontFamily={fontFamily} fontSize={fontSize} fontWeight={fontWeight} letterSpacing={letterSpacing}
  >
    <defs>
        <filter id={filterId}>
            <feGaussianBlur stdDeviation="4" result="coloredBlur"/>
            <feMerge> 
                <feMergeNode in="coloredBlur"/>
                <feMergeNode in="SourceGraphic"/>
            </feMerge>
        </filter>
    </defs>
    {/* 外层深红描边 */}
    <text x={centerX} y={textY} textAnchor="middle" className="svg-text style27-text"             
            fill="none" stroke={backgroundColor} strokeWidth={outerStrokeWidth}
            strokeLinejoin="round" strokeLinecap="round">{realText}</text>
    {/* 内层暗红描边 */}
    <text x={centerX} y={textY} textAnchor="middle" className="svg-text style27-text"             
            fill="none" stroke={strokeColor} strokeWidth={innerStrokeWidth}
            strokeLinejoin="round" strokeLinecap="round">{realText}</text>
    {/* 主体深红发光填充 */}
    <text x={centerX} y={textY} textAnchor="middle" className="svg-text style27-text"             
            fill={fillColor} filter={`url(#${filterId})`}>{realText}</text>
    </svg>
});

Flower027.showName = '深红发光';
Flower027.description = '深红发光效果花字';
Flower027.key = 'Flower027'; 