import React, { memo, useMemo } from 'react';
import { measureTextDimensions, parseStyle, measureTextBaselineOffset } from './util';

interface Flower115Props {
  text?: string | any;
  style?: React.CSSProperties;
  fillColor?: string;
  strokeColor?: string;
  backgroundColor?: string;
}

export const Flower115: any = memo(
  ({
    text = '花字',
    style = {},
    fillColor = '#C0C0C0',
    strokeColor = '#E5E5E5',
    backgroundColor = '#696969',
  }: Flower115Props) => {
    const { fontSize, fontFamily, fontWeight, letterSpacing, fontStyle } =
      parseStyle(style);

    let realText = (typeof text === 'string' ? text : text?.props?.text) || '';

    const textDimensions = useMemo(
      () => measureTextDimensions({ text: realText, fontFamily, fontSize, fontWeight, fontStyle }),
      [realText, fontFamily, fontSize, fontWeight, fontStyle],
    );

    const svgWidth = textDimensions.width;
    const svgHeight = textDimensions.height;

    const centerX = svgWidth / 2;
    const centerY = svgHeight / 2;

    const outerStrokeWidth = fontSize * 0.14;
    const innerStrokeWidth = fontSize * 0.07;

    const baselineOffset = measureTextBaselineOffset({text: realText, fontFamily, fontSize, fontWeight}) || textDimensions.height * 0.35;
    const textY = centerY + baselineOffset;

    const gradientId = `metal115_${Math.random().toString(36).substr(2, 9)}`;
    const metalId = `metallic115_${Math.random().toString(36).substr(2, 9)}`;

    return (
      <svg
        style={{ display: 'inline' }}
        width={svgWidth}
        height={svgHeight}
        viewBox={`0 0 ${svgWidth} ${svgHeight}`}
        fontFamily={fontFamily}
        fontSize={fontSize - outerStrokeWidth / 2}
        fontWeight={fontWeight}
        letterSpacing={letterSpacing}
      >
        <defs>
          <linearGradient id={gradientId} x1="0%" y1="0%" x2="0%" y2="100%">
            <stop offset="0%" style={{stopColor: "#F8F8FF", stopOpacity: 1}} />
            <stop offset="15%" style={{stopColor: "#E5E5E5", stopOpacity: 1}} />
            <stop offset="30%" style={{stopColor: "#D3D3D3", stopOpacity: 1}} />
            <stop offset="50%" style={{stopColor: "#C0C0C0", stopOpacity: 1}} />
            <stop offset="70%" style={{stopColor: "#A9A9A9", stopOpacity: 1}} />
            <stop offset="85%" style={{stopColor: "#808080", stopOpacity: 1}} />
            <stop offset="100%" style={{stopColor: "#696969", stopOpacity: 1}} />
          </linearGradient>
          <filter id={metalId}>
            <feSpecularLighting result="specOut" in="SourceAlpha" specularConstant="2" specularExponent="30" lightingColor="white">
              <fePointLight x="50" y="30" z="200"/>
            </feSpecularLighting>
            <feComposite in="SourceGraphic" in2="specOut" operator="arithmetic" k1="0" k2="1" k3="1" k4="0"/>
          </filter>
        </defs>
        {/* 外层深灰描边 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style115-text"
          fill="none"
          stroke="#2F2F2F"
          strokeWidth={outerStrokeWidth}
          strokeLinejoin="round"
          strokeLinecap="round"
        >
          {realText}
        </text>
        {/* 内层中灰描边 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style115-text"
          fill="none"
          stroke={backgroundColor}
          strokeWidth={innerStrokeWidth}
          strokeLinejoin="round"
          strokeLinecap="round"
        >
          {realText}
        </text>
        {/* 主体金属渐变填充 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style115-text"
          fill={`url(#${gradientId})`}
          filter={`url(#${metalId})`}
        >
          {realText}
        </text>
      </svg>
    );
  },
);

Flower115.showName = '金属质感';
Flower115.description = '金属质感银色高光反射效果花字';
Flower115.key = 'Flower115';
