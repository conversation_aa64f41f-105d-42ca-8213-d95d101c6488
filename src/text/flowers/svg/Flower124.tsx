import React, { memo, useMemo } from 'react';
import { measureTextDimensions, parseStyle, measureTextBaselineOffset } from './util';

interface Flower124Props {
  text?: string | any;
  style?: React.CSSProperties;
  fillColor?: string;
  strokeColor?: string;
  backgroundColor?: string;
}

export const Flower124: any = memo(
  ({
    text = '花字',
    style = {},
    fillColor = '#228B22',
    strokeColor = '#32CD32',
    backgroundColor = '#006400',
  }: Flower124Props) => {
    const { fontSize, fontFamily, fontWeight, letterSpacing, fontStyle } =
      parseStyle(style);

    let realText = (typeof text === 'string' ? text : text?.props?.text) || '';

    const textDimensions = useMemo(
      () => measureTextDimensions({ text: realText, fontFamily, fontSize, fontWeight, fontStyle }),
      [realText, fontFamily, fontSize, fontWeight, fontStyle],
    );

    const svgWidth = textDimensions.width;
    const svgHeight = textDimensions.height;

    const centerX = svgWidth / 2;
    const centerY = svgHeight / 2;

    const outerStrokeWidth = fontSize * 0.17;
    const innerStrokeWidth = fontSize * 0.085;

    const baselineOffset = measureTextBaselineOffset({text: realText, fontFamily, fontSize, fontWeight}) || textDimensions.height * 0.35;
    const textY = centerY + baselineOffset;

    const gradientId = `forest124_${Math.random().toString(36).substr(2, 9)}`;
    const forestId = `forestShadow124_${Math.random().toString(36).substr(2, 9)}`;

    return (
      <svg
        style={{ display: 'inline' }}
        width={svgWidth}
        height={svgHeight}
        viewBox={`0 0 ${svgWidth} ${svgHeight}`}
        fontFamily={fontFamily}
        fontSize={fontSize - outerStrokeWidth / 2}
        fontWeight={fontWeight}
        letterSpacing={letterSpacing}
      >
        <defs>
          <linearGradient id={gradientId} x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" style={{stopColor: "#ADFF2F", stopOpacity: 1}} />
            <stop offset="25%" style={{stopColor: "#7FFF00", stopOpacity: 1}} />
            <stop offset="50%" style={{stopColor: "#32CD32", stopOpacity: 1}} />
            <stop offset="75%" style={{stopColor: "#228B22", stopOpacity: 1}} />
            <stop offset="100%" style={{stopColor: "#006400", stopOpacity: 1}} />
          </linearGradient>
          <filter id={forestId}>
            <feDropShadow dx="3" dy="5" stdDeviation="2" floodColor="#003300" floodOpacity="0.9"/>
            <feTurbulence baseFrequency="0.03" numOctaves="3" result="noise"/>
            <feDisplacementMap in="SourceGraphic" in2="noise" scale="1"/>
          </filter>
        </defs>
        {/* 外层深绿描边 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style124-text"
          fill="none"
          stroke="#001100"
          strokeWidth={outerStrokeWidth}
          strokeLinejoin="round"
          strokeLinecap="round"
        >
          {realText}
        </text>
        {/* 内层暗绿描边 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style124-text"
          fill="none"
          stroke={backgroundColor}
          strokeWidth={innerStrokeWidth}
          strokeLinejoin="round"
          strokeLinecap="round"
        >
          {realText}
        </text>
        {/* 主体森林渐变填充 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style124-text"
          fill={`url(#${gradientId})`}
          filter={`url(#${forestId})`}
        >
          {realText}
        </text>
      </svg>
    );
  },
);

Flower124.showName = '深林阴影';
Flower124.description = '深林绿色渐变阴影纹理效果花字';
Flower124.key = 'Flower124';
