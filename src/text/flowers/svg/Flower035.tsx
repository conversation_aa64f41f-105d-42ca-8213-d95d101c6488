import React, { memo, useMemo } from 'react';
import { measureTextDimensions, parseStyle } from './util';

interface Flower035Props {
  text?: string|any;
  style?: React.CSSProperties;
  fillColor?: string;
  strokeColor?: string;
  backgroundColor?: string;
}

export const Flower035: any = memo(
  ({
    text = '花字',
    style = {},
    fillColor = '#00CED1',
    strokeColor = '#008B8B',
    backgroundColor = '#2F4F4F',
  }: Flower035Props) => {
    const { fontSize, fontFamily, fontWeight, letterSpacing } =
      parseStyle(style);
    let realText = (typeof text === 'string' ? text : text?.props?.text) || '';
    const textDimensions = useMemo(
      () =>
        measureTextDimensions({
          text: realText,
          fontFamily,
          fontSize,
          fontWeight,
        }),
      [realText, fontFamily, fontSize, fontWeight],
    );

    const svgWidth = textDimensions.width;
    const svgHeight = textDimensions.height;

    const centerX = svgWidth / 2;
    const centerY = svgHeight / 2;

    const outerStrokeWidth = fontSize * 0.08;
    const innerStrokeWidth = fontSize * 0.04;

    const baselineOffset = textDimensions.height * 0.35;
    const textY = centerY + baselineOffset;

    const shadowId = `cyanShadow35_${Math.random().toString(36).substr(2, 9)}`;

    return (
      <svg
        style={{ display: 'inline' }}
        width={svgWidth}
        height={svgHeight}
        viewBox={`0 0 ${svgWidth} ${svgHeight}`}
        fontFamily={fontFamily}
        fontSize={fontSize}
        fontWeight={fontWeight}
        letterSpacing={letterSpacing}
      >
        <defs>
          <filter id={shadowId}>
            <feDropShadow
              dx="2"
              dy="2"
              stdDeviation="2"
              floodColor="#00BFFF"
              floodOpacity="0.7"
            />
          </filter>
        </defs>
        {/* 外层深灰蓝描边 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style35-text"
          fill="none"
          stroke={backgroundColor}
          strokeWidth={outerStrokeWidth}
          strokeLinejoin="round"
          strokeLinecap="round"
        >
          {realText}
        </text>
        {/* 内层深青描边 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style35-text"
          fill="none"
          stroke={strokeColor}
          strokeWidth={innerStrokeWidth}
          strokeLinejoin="round"
          strokeLinecap="round"
        >
          {realText}
        </text>
        {/* 主体青蓝阴影填充 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style35-text"
          fill={fillColor}
          filter={`url(#${shadowId})`}
        >
          {realText}
        </text>
      </svg>
    );
  },
);

Flower035.showName = '青蓝阴影';
Flower035.description = '青蓝阴影效果花字';
Flower035.key = 'Flower035';
