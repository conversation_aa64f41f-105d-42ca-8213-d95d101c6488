import React, { memo, useMemo } from 'react';
import { measureTextDimensions, parseStyle } from './util';

interface Flower012Props {
  text?: string|any;
  style?: React.CSSProperties;
  fillColor?: string;
  strokeColor?: string;
  backgroundColor?: string;
}

export const Flower012: any = memo(({ 
  text = "花字", 
  style = {},
  fillColor = "#FF0080",
  strokeColor = "#000000", 
  backgroundColor = "#FFFFFF"
}: Flower012Props) => {
  const {fontSize, fontFamily, fontWeight, letterSpacing} = parseStyle(style);
      let realText = (typeof text === 'string' ? text : text?.props?.text) || '';
      const textDimensions = useMemo(
        () =>
          measureTextDimensions({
            text: realText,
            fontFamily,
            fontSize,
            fontWeight,
          }),
        [realText, fontFamily, fontSize, fontWeight],
      );
  
  const svgWidth = textDimensions.width
  const svgHeight = textDimensions.height
  
  const centerX = svgWidth / 2;
  const centerY = svgHeight / 2;
  
  const outerStrokeWidth = fontSize * 0.10;
  const innerStrokeWidth = fontSize * 0.05;
  
  const baselineOffset = textDimensions.height * 0.35;
  const textY = centerY + baselineOffset;
  
  const gradientId = `rainbow12_${Math.random().toString(36).substr(2, 9)}`;
  
  return <svg style={{display: 'inline'}} 
    width={svgWidth} height={svgHeight} 
    viewBox={`0 0 ${svgWidth} ${svgHeight}`}
    fontFamily={fontFamily} fontSize={fontSize} fontWeight={fontWeight} letterSpacing={letterSpacing}
  >
    <defs>
        <linearGradient id={gradientId} x1="0%" y1="0%" x2="100%" y2="0%">
            <stop offset="0%" style={{stopColor:"#FF0000", stopOpacity:1}} />
            <stop offset="16.66%" style={{stopColor:"#FF8000", stopOpacity:1}} />
            <stop offset="33.33%" style={{stopColor:"#FFFF00", stopOpacity:1}} />
            <stop offset="50%" style={{stopColor:"#00FF00", stopOpacity:1}} />
            <stop offset="66.66%" style={{stopColor:"#0080FF", stopOpacity:1}} />
            <stop offset="83.33%" style={{stopColor:"#8000FF", stopOpacity:1}} />
            <stop offset="100%" style={{stopColor:"#FF0080", stopOpacity:1}} />
        </linearGradient>
    </defs>
    {/* 外层白色描边 */}
    <text x={centerX} y={textY} textAnchor="middle" className="svg-text style12-text"             
            fill="none" stroke={backgroundColor} strokeWidth={outerStrokeWidth}
            strokeLinejoin="round" strokeLinecap="round">{realText}</text>
    {/* 内层黑色描边 */}
    <text x={centerX} y={textY} textAnchor="middle" className="svg-text style12-text"             
            fill="none" stroke={strokeColor} strokeWidth={innerStrokeWidth}
            strokeLinejoin="round" strokeLinecap="round">{realText}</text>
    {/* 主体彩虹渐变填充 */}
    <text x={centerX} y={textY} textAnchor="middle" className="svg-text style12-text"             
            fill={`url(#${gradientId})`}>{realText}</text>
    </svg>
});

Flower012.showName = '彩虹渐变';
Flower012.description = '彩虹色渐变花字';
Flower012.key = 'Flower012';