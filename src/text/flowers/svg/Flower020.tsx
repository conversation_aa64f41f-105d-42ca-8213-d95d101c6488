import React, { memo, useMemo } from 'react';
import { measureTextDimensions, parseStyle } from './util';

interface Flower020Props {
  text?: string | any;
  style?: React.CSSProperties;
  fillColor?: string;
  strokeColor?: string;
  backgroundColor?: string;
}

export const Flower020: any = memo(
  ({
    text = '花字',
    style = {},
    fillColor = '#FF8C00',
    strokeColor = '#FF4500',
    backgroundColor = '#8B0000',
  }: Flower020Props) => {
    const { fontSize, fontFamily, fontWeight, letterSpacing } =
      parseStyle(style);
    let realText = (typeof text === 'string' ? text : text?.props?.text) || '';
    const textDimensions = useMemo(
      () =>
        measureTextDimensions({
          text: realText,
          fontFamily,
          fontSize,
          fontWeight,
        }),
      [realText, fontFamily, fontSize, fontWeight],
    );

    const svgWidth = textDimensions.width;
    const svgHeight = textDimensions.height;

    const centerX = svgWidth / 2;
    const centerY = svgHeight / 2;

    const outerStrokeWidth = fontSize * 0.08;
    const innerStrokeWidth = fontSize * 0.04;

    const baselineOffset = textDimensions.height * 0.35;
    const textY = centerY + baselineOffset;

    const filterId = `vibrant20_${Math.random().toString(36).substr(2, 9)}`;

    return (
      <svg
        style={{ display: 'inline' }}
        width={svgWidth}
        height={svgHeight}
        viewBox={`0 0 ${svgWidth} ${svgHeight}`}
        fontFamily={fontFamily}
        fontSize={fontSize}
        fontWeight={fontWeight}
        letterSpacing={letterSpacing}
      >
        <defs>
          <filter id={filterId}>
            <feGaussianBlur stdDeviation="2" result="coloredBlur" />
            <feMerge>
              <feMergeNode in="coloredBlur" />
              <feMergeNode in="SourceGraphic" />
            </feMerge>
          </filter>
        </defs>
        {/* 外层深红描边 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style20-text"
          fill="none"
          stroke={backgroundColor}
          strokeWidth={outerStrokeWidth}
          strokeLinejoin="round"
          strokeLinecap="round"
        >
          {realText}
        </text>
        {/* 内层橙红描边 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style20-text"
          fill="none"
          stroke={strokeColor}
          strokeWidth={innerStrokeWidth}
          strokeLinejoin="round"
          strokeLinecap="round"
        >
          {realText}
        </text>
        {/* 主体橙色填充 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style20-text"
          fill={fillColor}
          filter={`url(#${filterId})`}
        >
          {realText}
        </text>
      </svg>
    );
  },
);

Flower020.showName = '橙色发光';
Flower020.description = '橙色发光效果花字';
Flower020.key = 'Flower020';
