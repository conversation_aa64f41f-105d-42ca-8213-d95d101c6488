const cache = {} as Record<string, {width: number, height: number}>

let span: HTMLSpanElement | null = null

const canvas = document.createElement('canvas');
const ctx = canvas.getContext('2d')!;

export const measureTextBaselineOffset = ({text, fontFamily, fontSize, fontWeight}: {text: string, fontFamily: string, fontSize: number, fontWeight: string}) => {
  ctx.font = `${fontWeight} ${fontSize}px ${fontFamily}`; // 设置字体
  const metrics = ctx.measureText(text); // 测量文本
  if (metrics.actualBoundingBoxAscent != null && metrics.actualBoundingBoxDescent != null) {
    const ascent = metrics.actualBoundingBoxAscent; // 基线以上高度
    const descent = metrics.actualBoundingBoxDescent; // 基线以下高度
    const baselineOffset = (ascent - descent) / 2; // 计算偏移量
    return baselineOffset
  } else {
    console.warn('无metrics', metrics, text, fontFamily, fontSize, fontWeight)
    // 回退到经验值
    return null
  }
}

export const measureTextDimensions = ({text, fontFamily, fontSize, fontWeight, fontStyle}: {text: string, fontFamily: string, fontSize: number, fontWeight: string, fontStyle?: string}) => {
  const data = {
    text,
    fontFamily,
    fontSize,
    fontWeight,
    fontStyle,
    // letterSpacing,
  }
  const key = JSON.stringify(data)
  // if (cache[key]) {
  //   return cache[key]
  // }
  try {
    if (!span) {
      span = document.createElement('span')
      Object.assign(span.style, {
        // position: 'absolute', // 注意：不能使用绝对定位，否则宽高计算不准
        visibility: 'hidden',
        pointerEvents: 'none',
      })
      document.body.appendChild(span)
    }
    span.textContent = text
    span.style.fontFamily = fontFamily
    span.style.fontSize = `${fontSize}px`
    span.style.fontWeight = fontWeight
    span.style.fontStyle = fontStyle || 'normal'

    const isFontLoaded = document.fonts.check(`${fontWeight} ${fontSize}px ${fontFamily}`)
    
    const dimensions = {width: span.offsetWidth, height: span.offsetHeight}
    console.log({dimensions, fontSize, fontWeight, fontFamily, text})
    
    if (isFontLoaded) cache[key] = dimensions
    return dimensions;
  } catch (error) {
    console.error('文本测量失败：', error);
    // 重新抛出错误，不使用估算值
    throw error;
  }
};

  export const parseStyle = (style: React.CSSProperties) => {
    return {
        fontSize: parseFloat(style.fontSize as string),
        fontFamily: (style.fontFamily as string) || 'Source Han Bold',
        fontWeight: (style.fontWeight as string) || 'normal',
        letterSpacing: (style.letterSpacing as string) || '0px',
        fontStyle: (style.fontStyle as string) || 'normal',
    }
  }