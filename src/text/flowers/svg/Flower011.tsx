import React, { memo, useMemo } from 'react';
import { measureTextDimensions, parseStyle } from './util';

interface Flower011Props {
  text?: string | any;
  style?: React.CSSProperties;
  fillColor?: string;
  strokeColor?: string;
  backgroundColor?: string;
}

export const Flower011: any = memo(
  ({
    text = '花字',
    style = {},
    fillColor = '#00FF41',
    strokeColor = '#FFFFFF',
    backgroundColor = '#003300',
  }: Flower011Props) => {
    const { fontSize, fontFamily, fontWeight, letterSpacing } =
      parseStyle(style);
    let realText = (typeof text === 'string' ? text : text?.props?.text) || '';
    const textDimensions = useMemo(
      () =>
        measureTextDimensions({
          text: realText,
          fontFamily,
          fontSize,
          fontWeight,
        }),
      [realText, fontFamily, fontSize, fontWeight],
    );

    const svgWidth = textDimensions.width;
    const svgHeight = textDimensions.height;

    const centerX = svgWidth / 2;
    const centerY = svgHeight / 2;

    const outerStrokeWidth = fontSize * 0.15;
    const innerStrokeWidth = fontSize * 0.08;

    const baselineOffset = textDimensions.height * 0.35;
    const textY = centerY + baselineOffset;

    const filterId = `neonGlow11_${Math.random().toString(36).substr(2, 9)}`;

    return (
      <svg
        style={{ display: 'inline' }}
        width={svgWidth}
        height={svgHeight}
        viewBox={`0 0 ${svgWidth} ${svgHeight}`}
        fontFamily={fontFamily}
        fontSize={fontSize}
        fontWeight={fontWeight}
        letterSpacing={letterSpacing}
      >
        <defs>
          <filter id={filterId}>
            <feGaussianBlur stdDeviation="5" result="coloredBlur" />
            <feMerge>
              <feMergeNode in="coloredBlur" />
              <feMergeNode in="SourceGraphic" />
            </feMerge>
          </filter>
        </defs>
        {/* 外层深绿描边 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style11-text"
          fill="none"
          stroke={backgroundColor}
          strokeWidth={outerStrokeWidth}
          strokeLinejoin="round"
          strokeLinecap="round"
        >
          {realText}
        </text>
        {/* 内层白色描边 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style11-text"
          fill="none"
          stroke={strokeColor}
          strokeWidth={innerStrokeWidth}
          strokeLinejoin="round"
          strokeLinecap="round"
        >
          {realText}
        </text>
        {/* 主体霓虹绿填充 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style11-text"
          fill={fillColor}
          filter={`url(#${filterId})`}
        >
          {realText}
        </text>
      </svg>
    );
  },
);

Flower011.showName = '霓虹绿光';
Flower011.description = '霓虹绿色发光花字';
Flower011.key = 'Flower011';
