import React, { memo, useMemo } from 'react';
import { measureTextDimensions, parseStyle, measureTextBaselineOffset } from './util';

interface Flower114Props {
  text?: string | any;
  style?: React.CSSProperties;
  fillColor?: string;
  strokeColor?: string;
  backgroundColor?: string;
}

export const Flower114: any = memo(
  ({
    text = '花字',
    style = {},
    fillColor = '#FFA500',
    strokeColor = '#FFD700',
    backgroundColor = '#B8860B',
  }: Flower114Props) => {
    const { fontSize, fontFamily, fontWeight, letterSpacing, fontStyle } =
      parseStyle(style);

    let realText = (typeof text === 'string' ? text : text?.props?.text) || '';

    const textDimensions = useMemo(
      () => measureTextDimensions({ text: realText, fontFamily, fontSize, fontWeight, fontStyle }),
      [realText, fontFamily, fontSize, fontWeight, fontStyle],
    );

    const svgWidth = textDimensions.width;
    const svgHeight = textDimensions.height;

    const centerX = svgWidth / 2;
    const centerY = svgHeight / 2;

    const outerStrokeWidth = fontSize * 0.16;
    const innerStrokeWidth = fontSize * 0.08;

    const baselineOffset = measureTextBaselineOffset({text: realText, fontFamily, fontSize, fontWeight}) || textDimensions.height * 0.35;
    const textY = centerY + baselineOffset;

    const gradientId = `autumn114_${Math.random().toString(36).substr(2, 9)}`;
    const leafFallId = `leafFall114_${Math.random().toString(36).substr(2, 9)}`;

    return (
      <svg
        style={{ display: 'inline' }}
        width={svgWidth}
        height={svgHeight}
        viewBox={`0 0 ${svgWidth} ${svgHeight}`}
        fontFamily={fontFamily}
        fontSize={fontSize - outerStrokeWidth / 2}
        fontWeight={fontWeight}
        letterSpacing={letterSpacing}
      >
        <defs>
          <linearGradient id={gradientId} x1="0%" y1="0%" x2="100%" y2="0%">
            <stop offset="0%" style={{stopColor: "#FFFF99", stopOpacity: 1}} />
            <stop offset="20%" style={{stopColor: "#FFD700", stopOpacity: 1}} />
            <stop offset="40%" style={{stopColor: "#FFA500", stopOpacity: 1}} />
            <stop offset="60%" style={{stopColor: "#FF8C00", stopOpacity: 1}} />
            <stop offset="80%" style={{stopColor: "#FF6347", stopOpacity: 1}} />
            <stop offset="100%" style={{stopColor: "#CD853F", stopOpacity: 1}} />
          </linearGradient>
          <filter id={leafFallId}>
            <feDropShadow dx="1" dy="3" stdDeviation="2" floodColor="#8B4513" floodOpacity="0.6"/>
            <feGaussianBlur stdDeviation="0.5" result="blur"/>
          </filter>
        </defs>
        {/* 外层深棕描边 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style114-text"
          fill="none"
          stroke="#8B4513"
          strokeWidth={outerStrokeWidth}
          strokeLinejoin="round"
          strokeLinecap="round"
        >
          {realText}
        </text>
        {/* 内层金棕描边 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style114-text"
          fill="none"
          stroke={backgroundColor}
          strokeWidth={innerStrokeWidth}
          strokeLinejoin="round"
          strokeLinecap="round"
        >
          {realText}
        </text>
        {/* 主体秋叶渐变填充 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style114-text"
          fill={`url(#${gradientId})`}
          filter={`url(#${leafFallId})`}
        >
          {realText}
        </text>
      </svg>
    );
  },
);

Flower114.showName = '秋叶飘落';
Flower114.description = '秋天叶子飘落效果暖色渐变花字';
Flower114.key = 'Flower114';
