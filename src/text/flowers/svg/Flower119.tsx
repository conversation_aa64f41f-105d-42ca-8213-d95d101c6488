import React, { memo, useMemo } from 'react';
import { measureTextDimensions, parseStyle, measureTextBaselineOffset } from './util';

interface Flower119Props {
  text?: string | any;
  style?: React.CSSProperties;
  fillColor?: string;
  strokeColor?: string;
  backgroundColor?: string;
}

export const Flower119: any = memo(
  ({
    text = '花字',
    style = {},
    fillColor = '#FF69B4',
    strokeColor = '#FFB6C1',
    backgroundColor = '#C71585',
  }: Flower119Props) => {
    const { fontSize, fontFamily, fontWeight, letterSpacing, fontStyle } =
      parseStyle(style);

    let realText = (typeof text === 'string' ? text : text?.props?.text) || '';

    const textDimensions = useMemo(
      () => measureTextDimensions({ text: realText, fontFamily, fontSize, fontWeight, fontStyle }),
      [realText, fontFamily, fontSize, fontWeight, fontStyle],
    );

    const svgWidth = textDimensions.width;
    const svgHeight = textDimensions.height;

    const centerX = svgWidth / 2;
    const centerY = svgHeight / 2;

    const outerStrokeWidth = fontSize * 0.19;
    const middleStrokeWidth = fontSize * 0.13;
    const innerStrokeWidth = fontSize * 0.07;

    const baselineOffset = measureTextBaselineOffset({text: realText, fontFamily, fontSize, fontWeight}) || textDimensions.height * 0.35;
    const textY = centerY + baselineOffset;

    const gradientId = `neon119_${Math.random().toString(36).substr(2, 9)}`;
    const neonId = `neonGlow119_${Math.random().toString(36).substr(2, 9)}`;

    return (
      <svg
        style={{ display: 'inline' }}
        width={svgWidth}
        height={svgHeight}
        viewBox={`0 0 ${svgWidth} ${svgHeight}`}
        fontFamily={fontFamily}
        fontSize={fontSize - outerStrokeWidth / 2}
        fontWeight={fontWeight}
        letterSpacing={letterSpacing}
      >
        <defs>
          <linearGradient id={gradientId} x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" style={{stopColor: "#FFFF99", stopOpacity: 1}} />
            <stop offset="25%" style={{stopColor: "#FFB6C1", stopOpacity: 1}} />
            <stop offset="50%" style={{stopColor: "#FF69B4", stopOpacity: 1}} />
            <stop offset="75%" style={{stopColor: "#FF1493", stopOpacity: 1}} />
            <stop offset="100%" style={{stopColor: "#C71585", stopOpacity: 1}} />
          </linearGradient>
          <filter id={neonId}>
            <feGaussianBlur stdDeviation="4" result="coloredBlur"/>
            <feFlood floodColor="#FF69B4" floodOpacity="0.8"/>
            <feComposite in="SourceGraphic" in2="coloredBlur" operator="over"/>
            <feGaussianBlur stdDeviation="2" result="innerGlow"/>
            <feMerge>
              <feMergeNode in="coloredBlur"/>
              <feMergeNode in="innerGlow"/>
              <feMergeNode in="SourceGraphic"/>
            </feMerge>
          </filter>
        </defs>
        {/* 外层深粉描边 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style119-text"
          fill="none"
          stroke="#8B0040"
          strokeWidth={outerStrokeWidth}
          strokeLinejoin="round"
          strokeLinecap="round"
        >
          {realText}
        </text>
        {/* 中层玫红描边 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style119-text"
          fill="none"
          stroke={backgroundColor}
          strokeWidth={middleStrokeWidth}
          strokeLinejoin="round"
          strokeLinecap="round"
        >
          {realText}
        </text>
        {/* 内层浅粉描边 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style119-text"
          fill="none"
          stroke={strokeColor}
          strokeWidth={innerStrokeWidth}
          strokeLinejoin="round"
          strokeLinecap="round"
        >
          {realText}
        </text>
        {/* 主体霓虹粉渐变填充 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style119-text"
          fill={`url(#${gradientId})`}
          filter={`url(#${neonId})`}
        >
          {realText}
        </text>
      </svg>
    );
  },
);

Flower119.showName = '霓虹粉光';
Flower119.description = '霓虹粉色多层发光渐变效果花字';
Flower119.key = 'Flower119';
