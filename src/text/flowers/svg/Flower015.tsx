import React, { memo, useMemo } from 'react';
import { measureTextDimensions, parseStyle } from './util';

interface Flower015Props {
  text?: string|any;
  style?: React.CSSProperties;
  fillColor?: string;
  strokeColor?: string;
  backgroundColor?: string;
}

export const Flower015: any = memo(({ 
  text = "花字", 
  style = {},
  fillColor = "#FF4500",
  strokeColor = "#8B0000", 
  backgroundColor = "#FFD700"
}: Flower015Props) => {
  const {fontSize, fontFamily, fontWeight, letterSpacing} = parseStyle(style);
     let realText = (typeof text === 'string' ? text : text?.props?.text) || '';
     const textDimensions = useMemo(
       () =>
         measureTextDimensions({
           text: realText,
           fontFamily,
           fontSize,
           fontWeight,
         }),
       [realText, fontFamily, fontSize, fontWeight],
     );
  
  const svgWidth = textDimensions.width
  const svgHeight = textDimensions.height
  
  const centerX = svgWidth / 2;
  const centerY = svgHeight / 2;
  
  const strokeWidth = fontSize * 0.08;
  
  const baselineOffset = textDimensions.height * 0.35;
  const textY = centerY + baselineOffset;
  
  const gradientId = `flame15_${Math.random().toString(36).substr(2, 9)}`;
  const filterId = `fire15_${Math.random().toString(36).substr(2, 9)}`;
  
  return <svg style={{display: 'inline'}} 
    width={svgWidth} height={svgHeight} 
    viewBox={`0 0 ${svgWidth} ${svgHeight}`}
    fontFamily={fontFamily} fontSize={fontSize} fontWeight={fontWeight} letterSpacing={letterSpacing}
  >
    <defs>
        <linearGradient id={gradientId} x1="0%" y1="0%" x2="0%" y2="100%">
            <stop offset="0%" style={{stopColor:"#FFD700", stopOpacity:1}} />
            <stop offset="30%" style={{stopColor:"#FF8C00", stopOpacity:1}} />
            <stop offset="70%" style={{stopColor:"#FF4500", stopOpacity:1}} />
            <stop offset="100%" style={{stopColor:"#DC143C", stopOpacity:1}} />
        </linearGradient>
        <filter id={filterId}>
            <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
            <feMerge> 
                <feMergeNode in="coloredBlur"/>
                <feMergeNode in="SourceGraphic"/>
            </feMerge>
        </filter>
    </defs>
    {/* 深红色描边 */}
    <text x={centerX} y={textY} textAnchor="middle" className="svg-text style15-text"             
            fill="none" stroke={strokeColor} strokeWidth={strokeWidth}
            strokeLinejoin="round" strokeLinecap="round">{realText}</text>
    {/* 火焰垂直渐变填充 */}
    <text x={centerX} y={textY} textAnchor="middle" className="svg-text style15-text"             
            fill={`url(#${gradientId})`} filter={`url(#${filterId})`}>{realText}</text>
    </svg>
});

Flower015.showName = '火焰垂直渐变';
Flower015.description = '火焰垂直渐变花字';
Flower015.key = 'Flower015'; 