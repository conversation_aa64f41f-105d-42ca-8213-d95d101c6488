import React, { memo, useMemo } from 'react';
import { measureTextDimensions, parseStyle, measureTextBaselineOffset } from './util';

interface Flower130Props {
  text?: string | any;
  style?: React.CSSProperties;
  fillColor?: string;
  strokeColor?: string;
  backgroundColor?: string;
}

export const Flower130: any = memo(
  ({
    text = '花字',
    style = {},
    fillColor = '#32CD32',
    strokeColor = '#FFFFFF',
    backgroundColor = '#228B22',
  }: Flower130Props) => {
    const { fontSize, fontFamily, fontWeight, letterSpacing, fontStyle } =
      parseStyle(style);

    let realText = (typeof text === 'string' ? text : text?.props?.text) || '';

    const textDimensions = useMemo(
      () => measureTextDimensions({ text: realText, fontFamily, fontSize, fontWeight, fontStyle }),
      [realText, fontFamily, fontSize, fontWeight, fontStyle],
    );

    // 为叶子效果增加padding
    const padding = fontSize * 0.5;
    const svgWidth = textDimensions.width + padding * 2;
    const svgHeight = textDimensions.height + padding * 2;

    const centerX = svgWidth / 2;
    const centerY = svgHeight / 2;

    const outerStrokeWidth = fontSize * 0.13;
    const innerStrokeWidth = fontSize * 0.07;

    const baselineOffset = measureTextBaselineOffset({text: realText, fontFamily, fontSize, fontWeight}) || textDimensions.height * 0.35;
    const textY = centerY + baselineOffset;

    const gradientId = `nature130_${Math.random().toString(36).substr(2, 9)}`;
    const leafId = `leaf130_${Math.random().toString(36).substr(2, 9)}`;
    const shadowId = `shadow130_${Math.random().toString(36).substr(2, 9)}`;

    // 叶子位置
    const leafPositions = [
      { x: centerX - textDimensions.width * 0.4, y: centerY - textDimensions.height * 0.2, rotation: -30 },
      { x: centerX + textDimensions.width * 0.4, y: centerY - textDimensions.height * 0.2, rotation: 30 },
      { x: centerX - textDimensions.width * 0.3, y: centerY + textDimensions.height * 0.3, rotation: -60 },
      { x: centerX + textDimensions.width * 0.3, y: centerY + textDimensions.height * 0.3, rotation: 60 },
    ];

    return (
      <svg
        style={{ display: 'inline' }}
        width={svgWidth}
        height={svgHeight}
        viewBox={`0 0 ${svgWidth} ${svgHeight}`}
        fontFamily={fontFamily}
        fontSize={fontSize - outerStrokeWidth / 2}
        fontWeight={fontWeight}
        letterSpacing={letterSpacing}
      >
        <defs>
          {/* 叶子渐变 */}
          <linearGradient id={leafId} x1="0%" y1="0%" x2="0%" y2="100%">
            <stop offset="0%" style={{stopColor: "#90EE90", stopOpacity: 0.8}} />
            <stop offset="50%" style={{stopColor: "#32CD32", stopOpacity: 0.6}} />
            <stop offset="100%" style={{stopColor: "#228B22", stopOpacity: 0.4}} />
          </linearGradient>
          {/* 文字垂直自然渐变 */}
          <linearGradient id={gradientId} x1="0%" y1="0%" x2="0%" y2="100%">
            <stop offset="0%" style={{stopColor: "#F0FFF0", stopOpacity: 1}} />
            <stop offset="25%" style={{stopColor: "#90EE90", stopOpacity: 1}} />
            <stop offset="60%" style={{stopColor: "#32CD32", stopOpacity: 1}} />
            <stop offset="100%" style={{stopColor: "#006400", stopOpacity: 1}} />
          </linearGradient>
          {/* 阴影滤镜 */}
          <filter id={shadowId}>
            <feDropShadow dx="1" dy="2" stdDeviation="1" floodColor="#006400" floodOpacity="0.6"/>
          </filter>
        </defs>
        
        {/* 叶子背景 */}
        {leafPositions.map((leaf, index) => (
          <ellipse
            key={index}
            cx={leaf.x}
            cy={leaf.y}
            rx={fontSize * 0.12}
            ry={fontSize * 0.2}
            fill={`url(#${leafId})`}
            opacity="0.7"
            transform={`rotate(${leaf.rotation} ${leaf.x} ${leaf.y})`}
          />
        ))}
        
        {/* 中心圆形背景 */}
        <circle
          cx={centerX}
          cy={centerY}
          r={Math.max(textDimensions.width, textDimensions.height) * 0.4}
          fill={`url(#${leafId})`}
          opacity="0.3"
        />
        
        {/* 外层深绿描边 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style130-text"
          fill="none"
          stroke="#004000"
          strokeWidth={outerStrokeWidth}
          strokeLinejoin="round"
          strokeLinecap="round"
        >
          {realText}
        </text>
        
        {/* 内层白色描边 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style130-text"
          fill="none"
          stroke={strokeColor}
          strokeWidth={innerStrokeWidth}
          strokeLinejoin="round"
          strokeLinecap="round"
        >
          {realText}
        </text>
        
        {/* 主体自然渐变填充 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style130-text"
          fill={`url(#${gradientId})`}
          filter={`url(#${shadowId})`}
        >
          {realText}
        </text>
      </svg>
    );
  },
);

Flower130.showName = '自然叶影';
Flower130.description = '自然叶子背景单字清晰字幕特效';
Flower130.key = 'Flower130';
