import React, { memo, useMemo } from 'react';
import { measureTextDimensions, parseStyle } from './util';

interface Flower052Props {
  text?: string | any;
  style?: React.CSSProperties;
  fillColor?: string;
  strokeColor?: string;
  backgroundColor?: string;
}

export const Flower052: any = memo(
  ({
    text = '花字',
    style = {},
    fillColor = '#8B00FF',
    strokeColor = '#6A0DAD',
    backgroundColor = '#4B0082',
  }: Flower052Props) => {
    const { fontSize, fontFamily, fontWeight, letterSpacing } =
      parseStyle(style);
    let realText = (typeof text === 'string' ? text : text?.props?.text) || '';
    const textDimensions = useMemo(
      () =>
        measureTextDimensions({
          text: realText,
          fontFamily,
          fontSize,
          fontWeight,
        }),
      [realText, fontFamily, fontSize, fontWeight],
    );
    const svgWidth = textDimensions.width;
    const svgHeight = textDimensions.height;

    const centerX = svgWidth / 2;
    const centerY = svgHeight / 2;

    const outerStrokeWidth = fontSize * 0.1;
    const innerStrokeWidth = fontSize * 0.05;

    const baselineOffset = textDimensions.height * 0.35;
    const textY = centerY + baselineOffset;

    const glowId = `violet52_${Math.random().toString(36).substr(2, 9)}`;

    return (
      <svg
        style={{ display: 'inline' }}
        width={svgWidth}
        height={svgHeight}
        viewBox={`0 0 ${svgWidth} ${svgHeight}`}
        fontFamily={fontFamily}
        fontSize={fontSize}
        fontWeight={fontWeight}
        letterSpacing={letterSpacing}
      >
        <defs>
          <filter id={glowId}>
            <feGaussianBlur stdDeviation="4" result="coloredBlur" />
            <feMerge>
              <feMergeNode in="coloredBlur" />
              <feMergeNode in="SourceGraphic" />
            </feMerge>
          </filter>
        </defs>
        {/* 外层靛蓝描边 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style52-text"
          fill="none"
          stroke={backgroundColor}
          strokeWidth={outerStrokeWidth}
          strokeLinejoin="round"
          strokeLinecap="round"
        >
          {realText}
        </text>
        {/* 内层紫色描边 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style52-text"
          fill="none"
          stroke={strokeColor}
          strokeWidth={innerStrokeWidth}
          strokeLinejoin="round"
          strokeLinecap="round"
        >
          {realText}
        </text>
        {/* 主体紫罗兰发光填充 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style52-text"
          fill={fillColor}
          filter={`url(#${glowId})`}
        >
          {realText}
        </text>
      </svg>
    );
  },
);

Flower052.showName = '紫罗兰发光';
Flower052.description = '紫罗兰发光效果花字';
Flower052.key = 'Flower052';
