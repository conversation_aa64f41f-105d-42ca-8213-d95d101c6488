import React, { memo, useMemo } from 'react';
import { measureTextDimensions, parseStyle } from './util';

interface Flower002Props {
  text?: string | any;
  style?: React.CSSProperties;
  fillColor?: string;
  strokeColor?: string;
  backgroundColor?: string;
}

export const Flower002: any = memo(({ 
  text = "花字", 
  style = {},
  fillColor = "#00BFFF",
  strokeColor = "#FFFFFF", 
  backgroundColor = "#003366"
}: Flower002Props) => {
  // 从style中获取字体属性
  const {fontSize, fontFamily, fontWeight, letterSpacing} = parseStyle(style);

  let realText = (typeof text === 'string' ? text : text?.props?.text) || '';

  const textDimensions = useMemo(() => measureTextDimensions({text: realText, fontFamily, fontSize, fontWeight}), [realText, fontFamily, fontSize, fontWeight]);
  
  // 计算SVG尺寸，添加合适的padding
  const svgWidth = textDimensions.width
  const svgHeight = textDimensions.height
  
  // 计算文本居中位置
  const centerX = svgWidth / 2;
  const centerY = svgHeight / 2;
  
  // 计算描边宽度，根据字体大小动态调整
  const outerStrokeWidth = fontSize * 0.10;
  const innerStrokeWidth = fontSize * 0.04;
  
  // 计算dominantBaseline的偏移量，确保文本垂直居中
  const baselineOffset = textDimensions.height * 0.35;
  const textY = centerY + baselineOffset;

  return <svg style={{display: 'inline'}} 
    width={svgWidth} height={svgHeight} 
    viewBox={`0 0 ${svgWidth} ${svgHeight}`}
    fontFamily={fontFamily} fontSize={fontSize} fontWeight={fontWeight} letterSpacing={letterSpacing}
  >
    <defs>
        <filter id="glow2">
            <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
            <feMerge> 
                <feMergeNode in="coloredBlur"/>
                <feMergeNode in="SourceGraphic"/>
            </feMerge>
        </filter>
    </defs>
    {/* 外层深蓝描边 */}
    <text x={centerX} y={textY} textAnchor="middle" className="svg-text style2-text"
            fill="none" stroke={backgroundColor} strokeWidth={outerStrokeWidth}
            strokeLinejoin="round" strokeLinecap="round">{realText}</text>
    {/* 内层白色描边 */}
    <text x={centerX} y={textY} textAnchor="middle" className="svg-text style2-text"
            fill="none" stroke={strokeColor} strokeWidth={innerStrokeWidth}
            strokeLinejoin="round" strokeLinecap="round">{realText}</text>
    {/* 主体蓝色填充 */}
    <text x={centerX} y={textY} textAnchor="middle" className="svg-text style2-text"
            fill={fillColor} filter="url(#glow2)">{realText}</text>
    </svg>
});

Flower002.showName = '蓝色科技';
Flower002.description = '蓝色科技风花字';
Flower002.key = 'Flower002'; 