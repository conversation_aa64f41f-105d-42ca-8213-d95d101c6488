import React, { memo, useMemo } from 'react';
import { measureTextDimensions, parseStyle } from './util';

interface Flower006Props {
  text?: string|any;
  style?: React.CSSProperties;
  fillColor?: string;
  strokeColor?: string;
  backgroundColor?: string;
}

export const Flower006: any = memo(({ 
  text = "花字", 
  style = {},
  fillColor = "#FF0000",
  strokeColor = "#000000", 
  backgroundColor = "#FFFFFF"
}: Flower006Props) => {
  // 从style中获取字体属性
  const {fontSize, fontFamily, fontWeight, letterSpacing} = parseStyle(style);
     let realText = (typeof text === 'string' ? text : text?.props?.text) || '';
     const textDimensions = useMemo(
       () =>
         measureTextDimensions({
           text: realText,
           fontFamily,
           fontSize,
           fontWeight,
         }),
       [realText, fontFamily, fontSize, fontWeight],
     );
  
  // 计算SVG尺寸，添加合适的padding
  const svgWidth = textDimensions.width
  const svgHeight = textDimensions.height
  
  // 计算文本居中位置
  const centerX = svgWidth / 2;
  const centerY = svgHeight / 2;
  
  // 计算描边宽度，根据字体大小动态调整
  const strokeWidth = fontSize * 0.08;
  
  // 计算dominantBaseline的偏移量，确保文本垂直居中
  const baselineOffset = textDimensions.height * 0.35;
  const textY = centerY + baselineOffset;

  // 生成唯一ID避免冲突
  const gradientId = `rainbowGrad-${Math.random().toString(36).substr(2, 9)}`;

  return <svg style={{display: 'inline'}} 
    width={svgWidth} height={svgHeight} 
    viewBox={`0 0 ${svgWidth} ${svgHeight}`}
    fontFamily={fontFamily} fontSize={fontSize} fontWeight={fontWeight} letterSpacing={letterSpacing}
  >
    <defs>
        <linearGradient id={gradientId} x1="0%" y1="0%" x2="100%" y2="0%">
            <stop offset="0%" style={{stopColor:"#FF0000", stopOpacity:1}} />
            <stop offset="16.66%" style={{stopColor:"#FF8C00", stopOpacity:1}} />
            <stop offset="33.33%" style={{stopColor:"#FFD700", stopOpacity:1}} />
            <stop offset="50%" style={{stopColor:"#00FF00", stopOpacity:1}} />
            <stop offset="66.66%" style={{stopColor:"#00BFFF", stopOpacity:1}} />
            <stop offset="83.33%" style={{stopColor:"#4169E1", stopOpacity:1}} />
            <stop offset="100%" style={{stopColor:"#8A2BE2", stopOpacity:1}} />
        </linearGradient>
    </defs>
    {/* 黑色描边 */}
    <text x={centerX} y={textY} textAnchor="middle" className="svg-text style6-text"             
            fill="none" stroke={strokeColor} strokeWidth={strokeWidth}
            strokeLinejoin="round" strokeLinecap="round">{realText}</text>
    {/* 彩虹渐变填充 */}
    <text x={centerX} y={textY} textAnchor="middle" className="svg-text style6-text"             
            fill={`url(#${gradientId})`}>{realText}</text>
    </svg>
});

Flower006.showName = '彩虹渐变';
Flower006.description = '七彩彩虹渐变花字';
Flower006.key = 'Flower006'; 