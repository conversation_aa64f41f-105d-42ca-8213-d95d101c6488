import React, { memo, useMemo } from 'react';
import { measureTextDimensions, parseStyle, measureTextBaselineOffset } from './util';

interface Flower129Props {
  text?: string | any;
  style?: React.CSSProperties;
  fillColor?: string;
  strokeColor?: string;
  backgroundColor?: string;
}

export const Flower129: any = memo(
  ({
    text = '花字',
    style = {},
    fillColor = '#FF69B4',
    strokeColor = '#FFFFFF',
    backgroundColor = '#8B008B',
  }: Flower129Props) => {
    const { fontSize, fontFamily, fontWeight, letterSpacing, fontStyle } =
      parseStyle(style);

    let realText = (typeof text === 'string' ? text : text?.props?.text) || '';

    const textDimensions = useMemo(
      () => measureTextDimensions({ text: realText, fontFamily, fontSize, fontWeight, fontStyle }),
      [realText, fontFamily, fontSize, fontWeight, fontStyle],
    );

    // 为樱花效果增加padding
    const padding = fontSize * 0.6;
    const svgWidth = textDimensions.width + padding * 2;
    const svgHeight = textDimensions.height + padding * 2;

    const centerX = svgWidth / 2;
    const centerY = svgHeight / 2;

    const outerStrokeWidth = fontSize * 0.12;
    const innerStrokeWidth = fontSize * 0.06;

    const baselineOffset = measureTextBaselineOffset({text: realText, fontFamily, fontSize, fontWeight}) || textDimensions.height * 0.35;
    const textY = centerY + baselineOffset;

    const gradientId = `sakura129_${Math.random().toString(36).substr(2, 9)}`;
    const petalId = `petal129_${Math.random().toString(36).substr(2, 9)}`;
    const bloomId = `bloom129_${Math.random().toString(36).substr(2, 9)}`;

    // 樱花花瓣位置
    const petalPositions = [
      { x: centerX - textDimensions.width * 0.4, y: centerY - textDimensions.height * 0.3 },
      { x: centerX + textDimensions.width * 0.4, y: centerY - textDimensions.height * 0.3 },
      { x: centerX - textDimensions.width * 0.5, y: centerY + textDimensions.height * 0.2 },
      { x: centerX + textDimensions.width * 0.5, y: centerY + textDimensions.height * 0.2 },
      { x: centerX, y: centerY - textDimensions.height * 0.5 },
    ];

    return (
      <svg
        style={{ display: 'inline' }}
        width={svgWidth}
        height={svgHeight}
        viewBox={`0 0 ${svgWidth} ${svgHeight}`}
        fontFamily={fontFamily}
        fontSize={fontSize - outerStrokeWidth / 2}
        fontWeight={fontWeight}
        letterSpacing={letterSpacing}
      >
        <defs>
          {/* 樱花花瓣渐变 */}
          <radialGradient id={petalId} cx="50%" cy="50%" r="60%">
            <stop offset="0%" style={{stopColor: "#FFE4E1", stopOpacity: 0.8}} />
            <stop offset="50%" style={{stopColor: "#FFB6C1", stopOpacity: 0.6}} />
            <stop offset="100%" style={{stopColor: "#FF69B4", stopOpacity: 0.3}} />
          </radialGradient>
          {/* 文字垂直樱花渐变 */}
          <linearGradient id={gradientId} x1="0%" y1="0%" x2="0%" y2="100%">
            <stop offset="0%" style={{stopColor: "#FFF0F5", stopOpacity: 1}} />
            <stop offset="30%" style={{stopColor: "#FFE4E1", stopOpacity: 1}} />
            <stop offset="60%" style={{stopColor: "#FF69B4", stopOpacity: 1}} />
            <stop offset="100%" style={{stopColor: "#C71585", stopOpacity: 1}} />
          </linearGradient>
          {/* 绽放滤镜 */}
          <filter id={bloomId}>
            <feGaussianBlur stdDeviation="1.5" result="blur"/>
            <feFlood floodColor="#FF69B4" floodOpacity="0.5"/>
            <feComposite in="SourceGraphic" in2="blur" operator="screen"/>
          </filter>
        </defs>
        
        {/* 樱花花瓣背景 */}
        {petalPositions.map((pos, index) => (
          <ellipse
            key={index}
            cx={pos.x}
            cy={pos.y}
            rx={fontSize * 0.15}
            ry={fontSize * 0.25}
            fill={`url(#${petalId})`}
            opacity="0.7"
            transform={`rotate(${index * 72} ${pos.x} ${pos.y})`}
          />
        ))}
        
        {/* 外层深紫描边 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style129-text"
          fill="none"
          stroke="#4B0040"
          strokeWidth={outerStrokeWidth}
          strokeLinejoin="round"
          strokeLinecap="round"
        >
          {realText}
        </text>
        
        {/* 内层白色描边 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style129-text"
          fill="none"
          stroke={strokeColor}
          strokeWidth={innerStrokeWidth}
          strokeLinejoin="round"
          strokeLinecap="round"
        >
          {realText}
        </text>
        
        {/* 主体樱花渐变填充 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style129-text"
          fill={`url(#${gradientId})`}
          filter={`url(#${bloomId})`}
        >
          {realText}
        </text>
      </svg>
    );
  },
);

Flower129.showName = '樱花飞舞';
Flower129.description = '樱花花瓣背景单字清晰字幕特效';
Flower129.key = 'Flower129';
