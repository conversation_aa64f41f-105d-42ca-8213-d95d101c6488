import React, { memo, useMemo } from 'react';
import { measureTextDimensions, parseStyle } from './util';

interface Flower028Props {
  text?: string|any;
  style?: React.CSSProperties;
  fillColor?: string;
  strokeColor?: string;
  backgroundColor?: string;
}

export const Flower028: any = memo(({ 
  text = "花字", 
  style = {},
  fillColor = "#9ACD32",
  strokeColor = "#556B2F", 
  backgroundColor = "#2F4F2F"
}: Flower028Props) => {
  const {fontSize, fontFamily, fontWeight, letterSpacing} = parseStyle(style);
       let realText = (typeof text === 'string' ? text : text?.props?.text) || '';
       const textDimensions = useMemo(
         () =>
           measureTextDimensions({
             text: realText,
             fontFamily,
             fontSize,
             fontWeight,
           }),
         [realText, fontFamily, fontSize, fontWeight],
       );
  
  const svgWidth = textDimensions.width
  const svgHeight = textDimensions.height
  
  const centerX = svgWidth / 2;
  const centerY = svgHeight / 2;
  
  const outerStrokeWidth = fontSize * 0.10;
  const innerStrokeWidth = fontSize * 0.05;
  
  const baselineOffset = textDimensions.height * 0.35;
  const textY = centerY + baselineOffset;
  
  const gradientId = `yellowGreen28_${Math.random().toString(36).substr(2, 9)}`;
  
  return <svg style={{display: 'inline'}} 
    width={svgWidth} height={svgHeight} 
    viewBox={`0 0 ${svgWidth} ${svgHeight}`}
    fontFamily={fontFamily} fontSize={fontSize} fontWeight={fontWeight} letterSpacing={letterSpacing}
  >
    <defs>
        <linearGradient id={gradientId} x1="0%" y1="0%" x2="0%" y2="100%">
            <stop offset="0%" style={{stopColor:"#ADFF2F", stopOpacity:1}} />
            <stop offset="40%" style={{stopColor:"#9ACD32", stopOpacity:1}} />
            <stop offset="80%" style={{stopColor:"#6B8E23", stopOpacity:1}} />
            <stop offset="100%" style={{stopColor:"#556B2F", stopOpacity:1}} />
        </linearGradient>
    </defs>
    {/* 外层深绿描边 */}
    <text x={centerX} y={textY} textAnchor="middle" className="svg-text style28-text"             
            fill="none" stroke={backgroundColor} strokeWidth={outerStrokeWidth}
            strokeLinejoin="round" strokeLinecap="round">{realText}</text>
    {/* 内层橄榄绿描边 */}
    <text x={centerX} y={textY} textAnchor="middle" className="svg-text style28-text"             
            fill="none" stroke={strokeColor} strokeWidth={innerStrokeWidth}
            strokeLinejoin="round" strokeLinecap="round">{realText}</text>
    {/* 主体黄绿垂直渐变填充 */}
    <text x={centerX} y={textY} textAnchor="middle" className="svg-text style28-text"             
            fill={`url(#${gradientId})`}>{realText}</text>
    </svg>
});

Flower028.showName = '黄绿垂直渐变';
Flower028.description = '黄绿垂直渐变花字';
Flower028.key = 'Flower028'; 