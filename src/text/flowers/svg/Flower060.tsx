import React, { memo, useMemo } from 'react';
import { measureTextDimensions, parseStyle } from './util';

interface Flower060Props {
  text?: string | any;
  style?: React.CSSProperties;
  fillColor?: string;
  strokeColor?: string;
  backgroundColor?: string;
}

export const Flower060: any = memo(
  ({
    text = '花字',
    style = {},
    fillColor = '#FF0000',
    strokeColor = '#8B008B',
    backgroundColor = '#2F2F2F',
  }: Flower060Props) => {
    const { fontSize, fontFamily, fontWeight, letterSpacing } =
      parseStyle(style);
    let realText = (typeof text === 'string' ? text : text?.props?.text) || '';
    const textDimensions = useMemo(
      () =>
        measureTextDimensions({
          text: realText,
          fontFamily,
          fontSize,
          fontWeight,
        }),
      [realText, fontFamily, fontSize, fontWeight],
    );
    const svgWidth = textDimensions.width;
    const svgHeight = textDimensions.height;

    const centerX = svgWidth / 2;
    const centerY = svgHeight / 2;

    const outerStrokeWidth = fontSize * 0.1;
    const innerStrokeWidth = fontSize * 0.05;

    const baselineOffset = textDimensions.height * 0.35;
    const textY = centerY + baselineOffset;

    const gradientId = `rainbow60_${Math.random().toString(36).substr(2, 9)}`;

    return (
      <svg
        style={{ display: 'inline' }}
        width={svgWidth}
        height={svgHeight}
        viewBox={`0 0 ${svgWidth} ${svgHeight}`}
        fontFamily={fontFamily}
        fontSize={fontSize}
        fontWeight={fontWeight}
        letterSpacing={letterSpacing}
      >
        <defs>
          <linearGradient id={gradientId} x1="0%" y1="0%" x2="0%" y2="100%">
            <stop
              offset="0%"
              style={{ stopColor: '#FF0000', stopOpacity: 1 }}
            />
            <stop
              offset="16%"
              style={{ stopColor: '#FF8C00', stopOpacity: 1 }}
            />
            <stop
              offset="33%"
              style={{ stopColor: '#FFD700', stopOpacity: 1 }}
            />
            <stop
              offset="50%"
              style={{ stopColor: '#32CD32', stopOpacity: 1 }}
            />
            <stop
              offset="66%"
              style={{ stopColor: '#00BFFF', stopOpacity: 1 }}
            />
            <stop
              offset="83%"
              style={{ stopColor: '#4169E1', stopOpacity: 1 }}
            />
            <stop
              offset="100%"
              style={{ stopColor: '#8B008B', stopOpacity: 1 }}
            />
          </linearGradient>
        </defs>
        {/* 外层深灰描边 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style60-text"
          fill="none"
          stroke={backgroundColor}
          strokeWidth={outerStrokeWidth}
          strokeLinejoin="round"
          strokeLinecap="round"
        >
          {realText}
        </text>
        {/* 内层深紫描边 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style60-text"
          fill="none"
          stroke={strokeColor}
          strokeWidth={innerStrokeWidth}
          strokeLinejoin="round"
          strokeLinecap="round"
        >
          {realText}
        </text>
        {/* 主体彩虹垂直渐变填充 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style60-text"
          fill={`url(#${gradientId})`}
        >
          {realText}
        </text>
      </svg>
    );
  },
);

Flower060.showName = '彩虹垂直渐变';
Flower060.description = '彩虹垂直渐变花字';
Flower060.key = 'Flower060';
