import React, { memo, useMemo } from 'react';
import { measureTextDimensions, parseStyle } from './util';

interface Flower066Props {
  text?: string | any;
  style?: React.CSSProperties;
  fillColor?: string;
  strokeColor?: string;
  backgroundColor?: string;
}

export const Flower066: any = memo(({ 
  text = "花字", 
  style = {},
  fillColor = "#EA580C",
  strokeColor = "#DC2626", 
  backgroundColor = "#991B1B"
}: Flower066Props) => {
  const {fontSize, fontFamily, fontWeight, letterSpacing} = parseStyle(style);

  let realText = (typeof text === 'string' ? text : text?.props?.text) || '';

  const textDimensions = useMemo(() => measureTextDimensions({text: realText, fontFamily, fontSize, fontWeight}), [realText, fontFamily, fontSize, fontWeight]);
  
  const svgWidth = textDimensions.width
  const svgHeight = textDimensions.height
  
  const centerX = svgWidth / 2;
  const centerY = svgHeight / 2;
  
  const outerStrokeWidth = fontSize * 0.10;
  const innerStrokeWidth = fontSize * 0.05;
  
  const baselineOffset = textDimensions.height * 0.35;
  const textY = centerY + baselineOffset;
  
  const gradientId = `orange66_${Math.random().toString(36).substr(2, 9)}`;
  
  return <svg style={{display: 'inline'}} 
    width={svgWidth} height={svgHeight} 
    viewBox={`0 0 ${svgWidth} ${svgHeight}`}
    fontFamily={fontFamily} fontSize={fontSize} fontWeight={fontWeight} letterSpacing={letterSpacing}
  >
    <defs>
        <linearGradient id={gradientId} x1="0%" y1="0%" x2="0%" y2="100%">
            <stop offset="0%" style={{stopColor:"#FB923C", stopOpacity:1}} />
            <stop offset="50%" style={{stopColor:"#EA580C", stopOpacity:1}} />
            <stop offset="100%" style={{stopColor:"#DC2626", stopOpacity:1}} />
        </linearGradient>
    </defs>
    {/* 外层深红描边 */}
    <text x={centerX} y={textY} textAnchor="middle" className="svg-text style66-text"             
            fill="none" stroke={backgroundColor} strokeWidth={outerStrokeWidth}
            strokeLinejoin="round" strokeLinecap="round">{realText}</text>
    {/* 内层红色描边 */}
    <text x={centerX} y={textY} textAnchor="middle" className="svg-text style66-text"             
            fill="none" stroke={strokeColor} strokeWidth={innerStrokeWidth}
            strokeLinejoin="round" strokeLinecap="round">{realText}</text>
    {/* 主体橙色渐变填充 */}
    <text x={centerX} y={textY} textAnchor="middle" className="svg-text style66-text"             
            fill={`url(#${gradientId})`}>{realText}</text>
    </svg>
});

Flower066.showName = '橙色渐变';
Flower066.description = '橙色系自然渐变花字';
Flower066.key = 'Flower066'; 