import React, { memo, useMemo } from 'react';
import { measureTextDimensions, parseStyle } from './util';

interface Flower061Props {
  text?: string | any;
  style?: React.CSSProperties;
  fillColor?: string;
  strokeColor?: string;
  backgroundColor?: string;
}

export const Flower061: any = memo(({ 
  text = "花字", 
  style = {},
  fillColor = "#1E3A8A",
  strokeColor = "#1E40AF", 
  backgroundColor = "#1E293B"
}: Flower061Props) => {
  const {fontSize, fontFamily, fontWeight, letterSpacing} = parseStyle(style);
  
  let realText = (typeof text === 'string' ? text : text?.props?.text) || '';
  
  const textDimensions = useMemo(() => measureTextDimensions({text: realText, fontFamily, fontSize, fontWeight}), [realText, fontFamily, fontSize, fontWeight]);
  
  const svgWidth = textDimensions.width
  const svgHeight = textDimensions.height
  
  const centerX = svgWidth / 2;
  const centerY = svgHeight / 2;
  
  const outerStrokeWidth = fontSize * 0.10;
  const innerStrokeWidth = fontSize * 0.05;
  
  const baselineOffset = textDimensions.height * 0.35;
  const textY = centerY + baselineOffset;
  
  const glowId = `deepBlue61_${Math.random().toString(36).substr(2, 9)}`;
  
  return <svg style={{display: 'inline'}} 
    width={svgWidth} height={svgHeight} 
    viewBox={`0 0 ${svgWidth} ${svgHeight}`}
    fontFamily={fontFamily} fontSize={fontSize} fontWeight={fontWeight} letterSpacing={letterSpacing}
  >
    <defs>
        <filter id={glowId}>
            <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
            <feMerge> 
                <feMergeNode in="coloredBlur"/>
                <feMergeNode in="SourceGraphic"/>
            </feMerge>
        </filter>
    </defs>
    {/* 外层深灰蓝描边 */}
    <text x={centerX} y={textY} textAnchor="middle" className="svg-text style61-text"             
            fill="none" stroke={backgroundColor} strokeWidth={outerStrokeWidth}
            strokeLinejoin="round" strokeLinecap="round">{realText}</text>
    {/* 内层蓝色描边 */}
    <text x={centerX} y={textY} textAnchor="middle" className="svg-text style61-text"             
            fill="none" stroke={strokeColor} strokeWidth={innerStrokeWidth}
            strokeLinejoin="round" strokeLinecap="round">{realText}</text>
    {/* 主体深蓝发光填充 */}
    <text x={centerX} y={textY} textAnchor="middle" className="svg-text style61-text"             
            fill={fillColor} filter={`url(#${glowId})`}>{realText}</text>
    </svg>
});

Flower061.showName = '深蓝纯色';
Flower061.description = '深蓝纯色发光花字';
Flower061.key = 'Flower061'; 