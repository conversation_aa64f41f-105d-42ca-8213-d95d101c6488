import React, { memo, useMemo } from 'react';
import { measureTextDimensions, parseStyle } from './util';

interface Flower041Props {
  text?: string|any;
  style?: React.CSSProperties;
  fillColor?: string;
  strokeColor?: string;
  backgroundColor?: string;
}

export const Flower041: any = memo(
  ({
    text = '花字',
    style = {},
    fillColor = '#800020',
    strokeColor = '#4C0013',
    backgroundColor = '#2F000A',
  }: Flower041Props) => {
    const { fontSize, fontFamily, fontWeight, letterSpacing } =
      parseStyle(style);
    let realText = (typeof text === 'string' ? text : text?.props?.text) || '';
    const textDimensions = useMemo(
      () =>
        measureTextDimensions({
          text: realText,
          fontFamily,
          fontSize,
          fontWeight,
        }),
      [realText, fontFamily, fontSize, fontWeight],
    );

    const svgWidth = textDimensions.width;
    const svgHeight = textDimensions.height;

    const centerX = svgWidth / 2;
    const centerY = svgHeight / 2;

    const outerStrokeWidth = fontSize * 0.08;
    const innerStrokeWidth = fontSize * 0.04;

    const baselineOffset = textDimensions.height * 0.35;
    const textY = centerY + baselineOffset;

    const shadowId = `wineShadow41_${Math.random().toString(36).substr(2, 9)}`;

    return (
      <svg
        style={{ display: 'inline' }}
        width={svgWidth}
        height={svgHeight}
        viewBox={`0 0 ${svgWidth} ${svgHeight}`}
        fontFamily={fontFamily}
        fontSize={fontSize}
        fontWeight={fontWeight}
        letterSpacing={letterSpacing}
      >
        <defs>
          <filter id={shadowId}>
            <feDropShadow
              dx="3"
              dy="3"
              stdDeviation="2"
              floodColor="#B22222"
              floodOpacity="0.8"
            />
          </filter>
        </defs>
        {/* 外层深酒红描边 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style41-text"
          fill="none"
          stroke={backgroundColor}
          strokeWidth={outerStrokeWidth}
          strokeLinejoin="round"
          strokeLinecap="round"
        >
          {realText}
        </text>
        {/* 内层暗酒红描边 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style41-text"
          fill="none"
          stroke={strokeColor}
          strokeWidth={innerStrokeWidth}
          strokeLinejoin="round"
          strokeLinecap="round"
        >
          {realText}
        </text>
        {/* 主体酒红阴影填充 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style41-text"
          fill={fillColor}
          filter={`url(#${shadowId})`}
        >
          {realText}
        </text>
      </svg>
    );
  },
);

Flower041.showName = '酒红阴影';
Flower041.description = '酒红阴影效果花字';
Flower041.key = 'Flower041';
