import React, { memo, useMemo } from 'react';
import { measureTextDimensions, parseStyle, measureTextBaselineOffset } from './util';

interface Flower109Props {
  text?: string | any;
  style?: React.CSSProperties;
  fillColor?: string;
  strokeColor?: string;
  backgroundColor?: string;
}

export const Flower109: any = memo(
  ({
    text = '花字',
    style = {},
    fillColor = '#FFD700',
    strokeColor = '#FF4500',
    backgroundColor = '#8B0000',
  }: Flower109Props) => {
    const { fontSize, fontFamily, fontWeight, letterSpacing, fontStyle } =
      parseStyle(style);

    let realText = (typeof text === 'string' ? text : text?.props?.text) || '';

    const textDimensions = useMemo(
      () => measureTextDimensions({ text: realText, fontFamily, fontSize, fontWeight, fontStyle }),
      [realText, fontFamily, fontSize, fontWeight, fontStyle],
    );

    const svgWidth = textDimensions.width;
    const svgHeight = textDimensions.height;

    const centerX = svgWidth / 2;
    const centerY = svgHeight / 2;

    const outerStrokeWidth = fontSize * 0.16;
    const innerStrokeWidth = fontSize * 0.08;

    const baselineOffset = measureTextBaselineOffset({text: realText, fontFamily, fontSize, fontWeight}) || textDimensions.height * 0.35;
    const textY = centerY + baselineOffset;

    const gradientId = `fire109_${Math.random().toString(36).substr(2, 9)}`;
    const flameId = `flame109_${Math.random().toString(36).substr(2, 9)}`;

    return (
      <svg
        style={{ display: 'inline' }}
        width={svgWidth}
        height={svgHeight}
        viewBox={`0 0 ${svgWidth} ${svgHeight}`}
        fontFamily={fontFamily}
        fontSize={fontSize - outerStrokeWidth / 2}
        fontWeight={fontWeight}
        letterSpacing={letterSpacing}
      >
        <defs>
          <radialGradient id={gradientId} cx="50%" cy="50%" r="50%">
            <stop offset="0%" style={{stopColor: "#FFFF00", stopOpacity: 1}} />
            <stop offset="30%" style={{stopColor: "#FFD700", stopOpacity: 1}} />
            <stop offset="60%" style={{stopColor: "#FF8C00", stopOpacity: 1}} />
            <stop offset="100%" style={{stopColor: "#FF4500", stopOpacity: 1}} />
          </radialGradient>
          <filter id={flameId}>
            <feGaussianBlur stdDeviation="1.5" result="blur"/>
            <feColorMatrix in="blur" type="matrix" values="1 0 0 0 0  0 0.5 0 0 0  0 0 0 0 0  0 0 0 1 0"/>
            <feMerge>
              <feMergeNode in="SourceGraphic"/>
              <feMergeNode in="blur"/>
            </feMerge>
          </filter>
        </defs>
        {/* 外层深红描边 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style109-text"
          fill="none"
          stroke={backgroundColor}
          strokeWidth={outerStrokeWidth}
          strokeLinejoin="round"
          strokeLinecap="round"
        >
          {realText}
        </text>
        {/* 内层橙色描边 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style109-text"
          fill="none"
          stroke={strokeColor}
          strokeWidth={innerStrokeWidth}
          strokeLinejoin="round"
          strokeLinecap="round"
        >
          {realText}
        </text>
        {/* 主体火焰渐变填充 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style109-text"
          fill={`url(#${gradientId})`}
          filter={`url(#${flameId})`}
        >
          {realText}
        </text>
      </svg>
    );
  },
);

Flower109.showName = '烈火金辉';
Flower109.description = '火焰效果金色辐射渐变花字';
Flower109.key = 'Flower109';
