import React, { memo, useMemo } from 'react';
import { measureTextDimensions, parseStyle } from './util';

interface Flower062Props {
  text?: string | any;
  style?: React.CSSProperties;
  fillColor?: string;
  strokeColor?: string;
  backgroundColor?: string;
}

export const Flower062: any = memo(({ 
  text = "花字", 
  style = {},
  fillColor = "#0891B2",
  strokeColor = "#0E7490", 
  backgroundColor = "#164E63"
}: Flower062Props) => {
  const {fontSize, fontFamily, fontWeight, letterSpacing} = parseStyle(style);

  let realText = (typeof text === 'string' ? text : text?.props?.text) || '';

  const textDimensions = useMemo(() => measureTextDimensions({text: realText, fontFamily, fontSize, fontWeight}), [realText, fontFamily, fontSize, fontWeight]);
  
  const svgWidth = textDimensions.width
  const svgHeight = textDimensions.height
  
  const centerX = svgWidth / 2;
  const centerY = svgHeight / 2;
  
  const outerStrokeWidth = fontSize * 0.10;
  const innerStrokeWidth = fontSize * 0.05;
  
  const baselineOffset = textDimensions.height * 0.35;
  const textY = centerY + baselineOffset;
  
  const gradientId = `blueGreen62_${Math.random().toString(36).substr(2, 9)}`;
  
  return <svg style={{display: 'inline'}} 
    width={svgWidth} height={svgHeight} 
    viewBox={`0 0 ${svgWidth} ${svgHeight}`}
    fontFamily={fontFamily} fontSize={fontSize} fontWeight={fontWeight} letterSpacing={letterSpacing}
  >
    <defs>
        <linearGradient id={gradientId} x1="0%" y1="0%" x2="0%" y2="100%">
            <stop offset="0%" style={{stopColor:"#22D3EE", stopOpacity:1}} />
            <stop offset="50%" style={{stopColor:"#0891B2", stopOpacity:1}} />
            <stop offset="100%" style={{stopColor:"#0E7490", stopOpacity:1}} />
        </linearGradient>
    </defs>
    {/* 外层深青描边 */}
    <text x={centerX} y={textY} textAnchor="middle" className="svg-text style62-text"             
            fill="none" stroke={backgroundColor} strokeWidth={outerStrokeWidth}
            strokeLinejoin="round" strokeLinecap="round">{realText}</text>
    {/* 内层青色描边 */}
    <text x={centerX} y={textY} textAnchor="middle" className="svg-text style62-text"             
            fill="none" stroke={strokeColor} strokeWidth={innerStrokeWidth}
            strokeLinejoin="round" strokeLinecap="round">{realText}</text>
    {/* 主体蓝绿渐变填充 */}
    <text x={centerX} y={textY} textAnchor="middle" className="svg-text style62-text"             
            fill={`url(#${gradientId})`}>{realText}</text>
    </svg>
});

Flower062.showName = '蓝绿渐变';
Flower062.description = '蓝绿色系自然渐变花字';
Flower062.key = 'Flower062'; 