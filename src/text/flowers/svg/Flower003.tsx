import React, { memo, useMemo } from 'react';
import { measureTextDimensions, parseStyle } from './util';

interface Flower003Props {
  text?: string|any;
  style?: React.CSSProperties;
  fillColor?: string;
  strokeColor?: string;
  backgroundColor?: string;
}

export const Flower003: any = memo(
  ({
    text = '花字',
    style = {},
    fillColor = '#FFD700',
    strokeColor = '#8B4513',
    backgroundColor = '#000000',
  }: Flower003Props) => {
    // 从style中获取字体属性
    const { fontSize, fontFamily, fontWeight, letterSpacing } =
      parseStyle(style);

      
    let realText = (typeof text === 'string' ? text : text?.props?.text) || '';
    const textDimensions = useMemo(
      () =>
        measureTextDimensions({
          text: realText,
          fontFamily,
          fontSize,
          fontWeight,
        }),
      [realText, fontFamily, fontSize, fontWeight],
    );

    // 计算SVG尺寸，添加合适的padding
    const svgWidth = textDimensions.width;
    const svgHeight = textDimensions.height;

    // 计算文本居中位置
    const centerX = svgWidth / 2;
    const centerY = svgHeight / 2;

    // 计算描边宽度，根据字体大小动态调整
    const outerStrokeWidth = fontSize * 0.12;
    const innerStrokeWidth = fontSize * 0.06;

    // 计算dominantBaseline的偏移量，确保文本垂直居中
    const baselineOffset = textDimensions.height * 0.35;
    const textY = centerY + baselineOffset;

    // 生成唯一ID避免冲突
    const gradientId = `goldGrad-${Math.random().toString(36).substr(2, 9)}`;
    const filterId = `goldGlow-${Math.random().toString(36).substr(2, 9)}`;

    return (
      <svg
        style={{ display: 'inline' }}
        width={svgWidth}
        height={svgHeight}
        viewBox={`0 0 ${svgWidth} ${svgHeight}`}
        fontFamily={fontFamily}
        fontSize={fontSize}
        fontWeight={fontWeight}
        letterSpacing={letterSpacing}
      >
        <defs>
          <linearGradient id={gradientId} x1="0%" y1="0%" x2="100%" y2="100%">
            <stop
              offset="0%"
              style={{ stopColor: '#FFD700', stopOpacity: 1 }}
            />
            <stop
              offset="50%"
              style={{ stopColor: '#FFA500', stopOpacity: 1 }}
            />
            <stop
              offset="100%"
              style={{ stopColor: '#FF8C00', stopOpacity: 1 }}
            />
          </linearGradient>
          <filter id={filterId}>
            <feGaussianBlur stdDeviation="2" result="coloredBlur" />
            <feMerge>
              <feMergeNode in="coloredBlur" />
              <feMergeNode in="SourceGraphic" />
            </feMerge>
          </filter>
        </defs>
        {/* 外层黑色描边 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style3-text"
          fill="none"
          stroke={backgroundColor}
          strokeWidth={outerStrokeWidth}
          strokeLinejoin="round"
          strokeLinecap="round"
        >
          {realText}
        </text>
        {/* 内层棕色描边 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style3-text"
          fill="none"
          stroke={strokeColor}
          strokeWidth={innerStrokeWidth}
          strokeLinejoin="round"
          strokeLinecap="round"
        >
          {realText}
        </text>
        {/* 主体金色渐变填充 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style3-text"
          fill={`url(#${gradientId})`}
          filter={`url(#${filterId})`}
        >
          {realText}
        </text>
      </svg>
    );
  },
);

Flower003.showName = '金色豪华';
Flower003.description = '金色渐变豪华花字';
Flower003.key = 'Flower003';
