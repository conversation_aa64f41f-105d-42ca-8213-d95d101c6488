import React, { memo, useMemo } from 'react';
import { measureTextDimensions, parseStyle, measureTextBaselineOffset } from './util';

interface Flower128Props {
  text?: string | any;
  style?: React.CSSProperties;
  fillColor?: string;
  strokeColor?: string;
  backgroundColor?: string;
}

export const Flower128: any = memo(
  ({
    text = '花字',
    style = {},
    fillColor = '#FFD700',
    strokeColor = '#FFFFFF',
    backgroundColor = '#8B4513',
  }: Flower128Props) => {
    const { fontSize, fontFamily, fontWeight, letterSpacing, fontStyle } =
      parseStyle(style);

    let realText = (typeof text === 'string' ? text : text?.props?.text) || '';

    const textDimensions = useMemo(
      () => measureTextDimensions({ text: realText, fontFamily, fontSize, fontWeight, fontStyle }),
      [realText, fontFamily, fontSize, fontWeight, fontStyle],
    );

    // 为皇冠效果增加padding
    const padding = fontSize * 0.5;
    const svgWidth = textDimensions.width + padding * 2;
    const svgHeight = textDimensions.height + padding * 2;

    const centerX = svgWidth / 2;
    const centerY = svgHeight / 2;

    const outerStrokeWidth = fontSize * 0.14;
    const middleStrokeWidth = fontSize * 0.08;
    const innerStrokeWidth = fontSize * 0.04;

    const baselineOffset = measureTextBaselineOffset({text: realText, fontFamily, fontSize, fontWeight}) || textDimensions.height * 0.35;
    const textY = centerY + baselineOffset;

    const gradientId = `royal128_${Math.random().toString(36).substr(2, 9)}`;
    const crownId = `crown128_${Math.random().toString(36).substr(2, 9)}`;
    const shadowId = `shadow128_${Math.random().toString(36).substr(2, 9)}`;

    return (
      <svg
        style={{ display: 'inline' }}
        width={svgWidth}
        height={svgHeight}
        viewBox={`0 0 ${svgWidth} ${svgHeight}`}
        fontFamily={fontFamily}
        fontSize={fontSize - outerStrokeWidth / 2}
        fontWeight={fontWeight}
        letterSpacing={letterSpacing}
      >
        <defs>
          {/* 皇冠背景渐变 */}
          <linearGradient id={crownId} x1="0%" y1="0%" x2="0%" y2="100%">
            <stop offset="0%" style={{stopColor: "#FFD700", stopOpacity: 0.8}} />
            <stop offset="50%" style={{stopColor: "#FFA500", stopOpacity: 0.6}} />
            <stop offset="100%" style={{stopColor: "#8B4513", stopOpacity: 0.4}} />
          </linearGradient>
          {/* 文字垂直金色渐变 */}
          <linearGradient id={gradientId} x1="0%" y1="0%" x2="0%" y2="100%">
            <stop offset="0%" style={{stopColor: "#FFFACD", stopOpacity: 1}} />
            <stop offset="20%" style={{stopColor: "#FFD700", stopOpacity: 1}} />
            <stop offset="60%" style={{stopColor: "#FFA500", stopOpacity: 1}} />
            <stop offset="100%" style={{stopColor: "#B8860B", stopOpacity: 1}} />
          </linearGradient>
          {/* 阴影滤镜 */}
          <filter id={shadowId}>
            <feDropShadow dx="2" dy="3" stdDeviation="2" floodColor="#8B4513" floodOpacity="0.8"/>
            <feSpecularLighting result="specOut" in="SourceAlpha" specularConstant="3" specularExponent="30" lightingColor="#FFFFFF">
              <fePointLight x="30" y="20" z="150"/>
            </feSpecularLighting>
            <feComposite in="SourceGraphic" in2="specOut" operator="arithmetic" k1="0" k2="1" k3="1" k4="0"/>
          </filter>
        </defs>
        
        {/* 皇冠背景矩形 */}
        <rect
          x={centerX - textDimensions.width * 0.6}
          y={centerY - textDimensions.height * 0.6}
          width={textDimensions.width * 1.2}
          height={textDimensions.height * 1.2}
          rx={fontSize * 0.1}
          fill={`url(#${crownId})`}
          opacity="0.7"
        />
        
        {/* 装饰性小圆点 */}
        <circle cx={centerX - textDimensions.width * 0.4} cy={centerY - textDimensions.height * 0.4} r={fontSize * 0.05} fill="#FFD700" opacity="0.8"/>
        <circle cx={centerX + textDimensions.width * 0.4} cy={centerY - textDimensions.height * 0.4} r={fontSize * 0.05} fill="#FFD700" opacity="0.8"/>
        <circle cx={centerX} cy={centerY - textDimensions.height * 0.5} r={fontSize * 0.08} fill="#FFD700" opacity="0.9"/>
        
        {/* 外层深棕描边 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style128-text"
          fill="none"
          stroke="#654321"
          strokeWidth={outerStrokeWidth}
          strokeLinejoin="round"
          strokeLinecap="round"
        >
          {realText}
        </text>
        
        {/* 中层棕色描边 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style128-text"
          fill="none"
          stroke={backgroundColor}
          strokeWidth={middleStrokeWidth}
          strokeLinejoin="round"
          strokeLinecap="round"
        >
          {realText}
        </text>
        
        {/* 内层白色描边 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style128-text"
          fill="none"
          stroke={strokeColor}
          strokeWidth={innerStrokeWidth}
          strokeLinejoin="round"
          strokeLinecap="round"
        >
          {realText}
        </text>
        
        {/* 主体皇家金色渐变填充 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style128-text"
          fill={`url(#${gradientId})`}
          filter={`url(#${shadowId})`}
        >
          {realText}
        </text>
      </svg>
    );
  },
);

Flower128.showName = '皇冠金字';
Flower128.description = '皇冠装饰背景单字清晰字幕特效';
Flower128.key = 'Flower128';
