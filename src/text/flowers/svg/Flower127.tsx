import React, { memo, useMemo } from 'react';
import { measureTextDimensions, parseStyle, measureTextBaselineOffset } from './util';

interface Flower127Props {
  text?: string | any;
  style?: React.CSSProperties;
  fillColor?: string;
  strokeColor?: string;
  backgroundColor?: string;
}

export const Flower127: any = memo(
  ({
    text = '花字',
    style = {},
    fillColor = '#00FFFF',
    strokeColor = '#FFFFFF',
    backgroundColor = '#0066CC',
  }: Flower127Props) => {
    const { fontSize, fontFamily, fontWeight, letterSpacing, fontStyle } =
      parseStyle(style);

    let realText = (typeof text === 'string' ? text : text?.props?.text) || '';

    const textDimensions = useMemo(
      () => measureTextDimensions({ text: realText, fontFamily, fontSize, fontWeight, fontStyle }),
      [realText, fontFamily, fontSize, fontWeight, fontStyle],
    );

    // 为光晕效果增加padding
    const padding = fontSize * 0.4;
    const svgWidth = textDimensions.width + padding * 2;
    const svgHeight = textDimensions.height + padding * 2;

    const centerX = svgWidth / 2;
    const centerY = svgHeight / 2;

    const outerStrokeWidth = fontSize * 0.15;
    const innerStrokeWidth = fontSize * 0.08;

    const baselineOffset = measureTextBaselineOffset({text: realText, fontFamily, fontSize, fontWeight}) || textDimensions.height * 0.35;
    const textY = centerY + baselineOffset;

    const gradientId = `ice127_${Math.random().toString(36).substr(2, 9)}`;
    const auraId = `aura127_${Math.random().toString(36).substr(2, 9)}`;
    const sparkleId = `sparkle127_${Math.random().toString(36).substr(2, 9)}`;

    return (
      <svg
        style={{ display: 'inline' }}
        width={svgWidth}
        height={svgHeight}
        viewBox={`0 0 ${svgWidth} ${svgHeight}`}
        fontFamily={fontFamily}
        fontSize={fontSize - outerStrokeWidth / 2}
        fontWeight={fontWeight}
        letterSpacing={letterSpacing}
      >
        <defs>
          {/* 冰晶光晕背景 */}
          <radialGradient id={auraId} cx="50%" cy="50%" r="80%">
            <stop offset="0%" style={{stopColor: "#FFFFFF", stopOpacity: 0.3}} />
            <stop offset="40%" style={{stopColor: "#00FFFF", stopOpacity: 0.6}} />
            <stop offset="70%" style={{stopColor: "#0066CC", stopOpacity: 0.4}} />
            <stop offset="100%" style={{stopColor: "#003366", stopOpacity: 0.1}} />
          </radialGradient>
          {/* 文字垂直冰晶渐变 */}
          <linearGradient id={gradientId} x1="0%" y1="0%" x2="0%" y2="100%">
            <stop offset="0%" style={{stopColor: "#FFFFFF", stopOpacity: 1}} />
            <stop offset="25%" style={{stopColor: "#E0FFFF", stopOpacity: 1}} />
            <stop offset="50%" style={{stopColor: "#00FFFF", stopOpacity: 1}} />
            <stop offset="75%" style={{stopColor: "#0099CC", stopOpacity: 1}} />
            <stop offset="100%" style={{stopColor: "#0066CC", stopOpacity: 1}} />
          </linearGradient>
          {/* 闪烁滤镜 */}
          <filter id={sparkleId}>
            <feGaussianBlur stdDeviation="1" result="blur"/>
            <feSpecularLighting result="specOut" in="blur" specularConstant="2" specularExponent="20" lightingColor="white">
              <fePointLight x="50" y="30" z="200"/>
            </feSpecularLighting>
            <feComposite in="SourceGraphic" in2="specOut" operator="arithmetic" k1="0" k2="1" k3="1" k4="0"/>
          </filter>
        </defs>
        
        {/* 冰晶光晕背景椭圆 */}
        <ellipse
          cx={centerX}
          cy={centerY}
          rx={textDimensions.width * 0.7}
          ry={textDimensions.height * 0.8}
          fill={`url(#${auraId})`}
          opacity="0.8"
        />
        
        {/* 外层深蓝描边 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style127-text"
          fill="none"
          stroke="#003366"
          strokeWidth={outerStrokeWidth}
          strokeLinejoin="round"
          strokeLinecap="round"
        >
          {realText}
        </text>
        
        {/* 内层白色描边 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style127-text"
          fill="none"
          stroke={strokeColor}
          strokeWidth={innerStrokeWidth}
          strokeLinejoin="round"
          strokeLinecap="round"
        >
          {realText}
        </text>
        
        {/* 主体冰晶渐变填充 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style127-text"
          fill={`url(#${gradientId})`}
          filter={`url(#${sparkleId})`}
        >
          {realText}
        </text>
      </svg>
    );
  },
);

Flower127.showName = '冰晶光晕';
Flower127.description = '冰晶光晕背景单字清晰字幕特效';
Flower127.key = 'Flower127';
