import React, { memo, useMemo } from 'react';
import { measureTextDimensions, parseStyle } from './util';

interface Flower026Props {
  text?: string|any;
  style?: React.CSSProperties;
  fillColor?: string;
  strokeColor?: string;
  backgroundColor?: string;
}

export const Flower026: any = memo(({ 
  text = "花字", 
  style = {},
  fillColor = "#FFFFFF",
  strokeColor = "#808080", 
  backgroundColor = "#000000"
}: Flower026Props) => {
  const {fontSize, fontFamily, fontWeight, letterSpacing} = parseStyle(style);
        let realText = (typeof text === 'string' ? text : text?.props?.text) || '';
        const textDimensions = useMemo(
          () =>
            measureTextDimensions({
              text: realText,
              fontFamily,
              fontSize,
              fontWeight,
            }),
          [realText, fontFamily, fontSize, fontWeight],
        );
  
  const svgWidth = textDimensions.width
  const svgHeight = textDimensions.height
  
  const centerX = svgWidth / 2;
  const centerY = svgHeight / 2;
  
  const outerStrokeWidth = fontSize * 0.08;
  const innerStrokeWidth = fontSize * 0.04;
  
  const baselineOffset = textDimensions.height * 0.35;
  const textY = centerY + baselineOffset;
  
  return <svg style={{display: 'inline'}} 
    width={svgWidth} height={svgHeight} 
    viewBox={`0 0 ${svgWidth} ${svgHeight}`}
    fontFamily={fontFamily} fontSize={fontSize} fontWeight={fontWeight} letterSpacing={letterSpacing}
  >
    {/* 外层黑色描边 */}
    <text x={centerX} y={textY} textAnchor="middle" className="svg-text style26-text"             
            fill="none" stroke={backgroundColor} strokeWidth={outerStrokeWidth}
            strokeLinejoin="round" strokeLinecap="round">{realText}</text>
    {/* 内层灰色描边 */}
    <text x={centerX} y={textY} textAnchor="middle" className="svg-text style26-text"             
            fill="none" stroke={strokeColor} strokeWidth={innerStrokeWidth}
            strokeLinejoin="round" strokeLinecap="round">{realText}</text>
    {/* 主体白色填充 */}
    <text x={centerX} y={textY} textAnchor="middle" className="svg-text style26-text"             
            fill={fillColor}>{realText}</text>
    </svg>
});

Flower026.showName = '简洁白色';
Flower026.description = '简洁白色花字';
Flower026.key = 'Flower026'; 