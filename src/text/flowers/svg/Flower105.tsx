import React, { memo, useMemo } from 'react';
import { measureTextDimensions, parseStyle } from './util';

interface Flower105Props {
  text?: string | any;
  style?: React.CSSProperties;
  fillColor?: string;
  strokeColor?: string;
  backgroundColor?: string;
}

export const Flower105: any = memo(({ 
  text = "花字", 
  style = {},
  fillColor = "#64748B",
  strokeColor = "#94A3B8", 
  backgroundColor = "#475569"
}: Flower105Props) => {
  const {fontSize, fontFamily, fontWeight, letterSpacing} = parseStyle(style);

  let realText = (typeof text === 'string' ? text : text?.props?.text) || '';

  const textDimensions = useMemo(() => measureTextDimensions({text: realText, fontFamily, fontSize, fontWeight}), [realText, fontFamily, fontSize, fontWeight]);
  
  const svgWidth = textDimensions.width
  const svgHeight = textDimensions.height
  
  const centerX = svgWidth / 2;
  const centerY = svgHeight / 2;
  
  const outerStrokeWidth = fontSize * 0.08;
  const innerStrokeWidth = fontSize * 0.04;
  
  const baselineOffset = textDimensions.height * 0.35;
  const textY = centerY + baselineOffset;
  
  const shadowId = `silverGray105_${Math.random().toString(36).substr(2, 9)}`;
  
  return <svg style={{display: 'inline'}} 
    width={svgWidth} height={svgHeight} 
    viewBox={`0 0 ${svgWidth} ${svgHeight}`}
    fontFamily={fontFamily} fontSize={fontSize} fontWeight={fontWeight} letterSpacing={letterSpacing}
  >
    <defs>
        <filter id={shadowId}>
            <feDropShadow dx="2" dy="2" stdDeviation="1" floodColor="#CBD5E1" floodOpacity="0.6"/>
        </filter>
    </defs>
    {/* 外层深灰描边 */}
    <text x={centerX} y={textY} textAnchor="middle" className="svg-text style105-text"             
            fill="none" stroke={backgroundColor} strokeWidth={outerStrokeWidth}
            strokeLinejoin="round" strokeLinecap="round">{realText}</text>
    {/* 内层灰色描边 */}
    <text x={centerX} y={textY} textAnchor="middle" className="svg-text style105-text"             
            fill="none" stroke={strokeColor} strokeWidth={innerStrokeWidth}
            strokeLinejoin="round" strokeLinecap="round">{realText}</text>
    {/* 主体银灰阴影填充 */}
    <text x={centerX} y={textY} textAnchor="middle" className="svg-text style105-text"             
            fill={fillColor} filter={`url(#${shadowId})`}>{realText}</text>
    </svg>
});

Flower105.showName = '银灰纯色';
Flower105.description = '深银灰纯色阴影花字';
Flower105.key = 'Flower105'; 