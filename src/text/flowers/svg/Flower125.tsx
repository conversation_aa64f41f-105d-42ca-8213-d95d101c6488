import React, { memo, useMemo } from 'react';
import { measureTextDimensions, parseStyle, measureTextBaselineOffset } from './util';

interface Flower125Props {
  text?: string | any;
  style?: React.CSSProperties;
  fillColor?: string;
  strokeColor?: string;
  backgroundColor?: string;
}

export const Flower125: any = memo(
  ({
    text = '花字',
    style = {},
    fillColor = '#FF1493',
    strokeColor = '#FF69B4',
    backgroundColor = '#8B008B',
  }: Flower125Props) => {
    const { fontSize, fontFamily, fontWeight, letterSpacing, fontStyle } =
      parseStyle(style);

    let realText = (typeof text === 'string' ? text : text?.props?.text) || '';

    const textDimensions = useMemo(
      () => measureTextDimensions({ text: realText, fontFamily, fontSize, fontWeight, fontStyle }),
      [realText, fontFamily, fontSize, fontWeight, fontStyle],
    );

    const svgWidth = textDimensions.width;
    const svgHeight = textDimensions.height;

    const centerX = svgWidth / 2;
    const centerY = svgHeight / 2;

    const outerStrokeWidth = fontSize * 0.35;
    const layer2StrokeWidth = fontSize * 0.28;
    const layer3StrokeWidth = fontSize * 0.21;
    const layer4StrokeWidth = fontSize * 0.14;
    const layer5StrokeWidth = fontSize * 0.08;
    const innerStrokeWidth = fontSize * 0.04;
    const ultraThinStroke = fontSize * 0.02;

    const baselineOffset = measureTextBaselineOffset({text: realText, fontFamily, fontSize, fontWeight}) || textDimensions.height * 0.35;
    const textY = centerY + baselineOffset;

    const gradientId = `diamond125_${Math.random().toString(36).substr(2, 9)}`;
    const gradientId2 = `diamond125_2_${Math.random().toString(36).substr(2, 9)}`;
    const gradientId3 = `diamond125_3_${Math.random().toString(36).substr(2, 9)}`;
    const gradientId4 = `diamond125_4_${Math.random().toString(36).substr(2, 9)}`;
    const diamondId = `diamondSparkle125_${Math.random().toString(36).substr(2, 9)}`;
    const prismId = `prism125_${Math.random().toString(36).substr(2, 9)}`;
    const rainbowId = `rainbow125_${Math.random().toString(36).substr(2, 9)}`;

    return (
      <svg
        style={{ display: 'inline' }}
        width={svgWidth}
        height={svgHeight}
        viewBox={`0 0 ${svgWidth} ${svgHeight}`}
        fontFamily={fontFamily}
        fontSize={fontSize - outerStrokeWidth / 2}
        fontWeight={fontWeight}
        letterSpacing={letterSpacing}
      >
        <defs>
          <radialGradient id={gradientId} cx="30%" cy="30%" r="80%">
            <stop offset="0%" style={{stopColor: "#FFFFFF", stopOpacity: 1}} />
            <stop offset="10%" style={{stopColor: "#FFE4E1", stopOpacity: 1}} />
            <stop offset="20%" style={{stopColor: "#FFB6C1", stopOpacity: 1}} />
            <stop offset="35%" style={{stopColor: "#FF69B4", stopOpacity: 1}} />
            <stop offset="50%" style={{stopColor: "#FF1493", stopOpacity: 1}} />
            <stop offset="65%" style={{stopColor: "#DC143C", stopOpacity: 1}} />
            <stop offset="80%" style={{stopColor: "#8B008B", stopOpacity: 1}} />
            <stop offset="100%" style={{stopColor: "#4B0082", stopOpacity: 1}} />
          </radialGradient>
          <linearGradient id={gradientId2} x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" style={{stopColor: "#FF0080", stopOpacity: 1}} />
            <stop offset="20%" style={{stopColor: "#8000FF", stopOpacity: 1}} />
            <stop offset="40%" style={{stopColor: "#0080FF", stopOpacity: 1}} />
            <stop offset="60%" style={{stopColor: "#00FF80", stopOpacity: 1}} />
            <stop offset="80%" style={{stopColor: "#FFFF00", stopOpacity: 1}} />
            <stop offset="100%" style={{stopColor: "#FF8000", stopOpacity: 1}} />
          </linearGradient>
          <radialGradient id={gradientId3} cx="70%" cy="20%" r="60%">
            <stop offset="0%" style={{stopColor: "#FFFFFF", stopOpacity: 0.9}} />
            <stop offset="30%" style={{stopColor: "#FFD700", stopOpacity: 0.7}} />
            <stop offset="60%" style={{stopColor: "#FF69B4", stopOpacity: 0.5}} />
            <stop offset="100%" style={{stopColor: "#8A2BE2", stopOpacity: 0.3}} />
          </radialGradient>
          <linearGradient id={gradientId4} x1="0%" y1="0%" x2="0%" y2="100%">
            <stop offset="0%" style={{stopColor: "#FFFFFF", stopOpacity: 0.8}} />
            <stop offset="50%" style={{stopColor: "#FFD700", stopOpacity: 0.4}} />
            <stop offset="100%" style={{stopColor: "#FF1493", stopOpacity: 0.2}} />
          </linearGradient>
          <filter id={diamondId}>
            <feSpecularLighting result="specOut" in="SourceAlpha" specularConstant="6" specularExponent="80" lightingColor="#FFFFFF">
              <fePointLight x="20" y="15" z="100"/>
            </feSpecularLighting>
            <feComposite in="SourceGraphic" in2="specOut" operator="arithmetic" k1="0" k2="1" k3="3" k4="0"/>
            <feTurbulence baseFrequency="1.5" numOctaves="2" result="sparkle"/>
            <feColorMatrix in="sparkle" type="saturate" values="0"/>
            <feComponentTransfer>
              <feFuncA type="discrete" tableValues="0 0 0 0.3 0 0.7 0 0.9 1"/>
            </feComponentTransfer>
            <feComposite in="SourceGraphic" in2="sparkle" operator="screen"/>
          </filter>
          <filter id={prismId}>
            <feGaussianBlur stdDeviation="3" result="blur"/>
            <feOffset in="blur" dx="2" dy="2" result="offset"/>
            <feFlood floodColor="#FF69B4" floodOpacity="0.8"/>
            <feComposite in="SourceGraphic" in2="offset" operator="over"/>
          </filter>
          <filter id={rainbowId}>
            <feGaussianBlur stdDeviation="5" result="coloredBlur"/>
            <feOffset in="coloredBlur" dx="0" dy="0" result="offset"/>
            <feFlood floodColor="#FF0080" floodOpacity="0.6"/>
            <feComposite in="SourceGraphic" in2="coloredBlur" operator="screen"/>
            <feGaussianBlur stdDeviation="2" result="innerGlow"/>
            <feMerge>
              <feMergeNode in="coloredBlur"/>
              <feMergeNode in="innerGlow"/>
              <feMergeNode in="SourceGraphic"/>
            </feMerge>
          </filter>
        </defs>
        {/* 第一层：最外层黑色描边 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style125-text"
          fill="none"
          stroke="#000000"
          strokeWidth={outerStrokeWidth}
          strokeLinejoin="round"
          strokeLinecap="round"
        >
          {realText}
        </text>
        {/* 第二层：深紫描边 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style125-text"
          fill="none"
          stroke="#4B0040"
          strokeWidth={layer2StrokeWidth}
          strokeLinejoin="round"
          strokeLinecap="round"
          filter={`url(#${prismId})`}
        >
          {realText}
        </text>
        {/* 第三层：彩虹描边 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style125-text"
          fill="none"
          stroke={`url(#${gradientId2})`}
          strokeWidth={layer3StrokeWidth}
          strokeLinejoin="round"
          strokeLinecap="round"
        >
          {realText}
        </text>
        {/* 第四层：暗紫描边 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style125-text"
          fill="none"
          stroke={backgroundColor}
          strokeWidth={layer4StrokeWidth}
          strokeLinejoin="round"
          strokeLinecap="round"
        >
          {realText}
        </text>
        {/* 第五层：粉色描边 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style125-text"
          fill="none"
          stroke={strokeColor}
          strokeWidth={layer5StrokeWidth}
          strokeLinejoin="round"
          strokeLinecap="round"
        >
          {realText}
        </text>
        {/* 第六层：金色细描边 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style125-text"
          fill="none"
          stroke="#FFD700"
          strokeWidth={innerStrokeWidth}
          strokeLinejoin="round"
          strokeLinecap="round"
        >
          {realText}
        </text>
        {/* 第七层：白色超细描边 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style125-text"
          fill="none"
          stroke="#FFFFFF"
          strokeWidth={ultraThinStroke}
          strokeLinejoin="round"
          strokeLinecap="round"
        >
          {realText}
        </text>
        {/* 主体钻石渐变填充 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style125-text"
          fill={`url(#${gradientId})`}
          filter={`url(#${diamondId})`}
        >
          {realText}
        </text>
        {/* 第一层高光效果 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style125-text"
          fill={`url(#${gradientId3})`}
          opacity="0.8"
        >
          {realText}
        </text>
        {/* 第二层高光效果 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style125-text"
          fill={`url(#${gradientId4})`}
          opacity="0.6"
          filter={`url(#${rainbowId})`}
        >
          {realText}
        </text>
      </svg>
    );
  },
);

Flower125.showName = '钻石闪耀';
Flower125.description = '钻石粉色多层闪耀高光效果花字';
Flower125.key = 'Flower125';
