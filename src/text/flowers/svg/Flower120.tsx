import React, { memo, useMemo } from 'react';
import { measureTextDimensions, parseStyle, measureTextBaselineOffset } from './util';

interface Flower120Props {
  text?: string | any;
  style?: React.CSSProperties;
  fillColor?: string;
  strokeColor?: string;
  backgroundColor?: string;
}

export const Flower120: any = memo(
  ({
    text = '花字',
    style = {},
    fillColor = '#00CED1',
    strokeColor = '#40E0D0',
    backgroundColor = '#008B8B',
  }: Flower120Props) => {
    const { fontSize, fontFamily, fontWeight, letterSpacing, fontStyle } =
      parseStyle(style);

    let realText = (typeof text === 'string' ? text : text?.props?.text) || '';

    const textDimensions = useMemo(
      () => measureTextDimensions({ text: realText, fontFamily, fontSize, fontWeight, fontStyle }),
      [realText, fontFamily, fontSize, fontWeight, fontStyle],
    );

    const svgWidth = textDimensions.width;
    const svgHeight = textDimensions.height;

    const centerX = svgWidth / 2;
    const centerY = svgHeight / 2;

    const outerStrokeWidth = fontSize * 0.15;
    const innerStrokeWidth = fontSize * 0.075;

    const baselineOffset = measureTextBaselineOffset({text: realText, fontFamily, fontSize, fontWeight}) || textDimensions.height * 0.35;
    const textY = centerY + baselineOffset;

    const gradientId = `ice120_${Math.random().toString(36).substr(2, 9)}`;
    const iceId = `iceEffect120_${Math.random().toString(36).substr(2, 9)}`;

    return (
      <svg
        style={{ display: 'inline' }}
        width={svgWidth}
        height={svgHeight}
        viewBox={`0 0 ${svgWidth} ${svgHeight}`}
        fontFamily={fontFamily}
        fontSize={fontSize - outerStrokeWidth / 2}
        fontWeight={fontWeight}
        letterSpacing={letterSpacing}
      >
        <defs>
          <linearGradient id={gradientId} x1="0%" y1="0%" x2="100%" y2="0%">
            <stop offset="0%" style={{stopColor: "#F0FFFF", stopOpacity: 1}} />
            <stop offset="20%" style={{stopColor: "#E0FFFF", stopOpacity: 1}} />
            <stop offset="40%" style={{stopColor: "#AFEEEE", stopOpacity: 1}} />
            <stop offset="60%" style={{stopColor: "#40E0D0", stopOpacity: 1}} />
            <stop offset="80%" style={{stopColor: "#00CED1", stopOpacity: 1}} />
            <stop offset="100%" style={{stopColor: "#008B8B", stopOpacity: 1}} />
          </linearGradient>
          <filter id={iceId}>
            <feGaussianBlur stdDeviation="1" result="blur"/>
            <feSpecularLighting result="specOut" in="blur" specularConstant="2" specularExponent="25" lightingColor="#FFFFFF">
              <feDistantLight azimuth="45" elevation="60"/>
            </feSpecularLighting>
            <feComposite in="SourceGraphic" in2="specOut" operator="arithmetic" k1="0" k2="1" k3="1" k4="0"/>
          </filter>
        </defs>
        {/* 外层深青描边 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style120-text"
          fill="none"
          stroke="#004444"
          strokeWidth={outerStrokeWidth}
          strokeLinejoin="round"
          strokeLinecap="round"
        >
          {realText}
        </text>
        {/* 内层暗青描边 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style120-text"
          fill="none"
          stroke={backgroundColor}
          strokeWidth={innerStrokeWidth}
          strokeLinejoin="round"
          strokeLinecap="round"
        >
          {realText}
        </text>
        {/* 主体冰晶渐变填充 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style120-text"
          fill={`url(#${gradientId})`}
          filter={`url(#${iceId})`}
        >
          {realText}
        </text>
      </svg>
    );
  },
);

Flower120.showName = '冰晶蓝光';
Flower120.description = '冰晶蓝色渐变高光反射效果花字';
Flower120.key = 'Flower120';
