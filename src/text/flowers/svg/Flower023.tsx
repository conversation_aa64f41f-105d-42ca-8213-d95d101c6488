import React, { memo, useMemo } from 'react';
import { measureTextDimensions, parseStyle } from './util';

interface Flower023Props {
  text?: string|any;
  style?: React.CSSProperties;
  fillColor?: string;
  strokeColor?: string;
  backgroundColor?: string;
}

export const Flower023: any = memo(({ 
  text = "花字", 
  style = {},
  fillColor = "#1E88E5",
  strokeColor = "#0D47A1", 
  backgroundColor = "#0A1A2A"
}: Flower023Props) => {
  const {fontSize, fontFamily, fontWeight, letterSpacing} = parseStyle(style);
        let realText = (typeof text === 'string' ? text : text?.props?.text) || '';
        const textDimensions = useMemo(
          () =>
            measureTextDimensions({
              text: realText,
              fontFamily,
              fontSize,
              fontWeight,
            }),
          [realText, fontFamily, fontSize, fontWeight],
        );
  
  const svgWidth = textDimensions.width
  const svgHeight = textDimensions.height
  
  const centerX = svgWidth / 2;
  const centerY = svgHeight / 2;
  
  const outerStrokeWidth = fontSize * 0.13;
  const innerStrokeWidth = fontSize * 0.065;
  
  const baselineOffset = textDimensions.height * 0.35;
  const textY = centerY + baselineOffset;
  
  const gradientId = `ocean23_${Math.random().toString(36).substr(2, 9)}`;
  const filterId = `depth23_${Math.random().toString(36).substr(2, 9)}`;
  
  return <svg style={{display: 'inline'}} 
    width={svgWidth} height={svgHeight} 
    viewBox={`0 0 ${svgWidth} ${svgHeight}`}
    fontFamily={fontFamily} fontSize={fontSize} fontWeight={fontWeight} letterSpacing={letterSpacing}
  >
    <defs>
        <linearGradient id={gradientId} x1="0%" y1="0%" x2="0%" y2="100%">
            <stop offset="0%" style={{stopColor:"#42A5F5", stopOpacity:1}} />
            <stop offset="30%" style={{stopColor:"#1E88E5", stopOpacity:1}} />
            <stop offset="70%" style={{stopColor:"#1565C0", stopOpacity:1}} />
            <stop offset="100%" style={{stopColor:"#0D47A1", stopOpacity:1}} />
        </linearGradient>
        <filter id={filterId}>
            <feGaussianBlur stdDeviation="3.5" result="coloredBlur"/>
            <feMerge> 
                <feMergeNode in="coloredBlur"/>
                <feMergeNode in="SourceGraphic"/>
            </feMerge>
        </filter>
    </defs>
    {/* 外层深夜蓝描边 */}
    <text x={centerX} y={textY} textAnchor="middle" className="svg-text style23-text"             
            fill="none" stroke={backgroundColor} strokeWidth={outerStrokeWidth}
            strokeLinejoin="round" strokeLinecap="round">{realText}</text>
    {/* 内层深蓝描边 */}
    <text x={centerX} y={textY} textAnchor="middle" className="svg-text style23-text"             
            fill="none" stroke={strokeColor} strokeWidth={innerStrokeWidth}
            strokeLinejoin="round" strokeLinecap="round">{realText}</text>
    {/* 主体蓝色垂直渐变填充 */}
    <text x={centerX} y={textY} textAnchor="middle" className="svg-text style23-text"             
            fill={`url(#${gradientId})`} filter={`url(#${filterId})`}>{realText}</text>
    </svg>
});

Flower023.showName = '蓝色垂直渐变';
Flower023.description = '蓝色垂直渐变花字';
Flower023.key = 'Flower023'; 