import React, { memo, useMemo } from 'react';
import { measureTextDimensions, parseStyle } from './util';

interface Flower097Props {
  text?: string | any;
  style?: React.CSSProperties;
  fillColor?: string;
  strokeColor?: string;
  backgroundColor?: string;
}

export const Flower097: any = memo(({ 
  text = "花字", 
  style = {},
  fillColor = "#BE123C",
  strokeColor = "#E11D48", 
  backgroundColor = "#9F1239"
}: Flower097Props) => {
  const {fontSize, fontFamily, fontWeight, letterSpacing} = parseStyle(style);

  let realText = (typeof text === 'string' ? text : text?.props?.text) || '';

  const textDimensions = useMemo(() => measureTextDimensions({text: realText, fontFamily, fontSize, fontWeight}), [realText, fontFamily, fontSize, fontWeight]);
  
  const svgWidth = textDimensions.width
  const svgHeight = textDimensions.height
  
  const centerX = svgWidth / 2;
  const centerY = svgHeight / 2;
  
  const outerStrokeWidth = fontSize * 0.08;
  const innerStrokeWidth = fontSize * 0.04;
  
  const baselineOffset = textDimensions.height * 0.35;
  const textY = centerY + baselineOffset;
  
  const highlightId = `peachRed97_${Math.random().toString(36).substr(2, 9)}`;
  
  return <svg style={{display: 'inline'}} 
    width={svgWidth} height={svgHeight} 
    viewBox={`0 0 ${svgWidth} ${svgHeight}`}
    fontFamily={fontFamily} fontSize={fontSize} fontWeight={fontWeight} letterSpacing={letterSpacing}
  >
    <defs>
        <filter id={highlightId}>
            <feDropShadow dx="-1" dy="-1" stdDeviation="1" floodColor="#F43F5E" floodOpacity="0.7"/>
        </filter>
    </defs>
    {/* 外层深红描边 */}
    <text x={centerX} y={textY} textAnchor="middle" className="svg-text style97-text"             
            fill="none" stroke={backgroundColor} strokeWidth={outerStrokeWidth}
            strokeLinejoin="round" strokeLinecap="round">{realText}</text>
    {/* 内层红色描边 */}
    <text x={centerX} y={textY} textAnchor="middle" className="svg-text style97-text"             
            fill="none" stroke={strokeColor} strokeWidth={innerStrokeWidth}
            strokeLinejoin="round" strokeLinecap="round">{realText}</text>
    {/* 主体桃红高光填充 */}
    <text x={centerX} y={textY} textAnchor="middle" className="svg-text style97-text"             
            fill={fillColor} filter={`url(#${highlightId})`}>{realText}</text>
    </svg>
});

Flower097.showName = '桃红纯色';
Flower097.description = '深桃红纯色高光花字';
Flower097.key = 'Flower097'; 