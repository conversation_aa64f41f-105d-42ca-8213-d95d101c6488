import React, { memo, useMemo } from 'react';
import { measureTextDimensions, parseStyle } from './util';

interface Flower010Props {
  text?: string|any;
  style?: React.CSSProperties;
  fillColor?: string;
  strokeColor?: string;
  backgroundColor?: string;
}

export const Flower010: any = memo(({ 
  text = "花字", 
  style = {},
  fillColor = "#FF69B4",
  strokeColor = "#FFFFFF", 
  backgroundColor = "#FF1493"
}: Flower010Props) => {
  // 从style中获取字体属性
  const {fontSize, fontFamily, fontWeight, letterSpacing} = parseStyle(style);
      let realText = (typeof text === 'string' ? text : text?.props?.text) || '';
      const textDimensions = useMemo(
        () =>
          measureTextDimensions({
            text: realText,
            fontFamily,
            fontSize,
            fontWeight,
          }),
        [realText, fontFamily, fontSize, fontWeight],
      );
  
  // 计算SVG尺寸，添加合适的padding
  const svgWidth = textDimensions.width
  const svgHeight = textDimensions.height
  
  // 计算文本居中位置
  const centerX = svgWidth / 2;
  const centerY = svgHeight / 2;
  
  // 计算描边宽度，根据字体大小动态调整
  const outerStrokeWidth = fontSize * 0.12;
  const innerStrokeWidth = fontSize * 0.04;
  
  // 计算dominantBaseline的偏移量，确保文本垂直居中
  const baselineOffset = textDimensions.height * 0.35;
  const textY = centerY + baselineOffset;

  // 生成唯一ID避免冲突
  const filterId = `neonGlow-${Math.random().toString(36).substr(2, 9)}`;

  return <svg style={{display: 'inline'}} 
    width={svgWidth} height={svgHeight} 
    viewBox={`0 0 ${svgWidth} ${svgHeight}`}
    fontFamily={fontFamily} fontSize={fontSize} fontWeight={fontWeight} letterSpacing={letterSpacing}
  >
    <defs>
        <filter id={filterId}>
            <feGaussianBlur stdDeviation="4" result="coloredBlur"/>
            <feMerge> 
                <feMergeNode in="coloredBlur"/>
                <feMergeNode in="SourceGraphic"/>
            </feMerge>
        </filter>
    </defs>
    {/* 外层粉色发光描边 */}
    <text x={centerX} y={textY} textAnchor="middle" className="svg-text style10-text"             
            fill="none" stroke={backgroundColor} strokeWidth={outerStrokeWidth}
            strokeLinejoin="round" strokeLinecap="round" filter={`url(#${filterId})`}>{realText}</text>
    {/* 内层白色描边 */}
    <text x={centerX} y={textY} textAnchor="middle" className="svg-text style10-text"             
            fill="none" stroke={strokeColor} strokeWidth={innerStrokeWidth}
            strokeLinejoin="round" strokeLinecap="round">{realText}</text>
    {/* 主体粉色填充 */}
    <text x={centerX} y={textY} textAnchor="middle" className="svg-text style10-text"             
            fill={fillColor}>{realText}</text>
    </svg>
});

Flower010.showName = '霓虹灯效果';
Flower010.description = '粉色霓虹灯花字';
Flower010.key = 'Flower010'; 