import React, { memo, useMemo } from 'react';
import { measureTextDimensions, parseStyle } from './util';

interface Flower016Props {
  text?: string|any;
  style?: React.CSSProperties;
  fillColor?: string;
  strokeColor?: string;
  backgroundColor?: string;
}

export const Flower016: any = memo(({ 
  text = "花字", 
  style = {},
  fillColor = "#E0FFFF",
  strokeColor = "#4169E1", 
  backgroundColor = "#000080"
}: Flower016Props) => {
  const {fontSize, fontFamily, fontWeight, letterSpacing} = parseStyle(style);
     let realText = (typeof text === 'string' ? text : text?.props?.text) || '';
     const textDimensions = useMemo(
       () =>
         measureTextDimensions({
           text: realText,
           fontFamily,
           fontSize,
           fontWeight,
         }),
       [realText, fontFamily, fontSize, fontWeight],
     );
  
  const svgWidth = textDimensions.width
  const svgHeight = textDimensions.height
  
  const centerX = svgWidth / 2;
  const centerY = svgHeight / 2;
  
  const outerStrokeWidth = fontSize * 0.10;
  const innerStrokeWidth = fontSize * 0.05;
  
  const baselineOffset = textDimensions.height * 0.35;
  const textY = centerY + baselineOffset;
  
  const filterId = `frost16_${Math.random().toString(36).substr(2, 9)}`;
  
  return <svg style={{display: 'inline'}} 
    width={svgWidth} height={svgHeight} 
    viewBox={`0 0 ${svgWidth} ${svgHeight}`}
    fontFamily={fontFamily} fontSize={fontSize} fontWeight={fontWeight} letterSpacing={letterSpacing}
  >
    <defs>
        <filter id={filterId}>
            <feGaussianBlur stdDeviation="2" result="coloredBlur"/>
            <feMerge> 
                <feMergeNode in="coloredBlur"/>
                <feMergeNode in="SourceGraphic"/>
            </feMerge>
        </filter>
    </defs>
    {/* 外层深蓝描边 */}
    <text x={centerX} y={textY} textAnchor="middle" className="svg-text style16-text"             
            fill="none" stroke={backgroundColor} strokeWidth={outerStrokeWidth}
            strokeLinejoin="round" strokeLinecap="round">{realText}</text>
    {/* 内层皇家蓝描边 */}
    <text x={centerX} y={textY} textAnchor="middle" className="svg-text style16-text"             
            fill="none" stroke={strokeColor} strokeWidth={innerStrokeWidth}
            strokeLinejoin="round" strokeLinecap="round">{realText}</text>
    {/* 主体淡青色填充 */}
    <text x={centerX} y={textY} textAnchor="middle" className="svg-text style16-text"             
            fill={fillColor} filter={`url(#${filterId})`}>{realText}</text>
    </svg>
});

Flower016.showName = '淡青发光';
Flower016.description = '淡青发光效果花字';
Flower016.key = 'Flower016'; 