import React, { memo, useMemo } from 'react';
import { measureTextDimensions, parseStyle, measureTextBaselineOffset } from './util';

interface Flower113Props {
  text?: string | any;
  style?: React.CSSProperties;
  fillColor?: string;
  strokeColor?: string;
  backgroundColor?: string;
}

export const Flower113: any = memo(
  ({
    text = '花字',
    style = {},
    fillColor = '#32CD32',
    strokeColor = '#ADFF2F',
    backgroundColor = '#228B22',
  }: Flower113Props) => {
    const { fontSize, fontFamily, fontWeight, letterSpacing, fontStyle } =
      parseStyle(style);

    let realText = (typeof text === 'string' ? text : text?.props?.text) || '';

    const textDimensions = useMemo(
      () => measureTextDimensions({ text: realText, fontFamily, fontSize, fontWeight, fontStyle }),
      [realText, fontFamily, fontSize, fontWeight, fontStyle],
    );

    const svgWidth = textDimensions.width;
    const svgHeight = textDimensions.height;

    const centerX = svgWidth / 2;
    const centerY = svgHeight / 2;

    const outerStrokeWidth = fontSize * 0.18;
    const innerStrokeWidth = fontSize * 0.09;

    const baselineOffset = measureTextBaselineOffset({text: realText, fontFamily, fontSize, fontWeight}) || textDimensions.height * 0.35;
    const textY = centerY + baselineOffset;

    const gradientId = `nature113_${Math.random().toString(36).substr(2, 9)}`;
    const leafId = `leaf113_${Math.random().toString(36).substr(2, 9)}`;

    return (
      <svg
        style={{ display: 'inline' }}
        width={svgWidth}
        height={svgHeight}
        viewBox={`0 0 ${svgWidth} ${svgHeight}`}
        fontFamily={fontFamily}
        fontSize={fontSize - outerStrokeWidth / 2}
        fontWeight={fontWeight}
        letterSpacing={letterSpacing}
      >
        <defs>
          <linearGradient id={gradientId} x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" style={{stopColor: "#98FB98", stopOpacity: 1}} />
            <stop offset="25%" style={{stopColor: "#90EE90", stopOpacity: 1}} />
            <stop offset="50%" style={{stopColor: "#32CD32", stopOpacity: 1}} />
            <stop offset="75%" style={{stopColor: "#228B22", stopOpacity: 1}} />
            <stop offset="100%" style={{stopColor: "#006400", stopOpacity: 1}} />
          </linearGradient>
          <filter id={leafId}>
            <feDropShadow dx="2" dy="2" stdDeviation="1" floodColor="#006400" floodOpacity="0.7"/>
            <feGaussianBlur stdDeviation="0.8" result="blur"/>
          </filter>
        </defs>
        {/* 外层深绿描边 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style113-text"
          fill="none"
          stroke="#004000"
          strokeWidth={outerStrokeWidth}
          strokeLinejoin="round"
          strokeLinecap="round"
        >
          {realText}
        </text>
        {/* 内层森林绿描边 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style113-text"
          fill="none"
          stroke={backgroundColor}
          strokeWidth={innerStrokeWidth}
          strokeLinejoin="round"
          strokeLinecap="round"
        >
          {realText}
        </text>
        {/* 主体自然绿渐变填充 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style113-text"
          fill={`url(#${gradientId})`}
          filter={`url(#${leafId})`}
        >
          {realText}
        </text>
      </svg>
    );
  },
);

Flower113.showName = '翠绿自然';
Flower113.description = '自然绿色渐变叶子阴影效果花字';
Flower113.key = 'Flower113';
