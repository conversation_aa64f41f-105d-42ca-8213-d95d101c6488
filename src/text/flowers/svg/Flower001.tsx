import React, { memo, useMemo } from 'react';
import { measureTextDimensions, parseStyle, measureTextBaselineOffset } from './util';

interface Flower001Props {
  text?: string | any;
  style?: React.CSSProperties;
  fillColor?: string;
  strokeColor?: string;
  backgroundColor?: string;
}

export const Flower001: any = memo(
  ({
    text = '花字',
    style = {},
    fillColor = '#FF0000',
    strokeColor = '#000000',
    backgroundColor = '#FFFFFF',
  }: Flower001Props) => {
    // 从style中获取字体属性
    const { fontSize, fontFamily, fontWeight, letterSpacing, fontStyle } =
      parseStyle(style);

    let realText = (typeof text === 'string' ? text : text?.props?.text) || '';


    const textDimensions = useMemo(
      () => measureTextDimensions({ text:realText, fontFamily, fontSize, fontWeight, fontStyle }),
      [realText, fontFamily, fontSize, fontWeight, fontStyle],
    );

    // 计算SVG尺寸，添加合适的padding
    const svgWidth = textDimensions.width;
    const svgHeight = textDimensions.height;

    // 计算文本居中位置
    const centerX = svgWidth / 2;
    const centerY = svgHeight / 2;

    // 计算描边宽度，根据字体大小动态调整
    const outerStrokeWidth = fontSize * 0.12;
    const innerStrokeWidth = fontSize * 0.06;

    // 计算dominantBaseline的偏移量，确保文本垂直居中
    // const baselineOffset = textDimensions.height * 0.35;
    const baselineOffset = measureTextBaselineOffset({text: realText, fontFamily, fontSize, fontWeight}) || textDimensions.height * 0.35
    const textY = centerY + baselineOffset;
    // console.log({fontFamily, fontSize, fontWeight, letterSpacing, style})
    return (
      <svg
        style={{ display: 'inline' }}
        width={svgWidth}
        height={svgHeight}
        viewBox={`0 0 ${svgWidth} ${svgHeight}`}
        fontFamily={fontFamily}
        fontSize={fontSize - outerStrokeWidth / 2}
        fontWeight={fontWeight}
        letterSpacing={letterSpacing}
      >
        <defs>
          <filter id="smooth1">
            <feGaussianBlur in="SourceGraphic" stdDeviation="0.1" />
          </filter>
        </defs>
        {/* 外层白色描边 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style1-text"
          fill="none"
          stroke={backgroundColor}
          strokeWidth={outerStrokeWidth}
          strokeLinejoin="round"
          strokeLinecap="round"
          filter="url(#smooth1)"
        >
          {realText}
        </text>
        {/* 内层黑色描边 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style1-text"
          fill="none"
          stroke={strokeColor}
          strokeWidth={innerStrokeWidth}
          strokeLinejoin="round"
          strokeLinecap="round"
          filter="url(#smooth1)"
        >
          {realText}
        </text>
        {/* 主体填充色 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style1-text"
          fill={fillColor}
          filter="url(#smooth1)"
        >
            {realText}
        </text>
      </svg>
    );
  },
);

Flower001.showName = '花字1';
Flower001.description = '花字001';
Flower001.key = 'Flower001';
