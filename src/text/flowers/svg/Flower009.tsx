import React, { memo, useMemo } from 'react';
import { measureTextDimensions, parseStyle } from './util';

interface Flower009Props {
  text?: string|any;
  style?: React.CSSProperties;
  fillColor?: string;
  strokeColor?: string;
  backgroundColor?: string;
}

export const Flower009: any = memo(({ 
  text = "花字", 
  style = {},
  fillColor = "#E8E8E8",
  strokeColor = "#696969", 
  backgroundColor = "#2F4F4F"
}: Flower009Props) => {
  // 从style中获取字体属性
  const {fontSize, fontFamily, fontWeight, letterSpacing} = parseStyle(style);
     let realText = (typeof text === 'string' ? text : text?.props?.text) || '';
     const textDimensions = useMemo(
       () =>
         measureTextDimensions({
           text: realText,
           fontFamily,
           fontSize,
           fontWeight,
         }),
       [realText, fontFamily, fontSize, fontWeight],
     );
  
  // 计算SVG尺寸，添加合适的padding
  const svgWidth = textDimensions.width
  const svgHeight = textDimensions.height
  
  // 计算文本居中位置
  const centerX = svgWidth / 2;
  const centerY = svgHeight / 2;
  
  // 计算描边宽度，根据字体大小动态调整
  const outerStrokeWidth = fontSize * 0.10;
  const innerStrokeWidth = fontSize * 0.04;
  
  // 计算dominantBaseline的偏移量，确保文本垂直居中
  const baselineOffset = textDimensions.height * 0.35;
  const textY = centerY + baselineOffset;

  // 生成唯一ID避免冲突
  const gradientId = `metalGrad-${Math.random().toString(36).substr(2, 9)}`;

  return <svg style={{display: 'inline'}} 
    width={svgWidth} height={svgHeight} 
    viewBox={`0 0 ${svgWidth} ${svgHeight}`}
    fontFamily={fontFamily} fontSize={fontSize} fontWeight={fontWeight} letterSpacing={letterSpacing}
  >
    <defs>
        <linearGradient id={gradientId} x1="0%" y1="0%" x2="0%" y2="100%">
            <stop offset="0%" style={{stopColor:"#E8E8E8", stopOpacity:1}} />
            <stop offset="25%" style={{stopColor:"#C0C0C0", stopOpacity:1}} />
            <stop offset="50%" style={{stopColor:"#A9A9A9", stopOpacity:1}} />
            <stop offset="75%" style={{stopColor:"#C0C0C0", stopOpacity:1}} />
            <stop offset="100%" style={{stopColor:"#E8E8E8", stopOpacity:1}} />
        </linearGradient>
    </defs>
    {/* 外层暗色描边 */}
    <text x={centerX} y={textY} textAnchor="middle" className="svg-text style9-text"             
            fill="none" stroke={backgroundColor} strokeWidth={outerStrokeWidth}
            strokeLinejoin="round" strokeLinecap="round">{realText}</text>
    {/* 内层灰色描边 */}
    <text x={centerX} y={textY} textAnchor="middle" className="svg-text style9-text"             
            fill="none" stroke={strokeColor} strokeWidth={innerStrokeWidth}
            strokeLinejoin="round" strokeLinecap="round">{realText}</text>
    {/* 银色金属渐变填充 */}
    <text x={centerX} y={textY} textAnchor="middle" className="svg-text style9-text"             
            fill={`url(#${gradientId})`}>{realText}</text>
    </svg>
});

Flower009.showName = '金属质感';
Flower009.description = '银色金属质感花字';
Flower009.key = 'Flower009'; 