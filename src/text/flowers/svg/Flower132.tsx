import React, { memo, useMemo } from 'react';
import { measureTextDimensions, parseStyle, measureTextBaselineOffset } from './util';

interface Flower132Props {
  text?: string | any;
  style?: React.CSSProperties;
  fillColor?: string;
  strokeColor?: string;
  backgroundColor?: string;
}

export const Flower132: any = memo(
  ({
    text = '花字',
    style = {},
    fillColor = '#00BFFF',
    strokeColor = '#FFFFFF',
    backgroundColor = '#1E90FF',
  }: Flower132Props) => {
    const { fontSize, fontFamily, fontWeight, letterSpacing, fontStyle } =
      parseStyle(style);

    let realText = (typeof text === 'string' ? text : text?.props?.text) || '';

    const textDimensions = useMemo(
      () => measureTextDimensions({ text: realText, fontFamily, fontSize, fontWeight, fontStyle }),
      [realText, fontFamily, fontSize, fontWeight, fontStyle],
    );

    // 简洁的padding
    const padding = fontSize * 0.15;
    const svgWidth = textDimensions.width + padding * 2;
    const svgHeight = textDimensions.height + padding * 2;

    const centerX = svgWidth / 2;
    const centerY = svgHeight / 2;

    // 清晰的描边设置
    const outerStrokeWidth = fontSize * 0.16;
    const innerStrokeWidth = fontSize * 0.08;

    const baselineOffset = measureTextBaselineOffset({text: realText, fontFamily, fontSize, fontWeight}) || textDimensions.height * 0.35;
    const textY = centerY + baselineOffset;

    const gradientId = `crystal132_${Math.random().toString(36).substr(2, 9)}`;
    const shadowId = `shadow132_${Math.random().toString(36).substr(2, 9)}`;

    return (
      <svg
        style={{ display: 'inline' }}
        width={svgWidth}
        height={svgHeight}
        viewBox={`0 0 ${svgWidth} ${svgHeight}`}
        fontFamily={fontFamily}
        fontSize={fontSize - outerStrokeWidth / 2}
        fontWeight={fontWeight}
        letterSpacing={letterSpacing}
      >
        <defs>
          {/* 清晰的垂直蓝色渐变 */}
          <linearGradient id={gradientId} x1="0%" y1="0%" x2="0%" y2="100%">
            <stop offset="0%" style={{stopColor: "#F0F8FF", stopOpacity: 1}} />
            <stop offset="25%" style={{stopColor: "#87CEEB", stopOpacity: 1}} />
            <stop offset="50%" style={{stopColor: "#00BFFF", stopOpacity: 1}} />
            <stop offset="75%" style={{stopColor: "#1E90FF", stopOpacity: 1}} />
            <stop offset="100%" style={{stopColor: "#0066CC", stopOpacity: 1}} />
          </linearGradient>
          {/* 清晰阴影 */}
          <filter id={shadowId}>
            <feDropShadow dx="2" dy="2" stdDeviation="1" floodColor="#003366" floodOpacity="0.6"/>
          </filter>
        </defs>
        
        {/* 外层深蓝描边 - 确保清晰度 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style132-text"
          fill="none"
          stroke="#003366"
          strokeWidth={outerStrokeWidth}
          strokeLinejoin="round"
          strokeLinecap="round"
        >
          {realText}
        </text>
        
        {/* 内层白色描边 - 增强清晰度 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style132-text"
          fill="none"
          stroke="#FFFFFF"
          strokeWidth={innerStrokeWidth}
          strokeLinejoin="round"
          strokeLinecap="round"
        >
          {realText}
        </text>
        
        {/* 主体水晶蓝渐变填充 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style132-text"
          fill={`url(#${gradientId})`}
          filter={`url(#${shadowId})`}
        >
          {realText}
        </text>
      </svg>
    );
  },
);

Flower132.showName = '水晶蓝光';
Flower132.description = '水晶蓝光清晰字幕特效';
Flower132.key = 'Flower132';
