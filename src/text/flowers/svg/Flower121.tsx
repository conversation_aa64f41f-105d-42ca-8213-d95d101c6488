import React, { memo, useMemo } from 'react';
import { measureTextDimensions, parseStyle, measureTextBaselineOffset } from './util';

interface Flower121Props {
  text?: string | any;
  style?: React.CSSProperties;
  fillColor?: string;
  strokeColor?: string;
  backgroundColor?: string;
}

export const Flower121: any = memo(
  ({
    text = '花字',
    style = {},
    fillColor = '#FFD700',
    strokeColor = '#FFA500',
    backgroundColor = '#B8860B',
  }: Flower121Props) => {
    const { fontSize, fontFamily, fontWeight, letterSpacing, fontStyle } =
      parseStyle(style);

    let realText = (typeof text === 'string' ? text : text?.props?.text) || '';

    const textDimensions = useMemo(
      () => measureTextDimensions({ text: realText, fontFamily, fontSize, fontWeight, fontStyle }),
      [realText, fontFamily, fontSize, fontWeight, fontStyle],
    );

    const svgWidth = textDimensions.width;
    const svgHeight = textDimensions.height;

    const centerX = svgWidth / 2;
    const centerY = svgHeight / 2;

    const outerStrokeWidth = fontSize * 0.16;
    const innerStrokeWidth = fontSize * 0.08;

    const baselineOffset = measureTextBaselineOffset({text: realText, fontFamily, fontSize, fontWeight}) || textDimensions.height * 0.35;
    const textY = centerY + baselineOffset;

    const gradientId = `royal121_${Math.random().toString(36).substr(2, 9)}`;
    const crownId = `crown121_${Math.random().toString(36).substr(2, 9)}`;

    return (
      <svg
        style={{ display: 'inline' }}
        width={svgWidth}
        height={svgHeight}
        viewBox={`0 0 ${svgWidth} ${svgHeight}`}
        fontFamily={fontFamily}
        fontSize={fontSize - outerStrokeWidth / 2}
        fontWeight={fontWeight}
        letterSpacing={letterSpacing}
      >
        <defs>
          <radialGradient id={gradientId} cx="50%" cy="40%" r="60%">
            <stop offset="0%" style={{stopColor: "#FFFACD", stopOpacity: 1}} />
            <stop offset="25%" style={{stopColor: "#FFD700", stopOpacity: 1}} />
            <stop offset="50%" style={{stopColor: "#FFA500", stopOpacity: 1}} />
            <stop offset="75%" style={{stopColor: "#FF8C00", stopOpacity: 1}} />
            <stop offset="100%" style={{stopColor: "#B8860B", stopOpacity: 1}} />
          </radialGradient>
          <filter id={crownId}>
            <feDropShadow dx="2" dy="4" stdDeviation="3" floodColor="#8B4513" floodOpacity="0.8"/>
            <feSpecularLighting result="specOut" in="SourceAlpha" specularConstant="3" specularExponent="40" lightingColor="#FFFFFF">
              <fePointLight x="30" y="20" z="150"/>
            </feSpecularLighting>
            <feComposite in="SourceGraphic" in2="specOut" operator="arithmetic" k1="0" k2="1" k3="1" k4="0"/>
          </filter>
        </defs>
        {/* 外层深棕描边 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style121-text"
          fill="none"
          stroke="#654321"
          strokeWidth={outerStrokeWidth}
          strokeLinejoin="round"
          strokeLinecap="round"
        >
          {realText}
        </text>
        {/* 内层金棕描边 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style121-text"
          fill="none"
          stroke={backgroundColor}
          strokeWidth={innerStrokeWidth}
          strokeLinejoin="round"
          strokeLinecap="round"
        >
          {realText}
        </text>
        {/* 主体皇家金渐变填充 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style121-text"
          fill={`url(#${gradientId})`}
          filter={`url(#${crownId})`}
        >
          {realText}
        </text>
      </svg>
    );
  },
);

Flower121.showName = '皇家金辉';
Flower121.description = '皇家金色径向渐变高光阴影花字';
Flower121.key = 'Flower121';
