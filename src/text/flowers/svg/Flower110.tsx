import React, { memo, useMemo } from 'react';
import { measureTextDimensions, parseStyle, measureTextBaselineOffset } from './util';

interface Flower110Props {
  text?: string | any;
  style?: React.CSSProperties;
  fillColor?: string;
  strokeColor?: string;
  backgroundColor?: string;
}

export const Flower110: any = memo(
  ({
    text = '花字',
    style = {},
    fillColor = '#E6E6FA',
    strokeColor = '#9370DB',
    backgroundColor = '#4B0082',
  }: Flower110Props) => {
    const { fontSize, fontFamily, fontWeight, letterSpacing, fontStyle } =
      parseStyle(style);

    let realText = (typeof text === 'string' ? text : text?.props?.text) || '';

    const textDimensions = useMemo(
      () => measureTextDimensions({ text: realText, fontFamily, fontSize, fontWeight, fontStyle }),
      [realText, fontFamily, fontSize, fontWeight, fontStyle],
    );

    const svgWidth = textDimensions.width;
    const svgHeight = textDimensions.height;

    const centerX = svgWidth / 2;
    const centerY = svgHeight / 2;

    const outerStrokeWidth = fontSize * 0.12;
    const innerStrokeWidth = fontSize * 0.06;

    const baselineOffset = measureTextBaselineOffset({text: realText, fontFamily, fontSize, fontWeight}) || textDimensions.height * 0.35;
    const textY = centerY + baselineOffset;

    const gradientId = `crystal110_${Math.random().toString(36).substr(2, 9)}`;
    const sparkleId = `sparkle110_${Math.random().toString(36).substr(2, 9)}`;

    return (
      <svg
        style={{ display: 'inline' }}
        width={svgWidth}
        height={svgHeight}
        viewBox={`0 0 ${svgWidth} ${svgHeight}`}
        fontFamily={fontFamily}
        fontSize={fontSize - outerStrokeWidth / 2}
        fontWeight={fontWeight}
        letterSpacing={letterSpacing}
      >
        <defs>
          <linearGradient id={gradientId} x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" style={{stopColor: "#F8F8FF", stopOpacity: 1}} />
            <stop offset="25%" style={{stopColor: "#E6E6FA", stopOpacity: 1}} />
            <stop offset="50%" style={{stopColor: "#DDA0DD", stopOpacity: 1}} />
            <stop offset="75%" style={{stopColor: "#DA70D6", stopOpacity: 1}} />
            <stop offset="100%" style={{stopColor: "#BA55D3", stopOpacity: 1}} />
          </linearGradient>
          <filter id={sparkleId}>
            <feGaussianBlur stdDeviation="0.5" result="blur"/>
            <feSpecularLighting result="specOut" in="blur" specularConstant="1.5" specularExponent="20" lightingColor="white">
              <fePointLight x="50" y="50" z="200"/>
            </feSpecularLighting>
            <feComposite in="SourceGraphic" in2="specOut" operator="arithmetic" k1="0" k2="1" k3="1" k4="0"/>
          </filter>
        </defs>
        {/* 外层深紫描边 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style110-text"
          fill="none"
          stroke={backgroundColor}
          strokeWidth={outerStrokeWidth}
          strokeLinejoin="round"
          strokeLinecap="round"
        >
          {realText}
        </text>
        {/* 内层中紫描边 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style110-text"
          fill="none"
          stroke={strokeColor}
          strokeWidth={innerStrokeWidth}
          strokeLinejoin="round"
          strokeLinecap="round"
        >
          {realText}
        </text>
        {/* 主体水晶渐变填充 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style110-text"
          fill={`url(#${gradientId})`}
          filter={`url(#${sparkleId})`}
        >
          {realText}
        </text>
      </svg>
    );
  },
);

Flower110.showName = '水晶紫光';
Flower110.description = '水晶质感紫色渐变闪光花字';
Flower110.key = 'Flower110';
