import React, { memo, useMemo } from 'react';
import { measureTextDimensions, parseStyle, measureTextBaselineOffset } from './util';

interface Flower112Props {
  text?: string | any;
  style?: React.CSSProperties;
  fillColor?: string;
  strokeColor?: string;
  backgroundColor?: string;
}

export const Flower112: any = memo(
  ({
    text = '花字',
    style = {},
    fillColor = '#FF1493',
    strokeColor = '#FFB6C1',
    backgroundColor = '#8B008B',
  }: Flower112Props) => {
    const { fontSize, fontFamily, fontWeight, letterSpacing, fontStyle } =
      parseStyle(style);

    let realText = (typeof text === 'string' ? text : text?.props?.text) || '';

    const textDimensions = useMemo(
      () => measureTextDimensions({ text: realText, fontFamily, fontSize, fontWeight, fontStyle }),
      [realText, fontFamily, fontSize, fontWeight, fontStyle],
    );

    const svgWidth = textDimensions.width;
    const svgHeight = textDimensions.height;

    const centerX = svgWidth / 2;
    const centerY = svgHeight / 2;

    const outerStrokeWidth = fontSize * 0.15;
    const innerStrokeWidth = fontSize * 0.08;

    const baselineOffset = measureTextBaselineOffset({text: realText, fontFamily, fontSize, fontWeight}) || textDimensions.height * 0.35;
    const textY = centerY + baselineOffset;

    const gradientId = `sakura112_${Math.random().toString(36).substr(2, 9)}`;
    const bloomId = `bloom112_${Math.random().toString(36).substr(2, 9)}`;

    return (
      <svg
        style={{ display: 'inline' }}
        width={svgWidth}
        height={svgHeight}
        viewBox={`0 0 ${svgWidth} ${svgHeight}`}
        fontFamily={fontFamily}
        fontSize={fontSize - outerStrokeWidth / 2}
        fontWeight={fontWeight}
        letterSpacing={letterSpacing}
      >
        <defs>
          <radialGradient id={gradientId} cx="30%" cy="30%" r="70%">
            <stop offset="0%" style={{stopColor: "#FFF0F5", stopOpacity: 1}} />
            <stop offset="20%" style={{stopColor: "#FFE4E1", stopOpacity: 1}} />
            <stop offset="40%" style={{stopColor: "#FFB6C1", stopOpacity: 1}} />
            <stop offset="60%" style={{stopColor: "#FF69B4", stopOpacity: 1}} />
            <stop offset="80%" style={{stopColor: "#FF1493", stopOpacity: 1}} />
            <stop offset="100%" style={{stopColor: "#DC143C", stopOpacity: 1}} />
          </radialGradient>
          <filter id={bloomId}>
            <feGaussianBlur stdDeviation="2" result="blur"/>
            <feFlood floodColor="#FF69B4" floodOpacity="0.6"/>
            <feComposite in="SourceGraphic" in2="blur" operator="screen"/>
          </filter>
        </defs>
        {/* 外层深紫描边 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style112-text"
          fill="none"
          stroke={backgroundColor}
          strokeWidth={outerStrokeWidth}
          strokeLinejoin="round"
          strokeLinecap="round"
        >
          {realText}
        </text>
        {/* 内层粉色描边 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style112-text"
          fill="none"
          stroke={strokeColor}
          strokeWidth={innerStrokeWidth}
          strokeLinejoin="round"
          strokeLinecap="round"
        >
          {realText}
        </text>
        {/* 主体樱花渐变填充 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style112-text"
          fill={`url(#${gradientId})`}
          filter={`url(#${bloomId})`}
        >
          {realText}
        </text>
      </svg>
    );
  },
);

Flower112.showName = '樱花绽放';
Flower112.description = '樱花粉色径向渐变绽放效果花字';
Flower112.key = 'Flower112';
