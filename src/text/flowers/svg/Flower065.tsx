import React, { memo, useMemo } from 'react';
import { measureTextDimensions, parseStyle } from './util';

interface Flower065Props {
  text?: string | any;
  style?: React.CSSProperties;
  fillColor?: string;
  strokeColor?: string;
  backgroundColor?: string;
}

export const Flower065: any = memo(({ 
  text = "花字", 
  style = {},
  fillColor = "#374151",
  strokeColor = "#4B5563", 
  backgroundColor = "#1F2937"
}: Flower065Props) => {
  const {fontSize, fontFamily, fontWeight, letterSpacing} = parseStyle(style);

  let realText = (typeof text === 'string' ? text : text?.props?.text) || '';

  const textDimensions = useMemo(() => measureTextDimensions({text: realText, fontFamily, fontSize, fontWeight}), [realText, fontFamily, fontSize, fontWeight]);
  
  const svgWidth = textDimensions.width
  const svgHeight = textDimensions.height
  
  const centerX = svgWidth / 2;
  const centerY = svgHeight / 2;
  
  const outerStrokeWidth = fontSize * 0.08;
  const innerStrokeWidth = fontSize * 0.04;
  
  const baselineOffset = textDimensions.height * 0.35;
  const textY = centerY + baselineOffset;
  
  const highlightId = `darkGray65_${Math.random().toString(36).substr(2, 9)}`;
  
  return <svg style={{display: 'inline'}} 
    width={svgWidth} height={svgHeight} 
    viewBox={`0 0 ${svgWidth} ${svgHeight}`}
    fontFamily={fontFamily} fontSize={fontSize} fontWeight={fontWeight} letterSpacing={letterSpacing}
  >
    <defs>
        <filter id={highlightId}>
            <feDropShadow dx="-1" dy="-1" stdDeviation="1" floodColor="#9CA3AF" floodOpacity="0.7"/>
        </filter>
    </defs>
    {/* 外层深灰描边 */}
    <text x={centerX} y={textY} textAnchor="middle" className="svg-text style65-text"             
            fill="none" stroke={backgroundColor} strokeWidth={outerStrokeWidth}
            strokeLinejoin="round" strokeLinecap="round">{realText}</text>
    {/* 内层灰色描边 */}
    <text x={centerX} y={textY} textAnchor="middle" className="svg-text style65-text"             
            fill="none" stroke={strokeColor} strokeWidth={innerStrokeWidth}
            strokeLinejoin="round" strokeLinecap="round">{realText}</text>
    {/* 主体深灰高光填充 */}
    <text x={centerX} y={textY} textAnchor="middle" className="svg-text style65-text"             
            fill={fillColor} filter={`url(#${highlightId})`}>{realText}</text>
    </svg>
});

Flower065.showName = '深灰纯色';
Flower065.description = '深灰纯色高光花字';
Flower065.key = 'Flower065'; 