import React, { memo, useMemo } from 'react';
import { measureTextDimensions, parseStyle } from './util';

interface Flower042Props {
  text?: string | any;
  style?: React.CSSProperties;
  fillColor?: string;
  strokeColor?: string;
  backgroundColor?: string;
}

export const Flower042: any = memo(
  ({
    text = '花字',
    style = {},
    fillColor = '#808000',
    strokeColor = '#556B2F',
    backgroundColor = '#2F4F2F',
  }: Flower042Props) => {
    const { fontSize, fontFamily, fontWeight, letterSpacing } =
      parseStyle(style);
    let realText = (typeof text === 'string' ? text : text?.props?.text) || '';
    const textDimensions = useMemo(
      () =>
        measureTextDimensions({
          text: realText,
          fontFamily,
          fontSize,
          fontWeight,
        }),
      [realText, fontFamily, fontSize, fontWeight],
    );

    const svgWidth = textDimensions.width;
    const svgHeight = textDimensions.height;

    const centerX = svgWidth / 2;
    const centerY = svgHeight / 2;

    const outerStrokeWidth = fontSize * 0.08;
    const innerStrokeWidth = fontSize * 0.04;

    const baselineOffset = textDimensions.height * 0.35;
    const textY = centerY + baselineOffset;

    const highlightId = `oliveHighlight42_${Math.random()
      .toString(36)
      .substr(2, 9)}`;

    return (
      <svg
        style={{ display: 'inline' }}
        width={svgWidth}
        height={svgHeight}
        viewBox={`0 0 ${svgWidth} ${svgHeight}`}
        fontFamily={fontFamily}
        fontSize={fontSize}
        fontWeight={fontWeight}
        letterSpacing={letterSpacing}
      >
        <defs>
          <filter id={highlightId}>
            <feDropShadow
              dx="-1"
              dy="-1"
              stdDeviation="1"
              floodColor="#9ACD32"
              floodOpacity="0.9"
            />
          </filter>
        </defs>
        {/* 外层深绿描边 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style42-text"
          fill="none"
          stroke={backgroundColor}
          strokeWidth={outerStrokeWidth}
          strokeLinejoin="round"
          strokeLinecap="round"
        >
          {realText}
        </text>
        {/* 内层橄榄绿描边 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style42-text"
          fill="none"
          stroke={strokeColor}
          strokeWidth={innerStrokeWidth}
          strokeLinejoin="round"
          strokeLinecap="round"
        >
          {realText}
        </text>
        {/* 主体橄榄绿高光填充 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style42-text"
          fill={fillColor}
          filter={`url(#${highlightId})`}
        >
          {realText}
        </text>
      </svg>
    );
  },
);

Flower042.showName = '橄榄绿高光';
Flower042.description = '橄榄绿高光效果花字';
Flower042.key = 'Flower042';
