import React, { memo, useMemo } from 'react';
import { measureTextDimensions, parseStyle, measureTextBaselineOffset } from './util';

interface Flower134Props {
  text?: string | any;
  style?: React.CSSProperties;
  fillColor?: string;
  strokeColor?: string;
  backgroundColor?: string;
}

export const Flower134: any = memo(
  ({
    text = '花字',
    style = {},
    fillColor = '#32CD32',
    strokeColor = '#FFFFFF',
    backgroundColor = '#228B22',
  }: Flower134Props) => {
    const { fontSize, fontFamily, fontWeight, letterSpacing, fontStyle } =
      parseStyle(style);

    let realText = (typeof text === 'string' ? text : text?.props?.text) || '';

    const textDimensions = useMemo(
      () => measureTextDimensions({ text: realText, fontFamily, fontSize, fontWeight, fontStyle }),
      [realText, fontFamily, fontSize, fontWeight, fontStyle],
    );

    // 为光环效果增加padding
    const padding = fontSize * 0.3;
    const svgWidth = textDimensions.width + padding * 2;
    const svgHeight = textDimensions.height + padding * 2;

    const centerX = svgWidth / 2;
    const centerY = svgHeight / 2;

    // 清晰的描边设置
    const outerStrokeWidth = fontSize * 0.15;
    const innerStrokeWidth = fontSize * 0.08;

    const baselineOffset = measureTextBaselineOffset({text: realText, fontFamily, fontSize, fontWeight}) || textDimensions.height * 0.35;
    const textY = centerY + baselineOffset;

    const gradientId = `emerald134_${Math.random().toString(36).substr(2, 9)}`;
    const auraId = `aura134_${Math.random().toString(36).substr(2, 9)}`;
    const glowId = `glow134_${Math.random().toString(36).substr(2, 9)}`;

    return (
      <svg
        style={{ display: 'inline' }}
        width={svgWidth}
        height={svgHeight}
        viewBox={`0 0 ${svgWidth} ${svgHeight}`}
        fontFamily={fontFamily}
        fontSize={fontSize - outerStrokeWidth / 2}
        fontWeight={fontWeight}
        letterSpacing={letterSpacing}
      >
        <defs>
          {/* 光环渐变 */}
          <radialGradient id={auraId} cx="50%" cy="50%" r="70%">
            <stop offset="0%" style={{stopColor: "#90EE90", stopOpacity: 0.3}} />
            <stop offset="50%" style={{stopColor: "#32CD32", stopOpacity: 0.2}} />
            <stop offset="100%" style={{stopColor: "#228B22", stopOpacity: 0.1}} />
          </radialGradient>
          {/* 清晰的垂直翡翠渐变 */}
          <linearGradient id={gradientId} x1="0%" y1="0%" x2="0%" y2="100%">
            <stop offset="0%" style={{stopColor: "#F0FFF0", stopOpacity: 1}} />
            <stop offset="25%" style={{stopColor: "#98FB98", stopOpacity: 1}} />
            <stop offset="50%" style={{stopColor: "#32CD32", stopOpacity: 1}} />
            <stop offset="75%" style={{stopColor: "#228B22", stopOpacity: 1}} />
            <stop offset="100%" style={{stopColor: "#006400", stopOpacity: 1}} />
          </linearGradient>
          {/* 柔和发光 */}
          <filter id={glowId}>
            <feGaussianBlur stdDeviation="1" result="coloredBlur"/>
            <feMerge>
              <feMergeNode in="coloredBlur"/>
              <feMergeNode in="SourceGraphic"/>
            </feMerge>
          </filter>
        </defs>
        
        {/* 光环背景 */}
        <circle
          cx={centerX}
          cy={centerY}
          r={Math.max(textDimensions.width, textDimensions.height) * 0.5}
          fill={`url(#${auraId})`}
          opacity="0.6"
        />
        
        {/* 外层深绿描边 - 确保清晰度 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style134-text"
          fill="none"
          stroke="#004000"
          strokeWidth={outerStrokeWidth}
          strokeLinejoin="round"
          strokeLinecap="round"
        >
          {realText}
        </text>
        
        {/* 内层白色描边 - 增强清晰度 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style134-text"
          fill="none"
          stroke="#FFFFFF"
          strokeWidth={innerStrokeWidth}
          strokeLinejoin="round"
          strokeLinecap="round"
        >
          {realText}
        </text>
        
        {/* 主体翡翠渐变填充 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style134-text"
          fill={`url(#${gradientId})`}
          filter={`url(#${glowId})`}
        >
          {realText}
        </text>
      </svg>
    );
  },
);

Flower134.showName = '翡翠光环';
Flower134.description = '翡翠光环清晰字幕特效';
Flower134.key = 'Flower134';
