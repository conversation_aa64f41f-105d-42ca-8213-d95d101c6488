import React, { memo, useMemo } from 'react';
import { measureTextDimensions, parseStyle } from './util';

interface Flower047Props {
  text?: string|any;
  style?: React.CSSProperties;
  fillColor?: string;
  strokeColor?: string;
  backgroundColor?: string;
}

export const Flower047: any = memo(({ 
  text = "花字", 
  style = {},
  fillColor = "#CD7F32",
  strokeColor = "#A0522D", 
  backgroundColor = "#8B4513"
}: Flower047Props) => {
  const {fontSize, fontFamily, fontWeight, letterSpacing} = parseStyle(style);
    let realText = (typeof text === 'string' ? text : text?.props?.text) || '';
      const textDimensions = useMemo(
        () =>
          measureTextDimensions({
            text: realText,
            fontFamily,
            fontSize,
            fontWeight,
          }),
        [realText, fontFamily, fontSize, fontWeight],
      );
  const svgWidth = textDimensions.width
  const svgHeight = textDimensions.height
  
  const centerX = svgWidth / 2;
  const centerY = svgHeight / 2;
  
  const outerStrokeWidth = fontSize * 0.08;
  const innerStrokeWidth = fontSize * 0.04;
  
  const baselineOffset = textDimensions.height * 0.35;
  const textY = centerY + baselineOffset;
  
  const highlightId = `bronzeHighlight47_${Math.random().toString(36).substr(2, 9)}`;
  
  return <svg style={{display: 'inline'}} 
    width={svgWidth} height={svgHeight} 
    viewBox={`0 0 ${svgWidth} ${svgHeight}`}
    fontFamily={fontFamily} fontSize={fontSize} fontWeight={fontWeight} letterSpacing={letterSpacing}
  >
    <defs>
        <filter id={highlightId}>
            <feDropShadow dx="-1" dy="-1" stdDeviation="1" floodColor="#DEB887" floodOpacity="0.9"/>
        </filter>
    </defs>
    {/* 外层马鞍棕描边 */}
    <text x={centerX} y={textY} textAnchor="middle" className="svg-text style47-text"             
            fill="none" stroke={backgroundColor} strokeWidth={outerStrokeWidth}
            strokeLinejoin="round" strokeLinecap="round">{realText}</text>
    {/* 内层赭石描边 */}
    <text x={centerX} y={textY} textAnchor="middle" className="svg-text style47-text"             
            fill="none" stroke={strokeColor} strokeWidth={innerStrokeWidth}
            strokeLinejoin="round" strokeLinecap="round">{realText}</text>
    {/* 主体青铜高光填充 */}
    <text x={centerX} y={textY} textAnchor="middle" className="svg-text style47-text"             
            fill={fillColor} filter={`url(#${highlightId})`}>{realText}</text>
    </svg>
});

Flower047.showName = '青铜高光';
Flower047.description = '青铜高光效果花字';
Flower047.key = 'Flower047'; 