import React, { memo, useMemo } from 'react';
import { measureTextDimensions, parseStyle } from './util';

interface Flower008Props {
  text?: string|any;
  style?: React.CSSProperties;
  fillColor?: string;
  strokeColor?: string;
  backgroundColor?: string;
}

export const Flower008: any = memo(({ 
  text = "花字", 
  style = {},
  fillColor = "#E0FFFF",
  strokeColor = "#FFFFFF", 
  backgroundColor = "#4682B4"
}: Flower008Props) => {
  // 从style中获取字体属性
  const {fontSize, fontFamily, fontWeight, letterSpacing} = parseStyle(style);
      let realText = (typeof text === 'string' ? text : text?.props?.text) || '';
      const textDimensions = useMemo(
        () =>
          measureTextDimensions({
            text: realText,
            fontFamily,
            fontSize,
            fontWeight,
          }),
        [realText, fontFamily, fontSize, fontWeight],
      );
  
  // 计算SVG尺寸，添加合适的padding
  const svgWidth = textDimensions.width
  const svgHeight = textDimensions.height
  
  // 计算文本居中位置
  const centerX = svgWidth / 2;
  const centerY = svgHeight / 2;
  
  // 计算描边宽度，根据字体大小动态调整
  const outerStrokeWidth = fontSize * 0.08;
  const innerStrokeWidth = fontSize * 0.02;
  
  // 计算dominantBaseline的偏移量，确保文本垂直居中
  const baselineOffset = textDimensions.height * 0.35;
  const textY = centerY + baselineOffset;

  // 生成唯一ID避免冲突
  const gradientId = `iceGrad-${Math.random().toString(36).substr(2, 9)}`;
  const filterId = `iceGlow-${Math.random().toString(36).substr(2, 9)}`;

  return <svg style={{display: 'inline'}} 
    width={svgWidth} height={svgHeight} 
    viewBox={`0 0 ${svgWidth} ${svgHeight}`}
    fontFamily={fontFamily} fontSize={fontSize} fontWeight={fontWeight} letterSpacing={letterSpacing}
  >
    <defs>
        <linearGradient id={gradientId} x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" style={{stopColor:"#E0FFFF", stopOpacity:1}} />
            <stop offset="50%" style={{stopColor:"#B0E0E6", stopOpacity:1}} />
            <stop offset="100%" style={{stopColor:"#87CEEB", stopOpacity:1}} />
        </linearGradient>
        <filter id={filterId}>
            <feGaussianBlur stdDeviation="1.5" result="coloredBlur"/>
            <feMerge> 
                <feMergeNode in="coloredBlur"/>
                <feMergeNode in="SourceGraphic"/>
            </feMerge>
        </filter>
    </defs>
    {/* 外层蓝色描边 */}
    <text x={centerX} y={textY} textAnchor="middle" className="svg-text style8-text"             
            fill="none" stroke={backgroundColor} strokeWidth={outerStrokeWidth}
            strokeLinejoin="round" strokeLinecap="round">{realText}</text>
    {/* 内层白色描边 */}
    <text x={centerX} y={textY} textAnchor="middle" className="svg-text style8-text"             
            fill="none" stroke={strokeColor} strokeWidth={innerStrokeWidth}
            strokeLinejoin="round" strokeLinecap="round">{realText}</text>
    {/* 冰蓝渐变填充 */}
    <text x={centerX} y={textY} textAnchor="middle" className="svg-text style8-text"             
            fill={`url(#${gradientId})`} filter={`url(#${filterId})`}>{realText}</text>
    </svg>
});

Flower008.showName = '冰雪效果';
Flower008.description = '冰蓝渐变清冷花字';
Flower008.key = 'Flower008'; 