import React, { memo, useMemo } from 'react';
import { measureTextDimensions, parseStyle } from './util';

interface Flower049Props {
  text?: string|any;
  style?: React.CSSProperties;
  fillColor?: string;
  strokeColor?: string;
  backgroundColor?: string;
}

export const Flower049: any = memo(({ 
  text = "花字", 
  style = {},
  fillColor = "#FF7F50",
  strokeColor = "#FF6347", 
  backgroundColor = "#CD5C5C"
}: Flower049Props) => {
  const {fontSize, fontFamily, fontWeight, letterSpacing} = parseStyle(style);
    let realText = (typeof text === 'string' ? text : text?.props?.text) || '';
      const textDimensions = useMemo(
        () =>
          measureTextDimensions({
            text: realText,
            fontFamily,
            fontSize,
            fontWeight,
          }),
        [realText, fontFamily, fontSize, fontWeight],
      );
  
  const svgWidth = textDimensions.width
  const svgHeight = textDimensions.height
  
  const centerX = svgWidth / 2;
  const centerY = svgHeight / 2;
  
  const outerStrokeWidth = fontSize * 0.08;
  const innerStrokeWidth = fontSize * 0.04;
  
  const baselineOffset = textDimensions.height * 0.35;
  const textY = centerY + baselineOffset;
  
  const shadowId = `coralShadow49_${Math.random().toString(36).substr(2, 9)}`;
  
  return <svg style={{display: 'inline'}} 
    width={svgWidth} height={svgHeight} 
    viewBox={`0 0 ${svgWidth} ${svgHeight}`}
    fontFamily={fontFamily} fontSize={fontSize} fontWeight={fontWeight} letterSpacing={letterSpacing}
  >
    <defs>
        <filter id={shadowId}>
            <feDropShadow dx="2" dy="2" stdDeviation="2" floodColor="#FFA07A" floodOpacity="0.8"/>
        </filter>
    </defs>
    {/* 外层印度红描边 */}
    <text x={centerX} y={textY} textAnchor="middle" className="svg-text style49-text"             
            fill="none" stroke={backgroundColor} strokeWidth={outerStrokeWidth}
            strokeLinejoin="round" strokeLinecap="round">{realText}</text>
    {/* 内层番茄红描边 */}
    <text x={centerX} y={textY} textAnchor="middle" className="svg-text style49-text"             
            fill="none" stroke={strokeColor} strokeWidth={innerStrokeWidth}
            strokeLinejoin="round" strokeLinecap="round">{realText}</text>
    {/* 主体珊瑚粉阴影填充 */}
    <text x={centerX} y={textY} textAnchor="middle" className="svg-text style49-text"             
            fill={fillColor} filter={`url(#${shadowId})`}>{realText}</text>
    </svg>
});

Flower049.showName = '珊瑚粉阴影';
Flower049.description = '珊瑚粉阴影效果花字';
Flower049.key = 'Flower049'; 