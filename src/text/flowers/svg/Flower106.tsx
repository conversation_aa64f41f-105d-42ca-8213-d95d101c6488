import React, { memo, useMemo } from 'react';
import { measureTextDimensions, parseStyle, measureTextBaselineOffset } from './util';

interface Flower106Props {
  text?: string | any;
  style?: React.CSSProperties;
  fillColor?: string;
  strokeColor?: string;
  backgroundColor?: string;
}

export const Flower106: any = memo(
  ({
    text = '花字',
    style = {},
    fillColor = '#FF6B35',
    strokeColor = '#FFD23F',
    backgroundColor = '#EE4266',
  }: Flower106Props) => {
    const { fontSize, fontFamily, fontWeight, letterSpacing, fontStyle } =
      parseStyle(style);

    let realText = (typeof text === 'string' ? text : text?.props?.text) || '';

    const textDimensions = useMemo(
      () => measureTextDimensions({ text: realText, fontFamily, fontSize, fontWeight, fontStyle }),
      [realText, fontFamily, fontSize, fontWeight, fontStyle],
    );

    const svgWidth = textDimensions.width;
    const svgHeight = textDimensions.height;

    const centerX = svgWidth / 2;
    const centerY = svgHeight / 2;

    const outerStrokeWidth = fontSize * 0.25;
    const middleStrokeWidth = fontSize * 0.18;
    const innerStrokeWidth = fontSize * 0.12;
    const thinStrokeWidth = fontSize * 0.06;

    const baselineOffset = measureTextBaselineOffset({text: realText, fontFamily, fontSize, fontWeight}) || textDimensions.height * 0.35;
    const textY = centerY + baselineOffset;

    const gradientId = `neonGlow106_${Math.random().toString(36).substr(2, 9)}`;
    const gradientId2 = `neonGlow106_2_${Math.random().toString(36).substr(2, 9)}`;
    const filterId = `neonFilter106_${Math.random().toString(36).substr(2, 9)}`;
    const pulseId = `pulse106_${Math.random().toString(36).substr(2, 9)}`;

    return (
      <svg
        style={{ display: 'inline' }}
        width={svgWidth}
        height={svgHeight}
        viewBox={`0 0 ${svgWidth} ${svgHeight}`}
        fontFamily={fontFamily}
        fontSize={fontSize - outerStrokeWidth / 2}
        fontWeight={fontWeight}
        letterSpacing={letterSpacing}
      >
        <defs>
          <linearGradient id={gradientId} x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" style={{stopColor: "#FF0080", stopOpacity: 1}} />
            <stop offset="25%" style={{stopColor: "#FF6B35", stopOpacity: 1}} />
            <stop offset="50%" style={{stopColor: "#FFD23F", stopOpacity: 1}} />
            <stop offset="75%" style={{stopColor: "#FF4500", stopOpacity: 1}} />
            <stop offset="100%" style={{stopColor: "#FF0080", stopOpacity: 1}} />
          </linearGradient>
          <radialGradient id={gradientId2} cx="50%" cy="50%" r="60%">
            <stop offset="0%" style={{stopColor: "#FFFFFF", stopOpacity: 0.8}} />
            <stop offset="40%" style={{stopColor: "#FFD23F", stopOpacity: 0.6}} />
            <stop offset="100%" style={{stopColor: "#FF6B35", stopOpacity: 0.3}} />
          </radialGradient>
          <filter id={filterId}>
            <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
            <feDropShadow dx="0" dy="0" stdDeviation="5" floodColor="#FF0080" floodOpacity="0.8"/>
            <feMerge>
              <feMergeNode in="coloredBlur"/>
              <feMergeNode in="SourceGraphic"/>
            </feMerge>
          </filter>
          <filter id={pulseId}>
            <feGaussianBlur stdDeviation="1" result="blur"/>
            <feColorMatrix in="blur" type="matrix" values="1 0 0 0 0  0 1 0 0 0  0 0 1 0 0  0 0 0 2 0"/>
            <feMerge>
              <feMergeNode in="SourceGraphic"/>
              <feMergeNode in="blur"/>
            </feMerge>
          </filter>
        </defs>
        {/* 最外层深色描边 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style106-text"
          fill="none"
          stroke="#000000"
          strokeWidth={outerStrokeWidth}
          strokeLinejoin="round"
          strokeLinecap="round"
        >
          {realText}
        </text>
        {/* 第二层霓虹粉描边 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style106-text"
          fill="none"
          stroke="#FF0080"
          strokeWidth={middleStrokeWidth}
          strokeLinejoin="round"
          strokeLinecap="round"
          filter={`url(#${pulseId})`}
        >
          {realText}
        </text>
        {/* 第三层橙色描边 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style106-text"
          fill="none"
          stroke="#FF6B35"
          strokeWidth={innerStrokeWidth}
          strokeLinejoin="round"
          strokeLinecap="round"
        >
          {realText}
        </text>
        {/* 第四层金色细描边 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style106-text"
          fill="none"
          stroke="#FFD23F"
          strokeWidth={thinStrokeWidth}
          strokeLinejoin="round"
          strokeLinecap="round"
        >
          {realText}
        </text>
        {/* 主体霓虹渐变填充 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style106-text"
          fill={`url(#${gradientId})`}
          filter={`url(#${filterId})`}
        >
          {realText}
        </text>
        {/* 顶层高光效果 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style106-text"
          fill={`url(#${gradientId2})`}
          opacity="0.7"
        >
          {realText}
        </text>
      </svg>
    );
  },
);

Flower106.showName = '霓虹橙光';
Flower106.description = '霓虹橙色发光特效花字';
Flower106.key = 'Flower106';
