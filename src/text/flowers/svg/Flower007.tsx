import React, { memo, useMemo } from 'react';
import { measureTextDimensions, parseStyle } from './util';

interface Flower007Props {
  text?: string|any;
  style?: React.CSSProperties;
  fillColor?: string;
  strokeColor?: string;
  backgroundColor?: string;
}

export const Flower007: any = memo(({ 
  text = "花字", 
  style = {},
  fillColor = "#FF4500",
  strokeColor = "#8B0000", 
  backgroundColor = "#FFD700"
}: Flower007Props) => {
  // 从style中获取字体属性
  const {fontSize, fontFamily, fontWeight, letterSpacing} = parseStyle(style);
      let realText = (typeof text === 'string' ? text : text?.props?.text) || '';
      const textDimensions = useMemo(
        () =>
          measureTextDimensions({
            text: realText,
            fontFamily,
            fontSize,
            fontWeight,
          }),
        [realText, fontFamily, fontSize, fontWeight],
      );
  
  // 计算SVG尺寸，添加合适的padding
  const svgWidth = textDimensions.width
  const svgHeight = textDimensions.height
  
  // 计算文本居中位置
  const centerX = svgWidth / 2;
  const centerY = svgHeight / 2;
  
  // 计算描边宽度，根据字体大小动态调整
  const strokeWidth = fontSize * 0.08;
  
  // 计算dominantBaseline的偏移量，确保文本垂直居中
  const baselineOffset = textDimensions.height * 0.35;
  const textY = centerY + baselineOffset;

  // 生成唯一ID避免冲突
  const gradientId = `fireGrad-${Math.random().toString(36).substr(2, 9)}`;
  const filterId = `fireGlow-${Math.random().toString(36).substr(2, 9)}`;

  return <svg style={{display: 'inline'}} 
    width={svgWidth} height={svgHeight} 
    viewBox={`0 0 ${svgWidth} ${svgHeight}`}
    fontFamily={fontFamily} fontSize={fontSize} fontWeight={fontWeight} letterSpacing={letterSpacing}
  >
    <defs>
        <linearGradient id={gradientId} x1="0%" y1="100%" x2="0%" y2="0%">
            <stop offset="0%" style={{stopColor:"#FF4500", stopOpacity:1}} />
            <stop offset="50%" style={{stopColor:"#FF6347", stopOpacity:1}} />
            <stop offset="100%" style={{stopColor:"#FFD700", stopOpacity:1}} />
        </linearGradient>
        <filter id={filterId}>
            <feGaussianBlur stdDeviation="2" result="coloredBlur"/>
            <feMerge> 
                <feMergeNode in="coloredBlur"/>
                <feMergeNode in="SourceGraphic"/>
            </feMerge>
        </filter>
    </defs>
    {/* 深红色描边 */}
    <text x={centerX} y={textY} textAnchor="middle" className="svg-text style7-text"             
            fill="none" stroke={strokeColor} strokeWidth={strokeWidth}
            strokeLinejoin="round" strokeLinecap="round">{realText}</text>
    {/* 火焰渐变填充 */}
    <text x={centerX} y={textY} textAnchor="middle" className="svg-text style7-text"             
            fill={`url(#${gradientId})`} filter={`url(#${filterId})`}>{realText}</text>
    </svg>
});

Flower007.showName = '火焰效果';
Flower007.description = '火焰渐变热烈花字';
Flower007.key = 'Flower007'; 