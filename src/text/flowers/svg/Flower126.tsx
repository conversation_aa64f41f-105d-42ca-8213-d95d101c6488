import React, { memo, useMemo } from 'react';
import { measureTextDimensions, parseStyle, measureTextBaselineOffset } from './util';

interface Flower126Props {
  text?: string | any;
  style?: React.CSSProperties;
  fillColor?: string;
  strokeColor?: string;
  backgroundColor?: string;
}

export const Flower126: any = memo(
  ({
    text = '花字',
    style = {},
    fillColor = '#FFD700',
    strokeColor = '#FFFFFF',
    backgroundColor = '#FF4500',
  }: Flower126Props) => {
    const { fontSize, fontFamily, fontWeight, letterSpacing, fontStyle } =
      parseStyle(style);

    let realText = (typeof text === 'string' ? text : text?.props?.text) || '';

    const textDimensions = useMemo(
      () => measureTextDimensions({ text: realText, fontFamily, fontSize, fontWeight, fontStyle }),
      [realText, fontFamily, fontSize, fontWeight, fontStyle],
    );

    // 为背景效果增加padding
    const padding = fontSize * 0.3;
    const svgWidth = textDimensions.width + padding * 2;
    const svgHeight = textDimensions.height + padding * 2;

    const centerX = svgWidth / 2;
    const centerY = svgHeight / 2;

    const outerStrokeWidth = fontSize * 0.12;
    const innerStrokeWidth = fontSize * 0.06;

    const baselineOffset = measureTextBaselineOffset({text: realText, fontFamily, fontSize, fontWeight}) || textDimensions.height * 0.35;
    const textY = centerY + baselineOffset;

    const gradientId = `fire126_${Math.random().toString(36).substr(2, 9)}`;
    const flameId = `flame126_${Math.random().toString(36).substr(2, 9)}`;
    const glowId = `glow126_${Math.random().toString(36).substr(2, 9)}`;

    return (
      <svg
        style={{ display: 'inline' }}
        width={svgWidth}
        height={svgHeight}
        viewBox={`0 0 ${svgWidth} ${svgHeight}`}
        fontFamily={fontFamily}
        fontSize={fontSize - outerStrokeWidth / 2}
        fontWeight={fontWeight}
        letterSpacing={letterSpacing}
      >
        <defs>
          {/* 火焰背景渐变 */}
          <radialGradient id={flameId} cx="50%" cy="80%" r="70%">
            <stop offset="0%" style={{stopColor: "#FF0000", stopOpacity: 0.8}} />
            <stop offset="30%" style={{stopColor: "#FF4500", stopOpacity: 0.6}} />
            <stop offset="60%" style={{stopColor: "#FFA500", stopOpacity: 0.4}} />
            <stop offset="100%" style={{stopColor: "#FFD700", stopOpacity: 0.2}} />
          </radialGradient>
          {/* 文字垂直渐变 */}
          <linearGradient id={gradientId} x1="0%" y1="0%" x2="0%" y2="100%">
            <stop offset="0%" style={{stopColor: "#FFFFFF", stopOpacity: 1}} />
            <stop offset="30%" style={{stopColor: "#FFD700", stopOpacity: 1}} />
            <stop offset="70%" style={{stopColor: "#FFA500", stopOpacity: 1}} />
            <stop offset="100%" style={{stopColor: "#FF4500", stopOpacity: 1}} />
          </linearGradient>
          {/* 发光滤镜 */}
          <filter id={glowId}>
            <feGaussianBlur stdDeviation="2" result="coloredBlur"/>
            <feMerge>
              <feMergeNode in="coloredBlur"/>
              <feMergeNode in="SourceGraphic"/>
            </feMerge>
          </filter>
        </defs>
        
        {/* 火焰背景圆形 */}
        <circle
          cx={centerX}
          cy={centerY}
          r={Math.max(textDimensions.width, textDimensions.height) * 0.6}
          fill={`url(#${flameId})`}
          opacity="0.7"
        />
        
        {/* 外层深色描边 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style126-text"
          fill="none"
          stroke="#8B0000"
          strokeWidth={outerStrokeWidth}
          strokeLinejoin="round"
          strokeLinecap="round"
        >
          {realText}
        </text>
        
        {/* 内层白色描边 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style126-text"
          fill="none"
          stroke={strokeColor}
          strokeWidth={innerStrokeWidth}
          strokeLinejoin="round"
          strokeLinecap="round"
        >
          {realText}
        </text>
        
        {/* 主体火焰渐变填充 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style126-text"
          fill={`url(#${gradientId})`}
          filter={`url(#${glowId})`}
        >
          {realText}
        </text>
      </svg>
    );
  },
);

Flower126.showName = '火焰背景';
Flower126.description = '火焰背景单字清晰字幕特效';
Flower126.key = 'Flower126';
