import React, { memo, useMemo } from 'react';
import { measureTextDimensions, parseStyle } from './util';

interface Flower084Props {
  text?: string | any;
  style?: React.CSSProperties;
  fillColor?: string;
  strokeColor?: string;
  backgroundColor?: string;
}

export const Flower084: any = memo(({ 
  text = "花字", 
  style = {},
  fillColor = "#BE185D",
  strokeColor = "#DB2777", 
  backgroundColor = "#9D174D"
}: Flower084Props) => {
  const {fontSize, fontFamily, fontWeight, letterSpacing} = parseStyle(style);

  let realText = (typeof text === 'string' ? text : text?.props?.text) || '';

  const textDimensions = useMemo(() => measureTextDimensions({text: realText, fontFamily, fontSize, fontWeight}), [realText, fontFamily, fontSize, fontWeight]);
  
  const svgWidth = textDimensions.width
  const svgHeight = textDimensions.height
  
  const centerX = svgWidth / 2;
  const centerY = svgHeight / 2;
  
  const outerStrokeWidth = fontSize * 0.10;
  const innerStrokeWidth = fontSize * 0.05;
  
  const baselineOffset = textDimensions.height * 0.35;
  const textY = centerY + baselineOffset;
  
  const gradientId = `roseGold84_${Math.random().toString(36).substr(2, 9)}`;
  
  return <svg style={{display: 'inline'}} 
    width={svgWidth} height={svgHeight} 
    viewBox={`0 0 ${svgWidth} ${svgHeight}`}
    fontFamily={fontFamily} fontSize={fontSize} fontWeight={fontWeight} letterSpacing={letterSpacing}
  >
    <defs>
        <linearGradient id={gradientId} x1="0%" y1="0%" x2="0%" y2="100%">
            <stop offset="0%" style={{stopColor:"#F472B6", stopOpacity:1}} />
            <stop offset="50%" style={{stopColor:"#BE185D", stopOpacity:1}} />
            <stop offset="100%" style={{stopColor:"#DB2777", stopOpacity:1}} />
        </linearGradient>
    </defs>
    {/* 外层深粉描边 */}
    <text x={centerX} y={textY} textAnchor="middle" className="svg-text style84-text"             
            fill="none" stroke={backgroundColor} strokeWidth={outerStrokeWidth}
            strokeLinejoin="round" strokeLinecap="round">{realText}</text>
    {/* 内层粉色描边 */}
    <text x={centerX} y={textY} textAnchor="middle" className="svg-text style84-text"             
            fill="none" stroke={strokeColor} strokeWidth={innerStrokeWidth}
            strokeLinejoin="round" strokeLinecap="round">{realText}</text>
    {/* 主体玫瑰金渐变填充 */}
    <text x={centerX} y={textY} textAnchor="middle" className="svg-text style84-text"             
            fill={`url(#${gradientId})`}>{realText}</text>
    </svg>
});

Flower084.showName = '玫瑰金渐变';
Flower084.description = '玫瑰金色系自然渐变花字';
Flower084.key = 'Flower084'; 