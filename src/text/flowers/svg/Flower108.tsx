import React, { memo, useMemo } from 'react';
import { measureTextDimensions, parseStyle, measureTextBaselineOffset } from './util';

interface Flower108Props {
  text?: string | any;
  style?: React.CSSProperties;
  fillColor?: string;
  strokeColor?: string;
  backgroundColor?: string;
}

export const Flower108: any = memo(
  ({
    text = '花字',
    style = {},
    fillColor = '#00D4AA',
    strokeColor = '#FFFFFF',
    backgroundColor = '#1A1A2E',
  }: Flower108Props) => {
    const { fontSize, fontFamily, fontWeight, letterSpacing, fontStyle } =
      parseStyle(style);

    let realText = (typeof text === 'string' ? text : text?.props?.text) || '';

    const textDimensions = useMemo(
      () => measureTextDimensions({ text: realText, fontFamily, fontSize, fontWeight, fontStyle }),
      [realText, fontFamily, fontSize, fontWeight, fontStyle],
    );

    const svgWidth = textDimensions.width;
    const svgHeight = textDimensions.height;

    const centerX = svgWidth / 2;
    const centerY = svgHeight / 2;

    const outerStrokeWidth = fontSize * 0.24;
    const layer2StrokeWidth = fontSize * 0.18;
    const layer3StrokeWidth = fontSize * 0.12;
    const innerStrokeWidth = fontSize * 0.06;
    const thinStrokeWidth = fontSize * 0.03;

    const baselineOffset = measureTextBaselineOffset({text: realText, fontFamily, fontSize, fontWeight}) || textDimensions.height * 0.35;
    const textY = centerY + baselineOffset;

    const gradientId = `cyber108_${Math.random().toString(36).substr(2, 9)}`;
    const glowId = `cyberGlow108_${Math.random().toString(36).substr(2, 9)}`;

    return (
      <svg
        style={{ display: 'inline' }}
        width={svgWidth}
        height={svgHeight}
        viewBox={`0 0 ${svgWidth} ${svgHeight}`}
        fontFamily={fontFamily}
        fontSize={fontSize - outerStrokeWidth / 2}
        fontWeight={fontWeight}
        letterSpacing={letterSpacing}
      >
        <defs>
          <linearGradient id={gradientId} x1="0%" y1="0%" x2="0%" y2="100%">
            <stop offset="0%" style={{stopColor: "#00FFFF", stopOpacity: 1}} />
            <stop offset="30%" style={{stopColor: "#00D4AA", stopOpacity: 1}} />
            <stop offset="70%" style={{stopColor: "#0099CC", stopOpacity: 1}} />
            <stop offset="100%" style={{stopColor: "#006699", stopOpacity: 1}} />
          </linearGradient>
          <filter id={glowId}>
            <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
            <feFlood floodColor="#00FFFF" floodOpacity="0.8"/>
            <feComposite in="SourceGraphic" in2="coloredBlur" operator="over"/>
          </filter>
        </defs>
        {/* 外层深色描边 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style108-text"
          fill="none"
          stroke={backgroundColor}
          strokeWidth={outerStrokeWidth}
          strokeLinejoin="round"
          strokeLinecap="round"
        >
          {realText}
        </text>
        {/* 内层白色描边 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style108-text"
          fill="none"
          stroke={strokeColor}
          strokeWidth={innerStrokeWidth}
          strokeLinejoin="round"
          strokeLinecap="round"
        >
          {realText}
        </text>
        {/* 主体赛博朋克渐变填充 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style108-text"
          fill={`url(#${gradientId})`}
          filter={`url(#${glowId})`}
        >
          {realText}
        </text>
      </svg>
    );
  },
);

Flower108.showName = '赛博朋克';
Flower108.description = '赛博朋克风格科技感花字';
Flower108.key = 'Flower108';
