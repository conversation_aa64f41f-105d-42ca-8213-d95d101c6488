import React, { memo, useMemo } from 'react';
import { measureTextDimensions, parseStyle } from './util';

interface Flower022Props {
  text?: string|any;
  style?: React.CSSProperties;
  fillColor?: string;
  strokeColor?: string;
  backgroundColor?: string;
}

export const Flower022: any = memo(({ 
  text = "花字", 
  style = {},
  fillColor = "#E91E63",
  strokeColor = "#AD1457", 
  backgroundColor = "#880E4F"
}: Flower022Props) => {
  const {fontSize, fontFamily, fontWeight, letterSpacing} = parseStyle(style);
       let realText = (typeof text === 'string' ? text : text?.props?.text) || '';
       const textDimensions = useMemo(
         () =>
           measureTextDimensions({
             text: realText,
             fontFamily,
             fontSize,
             fontWeight,
           }),
         [realText, fontFamily, fontSize, fontWeight],
       );
  
  const svgWidth = textDimensions.width
  const svgHeight = textDimensions.height
  
  const centerX = svgWidth / 2;
  const centerY = svgHeight / 2;
  
  const outerStrokeWidth = fontSize * 0.11;
  const innerStrokeWidth = fontSize * 0.055;
  
  const baselineOffset = textDimensions.height * 0.35;
  const textY = centerY + baselineOffset;
  
  const filterId = `elegant22_${Math.random().toString(36).substr(2, 9)}`;
  
  return <svg style={{display: 'inline'}} 
    width={svgWidth} height={svgHeight} 
    viewBox={`0 0 ${svgWidth} ${svgHeight}`}
    fontFamily={fontFamily} fontSize={fontSize} fontWeight={fontWeight} letterSpacing={letterSpacing}
  >
    <defs>
        <filter id={filterId}>
            <feDropShadow dx="2" dy="2" stdDeviation="2" floodColor="#880E4F" floodOpacity="0.4"/>
            <feGaussianBlur stdDeviation="1.5" result="coloredBlur"/>
            <feMerge> 
                <feMergeNode in="coloredBlur"/>
                <feMergeNode in="SourceGraphic"/>
            </feMerge>
        </filter>
    </defs>
    {/* 外层深玫红描边 */}
    <text x={centerX} y={textY} textAnchor="middle" className="svg-text style22-text"             
            fill="none" stroke={backgroundColor} strokeWidth={outerStrokeWidth}
            strokeLinejoin="round" strokeLinecap="round">{realText}</text>
    {/* 内层玫红描边 */}
    <text x={centerX} y={textY} textAnchor="middle" className="svg-text style22-text"             
            fill="none" stroke={strokeColor} strokeWidth={innerStrokeWidth}
            strokeLinejoin="round" strokeLinecap="round">{realText}</text>
    {/* 主体玫红色填充 */}
    <text x={centerX} y={textY} textAnchor="middle" className="svg-text style22-text"             
            fill={fillColor} filter={`url(#${filterId})`}>{realText}</text>
    </svg>
});

Flower022.showName = '玫红阴影';
Flower022.description = '玫红阴影效果花字';
Flower022.key = 'Flower022'; 