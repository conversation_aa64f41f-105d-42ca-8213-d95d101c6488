import React, { memo, useMemo } from 'react';
import { measureTextDimensions, parseStyle } from './util';

interface Flower013Props {
  text?: string|any;
  style?: React.CSSProperties;
  fillColor?: string;
  strokeColor?: string;
  backgroundColor?: string;
}

export const Flower013: any = memo(({ 
  text = "花字", 
  style = {},
  fillColor = "#C0C0C0",
  strokeColor = "#404040", 
  backgroundColor = "#000000"
}: Flower013Props) => {
  const {fontSize, fontFamily, fontWeight, letterSpacing} = parseStyle(style);
      let realText = (typeof text === 'string' ? text : text?.props?.text) || '';
      const textDimensions = useMemo(
        () =>
          measureTextDimensions({
            text: realText,
            fontFamily,
            fontSize,
            fontWeight,
          }),
        [realText, fontFamily, fontSize, fontWeight],
      );
  
  const svgWidth = textDimensions.width
  const svgHeight = textDimensions.height
  
  const centerX = svgWidth / 2;
  const centerY = svgHeight / 2;
  
  const outerStrokeWidth = fontSize * 0.08;
  const innerStrokeWidth = fontSize * 0.04;
  
  const baselineOffset = textDimensions.height * 0.35;
  const textY = centerY + baselineOffset;
  
  const filterId = `shadow13_${Math.random().toString(36).substr(2, 9)}`;
  
  return <svg style={{display: 'inline'}} 
    width={svgWidth} height={svgHeight} 
    viewBox={`0 0 ${svgWidth} ${svgHeight}`}
    fontFamily={fontFamily} fontSize={fontSize} fontWeight={fontWeight} letterSpacing={letterSpacing}
  >
    <defs>
        <filter id={filterId}>
            <feDropShadow dx="3" dy="3" stdDeviation="2" floodColor="#000000" floodOpacity="0.5"/>
        </filter>
    </defs>
    {/* 外层黑色描边 */}
    <text x={centerX} y={textY} textAnchor="middle" className="svg-text style13-text"             
            fill="none" stroke={backgroundColor} strokeWidth={outerStrokeWidth}
            strokeLinejoin="round" strokeLinecap="round">{realText}</text>
    {/* 内层深灰描边 */}
    <text x={centerX} y={textY} textAnchor="middle" className="svg-text style13-text"             
            fill="none" stroke={strokeColor} strokeWidth={innerStrokeWidth}
            strokeLinejoin="round" strokeLinecap="round">{realText}</text>
    {/* 主体银色填充 */}
    <text x={centerX} y={textY} textAnchor="middle" className="svg-text style13-text"             
            fill={fillColor} filter={`url(#${filterId})`}>{realText}</text>
    </svg>
});

Flower013.showName = '银色阴影';
Flower013.description = '银色阴影效果花字';
Flower013.key = 'Flower013'; 