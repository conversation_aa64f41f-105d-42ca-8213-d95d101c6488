import React, { memo, useMemo } from 'react';
import { measureTextDimensions, parseStyle } from './util';

interface Flower099Props {
  text?: string | any;
  style?: React.CSSProperties;
  fillColor?: string;
  strokeColor?: string;
  backgroundColor?: string;
}

export const Flower099: any = memo(({ 
  text = "花字", 
  style = {},
  fillColor = "#8B5CF6",
  strokeColor = "#7C3AED", 
  backgroundColor = "#6D28D9"
}: Flower099Props) => {
  const {fontSize, fontFamily, fontWeight, letterSpacing} = parseStyle(style);

  let realText = (typeof text === 'string' ? text : text?.props?.text) || '';

  const textDimensions = useMemo(() => measureTextDimensions({text: realText, fontFamily, fontSize, fontWeight}), [realText, fontFamily, fontSize, fontWeight]);
  
  const svgWidth = textDimensions.width
  const svgHeight = textDimensions.height
  
  const centerX = svgWidth / 2;
  const centerY = svgHeight / 2;
  
  const outerStrokeWidth = fontSize * 0.10;
  const innerStrokeWidth = fontSize * 0.05;
  
  const baselineOffset = textDimensions.height * 0.35;
  const textY = centerY + baselineOffset;
  
  const gradientId = `lavender99_${Math.random().toString(36).substr(2, 9)}`;
  
  return <svg style={{display: 'inline'}} 
    width={svgWidth} height={svgHeight} 
    viewBox={`0 0 ${svgWidth} ${svgHeight}`}
    fontFamily={fontFamily} fontSize={fontSize} fontWeight={fontWeight} letterSpacing={letterSpacing}
  >
    <defs>
        <linearGradient id={gradientId} x1="0%" y1="0%" x2="0%" y2="100%">
            <stop offset="0%" style={{stopColor:"#A78BFA", stopOpacity:1}} />
            <stop offset="50%" style={{stopColor:"#8B5CF6", stopOpacity:1}} />
            <stop offset="100%" style={{stopColor:"#7C3AED", stopOpacity:1}} />
        </linearGradient>
    </defs>
    {/* 外层深紫描边 */}
    <text x={centerX} y={textY} textAnchor="middle" className="svg-text style99-text"             
            fill="none" stroke={backgroundColor} strokeWidth={outerStrokeWidth}
            strokeLinejoin="round" strokeLinecap="round">{realText}</text>
    {/* 内层紫色描边 */}
    <text x={centerX} y={textY} textAnchor="middle" className="svg-text style99-text"             
            fill="none" stroke={strokeColor} strokeWidth={innerStrokeWidth}
            strokeLinejoin="round" strokeLinecap="round">{realText}</text>
    {/* 主体薰衣草紫渐变填充 */}
    <text x={centerX} y={textY} textAnchor="middle" className="svg-text style99-text"             
            fill={`url(#${gradientId})`}>{realText}</text>
    </svg>
});

Flower099.showName = '薰衣草紫渐变';
Flower099.description = '薰衣草紫色系自然渐变花字';
Flower099.key = 'Flower099'; 