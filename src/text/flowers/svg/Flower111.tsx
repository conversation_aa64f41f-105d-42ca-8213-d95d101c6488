import React, { memo, useMemo } from 'react';
import { measureTextDimensions, parseStyle, measureTextBaselineOffset } from './util';

interface Flower111Props {
  text?: string | any;
  style?: React.CSSProperties;
  fillColor?: string;
  strokeColor?: string;
  backgroundColor?: string;
}

export const Flower111: any = memo(
  ({
    text = '花字',
    style = {},
    fillColor = '#20B2AA',
    strokeColor = '#48D1CC',
    backgroundColor = '#008B8B',
  }: Flower111Props) => {
    const { fontSize, fontFamily, fontWeight, letterSpacing, fontStyle } =
      parseStyle(style);

    let realText = (typeof text === 'string' ? text : text?.props?.text) || '';

    const textDimensions = useMemo(
      () => measureTextDimensions({ text: realText, fontFamily, fontSize, fontWeight, fontStyle }),
      [realText, fontFamily, fontSize, fontWeight, fontStyle],
    );

    const svgWidth = textDimensions.width;
    const svgHeight = textDimensions.height;

    const centerX = svgWidth / 2;
    const centerY = svgHeight / 2;

    const outerStrokeWidth = fontSize * 0.20;
    const middleStrokeWidth = fontSize * 0.14;
    const innerStrokeWidth = fontSize * 0.08;

    const baselineOffset = measureTextBaselineOffset({text: realText, fontFamily, fontSize, fontWeight}) || textDimensions.height * 0.35;
    const textY = centerY + baselineOffset;

    const gradientId = `ocean111_${Math.random().toString(36).substr(2, 9)}`;
    const waveId = `wave111_${Math.random().toString(36).substr(2, 9)}`;

    return (
      <svg
        style={{ display: 'inline' }}
        width={svgWidth}
        height={svgHeight}
        viewBox={`0 0 ${svgWidth} ${svgHeight}`}
        fontFamily={fontFamily}
        fontSize={fontSize - outerStrokeWidth / 2}
        fontWeight={fontWeight}
        letterSpacing={letterSpacing}
      >
        <defs>
          <linearGradient id={gradientId} x1="0%" y1="0%" x2="0%" y2="100%">
            <stop offset="0%" style={{stopColor: "#87CEEB", stopOpacity: 1}} />
            <stop offset="25%" style={{stopColor: "#48D1CC", stopOpacity: 1}} />
            <stop offset="50%" style={{stopColor: "#20B2AA", stopOpacity: 1}} />
            <stop offset="75%" style={{stopColor: "#008B8B", stopOpacity: 1}} />
            <stop offset="100%" style={{stopColor: "#006666", stopOpacity: 1}} />
          </linearGradient>
          <filter id={waveId}>
            <feTurbulence baseFrequency="0.02" numOctaves="3" result="noise"/>
            <feDisplacementMap in="SourceGraphic" in2="noise" scale="2"/>
          </filter>
        </defs>
        {/* 外层深青描边 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style111-text"
          fill="none"
          stroke="#004444"
          strokeWidth={outerStrokeWidth}
          strokeLinejoin="round"
          strokeLinecap="round"
        >
          {realText}
        </text>
        {/* 中层青色描边 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style111-text"
          fill="none"
          stroke={backgroundColor}
          strokeWidth={middleStrokeWidth}
          strokeLinejoin="round"
          strokeLinecap="round"
        >
          {realText}
        </text>
        {/* 内层浅青描边 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style111-text"
          fill="none"
          stroke={strokeColor}
          strokeWidth={innerStrokeWidth}
          strokeLinejoin="round"
          strokeLinecap="round"
        >
          {realText}
        </text>
        {/* 主体海洋渐变填充 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style111-text"
          fill={`url(#${gradientId})`}
          filter={`url(#${waveId})`}
        >
          {realText}
        </text>
      </svg>
    );
  },
);

Flower111.showName = '深海波浪';
Flower111.description = '深海波浪效果多层青色花字';
Flower111.key = 'Flower111';
