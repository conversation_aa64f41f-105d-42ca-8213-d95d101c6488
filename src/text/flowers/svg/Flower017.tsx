import React, { memo, useMemo } from 'react';
import { measureTextDimensions, parseStyle } from './util';

interface Flower017Props {
  text?: string|any;
  style?: React.CSSProperties;
  fillColor?: string;
  strokeColor?: string;
  backgroundColor?: string;
}

export const Flower017: any = memo(({ 
  text = "花字", 
  style = {},
  fillColor = "#FFD700",
  strokeColor = "#B8860B", 
  backgroundColor = "#8B4513"
}: Flower017Props) => {
  const {fontSize, fontFamily, fontWeight, letterSpacing} = parseStyle(style);
     let realText = (typeof text === 'string' ? text : text?.props?.text) || '';
     const textDimensions = useMemo(
       () =>
         measureTextDimensions({
           text: realText,
           fontFamily,
           fontSize,
           fontWeight,
         }),
       [realText, fontFamily, fontSize, fontWeight],
     );
  
  const svgWidth = textDimensions.width
  const svgHeight = textDimensions.height
  
  const centerX = svgWidth / 2;
  const centerY = svgHeight / 2;
  
  const outerStrokeWidth = fontSize * 0.14;
  const innerStrokeWidth = fontSize * 0.07;
  
  const baselineOffset = textDimensions.height * 0.35;
  const textY = centerY + baselineOffset;
  
  const gradientId = `gold17_${Math.random().toString(36).substr(2, 9)}`;
  const filterId = `luxury17_${Math.random().toString(36).substr(2, 9)}`;
  
  return <svg style={{display: 'inline'}} 
    width={svgWidth} height={svgHeight} 
    viewBox={`0 0 ${svgWidth} ${svgHeight}`}
    fontFamily={fontFamily} fontSize={fontSize} fontWeight={fontWeight} letterSpacing={letterSpacing}
  >
    <defs>
        <linearGradient id={gradientId} x1="0%" y1="0%" x2="0%" y2="100%">
            <stop offset="0%" style={{stopColor:"#FFFF99", stopOpacity:1}} />
            <stop offset="25%" style={{stopColor:"#FFD700", stopOpacity:1}} />
            <stop offset="75%" style={{stopColor:"#FFA500", stopOpacity:1}} />
            <stop offset="100%" style={{stopColor:"#DAA520", stopOpacity:1}} />
        </linearGradient>
        <filter id={filterId}>
            <feDropShadow dx="4" dy="4" stdDeviation="3" floodColor="#8B4513" floodOpacity="0.6"/>
            <feGaussianBlur stdDeviation="1" result="coloredBlur"/>
            <feMerge> 
                <feMergeNode in="coloredBlur"/>
                <feMergeNode in="SourceGraphic"/>
            </feMerge>
        </filter>
    </defs>
    {/* 外层棕色描边 */}
    <text x={centerX} y={textY} textAnchor="middle" className="svg-text style17-text"             
            fill="none" stroke={backgroundColor} strokeWidth={outerStrokeWidth}
            strokeLinejoin="round" strokeLinecap="round">{realText}</text>
    {/* 内层暗金描边 */}
    <text x={centerX} y={textY} textAnchor="middle" className="svg-text style17-text"             
            fill="none" stroke={strokeColor} strokeWidth={innerStrokeWidth}
            strokeLinejoin="round" strokeLinecap="round">{realText}</text>
    {/* 主体金色垂直渐变填充 */}
    <text x={centerX} y={textY} textAnchor="middle" className="svg-text style17-text"             
            fill={`url(#${gradientId})`} filter={`url(#${filterId})`}>{realText}</text>
    </svg>
});

Flower017.showName = '金色垂直渐变';
Flower017.description = '金色垂直渐变花字';
Flower017.key = 'Flower017'; 