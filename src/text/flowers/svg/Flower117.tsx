import React, { memo, useMemo } from 'react';
import { measureTextDimensions, parseStyle, measureTextBaselineOffset } from './util';

interface Flower117Props {
  text?: string | any;
  style?: React.CSSProperties;
  fillColor?: string;
  strokeColor?: string;
  backgroundColor?: string;
}

export const Flower117: any = memo(
  ({
    text = '花字',
    style = {},
    fillColor = '#87CEEB',
    strokeColor = '#B0E0E6',
    backgroundColor = '#4682B4',
  }: Flower117Props) => {
    const { fontSize, fontFamily, fontWeight, letterSpacing, fontStyle } =
      parseStyle(style);

    let realText = (typeof text === 'string' ? text : text?.props?.text) || '';

    const textDimensions = useMemo(
      () => measureTextDimensions({ text: realText, fontFamily, fontSize, fontWeight, fontStyle }),
      [realText, fontFamily, fontSize, fontWeight, fontStyle],
    );

    const svgWidth = textDimensions.width;
    const svgHeight = textDimensions.height;

    const centerX = svgWidth / 2;
    const centerY = svgHeight / 2;

    const outerStrokeWidth = fontSize * 0.13;
    const innerStrokeWidth = fontSize * 0.065;

    const baselineOffset = measureTextBaselineOffset({text: realText, fontFamily, fontSize, fontWeight}) || textDimensions.height * 0.35;
    const textY = centerY + baselineOffset;

    const gradientId = `sky117_${Math.random().toString(36).substr(2, 9)}`;
    const cloudId = `cloud117_${Math.random().toString(36).substr(2, 9)}`;

    return (
      <svg
        style={{ display: 'inline' }}
        width={svgWidth}
        height={svgHeight}
        viewBox={`0 0 ${svgWidth} ${svgHeight}`}
        fontFamily={fontFamily}
        fontSize={fontSize - outerStrokeWidth / 2}
        fontWeight={fontWeight}
        letterSpacing={letterSpacing}
      >
        <defs>
          <linearGradient id={gradientId} x1="0%" y1="0%" x2="0%" y2="100%">
            <stop offset="0%" style={{stopColor: "#E0F6FF", stopOpacity: 1}} />
            <stop offset="25%" style={{stopColor: "#B0E0E6", stopOpacity: 1}} />
            <stop offset="50%" style={{stopColor: "#87CEEB", stopOpacity: 1}} />
            <stop offset="75%" style={{stopColor: "#6495ED", stopOpacity: 1}} />
            <stop offset="100%" style={{stopColor: "#4682B4", stopOpacity: 1}} />
          </linearGradient>
          <filter id={cloudId}>
            <feTurbulence baseFrequency="0.01" numOctaves="4" result="noise"/>
            <feColorMatrix in="noise" type="saturate" values="0"/>
            <feComponentTransfer>
              <feFuncA type="discrete" tableValues="0 .5 .5 .7 .7 .9 1"/>
            </feComponentTransfer>
            <feComposite in="SourceGraphic" in2="noise" operator="over"/>
          </filter>
        </defs>
        {/* 外层深蓝描边 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style117-text"
          fill="none"
          stroke="#191970"
          strokeWidth={outerStrokeWidth}
          strokeLinejoin="round"
          strokeLinecap="round"
        >
          {realText}
        </text>
        {/* 内层钢蓝描边 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style117-text"
          fill="none"
          stroke={backgroundColor}
          strokeWidth={innerStrokeWidth}
          strokeLinejoin="round"
          strokeLinecap="round"
        >
          {realText}
        </text>
        {/* 主体天空渐变填充 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style117-text"
          fill={`url(#${gradientId})`}
          filter={`url(#${cloudId})`}
        >
          {realText}
        </text>
      </svg>
    );
  },
);

Flower117.showName = '天空云朵';
Flower117.description = '天空蓝色渐变云朵纹理效果花字';
Flower117.key = 'Flower117';
