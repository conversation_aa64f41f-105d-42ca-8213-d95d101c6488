import React, { memo, useMemo } from 'react';
import { measureTextDimensions, parseStyle } from './util';

interface Flower068Props {
  text?: string | any;
  style?: React.CSSProperties;
  fillColor?: string;
  strokeColor?: string;
  backgroundColor?: string;
}

export const Flower068: any = memo(({ 
  text = "花字", 
  style = {},
  fillColor = "#65A30D",
  strokeColor = "#4D7C0F", 
  backgroundColor = "#365314"
}: Flower068Props) => {
  const {fontSize, fontFamily, fontWeight, letterSpacing} = parseStyle(style);

  let realText = (typeof text === 'string' ? text : text?.props?.text) || '';

  const textDimensions = useMemo(() => measureTextDimensions({text: realText, fontFamily, fontSize, fontWeight}), [realText, fontFamily, fontSize, fontWeight]);
  
  const svgWidth = textDimensions.width
  const svgHeight = textDimensions.height
  
  const centerX = svgWidth / 2;
  const centerY = svgHeight / 2;
  
  const outerStrokeWidth = fontSize * 0.10;
  const innerStrokeWidth = fontSize * 0.05;
  
  const baselineOffset = textDimensions.height * 0.35;
  const textY = centerY + baselineOffset;
  
  const gradientId = `limeGreen68_${Math.random().toString(36).substr(2, 9)}`;
  
  return <svg style={{display: 'inline'}} 
    width={svgWidth} height={svgHeight} 
    viewBox={`0 0 ${svgWidth} ${svgHeight}`}
    fontFamily={fontFamily} fontSize={fontSize} fontWeight={fontWeight} letterSpacing={letterSpacing}
  >
    <defs>
        <linearGradient id={gradientId} x1="0%" y1="0%" x2="0%" y2="100%">
            <stop offset="0%" style={{stopColor:"#84CC16", stopOpacity:1}} />
            <stop offset="50%" style={{stopColor:"#65A30D", stopOpacity:1}} />
            <stop offset="100%" style={{stopColor:"#4D7C0F", stopOpacity:1}} />
        </linearGradient>
    </defs>
    {/* 外层深绿描边 */}
    <text x={centerX} y={textY} textAnchor="middle" className="svg-text style68-text"             
            fill="none" stroke={backgroundColor} strokeWidth={outerStrokeWidth}
            strokeLinejoin="round" strokeLinecap="round">{realText}</text>
    {/* 内层绿色描边 */}
    <text x={centerX} y={textY} textAnchor="middle" className="svg-text style68-text"             
            fill="none" stroke={strokeColor} strokeWidth={innerStrokeWidth}
            strokeLinejoin="round" strokeLinecap="round">{realText}</text>
    {/* 主体黄绿渐变填充 */}
    <text x={centerX} y={textY} textAnchor="middle" className="svg-text style68-text"             
            fill={`url(#${gradientId})`}>{realText}</text>
    </svg>
});

Flower068.showName = '黄绿渐变';
Flower068.description = '黄绿色系自然渐变花字';
Flower068.key = 'Flower068'; 