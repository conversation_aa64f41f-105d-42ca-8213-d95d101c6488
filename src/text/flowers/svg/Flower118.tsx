import React, { memo, useMemo } from 'react';
import { measureTextDimensions, parseStyle, measureTextBaselineOffset } from './util';

interface Flower118Props {
  text?: string | any;
  style?: React.CSSProperties;
  fillColor?: string;
  strokeColor?: string;
  backgroundColor?: string;
}

export const Flower118: any = memo(
  ({
    text = '花字',
    style = {},
    fillColor = '#8A2BE2',
    strokeColor = '#DA70D6',
    backgroundColor = '#4B0082',
  }: Flower118Props) => {
    const { fontSize, fontFamily, fontWeight, letterSpacing, fontStyle } =
      parseStyle(style);

    let realText = (typeof text === 'string' ? text : text?.props?.text) || '';

    const textDimensions = useMemo(
      () => measureTextDimensions({ text: realText, fontFamily, fontSize, fontWeight, fontStyle }),
      [realText, fontFamily, fontSize, fontWeight, fontStyle],
    );

    const svgWidth = textDimensions.width;
    const svgHeight = textDimensions.height;

    const centerX = svgWidth / 2;
    const centerY = svgHeight / 2;

    const outerStrokeWidth = fontSize * 0.17;
    const innerStrokeWidth = fontSize * 0.085;

    const baselineOffset = measureTextBaselineOffset({text: realText, fontFamily, fontSize, fontWeight}) || textDimensions.height * 0.35;
    const textY = centerY + baselineOffset;

    const gradientId = `galaxy118_${Math.random().toString(36).substr(2, 9)}`;
    const starId = `star118_${Math.random().toString(36).substr(2, 9)}`;

    return (
      <svg
        style={{ display: 'inline' }}
        width={svgWidth}
        height={svgHeight}
        viewBox={`0 0 ${svgWidth} ${svgHeight}`}
        fontFamily={fontFamily}
        fontSize={fontSize - outerStrokeWidth / 2}
        fontWeight={fontWeight}
        letterSpacing={letterSpacing}
      >
        <defs>
          <radialGradient id={gradientId} cx="50%" cy="50%" r="70%">
            <stop offset="0%" style={{stopColor: "#E6E6FA", stopOpacity: 1}} />
            <stop offset="20%" style={{stopColor: "#DDA0DD", stopOpacity: 1}} />
            <stop offset="40%" style={{stopColor: "#DA70D6", stopOpacity: 1}} />
            <stop offset="60%" style={{stopColor: "#BA55D3", stopOpacity: 1}} />
            <stop offset="80%" style={{stopColor: "#8A2BE2", stopOpacity: 1}} />
            <stop offset="100%" style={{stopColor: "#4B0082", stopOpacity: 1}} />
          </radialGradient>
          <filter id={starId}>
            <feTurbulence baseFrequency="0.9" numOctaves="1" result="noise"/>
            <feColorMatrix in="noise" type="saturate" values="0"/>
            <feComponentTransfer>
              <feFuncA type="discrete" tableValues="0 0 0 0 0 0 0.7 0 0.9 1"/>
            </feComponentTransfer>
            <feComposite in="SourceGraphic" in2="noise" operator="screen"/>
          </filter>
        </defs>
        {/* 外层深紫描边 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style118-text"
          fill="none"
          stroke="#2E0854"
          strokeWidth={outerStrokeWidth}
          strokeLinejoin="round"
          strokeLinecap="round"
        >
          {realText}
        </text>
        {/* 内层靛紫描边 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style118-text"
          fill="none"
          stroke={backgroundColor}
          strokeWidth={innerStrokeWidth}
          strokeLinejoin="round"
          strokeLinecap="round"
        >
          {realText}
        </text>
        {/* 主体星系渐变填充 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style118-text"
          fill={`url(#${gradientId})`}
          filter={`url(#${starId})`}
        >
          {realText}
        </text>
      </svg>
    );
  },
);

Flower118.showName = '星系紫光';
Flower118.description = '星系紫色径向渐变星点效果花字';
Flower118.key = 'Flower118';
