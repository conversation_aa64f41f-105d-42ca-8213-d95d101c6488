import React, { memo, useMemo } from 'react';
import { measureTextDimensions, parseStyle } from './util';

interface Flower039Props {
  text?: string | any;
  style?: React.CSSProperties;
  fillColor?: string;
  strokeColor?: string;
  backgroundColor?: string;
}

export const Flower039: any = memo(
  ({
    text = '花字',
    style = {},
    fillColor = '#8A2BE2',
    strokeColor = '#663399',
    backgroundColor = '#4B0082',
  }: Flower039Props) => {
    const { fontSize, fontFamily, fontWeight, letterSpacing } =
      parseStyle(style);
    let realText = (typeof text === 'string' ? text : text?.props?.text) || '';
    const textDimensions = useMemo(
      () =>
        measureTextDimensions({
          text: realText,
          fontFamily,
          fontSize,
          fontWeight,
        }),
      [realText, fontFamily, fontSize, fontWeight],
    );

    const svgWidth = textDimensions.width;
    const svgHeight = textDimensions.height;

    const centerX = svgWidth / 2;
    const centerY = svgHeight / 2;

    const outerStrokeWidth = fontSize * 0.1;
    const innerStrokeWidth = fontSize * 0.05;

    const baselineOffset = textDimensions.height * 0.35;
    const textY = centerY + baselineOffset;

    const glowId = `purpleGlow39_${Math.random().toString(36).substr(2, 9)}`;

    return (
      <svg
        style={{ display: 'inline' }}
        width={svgWidth}
        height={svgHeight}
        viewBox={`0 0 ${svgWidth} ${svgHeight}`}
        fontFamily={fontFamily}
        fontSize={fontSize}
        fontWeight={fontWeight}
        letterSpacing={letterSpacing}
      >
        <defs>
          <filter id={glowId}>
            <feGaussianBlur stdDeviation="4" result="coloredBlur" />
            <feMerge>
              <feMergeNode in="coloredBlur" />
              <feMergeNode in="SourceGraphic" />
            </feMerge>
          </filter>
        </defs>
        {/* 外层靛蓝描边 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style39-text"
          fill="none"
          stroke={backgroundColor}
          strokeWidth={outerStrokeWidth}
          strokeLinejoin="round"
          strokeLinecap="round"
        >
          {realText}
        </text>
        {/* 内层深紫描边 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style39-text"
          fill="none"
          stroke={strokeColor}
          strokeWidth={innerStrokeWidth}
          strokeLinejoin="round"
          strokeLinecap="round"
        >
          {realText}
        </text>
        {/* 主体紫色发光填充 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style39-text"
          fill={fillColor}
          filter={`url(#${glowId})`}
        >
          {realText}
        </text>
      </svg>
    );
  },
);

Flower039.showName = '紫色发光';
Flower039.description = '紫色发光效果花字';
Flower039.key = 'Flower039';
