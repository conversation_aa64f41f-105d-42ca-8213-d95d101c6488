import React, { memo, useMemo } from 'react';
import { measureTextDimensions, parseStyle, measureTextBaselineOffset } from './util';

interface Flower123Props {
  text?: string | any;
  style?: React.CSSProperties;
  fillColor?: string;
  strokeColor?: string;
  backgroundColor?: string;
}

export const Flower123: any = memo(
  ({
    text = '花字',
    style = {},
    fillColor = '#9370DB',
    strokeColor = '#DDA0DD',
    backgroundColor = '#663399',
  }: Flower123Props) => {
    const { fontSize, fontFamily, fontWeight, letterSpacing, fontStyle } =
      parseStyle(style);

    let realText = (typeof text === 'string' ? text : text?.props?.text) || '';

    const textDimensions = useMemo(
      () => measureTextDimensions({ text: realText, fontFamily, fontSize, fontWeight, fontStyle }),
      [realText, fontFamily, fontSize, fontWeight, fontStyle],
    );

    const svgWidth = textDimensions.width;
    const svgHeight = textDimensions.height;

    const centerX = svgWidth / 2;
    const centerY = svgHeight / 2;

    const outerStrokeWidth = fontSize * 0.20;
    const middleStrokeWidth = fontSize * 0.14;
    const innerStrokeWidth = fontSize * 0.08;

    const baselineOffset = measureTextBaselineOffset({text: realText, fontFamily, fontSize, fontWeight}) || textDimensions.height * 0.35;
    const textY = centerY + baselineOffset;

    const gradientId = `magic123_${Math.random().toString(36).substr(2, 9)}`;
    const magicId = `magicSparkle123_${Math.random().toString(36).substr(2, 9)}`;

    return (
      <svg
        style={{ display: 'inline' }}
        width={svgWidth}
        height={svgHeight}
        viewBox={`0 0 ${svgWidth} ${svgHeight}`}
        fontFamily={fontFamily}
        fontSize={fontSize - outerStrokeWidth / 2}
        fontWeight={fontWeight}
        letterSpacing={letterSpacing}
      >
        <defs>
          <radialGradient id={gradientId} cx="50%" cy="50%" r="70%">
            <stop offset="0%" style={{stopColor: "#F8F8FF", stopOpacity: 1}} />
            <stop offset="20%" style={{stopColor: "#E6E6FA", stopOpacity: 1}} />
            <stop offset="40%" style={{stopColor: "#DDA0DD", stopOpacity: 1}} />
            <stop offset="60%" style={{stopColor: "#DA70D6", stopOpacity: 1}} />
            <stop offset="80%" style={{stopColor: "#9370DB", stopOpacity: 1}} />
            <stop offset="100%" style={{stopColor: "#663399", stopOpacity: 1}} />
          </radialGradient>
          <filter id={magicId}>
            <feTurbulence baseFrequency="0.8" numOctaves="2" result="noise"/>
            <feColorMatrix in="noise" type="saturate" values="0"/>
            <feComponentTransfer>
              <feFuncA type="discrete" tableValues="0 0 0 0.3 0 0.7 0 0.9 1"/>
            </feComponentTransfer>
            <feComposite in="SourceGraphic" in2="noise" operator="screen"/>
            <feGaussianBlur stdDeviation="1.5" result="glow"/>
            <feMerge>
              <feMergeNode in="glow"/>
              <feMergeNode in="SourceGraphic"/>
            </feMerge>
          </filter>
        </defs>
        {/* 外层深紫描边 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style123-text"
          fill="none"
          stroke="#2E0854"
          strokeWidth={outerStrokeWidth}
          strokeLinejoin="round"
          strokeLinecap="round"
        >
          {realText}
        </text>
        {/* 中层紫色描边 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style123-text"
          fill="none"
          stroke={backgroundColor}
          strokeWidth={middleStrokeWidth}
          strokeLinejoin="round"
          strokeLinecap="round"
        >
          {realText}
        </text>
        {/* 内层中紫描边 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style123-text"
          fill="none"
          stroke={fillColor}
          strokeWidth={innerStrokeWidth}
          strokeLinejoin="round"
          strokeLinecap="round"
        >
          {realText}
        </text>
        {/* 主体魔法渐变填充 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style123-text"
          fill={`url(#${gradientId})`}
          filter={`url(#${magicId})`}
        >
          {realText}
        </text>
      </svg>
    );
  },
);

Flower123.showName = '魔法闪烁';
Flower123.description = '魔法紫色多层闪烁星点效果花字';
Flower123.key = 'Flower123';
