import React, { memo, useMemo } from 'react';
import { measureTextDimensions, parseStyle } from './util';

interface Flower053Props {
  text?: string|any;
  style?: React.CSSProperties;
  fillColor?: string;
  strokeColor?: string;
  backgroundColor?: string;
}

export const Flower053: any = memo(({ 
  text = "花字", 
  style = {},
  fillColor = "#FFBF00",
  strokeColor = "#FF8C00", 
  backgroundColor = "#B8860B"
}: Flower053Props) => {
  const {fontSize, fontFamily, fontWeight, letterSpacing} = parseStyle(style);
   let realText = (typeof text === 'string' ? text : text?.props?.text) || '';
     const textDimensions = useMemo(
       () =>
         measureTextDimensions({
           text: realText,
           fontFamily,
           fontSize,
           fontWeight,
         }),
       [realText, fontFamily, fontSize, fontWeight],
     );
  
  const svgWidth = textDimensions.width
  const svgHeight = textDimensions.height
  
  const centerX = svgWidth / 2;
  const centerY = svgHeight / 2;
  
  const outerStrokeWidth = fontSize * 0.08;
  const innerStrokeWidth = fontSize * 0.04;
  
  const baselineOffset = textDimensions.height * 0.35;
  const textY = centerY + baselineOffset;
  
  const shadowId = `amber53_${Math.random().toString(36).substr(2, 9)}`;
  
  return <svg style={{display: 'inline'}} 
    width={svgWidth} height={svgHeight} 
    viewBox={`0 0 ${svgWidth} ${svgHeight}`}
    fontFamily={fontFamily} fontSize={fontSize} fontWeight={fontWeight} letterSpacing={letterSpacing}
  >
    <defs>
        <filter id={shadowId}>
            <feDropShadow dx="2" dy="2" stdDeviation="2" floodColor="#FFD700" floodOpacity="0.8"/>
        </filter>
    </defs>
    {/* 外层深金描边 */}
    <text x={centerX} y={textY} textAnchor="middle" className="svg-text style53-text"             
            fill="none" stroke={backgroundColor} strokeWidth={outerStrokeWidth}
            strokeLinejoin="round" strokeLinecap="round">{realText}</text>
    {/* 内层橙色描边 */}
    <text x={centerX} y={textY} textAnchor="middle" className="svg-text style53-text"             
            fill="none" stroke={strokeColor} strokeWidth={innerStrokeWidth}
            strokeLinejoin="round" strokeLinecap="round">{realText}</text>
    {/* 主体琥珀阴影填充 */}
    <text x={centerX} y={textY} textAnchor="middle" className="svg-text style53-text"             
            fill={fillColor} filter={`url(#${shadowId})`}>{realText}</text>
    </svg>
});

Flower053.showName = '琥珀阴影';
Flower053.description = '琥珀阴影效果花字';
Flower053.key = 'Flower053'; 