import React, { memo, useMemo } from 'react';
import { measureTextDimensions, parseStyle } from './util';

interface Flower051Props {
  text?: string|any;
  style?: React.CSSProperties;
  fillColor?: string;
  strokeColor?: string;
  backgroundColor?: string;
}

export const Flower051: any = memo(
  ({
    text = '花字',
    style = {},
    fillColor = '#50C878',
    strokeColor = '#228B22',
    backgroundColor = '#006400',
  }: Flower051Props) => {
    const { fontSize, fontFamily, fontWeight, letterSpacing } =
      parseStyle(style);
    let realText = (typeof text === 'string' ? text : text?.props?.text) || '';
    const textDimensions = useMemo(
      () =>
        measureTextDimensions({
          text: realText,
          fontFamily,
          fontSize,
          fontWeight,
        }),
      [realText, fontFamily, fontSize, fontWeight],
    );

    const svgWidth = textDimensions.width;
    const svgHeight = textDimensions.height;

    const centerX = svgWidth / 2;
    const centerY = svgHeight / 2;

    const outerStrokeWidth = fontSize * 0.1;
    const innerStrokeWidth = fontSize * 0.05;

    const baselineOffset = textDimensions.height * 0.35;
    const textY = centerY + baselineOffset;

    const gradientId = `emerald51_${Math.random().toString(36).substr(2, 9)}`;

    return (
      <svg
        style={{ display: 'inline' }}
        width={svgWidth}
        height={svgHeight}
        viewBox={`0 0 ${svgWidth} ${svgHeight}`}
        fontFamily={fontFamily}
        fontSize={fontSize}
        fontWeight={fontWeight}
        letterSpacing={letterSpacing}
      >
        <defs>
          <linearGradient id={gradientId} x1="0%" y1="0%" x2="0%" y2="100%">
            <stop
              offset="0%"
              style={{ stopColor: '#98FB98', stopOpacity: 1 }}
            />
            <stop
              offset="30%"
              style={{ stopColor: '#50C878', stopOpacity: 1 }}
            />
            <stop
              offset="70%"
              style={{ stopColor: '#228B22', stopOpacity: 1 }}
            />
            <stop
              offset="100%"
              style={{ stopColor: '#006400', stopOpacity: 1 }}
            />
          </linearGradient>
        </defs>
        {/* 外层深绿描边 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style51-text"
          fill="none"
          stroke={backgroundColor}
          strokeWidth={outerStrokeWidth}
          strokeLinejoin="round"
          strokeLinecap="round"
        >
          {realText}
        </text>
        {/* 内层森林绿描边 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style51-text"
          fill="none"
          stroke={strokeColor}
          strokeWidth={innerStrokeWidth}
          strokeLinejoin="round"
          strokeLinecap="round"
        >
          {realText}
        </text>
        {/* 主体翡翠绿垂直渐变填充 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style51-text"
          fill={`url(#${gradientId})`}
        >
          {realText}
        </text>
      </svg>
    );
  },
);

Flower051.showName = '翡翠绿垂直渐变';
Flower051.description = '翡翠绿垂直渐变花字';
Flower051.key = 'Flower051';
