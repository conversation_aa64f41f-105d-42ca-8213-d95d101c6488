import React, { memo, useMemo } from 'react';
import { measureTextDimensions, parseStyle, measureTextBaselineOffset } from './util';

interface Flower116Props {
  text?: string | any;
  style?: React.CSSProperties;
  fillColor?: string;
  strokeColor?: string;
  backgroundColor?: string;
}

export const Flower116: any = memo(
  ({
    text = '花字',
    style = {},
    fillColor = '#FF4500',
    strokeColor = '#FF6347',
    backgroundColor = '#8B0000',
  }: Flower116Props) => {
    const { fontSize, fontFamily, fontWeight, letterSpacing, fontStyle } =
      parseStyle(style);

    let realText = (typeof text === 'string' ? text : text?.props?.text) || '';

    const textDimensions = useMemo(
      () => measureTextDimensions({ text: realText, fontFamily, fontSize, fontWeight, fontStyle }),
      [realText, fontFamily, fontSize, fontWeight, fontStyle],
    );

    const svgWidth = textDimensions.width;
    const svgHeight = textDimensions.height;

    const centerX = svgWidth / 2;
    const centerY = svgHeight / 2;

    const outerStrokeWidth = fontSize * 0.22;
    const middleStrokeWidth = fontSize * 0.16;
    const innerStrokeWidth = fontSize * 0.10;

    const baselineOffset = measureTextBaselineOffset({text: realText, fontFamily, fontSize, fontWeight}) || textDimensions.height * 0.35;
    const textY = centerY + baselineOffset;

    const gradientId = `lava116_${Math.random().toString(36).substr(2, 9)}`;
    const lavaId = `lavaEffect116_${Math.random().toString(36).substr(2, 9)}`;

    return (
      <svg
        style={{ display: 'inline' }}
        width={svgWidth}
        height={svgHeight}
        viewBox={`0 0 ${svgWidth} ${svgHeight}`}
        fontFamily={fontFamily}
        fontSize={fontSize - outerStrokeWidth / 2}
        fontWeight={fontWeight}
        letterSpacing={letterSpacing}
      >
        <defs>
          <radialGradient id={gradientId} cx="50%" cy="30%" r="80%">
            <stop offset="0%" style={{stopColor: "#FFFF00", stopOpacity: 1}} />
            <stop offset="20%" style={{stopColor: "#FFA500", stopOpacity: 1}} />
            <stop offset="40%" style={{stopColor: "#FF4500", stopOpacity: 1}} />
            <stop offset="60%" style={{stopColor: "#FF0000", stopOpacity: 1}} />
            <stop offset="80%" style={{stopColor: "#DC143C", stopOpacity: 1}} />
            <stop offset="100%" style={{stopColor: "#8B0000", stopOpacity: 1}} />
          </radialGradient>
          <filter id={lavaId}>
            <feTurbulence baseFrequency="0.05" numOctaves="2" result="noise"/>
            <feDisplacementMap in="SourceGraphic" in2="noise" scale="3"/>
            <feGaussianBlur stdDeviation="1" result="blur"/>
            <feColorMatrix in="blur" type="saturate" values="1.5"/>
          </filter>
        </defs>
        {/* 外层黑色描边 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style116-text"
          fill="none"
          stroke="#000000"
          strokeWidth={outerStrokeWidth}
          strokeLinejoin="round"
          strokeLinecap="round"
        >
          {realText}
        </text>
        {/* 中层深红描边 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style116-text"
          fill="none"
          stroke={backgroundColor}
          strokeWidth={middleStrokeWidth}
          strokeLinejoin="round"
          strokeLinecap="round"
        >
          {realText}
        </text>
        {/* 内层橙红描边 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style116-text"
          fill="none"
          stroke={strokeColor}
          strokeWidth={innerStrokeWidth}
          strokeLinejoin="round"
          strokeLinecap="round"
        >
          {realText}
        </text>
        {/* 主体熔岩渐变填充 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style116-text"
          fill={`url(#${gradientId})`}
          filter={`url(#${lavaId})`}
        >
          {realText}
        </text>
      </svg>
    );
  },
);

Flower116.showName = '熔岩爆发';
Flower116.description = '熔岩爆发效果多层火焰渐变花字';
Flower116.key = 'Flower116';
