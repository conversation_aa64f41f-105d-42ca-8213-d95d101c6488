import React, { memo, useMemo } from 'react';
import { measureTextDimensions, parseStyle, measureTextBaselineOffset } from './util';

interface Flower135Props {
  text?: string | any;
  style?: React.CSSProperties;
  fillColor?: string;
  strokeColor?: string;
  backgroundColor?: string;
}

export const Flower135: any = memo(
  ({
    text = '花字',
    style = {},
    fillColor = '#9370DB',
    strokeColor = '#FFFFFF',
    backgroundColor = '#4B0082',
  }: Flower135Props) => {
    const { fontSize, fontFamily, fontWeight, letterSpacing, fontStyle } =
      parseStyle(style);

    let realText = (typeof text === 'string' ? text : text?.props?.text) || '';

    const textDimensions = useMemo(
      () => measureTextDimensions({ text: realText, fontFamily, fontSize, fontWeight, fontStyle }),
      [realText, fontFamily, fontSize, fontWeight, fontStyle],
    );

    // 为魔法效果增加padding
    const padding = fontSize * 0.35;
    const svgWidth = textDimensions.width + padding * 2;
    const svgHeight = textDimensions.height + padding * 2;

    const centerX = svgWidth / 2;
    const centerY = svgHeight / 2;

    // 清晰的描边设置
    const outerStrokeWidth = fontSize * 0.16;
    const middleStrokeWidth = fontSize * 0.09;
    const innerStrokeWidth = fontSize * 0.04;

    const baselineOffset = measureTextBaselineOffset({text: realText, fontFamily, fontSize, fontWeight}) || textDimensions.height * 0.35;
    const textY = centerY + baselineOffset;

    const gradientId = `magic135_${Math.random().toString(36).substr(2, 9)}`;
    const orbId = `orb135_${Math.random().toString(36).substr(2, 9)}`;
    const mysticalId = `mystical135_${Math.random().toString(36).substr(2, 9)}`;

    // 魔法光球位置
    const orbPositions = [
      { x: centerX - textDimensions.width * 0.35, y: centerY - textDimensions.height * 0.25, size: 0.04 },
      { x: centerX + textDimensions.width * 0.35, y: centerY - textDimensions.height * 0.25, size: 0.05 },
      { x: centerX, y: centerY - textDimensions.height * 0.4, size: 0.06 },
      { x: centerX - textDimensions.width * 0.25, y: centerY + textDimensions.height * 0.3, size: 0.04 },
      { x: centerX + textDimensions.width * 0.25, y: centerY + textDimensions.height * 0.3, size: 0.05 },
    ];

    return (
      <svg
        style={{ display: 'inline' }}
        width={svgWidth}
        height={svgHeight}
        viewBox={`0 0 ${svgWidth} ${svgHeight}`}
        fontFamily={fontFamily}
        fontSize={fontSize - outerStrokeWidth / 2}
        fontWeight={fontWeight}
        letterSpacing={letterSpacing}
      >
        <defs>
          {/* 魔法光球渐变 */}
          <radialGradient id={orbId} cx="50%" cy="50%" r="50%">
            <stop offset="0%" style={{stopColor: "#E6E6FA", stopOpacity: 0.8}} />
            <stop offset="50%" style={{stopColor: "#9370DB", stopOpacity: 0.6}} />
            <stop offset="100%" style={{stopColor: "#4B0082", stopOpacity: 0.3}} />
          </radialGradient>
          {/* 清晰的垂直紫色渐变 */}
          <linearGradient id={gradientId} x1="0%" y1="0%" x2="0%" y2="100%">
            <stop offset="0%" style={{stopColor: "#F8F8FF", stopOpacity: 1}} />
            <stop offset="25%" style={{stopColor: "#E6E6FA", stopOpacity: 1}} />
            <stop offset="50%" style={{stopColor: "#9370DB", stopOpacity: 1}} />
            <stop offset="75%" style={{stopColor: "#663399", stopOpacity: 1}} />
            <stop offset="100%" style={{stopColor: "#4B0082", stopOpacity: 1}} />
          </linearGradient>
          {/* 神秘滤镜 */}
          <filter id={mysticalId}>
            <feGaussianBlur stdDeviation="1.2" result="blur"/>
            <feSpecularLighting result="specOut" in="blur" specularConstant="2" specularExponent="25" lightingColor="white">
              <fePointLight x="40" y="25" z="180"/>
            </feSpecularLighting>
            <feComposite in="SourceGraphic" in2="specOut" operator="arithmetic" k1="0" k2="1" k3="0.7" k4="0"/>
          </filter>
        </defs>
        
        {/* 魔法光球装饰 */}
        {orbPositions.map((orb, index) => (
          <circle
            key={index}
            cx={orb.x}
            cy={orb.y}
            r={fontSize * orb.size}
            fill={`url(#${orbId})`}
            opacity="0.7"
          />
        ))}
        
        {/* 外层深紫描边 - 确保清晰度 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style135-text"
          fill="none"
          stroke="#2E0854"
          strokeWidth={outerStrokeWidth}
          strokeLinejoin="round"
          strokeLinecap="round"
        >
          {realText}
        </text>
        
        {/* 中层紫色描边 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style135-text"
          fill="none"
          stroke="#4B0082"
          strokeWidth={middleStrokeWidth}
          strokeLinejoin="round"
          strokeLinecap="round"
        >
          {realText}
        </text>
        
        {/* 内层白色描边 - 增强清晰度 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style135-text"
          fill="none"
          stroke="#FFFFFF"
          strokeWidth={innerStrokeWidth}
          strokeLinejoin="round"
          strokeLinecap="round"
        >
          {realText}
        </text>
        
        {/* 主体神秘紫色渐变填充 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style135-text"
          fill={`url(#${gradientId})`}
          filter={`url(#${mysticalId})`}
        >
          {realText}
        </text>
      </svg>
    );
  },
);

Flower135.showName = '神秘魔法';
Flower135.description = '神秘魔法清晰字幕特效';
Flower135.key = 'Flower135';
