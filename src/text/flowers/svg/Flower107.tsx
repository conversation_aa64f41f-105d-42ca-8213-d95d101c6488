import React, { memo, useMemo } from 'react';
import { measureTextDimensions, parseStyle, measureTextBaselineOffset } from './util';

interface Flower107Props {
  text?: string | any;
  style?: React.CSSProperties;
  fillColor?: string;
  strokeColor?: string;
  backgroundColor?: string;
}

export const Flower107: any = memo(
  ({
    text = '花字',
    style = {},
    fillColor = '#9B59B6',
    strokeColor = '#E74C3C',
    backgroundColor = '#2C3E50',
  }: Flower107Props) => {
    const { fontSize, fontFamily, fontWeight, letterSpacing, fontStyle } =
      parseStyle(style);

    let realText = (typeof text === 'string' ? text : text?.props?.text) || '';

    const textDimensions = useMemo(
      () => measureTextDimensions({ text: realText, fontFamily, fontSize, fontWeight, fontStyle }),
      [realText, fontFamily, fontSize, fontWeight, fontStyle],
    );

    const svgWidth = textDimensions.width;
    const svgHeight = textDimensions.height;

    const centerX = svgWidth / 2;
    const centerY = svgHeight / 2;

    const outerStrokeWidth = fontSize * 0.28;
    const layer2StrokeWidth = fontSize * 0.22;
    const layer3StrokeWidth = fontSize * 0.16;
    const layer4StrokeWidth = fontSize * 0.10;
    const innerStrokeWidth = fontSize * 0.05;

    const baselineOffset = measureTextBaselineOffset({text: realText, fontFamily, fontSize, fontWeight}) || textDimensions.height * 0.35;
    const textY = centerY + baselineOffset;

    const gradientId = `rainbow107_${Math.random().toString(36).substr(2, 9)}`;
    const gradientId2 = `rainbow107_2_${Math.random().toString(36).substr(2, 9)}`;
    const gradientId3 = `rainbow107_3_${Math.random().toString(36).substr(2, 9)}`;
    const shadowId = `shadow107_${Math.random().toString(36).substr(2, 9)}`;
    const glowId = `glow107_${Math.random().toString(36).substr(2, 9)}`;

    return (
      <svg
        style={{ display: 'inline' }}
        width={svgWidth}
        height={svgHeight}
        viewBox={`0 0 ${svgWidth} ${svgHeight}`}
        fontFamily={fontFamily}
        fontSize={fontSize - outerStrokeWidth / 2}
        fontWeight={fontWeight}
        letterSpacing={letterSpacing}
      >
        <defs>
          <linearGradient id={gradientId} x1="0%" y1="0%" x2="100%" y2="0%">
            <stop offset="0%" style={{stopColor: "#FF0080", stopOpacity: 1}} />
            <stop offset="16%" style={{stopColor: "#8000FF", stopOpacity: 1}} />
            <stop offset="33%" style={{stopColor: "#0080FF", stopOpacity: 1}} />
            <stop offset="50%" style={{stopColor: "#00FF80", stopOpacity: 1}} />
            <stop offset="66%" style={{stopColor: "#FFFF00", stopOpacity: 1}} />
            <stop offset="83%" style={{stopColor: "#FF8000", stopOpacity: 1}} />
            <stop offset="100%" style={{stopColor: "#FF0080", stopOpacity: 1}} />
          </linearGradient>
          <linearGradient id={gradientId2} x1="0%" y1="0%" x2="0%" y2="100%">
            <stop offset="0%" style={{stopColor: "#FFFFFF", stopOpacity: 0.9}} />
            <stop offset="50%" style={{stopColor: "#FFD700", stopOpacity: 0.5}} />
            <stop offset="100%" style={{stopColor: "#FF69B4", stopOpacity: 0.3}} />
          </linearGradient>
          <radialGradient id={gradientId3} cx="50%" cy="50%" r="70%">
            <stop offset="0%" style={{stopColor: "#FFFFFF", stopOpacity: 1}} />
            <stop offset="30%" style={{stopColor: "#FFD700", stopOpacity: 0.8}} />
            <stop offset="60%" style={{stopColor: "#FF69B4", stopOpacity: 0.6}} />
            <stop offset="100%" style={{stopColor: "#8A2BE2", stopOpacity: 0.4}} />
          </radialGradient>
          <filter id={shadowId}>
            <feDropShadow dx="4" dy="4" stdDeviation="3" floodColor="#000000" floodOpacity="0.7"/>
            <feDropShadow dx="-2" dy="-2" stdDeviation="2" floodColor="#FFFFFF" floodOpacity="0.3"/>
          </filter>
          <filter id={glowId}>
            <feGaussianBlur stdDeviation="4" result="coloredBlur"/>
            <feFlood floodColor="#FF69B4" floodOpacity="0.6"/>
            <feComposite in="SourceGraphic" in2="coloredBlur" operator="over"/>
            <feGaussianBlur stdDeviation="2" result="innerGlow"/>
            <feMerge>
              <feMergeNode in="coloredBlur"/>
              <feMergeNode in="innerGlow"/>
              <feMergeNode in="SourceGraphic"/>
            </feMerge>
          </filter>
        </defs>
        {/* 第一层：最外层黑色描边 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style107-text"
          fill="none"
          stroke="#000000"
          strokeWidth={outerStrokeWidth}
          strokeLinejoin="round"
          strokeLinecap="round"
        >
          {realText}
        </text>
        {/* 第二层：深色描边 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style107-text"
          fill="none"
          stroke={backgroundColor}
          strokeWidth={layer2StrokeWidth}
          strokeLinejoin="round"
          strokeLinecap="round"
        >
          {realText}
        </text>
        {/* 第三层：彩虹渐变描边 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style107-text"
          fill="none"
          stroke={`url(#${gradientId})`}
          strokeWidth={layer3StrokeWidth}
          strokeLinejoin="round"
          strokeLinecap="round"
        >
          {realText}
        </text>
        {/* 第四层：红色描边 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style107-text"
          fill="none"
          stroke={strokeColor}
          strokeWidth={layer4StrokeWidth}
          strokeLinejoin="round"
          strokeLinecap="round"
        >
          {realText}
        </text>
        {/* 第五层：紫色细描边 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style107-text"
          fill="none"
          stroke={fillColor}
          strokeWidth={innerStrokeWidth}
          strokeLinejoin="round"
          strokeLinecap="round"
        >
          {realText}
        </text>
        {/* 主体彩虹渐变填充 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style107-text"
          fill={`url(#${gradientId})`}
          filter={`url(#${shadowId})`}
        >
          {realText}
        </text>
        {/* 第一层高光效果 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style107-text"
          fill={`url(#${gradientId2})`}
          opacity="0.7"
        >
          {realText}
        </text>
        {/* 第二层高光效果 */}
        <text
          x={centerX}
          y={textY}
          textAnchor="middle"
          className="svg-text style107-text"
          fill={`url(#${gradientId3})`}
          opacity="0.5"
          filter={`url(#${glowId})`}
        >
          {realText}
        </text>
      </svg>
    );
  },
);

Flower107.showName = '彩虹炫光';
Flower107.description = '多层彩虹渐变炫光花字';
Flower107.key = 'Flower107';
