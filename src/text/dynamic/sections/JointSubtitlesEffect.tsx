import React from 'react';

export interface JointSubtitlesEffectProps {
    children: React.ReactNode;
}

/**
 * 文字弹跳特效组件
 * 从左到右逐个放大文字，每次只放大一个字，同时变为金色
 */
export const JointSubtitlesEffect = ({
  children
}: JointSubtitlesEffectProps) => {
  return children
};

// 为了在特效选择器中显示
JointSubtitlesEffect.key = 'JointSubtitlesEffect';
JointSubtitlesEffect.showName = '联合字幕特效';
JointSubtitlesEffect.description = '联合字幕特效';
JointSubtitlesEffect.supportsJoints = true;
JointSubtitlesEffect.sectionStyles = [
    {
        flower: "CelestialGloryText",
        fontSize: 90,
    },
    {
        flower: "MultiStrokeText",
        fontSize: 90,
    }
];

JointSubtitlesEffect.highlightStyles = [
    {
        // 第一个分段的高亮样式 - 明显的金色加粗
        color: '#FFD700', // 金色
        fontWeight: 'bold',
        fontSize: 80,     // 比普通文字大
        scale: 1.2       // 放大效果
    },
    {
        // 第二个分段的高亮样式 - 明显的红色加粗
        color: '#FF4444', // 红色
        fontWeight: 'bold',
        fontSize: 80,
        scale: 1.15
    }
];

JointSubtitlesEffect.sectionEffects = [
    {
        // 第一个分段的高亮特效
        loopEffects: 'EnergyPulseEffect',   // 发光循环特效
        effectDurations: {
            loopEffects: 1     // 循环速度
        }
    },
    {
        // 第二个分段的高亮特效
        inEffects: 'FadeInEffect',   // 放大入场特效
        outEffects: 'FadeOutEffect', // 淡出出场特效
        effectDurations: {
            inEffects: 0.5,
            outEffects: 0.5
        }
    }
];

JointSubtitlesEffect.highlightEffects = [] as any;