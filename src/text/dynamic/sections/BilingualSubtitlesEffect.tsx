import React from 'react';

export interface BilingualSubtitlesEffectProps {
  children: React.ReactNode;
}

/**
 * 文字弹跳特效组件
 * 从左到右逐个放大文字，每次只放大一个字，同时变为金色
 */
export const BilingualSubtitlesEffect = ({
  children
}: BilingualSubtitlesEffectProps) => {
  return children
};

// 为了在特效选择器中显示
BilingualSubtitlesEffect.key = 'BilingualSubtitlesEffect';
BilingualSubtitlesEffect.showName = '双语字幕特效';
BilingualSubtitlesEffect.description = '双语字幕特效';
BilingualSubtitlesEffect.supportsSections = true;
BilingualSubtitlesEffect.sectionStyles = [
    {
        fontSize: 90,
        position: {
            x: 0.1,
            y: 0.7
        }
    },
    {
        fontSize: 90,
        position: {
            x: 0.6,
            y: 0.8
        },
        maxWidth: 0.4,
    }
];

// 单句的样式
BilingualSubtitlesEffect.singleSectionStyle = {
  fontSize: 90,
  position: {
      x: null,
      y: 0.7
  }
};

BilingualSubtitlesEffect.highlightStyles = [
    {
        // 第一个分段的高亮样式 - 明显的金色加粗
        color: '#FFD700', // 金色
        fontWeight: 'bold',
        fontSize: 80,     // 比普通文字大
        scale: 1.2       // 放大效果
    },
    {
        // 第二个分段的高亮样式 - 明显的红色加粗
        color: '#FF4444', // 红色
        fontWeight: 'bold',
        fontSize: 80,
        scale: 1.15
    }
];

BilingualSubtitlesEffect.sectionEffects = [
    {
        // 第一个分段的高亮特效
        loopEffects: 'EnergyPulseEffect',   // 发光循环特效
        effectDurations: {
            loopEffects: 1     // 循环速度
        }
    },
    {
        // 第二个分段的高亮特效
        inEffects: 'FadeInEffect',   // 放大入场特效
        outEffects: 'FadeOutEffect', // 淡出出场特效
        effectDurations: {
            inEffects: 0.5,
            outEffects: 0.5
        }
    }
];

BilingualSubtitlesEffect.highlightEffects = [] as any;