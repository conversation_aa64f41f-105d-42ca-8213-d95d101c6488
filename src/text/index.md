```jsx

import FadeInEffect from "./effects/textInEffects/StarJumpEffect.tsx"
import {Player} from '@remotion/player';
import {
  Flower106, <PERSON>107, <PERSON>108, <PERSON>109, <PERSON>110,
  <PERSON>111, <PERSON>112, <PERSON>113, <PERSON>114, <PERSON>115,
  <PERSON>116, <PERSON>117, <PERSON>118, <PERSON>119, <PERSON>120,
  <PERSON>121, <PERSON>122, <PERSON>123, <PERSON>124, <PERSON>125,
  <PERSON>126, <PERSON>127, Flower128, Flower129, Flower130
} from './flowers/svg';

const Component = () => {
   return (
     <div style={{
       display: 'flex',
       flexDirection: 'column',
       gap: '30px',
       padding: '40px',
       overflow:"auto"
     }}>
       {/* 原有的文字特效 */}
       <div style={{marginBottom: '40px'}}>
         <h2 style={{color: '#333', marginBottom: '15px', fontSize: '24px'}}>文字动画特效</h2>
         <FadeInEffect
            text={'你信不信，我用AI一夜之间。。。'}
            style={{fontSize: 40}}
         />
       </div>

       {/* 字幕专用花字特效展示 */}
       <div>
         <h2 style={{color: '#333', marginBottom: '30px', fontSize: '28px', fontWeight: 'bold'}}>🎬 字幕专用花字特效 (Flower126-130)</h2>
         <p style={{color: '#666', marginBottom: '30px', fontSize: '16px'}}>
           专为视频字幕设计：单字独立效果 • 清晰可读 • 垂直渐变 • 静态背景装饰
         </p>

         <div style={{
           display: 'grid',
           gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
           gap: '30px',
           marginBottom: '40px'
         }}>

           {/* 字幕专用花字 */}
           <div style={{textAlign: 'center', padding: '25px'}}>
             <h3 style={{color: '#666', fontSize: '16px', marginBottom: '15px', fontWeight: 'bold'}}>Flower126 - 火焰背景</h3>
             <Flower126 text="火" style={{fontSize: 80, fontWeight: 'bold'}} />
             <p style={{color: '#999', fontSize: '12px', marginTop: '10px'}}>火焰圆形背景 + 垂直金色渐变</p>
           </div>

           <div style={{textAlign: 'center', padding: '25px'}}>
             <h3 style={{color: '#666', fontSize: '16px', marginBottom: '15px', fontWeight: 'bold'}}>Flower127 - 冰晶光晕</h3>
             <Flower127 text="冰" style={{fontSize: 80, fontWeight: 'bold'}} />
             <p style={{color: '#999', fontSize: '12px', marginTop: '10px'}}>冰晶椭圆光晕 + 垂直蓝色渐变</p>
           </div>

           <div style={{textAlign: 'center', padding: '25px'}}>
             <h3 style={{color: '#666', fontSize: '16px', marginBottom: '15px', fontWeight: 'bold'}}>Flower128 - 皇冠金字</h3>
             <Flower128 text="王" style={{fontSize: 80, fontWeight: 'bold'}} />
             <p style={{color: '#999', fontSize: '12px', marginTop: '10px'}}>皇冠装饰背景 + 垂直金色渐变</p>
           </div>

           <div style={{textAlign: 'center', padding: '25px'}}>
             <h3 style={{color: '#666', fontSize: '16px', marginBottom: '15px', fontWeight: 'bold'}}>Flower129 - 樱花飞舞</h3>
             <Flower129 text="花" style={{fontSize: 80, fontWeight: 'bold'}} />
             <p style={{color: '#999', fontSize: '12px', marginTop: '10px'}}>樱花花瓣背景 + 垂直粉色渐变</p>
           </div>

           <div style={{textAlign: 'center', padding: '25px'}}>
             <h3 style={{color: '#666', fontSize: '16px', marginBottom: '15px', fontWeight: 'bold'}}>Flower130 - 自然叶影</h3>
             <Flower130 text="叶" style={{fontSize: 80, fontWeight: 'bold'}} />
             <p style={{color: '#999', fontSize: '12px', marginTop: '10px'}}>自然叶子背景 + 垂直绿色渐变</p>
           </div>
         </div>

         {/* 字幕效果演示 */}
         <div style={{marginTop: '50px', textAlign: 'center'}}>
           <h3 style={{color: '#333', marginBottom: '30px', fontSize: '24px', fontWeight: 'bold'}}>📺 字幕效果演示</h3>

           {/* 单字展示 */}
           <div style={{marginBottom: '40px'}}>
             <h4 style={{color: '#666', fontSize: '18px', marginBottom: '20px'}}>单字独立效果（适合逐字显示）</h4>
             <div style={{
               display: 'flex',
               gap: '15px',
               justifyContent: 'center',
               flexWrap: 'wrap'
             }}>
               <Flower126 text="我" style={{fontSize: 72, fontWeight: 'bold'}} />
               <Flower127 text="爱" style={{fontSize: 72, fontWeight: 'bold'}} />
               <Flower128 text="中" style={{fontSize: 72, fontWeight: 'bold'}} />
               <Flower129 text="国" style={{fontSize: 72, fontWeight: 'bold'}} />
             </div>
           </div>

           {/* 句子展示 */}
           <div style={{marginBottom: '40px'}}>
             <h4 style={{color: '#666', fontSize: '18px', marginBottom: '20px'}}>句子效果（每个字独立渲染）</h4>
             <div style={{
               display: 'flex',
               gap: '8px',
               justifyContent: 'center',
               flexWrap: 'wrap'
             }}>
               <Flower126 text="欢" style={{fontSize: 56, fontWeight: 'bold'}} />
               <Flower127 text="迎" style={{fontSize: 56, fontWeight: 'bold'}} />
               <Flower128 text="来" style={{fontSize: 56, fontWeight: 'bold'}} />
               <Flower129 text="到" style={{fontSize: 56, fontWeight: 'bold'}} />
               <Flower130 text="我" style={{fontSize: 56, fontWeight: 'bold'}} />
               <Flower126 text="的" style={{fontSize: 56, fontWeight: 'bold'}} />
               <Flower127 text="频" style={{fontSize: 56, fontWeight: 'bold'}} />
               <Flower128 text="道" style={{fontSize: 56, fontWeight: 'bold'}} />
             </div>
           </div>

           {/* 英文展示 */}
           <div>
             <h4 style={{color: '#666', fontSize: '18px', marginBottom: '20px'}}>英文字幕效果</h4>
             <div style={{
               display: 'flex',
               gap: '5px',
               justifyContent: 'center',
               flexWrap: 'wrap'
             }}>
               <Flower126 text="H" style={{fontSize: 48, fontWeight: 'bold'}} />
               <Flower127 text="E" style={{fontSize: 48, fontWeight: 'bold'}} />
               <Flower128 text="L" style={{fontSize: 48, fontWeight: 'bold'}} />
               <Flower129 text="L" style={{fontSize: 48, fontWeight: 'bold'}} />
               <Flower130 text="O" style={{fontSize: 48, fontWeight: 'bold'}} />
               <span style={{width: '10px'}}></span>
               <Flower126 text="W" style={{fontSize: 48, fontWeight: 'bold'}} />
               <Flower127 text="O" style={{fontSize: 48, fontWeight: 'bold'}} />
               <Flower128 text="R" style={{fontSize: 48, fontWeight: 'bold'}} />
               <Flower129 text="L" style={{fontSize: 48, fontWeight: 'bold'}} />
               <Flower130 text="D" style={{fontSize: 48, fontWeight: 'bold'}} />
             </div>
           </div>
         </div>

         <h2 style={{color: '#333', marginBottom: '30px', fontSize: '28px', fontWeight: 'bold', marginTop: '60px'}}>🎨 艺术花字特效 (Flower106-125)</h2>
         <p style={{color: '#666', marginBottom: '30px', fontSize: '16px'}}>
           复杂艺术效果：多层描边 • 复合渐变 • 高级滤镜（适合标题装饰）
         </p>

         <div style={{
           display: 'grid',
           gridTemplateColumns: 'repeat(auto-fit, minmax(350px, 1fr))',
           gap: '40px',
           marginBottom: '40px'
         }}>

           {/* 第一行：超复杂霓虹系列 */}
           <div style={{textAlign: 'center', padding: '25px'}}>
             <h3 style={{color: '#666', fontSize: '16px', marginBottom: '15px', fontWeight: 'bold'}}>Flower106 - 超级霓虹橙光</h3>
             <Flower106 text="霓虹橙光" style={{fontSize: 64, fontWeight: 'bold'}} />
           </div>

           <div style={{textAlign: 'center', padding: '25px'}}>
             <h3 style={{color: '#666', fontSize: '16px', marginBottom: '15px', fontWeight: 'bold'}}>Flower107 - 七彩炫光</h3>
             <Flower107 text="彩虹炫光" style={{fontSize: 64, fontWeight: 'bold'}} />
           </div>

           <div style={{textAlign: 'center', padding: '25px'}}>
             <h3 style={{color: '#666', fontSize: '16px', marginBottom: '15px', fontWeight: 'bold'}}>Flower108 - 赛博朋克</h3>
             <Flower108 text="赛博朋克" style={{fontSize: 64, fontWeight: 'bold'}} />
           </div>

           <div style={{textAlign: 'center', padding: '25px'}}>
             <h3 style={{color: '#666', fontSize: '16px', marginBottom: '15px', fontWeight: 'bold'}}>Flower109 - 烈火金辉</h3>
             <Flower109 text="烈火金辉" style={{fontSize: 64, fontWeight: 'bold'}} />
           </div>

           <div style={{textAlign: 'center', padding: '25px'}}>
             <h3 style={{color: '#666', fontSize: '16px', marginBottom: '15px', fontWeight: 'bold'}}>Flower110 - 水晶紫光</h3>
             <Flower110 text="水晶紫光" style={{fontSize: 64, fontWeight: 'bold'}} />
           </div>

           {/* 第二行：自然系列 */}
           <div style={{textAlign: 'center', padding: '15px', backgroundColor: '#111', borderRadius: '8px'}}>
             <h3 style={{color: '#fff', fontSize: '14px', marginBottom: '10px'}}>Flower111 - 深海波浪</h3>
             <Flower111 text="深海波浪" style={{fontSize: 48}} />
           </div>

           <div style={{textAlign: 'center', padding: '15px', backgroundColor: '#111', borderRadius: '8px'}}>
             <h3 style={{color: '#fff', fontSize: '14px', marginBottom: '10px'}}>Flower112 - 樱花绽放</h3>
             <Flower112 text="樱花绽放" style={{fontSize: 48}} />
           </div>

           <div style={{textAlign: 'center', padding: '15px', backgroundColor: '#111', borderRadius: '8px'}}>
             <h3 style={{color: '#fff', fontSize: '14px', marginBottom: '10px'}}>Flower113 - 翠绿自然</h3>
             <Flower113 text="翠绿自然" style={{fontSize: 48}} />
           </div>

           <div style={{textAlign: 'center', padding: '15px', backgroundColor: '#111', borderRadius: '8px'}}>
             <h3 style={{color: '#fff', fontSize: '14px', marginBottom: '10px'}}>Flower114 - 秋叶飘落</h3>
             <Flower114 text="秋叶飘落" style={{fontSize: 48}} />
           </div>

           <div style={{textAlign: 'center', padding: '15px', backgroundColor: '#111', borderRadius: '8px'}}>
             <h3 style={{color: '#fff', fontSize: '14px', marginBottom: '10px'}}>Flower115 - 金属质感</h3>
             <Flower115 text="金属质感" style={{fontSize: 48}} />
           </div>

           {/* 第三行：特效系列 */}
           <div style={{textAlign: 'center', padding: '15px', backgroundColor: '#111', borderRadius: '8px'}}>
             <h3 style={{color: '#fff', fontSize: '14px', marginBottom: '10px'}}>Flower116 - 熔岩爆发</h3>
             <Flower116 text="熔岩爆发" style={{fontSize: 48}} />
           </div>

           <div style={{textAlign: 'center', padding: '15px', backgroundColor: '#111', borderRadius: '8px'}}>
             <h3 style={{color: '#fff', fontSize: '14px', marginBottom: '10px'}}>Flower117 - 天空云朵</h3>
             <Flower117 text="天空云朵" style={{fontSize: 48}} />
           </div>

           <div style={{textAlign: 'center', padding: '15px', backgroundColor: '#111', borderRadius: '8px'}}>
             <h3 style={{color: '#fff', fontSize: '14px', marginBottom: '10px'}}>Flower118 - 星系紫光</h3>
             <Flower118 text="星系紫光" style={{fontSize: 48}} />
           </div>

           <div style={{textAlign: 'center', padding: '15px', backgroundColor: '#111', borderRadius: '8px'}}>
             <h3 style={{color: '#fff', fontSize: '14px', marginBottom: '10px'}}>Flower119 - 霓虹粉光</h3>
             <Flower119 text="霓虹粉光" style={{fontSize: 48}} />
           </div>

           <div style={{textAlign: 'center', padding: '15px', backgroundColor: '#111', borderRadius: '8px'}}>
             <h3 style={{color: '#fff', fontSize: '14px', marginBottom: '10px'}}>Flower120 - 冰晶蓝光</h3>
             <Flower120 text="冰晶蓝光" style={{fontSize: 48}} />
           </div>

           {/* 第四行：奢华系列 */}
           <div style={{textAlign: 'center', padding: '15px', backgroundColor: '#111', borderRadius: '8px'}}>
             <h3 style={{color: '#fff', fontSize: '14px', marginBottom: '10px'}}>Flower121 - 皇家金辉</h3>
             <Flower121 text="皇家金辉" style={{fontSize: 48}} />
           </div>

           <div style={{textAlign: 'center', padding: '15px', backgroundColor: '#111', borderRadius: '8px'}}>
             <h3 style={{color: '#fff', fontSize: '14px', marginBottom: '10px'}}>Flower122 - 夕阳西下</h3>
             <Flower122 text="夕阳西下" style={{fontSize: 48}} />
           </div>

           <div style={{textAlign: 'center', padding: '15px', backgroundColor: '#111', borderRadius: '8px'}}>
             <h3 style={{color: '#fff', fontSize: '14px', marginBottom: '10px'}}>Flower123 - 魔法闪烁</h3>
             <Flower123 text="魔法闪烁" style={{fontSize: 48}} />
           </div>

           <div style={{textAlign: 'center', padding: '15px', backgroundColor: '#111', borderRadius: '8px'}}>
             <h3 style={{color: '#fff', fontSize: '14px', marginBottom: '10px'}}>Flower124 - 深林阴影</h3>
             <Flower124 text="深林阴影" style={{fontSize: 48}} />
           </div>

           <div style={{textAlign: 'center', padding: '15px', backgroundColor: '#111', borderRadius: '8px'}}>
             <h3 style={{color: '#fff', fontSize: '14px', marginBottom: '10px'}}>Flower125 - 钻石闪耀</h3>
             <Flower125 text="钻石闪耀" style={{fontSize: 48}} />
           </div>
         </div>

         {/* 超复杂特效展示区域 */}
         <div style={{marginTop: '50px', textAlign: 'center'}}>
           <h3 style={{color: '#333', marginBottom: '30px', fontSize: '24px', fontWeight: 'bold'}}>🌟 超复杂特效展示 🌟</h3>

           {/* 最复杂的钻石特效 */}
           <div style={{marginBottom: '40px', padding: '30px'}}>
             <h4 style={{color: '#666', fontSize: '18px', marginBottom: '20px'}}>七层描边钻石闪耀特效</h4>
             <Flower125 text="DIAMOND" style={{fontSize: 96, fontWeight: 'bold'}} />
             <p style={{color: '#999', fontSize: '14px', marginTop: '15px'}}>
               包含：黑色外框 → 深紫描边 → 彩虹描边 → 暗紫描边 → 粉色描边 → 金色描边 → 白色细描边 + 三层渐变填充
             </p>
           </div>

           {/* 其他复杂特效 */}
           <div style={{
             display: 'grid',
             gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
             gap: '30px',
             marginTop: '40px'
           }}>
             <div style={{textAlign: 'center', padding: '20px'}}>
               <h4 style={{color: '#666', fontSize: '16px', marginBottom: '15px'}}>五层霓虹发光</h4>
               <Flower106 text="NEON" style={{fontSize: 72, fontWeight: 'bold'}} />
               <p style={{color: '#999', fontSize: '12px', marginTop: '10px'}}>多层描边 + 发光滤镜</p>
             </div>
             <div style={{textAlign: 'center', padding: '20px'}}>
               <h4 style={{color: '#666', fontSize: '16px', marginBottom: '15px'}}>七彩渐变炫光</h4>
               <Flower107 text="RAINBOW" style={{fontSize: 72, fontWeight: 'bold'}} />
               <p style={{color: '#999', fontSize: '12px', marginTop: '10px'}}>彩虹渐变 + 多重阴影</p>
             </div>
             <div style={{textAlign: 'center', padding: '20px'}}>
               <h4 style={{color: '#666', fontSize: '16px', marginBottom: '15px'}}>火焰爆发效果</h4>
               <Flower109 text="FIRE" style={{fontSize: 72, fontWeight: 'bold'}} />
               <p style={{color: '#999', fontSize: '12px', marginTop: '10px'}}>径向渐变 + 火焰滤镜</p>
             </div>
           </div>

           {/* 英文展示 */}
           <div style={{marginTop: '50px'}}>
             <h4 style={{color: '#333', fontSize: '20px', marginBottom: '25px'}}>英文字体效果展示</h4>
             <div style={{
               display: 'flex',
               flexDirection: 'column',
               gap: '25px',
               alignItems: 'center'
             }}>
               <Flower125 text="AMAZING" style={{fontSize: 80, fontWeight: 'bold'}} />
               <Flower106 text="FANTASTIC" style={{fontSize: 80, fontWeight: 'bold'}} />
               <Flower107 text="WONDERFUL" style={{fontSize: 80, fontWeight: 'bold'}} />
             </div>
           </div>
         </div>
       </div>
     </div>
   )
}

export default () => {
   return  <Player style={{background:"white", color: '#333'}}
      component={ Component }
      durationInFrames={480} compositionWidth={1400} compositionHeight={1000} fps={30} autoPlay={true}
   />
}
```