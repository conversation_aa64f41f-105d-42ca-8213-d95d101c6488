import React from 'react';
import { useCurrentFrame, useVideoConfig } from 'remotion';

interface SyntaxHighlightEffectProps {
  text: string;
  style?: React.CSSProperties;
}

const SyntaxHighlightEffect: React.FC<SyntaxHighlightEffectProps> & { showName: string } = ({
  text,
  style
}) => {
  const frame = useCurrentFrame();
  const { fps } = useVideoConfig();
  
  const chars = text.split('');
  
  return (
    <div style={{
      display: 'inline-flex',
      alignItems: 'center',
      justifyContent: 'center',
      position: 'relative',
      backgroundColor: 'transparent',
      fontFamily: 'monospace',
      gap: '1px',
      ...style
    }}>
      {chars.map((char, index) => {
        // 语法高亮颜色
        const syntaxColors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7'];
        const colorIndex = (index + Math.floor(frame / 10)) % syntaxColors.length;
        const currentColor = syntaxColors[colorIndex];
        
        // 扫描高亮
        const scanProgress = (frame / fps * 2) % (chars.length + 2);
        const isScanning = Math.abs(scanProgress - index) < 1;
        
        return (
          <span key={index} style={{
            fontSize: '60px',
            fontWeight: 'bold',
            color: isScanning ? '#FFFFFF' : currentColor,
            display: 'inline-block',
            textShadow: isScanning 
              ? `0 0 10px ${currentColor}, 0 0 20px ${currentColor}`
              : `0 0 5px ${currentColor}`,
            transition: 'all 0.2s ease'
          }}>
            {char}
          </span>
        );
      })}
    </div>
  );
};

SyntaxHighlightEffect.showName = "语法高亮";

export default SyntaxHighlightEffect; 