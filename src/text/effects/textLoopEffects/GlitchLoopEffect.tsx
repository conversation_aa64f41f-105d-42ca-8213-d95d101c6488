import React from 'react';
import { useCurrentFrame } from 'remotion';

interface GlitchLoopEffectProps {
  text: string | React.ReactNode[];
  style: React.CSSProperties;
}

export default function GlitchLoopEffect({
  text = '展示文本',
  style = {},
}: GlitchLoopEffectProps) {
  const frame = useCurrentFrame();

  // 故障效果
  const glitchIntensity = Math.sin(frame * 0.3) * 0.5 + 0.5;
  const glitchX = (Math.random() - 0.5) * glitchIntensity * 4;
  const glitchY = (Math.random() - 0.5) * glitchIntensity * 2;
  
  // 随机颜色通道偏移
  const colorShift = Math.random() > 0.8;

  return (
    <span
      style={{
        display: 'inline-flex',
        alignItems: 'center',
        justifyContent: 'center',
        height: '100%',
      }}
    >
      <span
        style={{
          transform: `translate(${glitchX}px, ${glitchY}px)`,
          color: colorShift ? '#ff0080' : '#ffffff',
          textAlign: 'center',
          textShadow: colorShift 
            ? '2px 0 #00ffff, -2px 0 #ff0080' 
            : '0 0 8px #ffffff',
          filter: `contrast(${1 + glitchIntensity * 0.5})`,
          ...style,
        }}
      >
        {text}
      </span>
    </span>
  );
}

GlitchLoopEffect.key = 'GlitchLoopEffect';
GlitchLoopEffect.description = 'glitch loop text effect';
GlitchLoopEffect.showName = '故障循环'; 