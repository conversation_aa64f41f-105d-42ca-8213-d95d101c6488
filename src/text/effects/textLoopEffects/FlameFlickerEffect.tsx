import React from 'react';
import { useCurrentFrame, interpolate, useVideoConfig } from 'remotion';

interface FlameFlickerEffectProps {
  text: string;
  style?: React.CSSProperties;
}

const FlameFlickerEffect: React.FC<FlameFlickerEffectProps> & { showName: string } = ({
  text,
  style
}) => {
  const frame = useCurrentFrame();
  const { fps } = useVideoConfig();
  
  const swayAmplitude = 8; // ±8px摇摆幅度
  const distortionCoeff = 0.3; // 0.3扭曲系数
  
  const chars = text.split('');
  
  return (
    <div style={{
      display: 'inline-flex',
      alignItems: 'center',
      justifyContent: 'center',
      position: 'relative',
      backgroundColor: 'transparent',
      gap: '2px',
      ...style
    }}>
      {chars.map((char, index) => {
        const charOffset = index * 0.3;
        
        // 热浪扭曲
        const heatDistortion = Math.sin(frame * 0.2 + charOffset) * distortionCoeff;
        const verticalSway = Math.sin(frame * 0.15 + charOffset) * swayAmplitude;
        
        return (
          <div key={index} style={{
            position: 'relative',
            display: 'inline-block'
          }}>
            {/* 主文字 */}
            <span style={{
              fontSize: '60px',
              fontWeight: 'bold',
              color: 'white',
              display: 'inline-block',
              transform: `translate(${heatDistortion}px, ${verticalSway * 0.3}px)`,
              textShadow: `
                0 0 10px rgba(255, 100, 0, 0.8),
                0 0 20px rgba(255, 50, 0, 0.6),
                0 0 30px rgba(255, 0, 0, 0.4)
              `,
              filter: `blur(${Math.abs(heatDistortion) * 0.5}px)`
            }}>
              {char}
            </span>
            
            {/* 火焰粒子 */}
            {Array.from({ length: 8 }, (_, flameIndex) => {
              const flameX = (flameIndex - 4) * 8 + Math.sin(frame * 0.3 + flameIndex + charOffset) * 5;
              const flameHeight = 20 + Math.sin(frame * 0.25 + flameIndex + charOffset) * 15;
              const flameY = 35 + Math.sin(frame * 0.2 + flameIndex) * 5;
              const flameOpacity = Math.abs(Math.sin(frame * 0.4 + flameIndex + charOffset)) * 0.8;
              const flameSize = 3 + Math.sin(frame * 0.35 + flameIndex) * 2;
              
              // 火焰颜色变化
              const flameColor = flameHeight > 25 ? 
                `rgba(255, ${100 + flameHeight * 2}, 0, ${flameOpacity})` :
                `rgba(255, ${50 + flameHeight * 3}, 0, ${flameOpacity})`;
              
              return (
                <div
                  key={`flame-${flameIndex}`}
                  style={{
                    position: 'absolute',
                    left: '50%',
                    top: '50%',
                    width: `${flameSize}px`,
                    height: `${flameHeight}px`,
                    background: `linear-gradient(180deg, 
                      ${flameColor} 0%,
                      rgba(255, 150, 0, ${flameOpacity * 0.8}) 30%,
                      rgba(255, 200, 0, ${flameOpacity * 0.6}) 60%,
                      transparent 100%)`,
                    borderRadius: '50% 50% 50% 50% / 80% 80% 20% 20%',
                    transform: `translate(-50%, -50%) translate(${flameX}px, ${flameY}px)`,
                    filter: 'blur(1px)',
                    pointerEvents: 'none'
                  }}
                />
              );
            })}
            
            {/* 火星飞溅 */}
            {Array.from({ length: 4 }, (_, sparkIndex) => {
              const sparkLife = (frame + sparkIndex * 15) % 30;
              if (sparkLife > 20) return null;
              
              const sparkProgress = sparkLife / 20;
              const sparkAngle = (sparkIndex / 4) * Math.PI * 2 + Math.sin(frame * 0.1) * 0.5;
              const sparkDistance = sparkProgress * (20 + Math.random() * 15);
              const sparkX = Math.cos(sparkAngle) * sparkDistance;
              const sparkY = Math.sin(sparkAngle) * sparkDistance + 30;
              const sparkOpacity = interpolate(sparkProgress, [0, 0.3, 1], [0, 1, 0]);
              
              return (
                <div
                  key={`spark-${sparkIndex}`}
                  style={{
                    position: 'absolute',
                    left: '50%',
                    top: '50%',
                    width: '2px',
                    height: '2px',
                    background: `rgba(255, 200, 100, ${sparkOpacity})`,
                    borderRadius: '50%',
                    transform: `translate(-50%, -50%) translate(${sparkX}px, ${sparkY}px)`,
                    boxShadow: `0 0 4px rgba(255, 200, 100, ${sparkOpacity})`,
                    pointerEvents: 'none'
                  }}
                />
              );
            })}
            
            {/* 热浪效果 */}
            {Array.from({ length: 3 }, (_, heatIndex) => {
              const heatY = 25 + heatIndex * 10;
              const heatSway = Math.sin(frame * 0.3 + heatIndex + charOffset) * 3;
              const heatOpacity = Math.abs(Math.sin(frame * 0.25 + heatIndex)) * 0.2;
              
              return (
                <div
                  key={`heat-${heatIndex}`}
                  style={{
                    position: 'absolute',
                    left: '50%',
                    top: '50%',
                    width: '30px',
                    height: '8px',
                    background: `radial-gradient(ellipse, 
                      rgba(255, 100, 0, ${heatOpacity}) 0%, 
                      transparent 100%)`,
                    borderRadius: '50%',
                    transform: `translate(-50%, -50%) translate(${heatSway}px, ${heatY}px)`,
                    filter: 'blur(3px)',
                    pointerEvents: 'none'
                  }}
                />
              );
            })}
            
            {/* 底部发光 */}
            <div style={{
              position: 'absolute',
              left: '50%',
              top: '50%',
              width: '40px',
              height: '20px',
              background: `radial-gradient(ellipse, 
                rgba(255, 100, 0, ${Math.abs(Math.sin(frame * 0.2 + charOffset)) * 0.4}) 0%, 
                rgba(255, 50, 0, ${Math.abs(Math.sin(frame * 0.2 + charOffset)) * 0.2}) 50%, 
                transparent 100%)`,
              borderRadius: '50%',
              transform: 'translate(-50%, -50%) translateY(40px)',
              filter: 'blur(2px)',
              pointerEvents: 'none'
            }} />
          </div>
        );
      })}
    </div>
  );
};

FlameFlickerEffect.showName = "火焰摇曳";

export default FlameFlickerEffect; 