import React from 'react';
import { Easing, interpolate, useCurrentFrame, useVideoConfig } from 'remotion';

interface ZoomEffectProps {
  text: string;
  color?: string;
  fontSize?: number;
  style?: React.CSSProperties;
}

export default function ZoomEffect({
  text = '展示文本',
  color = '#fff',
  fontSize = 48,
  style = {},
}: ZoomEffectProps) {
  const frame = useCurrentFrame();
  const { fps } = useVideoConfig();

  // 缩放动画
  const scale = interpolate(frame, [0, 5, 10, 15], [0, 1.5, 0.8, 1], {
    easing: Easing.bezier(0.68, -0.55, 0.265, 1.55),
    extrapolateRight: 'clamp',
  });

  // 透明度
  const opacity = interpolate(frame, [0, 3, 15], [0, 1, 1], {
    extrapolateRight: 'clamp',
  });

  // 爆炸效果
  const explosionScale = frame < 5 ? interpolate(frame, [0, 5], [3, 1]) : 1;

  return (
    <span
      style={{
        display: 'inline-flex',
        alignItems: 'center',
        justifyContent: 'center',
        height: '100%',
        fontSize: `${fontSize}px`,
      }}
    >
      {/* 爆炸背景 */}
      <div
        style={{
          position: 'absolute',
          color: '#ff6b35',
          transform: `scale(${explosionScale})`,
          opacity: frame < 5 ? 0.3 : 0,
          filter: 'blur(10px)',
        }}
      >
        {text}
      </div>

      {/* 主文字 */}
      <span
        style={{
          color,
          transform: `scale(${scale})`,
          opacity,
          ...(style || {}),
        }}
      >
        {text}
      </span>
    </span>
  );
}

ZoomEffect.key = 'ZoomEffect';
ZoomEffect.description = 'zoom effect';
ZoomEffect.showName = '缩放';
