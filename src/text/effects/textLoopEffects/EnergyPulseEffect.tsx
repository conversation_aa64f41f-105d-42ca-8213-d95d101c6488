import React from 'react';
import { interpolate, useCurrentFrame } from 'remotion';

interface EnergyPulseEffectProps {
  text: string;
  color?: string;
  fontSize?: number;
  style?: React.CSSProperties;
}

export default function EnergyPulseEffect({
  text = '展示文本',
  color = '#fff',
  fontSize = 48,
  style = {},
}: EnergyPulseEffectProps) {
  const frame = useCurrentFrame();

  const pulseWaves = Array.from({ length: 3 }, (_, i) => {
    const delay = i * 10;
    const scale = interpolate((frame - delay) % 30, [0, 30], [0.5, 3], {
      extrapolateRight: 'clamp',
    });
    const opacity = interpolate((frame - delay) % 30, [0, 30], [0.8, 0], {
      extrapolateRight: 'clamp',
    });
    return { scale, opacity };
  });

  return (
    <span
      style={{
        display: 'inline-flex',
        alignItems: 'center',
        justifyContent: 'center',
        height: '100%',
        fontSize: `${fontSize}px`,
        position: 'relative',
      }}
    >
      {pulseWaves.map((wave, index) => (
        <div
          key={index}
          style={{
            position: 'absolute',
            width: '100px',
            height: '100px',
            border: `2px solid ${color}`,
            borderRadius: '50%',
            transform: `scale(${wave.scale})`,
            opacity: wave.opacity,
          }}
        />
      ))}

      <span
        style={{
          color: '#ffffff',

          zIndex: 10,
          ...(style || {}),
        }}
      >
        {text}
      </span>
    </span>
  );
}

EnergyPulseEffect.key = 'EnergyPulseEffect';
EnergyPulseEffect.description = 'energy pulse effect';
EnergyPulseEffect.showName = '能量脉冲';
