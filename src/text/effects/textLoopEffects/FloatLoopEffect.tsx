import React from 'react';
import { useCurrentFrame } from 'remotion';

interface FloatLoopEffectProps {
  text: string | React.ReactNode[];
  style: React.CSSProperties;
}

export default function FloatLoopEffect({
  text = '展示文本',
  style = {},
}: FloatLoopEffectProps) {
  const frame = useCurrentFrame();

  // 漂浮动画
  const floatY = Math.sin(frame * 0.08) * 5;
  const floatX = Math.sin(frame * 0.06) * 3;

  return (
    <span
      style={{
        display: 'inline-flex',
        alignItems: 'center',
        justifyContent: 'center',
        height: '100%',
      }}
    >
      <span
        style={{
          transform: `translate(${floatX}px, ${floatY}px)`,
          color: '#ffffff',
          textAlign: 'center',
          textShadow: '0 0 6px #ffffff',
          ...style,
        }}
      >
        {text}
      </span>
    </span>
  );
}

FloatLoopEffect.key = 'FloatLoopEffect';
FloatLoopEffect.description = 'floating loop text effect';
FloatLoopEffect.showName = '漂浮循环'; 