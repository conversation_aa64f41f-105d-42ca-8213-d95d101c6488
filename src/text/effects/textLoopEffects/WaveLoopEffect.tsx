import React from 'react';
import { useCurrentFrame } from 'remotion';

interface WaveLoopEffectProps {
  text: string | React.ReactNode[];
  style: React.CSSProperties;
}

export default function WaveLoopEffect({
  text = '展示文本',
  style = {},
}: WaveLoopEffectProps) {
  const frame = useCurrentFrame();

  // 波浪效果
  const waveY = Math.sin(frame * 0.1) * 8;
  const waveScale = 1 + Math.sin(frame * 0.12) * 0.1;

  return (
    <span
      style={{
        display: 'inline-flex',
        alignItems: 'center',
        justifyContent: 'center',
        height: '100%',
      }}
    >
      <span
        style={{
          transform: `translateY(${waveY}px) scale(${waveScale})`,
          color: '#ffffff',
          textAlign: 'center',
          textShadow: '0 0 8px #ffffff',
          ...style,
        }}
      >
        {text}
      </span>
    </span>
  );
}

WaveLoopEffect.key = 'WaveLoopEffect';
WaveLoopEffect.description = 'wave loop text effect';
WaveLoopEffect.showName = '波浪循环'; 