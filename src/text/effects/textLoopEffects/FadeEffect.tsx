import React from 'react';
import { Easing, interpolate, useCurrentFrame, useVideoConfig } from 'remotion';

interface FadeEffectProps {
  text: string;
  color?: string;
  fontSize?: number;
  style?: React.CSSProperties;
}

export default function FadeEffect({
  text = '展示文本',
  color = '#fff',
  fontSize = 48,
  style = {},
}: FadeEffectProps) {
  const frame = useCurrentFrame();
  const { fps } = useVideoConfig();

  // 透明度变化
  const opacity = interpolate(frame, [0, 8, 12, 15], [0, 1, 1, 0.7], {
    easing: Easing.inOut(Easing.cubic),
    extrapolateRight: 'clamp',
  });

  // 轻微的上升效果
  const translateY = interpolate(frame, [0, 10], [20, 0], {
    easing: Easing.out(Easing.cubic),
    extrapolateRight: 'clamp',
  });

  // 文字发光强度
  const glowIntensity = interpolate(opacity, [0, 1], [0, 25]);

  return (
    <span
      style={{
        display: 'inline-flex',
        alignItems: 'center',
        justifyContent: 'center',
        height: '100%',
        fontSize: `${fontSize}px`,
      }}
    >
      <span
        style={{
          color,
          opacity,
          transform: `translateY(${translateY}px)`,
          letterSpacing: '2px',
          ...(style || {}),
        }}
      >
        {text}
      </span>
    </span>
  );
}

FadeEffect.key = 'FadeEffect';
FadeEffect.description = 'fade effect';
FadeEffect.showName = '渐变';
