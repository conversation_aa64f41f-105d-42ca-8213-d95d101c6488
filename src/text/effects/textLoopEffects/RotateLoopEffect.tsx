import React from 'react';
import { useCurrentFrame } from 'remotion';

interface RotateLoopEffectProps {
  text: string | React.ReactNode[];
  style: React.CSSProperties;
}

export default function RotateLoopEffect({
  text = '展示文本',
  style = {},
}: RotateLoopEffectProps) {
  const frame = useCurrentFrame();

  // 旋转动画
  const rotation = frame * 2; // 每秒转动约48度

  return (
    <span
      style={{
        display: 'inline-flex',
        alignItems: 'center',
        justifyContent: 'center',
        height: '100%',
      }}
    >
      <span
        style={{
          transform: `rotate(${rotation}deg)`,
          color: '#ffffff',
          textAlign: 'center',
          textShadow: '0 0 8px #ffffff',
          ...style,
        }}
      >
        {text}
      </span>
    </span>
  );
}

RotateLoopEffect.key = 'RotateLoopEffect';
RotateLoopEffect.description = 'rotating loop text effect';
RotateLoopEffect.showName = '旋转循环'; 