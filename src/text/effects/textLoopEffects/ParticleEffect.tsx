import React from 'react';
import { interpolate, useCurrentFrame } from 'remotion';

interface ParticleEffectProps {
  text: string;
  color?: string;
  fontSize?: number;
  style?: React.CSSProperties;
}

export default function ParticleEffect({
  text = '展示文本',
  color = 'white',
  fontSize = 48,
  style = {},
}: ParticleEffectProps) {
  const frame = useCurrentFrame();

  // 生成粒子
  const particleCount = 20;
  const particles = Array.from({ length: particleCount }, (_, i) => {
    const angle = (i / particleCount) * Math.PI * 2;
    const radius = 80 + Math.sin(frame * 0.1 + i) * 30;
    const x = Math.cos(angle + frame * 0.05) * radius;
    const y = Math.sin(angle + frame * 0.05) * radius;

    const particleOpacity = interpolate(
      Math.sin(frame * 0.2 + i),
      [-1, 1],
      [0.3, 1],
    );

    const particleSize = interpolate(
      Math.sin(frame * 0.15 + i * 0.5),
      [-1, 1],
      [2, 8],
    );

    return {
      x,
      y,
      opacity: particleOpacity,
      size: particleSize,
      hue: (frame * 2 + i * 18) % 360,
    };
  });

  return (
    <span
      style={{
        display: 'inline-flex',
        alignItems: 'center',
        justifyContent: 'center',
        height: '100%',
        fontSize: `${fontSize}px`,
        position: 'relative',
      }}
    >
      {/* 粒子层 */}
      {particles.map((particle, index) => (
        <div
          key={index}
          style={{
            position: 'absolute',
            left: '50%',
            top: '50%',
            width: `${particle.size}px`,
            height: `${particle.size}px`,
            backgroundColor: `hsl(${particle.hue}, 100%, 60%)`,
            borderRadius: '50%',
            transform: `translate(${particle.x - particle.size / 2}px, ${
              particle.y - particle.size / 2
            }px)`,
            opacity: particle.opacity,
            boxShadow: `0 0 ${particle.size * 2}px hsl(${
              particle.hue
            }, 100%, 60%)`,
            filter: 'blur(0.5px)',
          }}
        />
      ))}

      {/* 主文字 */}
      <span
        style={{
          color,
          zIndex: 10,
          position: 'relative',
          ...(style || {}),
        }}
      >
        {text}
      </span>
    </span>
  );
} 

ParticleEffect.key = 'ParticleEffect';
ParticleEffect.description = 'particle effect';
ParticleEffect.showName = '粒子';
