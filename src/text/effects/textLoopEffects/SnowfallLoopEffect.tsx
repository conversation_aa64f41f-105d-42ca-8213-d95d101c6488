import React from 'react';
import { useCurrentFrame } from 'remotion';

interface SnowfallLoopEffectProps {
  text: string | React.ReactNode[];
  style: React.CSSProperties;
}

export default function SnowfallLoopEffect({
  text = '展示文本',
  style = {},
}: SnowfallLoopEffectProps) {
  const frame = useCurrentFrame();

  // 雪花飘落
  const snowflakes = Array.from({ length: 6 }, (_, i) => {
    const x = 20 + i * 12 + Math.sin(frame * 0.03 + i) * 15;
    const y = ((frame * 0.8 + i * 20) % 120);
    const snowOpacity = 0.6 + Math.sin(frame * 0.05 + i) * 0.4;
    
    return (
      <div
        key={i}
        style={{
          position: 'absolute',
          left: `${x}%`,
          top: `${y}%`,
          fontSize: '14px',
          opacity: snowOpacity,
          transform: 'translate(-50%, -50%)',
        }}
      >
        ❄️
      </div>
    );
  });

  return (
    <span
      style={{
        display: 'inline-flex',
        alignItems: 'center',
        justifyContent: 'center',
        height: '100%',
        position: 'relative',
      }}
    >
      <span
        style={{
          color: '#ffffff',
          textAlign: 'center',
          textShadow: '0 0 10px #ffffff',
          filter: `brightness(${1 + Math.sin(frame * 0.1) * 0.2})`,
          ...style,
        }}
      >
        {text}
      </span>
      {snowflakes}
    </span>
  );
}

SnowfallLoopEffect.key = 'SnowfallLoopEffect';
SnowfallLoopEffect.description = 'snowfall loop text effect with falling snowflakes';
SnowfallLoopEffect.showName = '雪花飘落'; 