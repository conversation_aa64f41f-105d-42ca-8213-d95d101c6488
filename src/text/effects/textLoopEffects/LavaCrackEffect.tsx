import React from 'react';
import { useCurrentFrame, interpolate, useVideoConfig } from 'remotion';

interface LavaCrackEffectProps {
  text: string;
  style?: React.CSSProperties;
}

const LavaCrackEffect: React.FC<LavaCrackEffectProps> & { showName: string } = ({
  text,
  style
}) => {
  const frame = useCurrentFrame();
  const { fps } = useVideoConfig();
  
  const crackSpeed = 5; // 5px/s裂纹速度
  const pulseFrequency = 0.5; // 0.5Hz脉动频率
  
  // 岩浆脉动
  const lavaGlow = Math.sin(frame * pulseFrequency * 2 * Math.PI / fps) * 0.5 + 0.5;
  
  return (
    <div style={{
      display: 'inline-flex',
      alignItems: 'center',
      justifyContent: 'center',
      position: 'relative',
      backgroundColor: 'transparent',
      ...style
    }}>
      {/* 主文字 */}
      <span style={{
        fontSize: '60px',
        fontWeight: 'bold',
        color: '#2A0A0A',
        display: 'inline-block',
        position: 'relative',
        textShadow: `
          0 0 ${10 + lavaGlow * 20}px #FF4500,
          0 0 ${20 + lavaGlow * 30}px #FF6B00,
          0 0 ${30 + lavaGlow * 40}px rgba(255, 69, 0, 0.5)
        `,
        filter: `brightness(${0.8 + lavaGlow * 0.6})`
      }}>
        {text}
        
        {/* 熔岩裂纹效果 */}
        <div style={{
          position: 'absolute',
          left: 0,
          top: 0,
          right: 0,
          bottom: 0,
          overflow: 'hidden',
          pointerEvents: 'none'
        }}>
          {/* 主要裂纹 */}
          {Array.from({ length: 8 }, (_, crackIndex) => {
            const crackProgress = ((frame * crackSpeed / fps) + crackIndex * 0.5) % 4;
            const crackLength = Math.min(crackProgress * 20, 60);
            const crackOpacity = Math.sin(crackProgress * Math.PI / 2) * (0.6 + lavaGlow * 0.4);
            
            const crackAngle = crackIndex * 45 + Math.sin(frame * 0.02 + crackIndex) * 10;
            const crackX = 20 + (crackIndex % 3) * 30;
            const crackY = 10 + Math.floor(crackIndex / 3) * 20;
            
            return (
              <div
                key={`crack-${crackIndex}`}
                style={{
                  position: 'absolute',
                  left: `${crackX}%`,
                  top: `${crackY}%`,
                  width: `${crackLength}px`,
                  height: '2px',
                  background: `linear-gradient(90deg, 
                    rgba(255, 69, 0, ${crackOpacity}) 0%, 
                    rgba(255, 140, 0, ${crackOpacity * 0.8}) 50%,
                    transparent 100%)`,
                  transform: `rotate(${crackAngle}deg)`,
                  transformOrigin: 'left center',
                  boxShadow: `0 0 ${3 + lavaGlow * 5}px rgba(255, 69, 0, ${crackOpacity})`,
                  pointerEvents: 'none'
                }}
              />
            );
          })}
          
          {/* 岩浆流淌效果 */}
          {Array.from({ length: 5 }, (_, lavaIndex) => {
            const lavaProgress = ((frame * 0.5 / fps) + lavaIndex * 0.8) % 3;
            const lavaY = lavaProgress * 30;
            const lavaOpacity = Math.sin(lavaProgress * Math.PI / 3) * (0.4 + lavaGlow * 0.6);
            
            return (
              <div
                key={`lava-${lavaIndex}`}
                style={{
                  position: 'absolute',
                  left: `${20 + lavaIndex * 15}%`,
                  top: `${lavaY}px`,
                  width: '3px',
                  height: '15px',
                  background: `linear-gradient(180deg, 
                    rgba(255, 100, 0, ${lavaOpacity}) 0%, 
                    rgba(255, 69, 0, ${lavaOpacity * 0.7}) 70%,
                    transparent 100%)`,
                  borderRadius: '2px',
                  opacity: lavaOpacity,
                  boxShadow: `0 0 4px rgba(255, 69, 0, ${lavaOpacity})`,
                  pointerEvents: 'none'
                }}
              />
            );
          })}
        </div>
      </span>
      
      {/* 热浪效果 */}
      <div style={{
        position: 'absolute',
        left: -20,
        right: -20,
        top: '100%',
        height: '30px',
        background: `
          linear-gradient(180deg, 
            rgba(255, 69, 0, ${lavaGlow * 0.2}) 0%, 
            transparent 100%)
        `,
        filter: 'blur(8px)',
        opacity: 0.6,
        pointerEvents: 'none'
      }} />
    </div>
  );
};

LavaCrackEffect.showName = "熔岩裂纹";

export default LavaCrackEffect; 