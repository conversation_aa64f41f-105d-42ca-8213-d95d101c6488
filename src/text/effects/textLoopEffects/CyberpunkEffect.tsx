import React from 'react';
import { interpolate, useCurrentFrame } from 'remotion';

interface CyberpunkEffectProps {
  text: string;
  color?: string;
  fontSize?: number;
  style?: React.CSSProperties;
}

export default function CyberpunkEffect({
  text = '展示文本',
  color = '#fff',
  fontSize = 48,
  style = {},
}: CyberpunkEffectProps) {
  const frame = useCurrentFrame();

  // 霓虹闪烁
  const neonFlicker = interpolate(
    Math.sin(frame * 1.2) + Math.sin(frame * 2.8) * 0.3,
    [-1.3, 1.3],
    [0.4, 1],
  );

  // 扫描线
  const scanLines = Array.from({ length: 5 }, (_, i) => {
    const y = ((frame * 2 + i * 30) % 200) - 100;
    return { y, opacity: interpolate(Math.abs(y), [0, 100], [0.8, 0]) };
  });

  return (
    <span
      style={{
        display: 'inline-flex',
        alignItems: 'center',
        justifyContent: 'center',
        height: '100%',
        fontSize: `${fontSize}px`,
        position: 'relative',
        background: 'linear-gradient(45deg, #0a0a0a, #1a0a1a)',
      }}
    >
      {/* 霓虹边框 */}
      <div
        style={{
          position: 'absolute',
          width: '300px',
          height: '150px',
          border: `2px solid ${color}`,
          borderRadius: '8px',
          opacity: neonFlicker * 0.6,
          boxShadow: `
            0 0 20px ${color},
            inset 0 0 20px ${color}40
          `,
        }}
      />

      {/* 扫描线 */}
      {scanLines.map((line, index) => (
        <div
          key={index}
          style={{
            position: 'absolute',
            left: 0,
            right: 0,
            height: '2px',
            background: `linear-gradient(90deg, transparent, #00ffff, transparent)`,
            transform: `translateY(${line.y}px)`,
            opacity: line.opacity * 0.5,
          }}
        />
      ))}

      {/* 主文字 */}
      <span
        style={{
          color: '#ffffff',
          zIndex: 10,
          position: 'relative',
          filter: `brightness(${neonFlicker})`,
          ...(style || {}),
        }}
      >
        {text}
      </span>
    </span>
  );
}

CyberpunkEffect.key = 'CyberpunkEffect';
CyberpunkEffect.description = 'cyberpunk effect';
CyberpunkEffect.showName = '赛博朋克';
