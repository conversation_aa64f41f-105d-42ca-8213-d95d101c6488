import React from 'react';
import { useCurrentFrame } from 'remotion';

interface ShimmerLoopEffectProps {
  text: string | React.ReactNode[];
  style: React.CSSProperties;
}

export default function ShimmerLoopEffect({
  text = '展示文本',
  style = {},
}: ShimmerLoopEffectProps) {
  const frame = useCurrentFrame();

  // 闪光效果
  const shimmer = Math.sin(frame * 0.2) * 0.5 + 0.5;
  const brightness = 1 + shimmer * 0.8;

  return (
    <span
      style={{
        display: 'inline-flex',
        alignItems: 'center',
        justifyContent: 'center',
        height: '100%',
      }}
    >
      <span
        style={{
          color: '#ffffff',
          textAlign: 'center',
          textShadow: `0 0 ${15 + shimmer * 10}px rgba(255, 255, 255, ${shimmer})`,
          filter: `brightness(${brightness})`,
          ...style,
        }}
      >
        {text}
      </span>
    </span>
  );
}

ShimmerLoopEffect.key = 'ShimmerLoopEffect';
ShimmerLoopEffect.description = 'shimmer loop text effect';
ShimmerLoopEffect.showName = '闪光循环'; 