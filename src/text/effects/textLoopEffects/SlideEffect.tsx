import React from 'react';
import { Easing, interpolate, useCurrentFrame, useVideoConfig } from 'remotion';

interface SlideEffectProps {
  text: string;
  color?: string;
  fontSize?: number;
  style?: React.CSSProperties;
}

export default function SlideEffect({
  text = '展示文本',
  color = '#fff',
  fontSize = 48,
  style = {},
}: SlideEffectProps) {
  const frame = useCurrentFrame();
  const { fps } = useVideoConfig();

  // 滑动位置
  const slideX = interpolate(frame, [0, 12], [-300, 0], {
    easing: Easing.out(Easing.back(1.7)),
    extrapolateRight: 'clamp',
  });

  // 旋转角度
  const rotation = interpolate(frame, [0, 12], [-45, 0], {
    easing: Easing.out(Easing.cubic),
    extrapolateRight: 'clamp',
  });

  return (
    <span
      style={{
        display: 'inline-flex',
        alignItems: 'center',
        justifyContent: 'center',
        height: '100%',
        fontSize: `${fontSize}px`,
        overflow: 'hidden',
      }}
    >
      <span
        style={{
          color,
          transform: `translateX(${slideX}px) rotate(${rotation}deg)`,
          ...(style || {}),
        }}
      >
        {text}
      </span>
    </span>
  );
}

SlideEffect.key = 'SlideEffect';
SlideEffect.description = 'slide effect';
SlideEffect.showName = '滑动';
