import React from 'react';
import { interpolate, useCurrentFrame, useVideoConfig } from 'remotion';

interface RotateEffectProps {
  text: string;
  color?: string;
  fontSize?: number;
  style?: React.CSSProperties;
}

export default function RotateEffect({
  text = '展示文本',
  color = 'white',
  fontSize = 48,
  style = {},
}: RotateEffectProps) {
  const frame = useCurrentFrame();
  const { fps } = useVideoConfig();

  // 旋转角度
  const rotationZ = (frame * 12) % 360;
  const rotationX = Math.sin(frame * 0.1) * 30;
  const rotationY = Math.cos(frame * 0.08) * 20;

  // 缩放变化
  const scale = interpolate(Math.sin(frame * 0.15), [-1, 1], [0.8, 1.2]);

  return (
    <span
      style={{
        display: 'inline-flex',
        alignItems: 'center',
        justifyContent: 'center',
        height: '100%',
        perspective: '1000px',
        fontFamily: 'Arial, sans-serif',
        fontSize: `${fontSize}px`,
        fontWeight: 'bold',
      }}
    >
      <span
        style={{
          color,
          transform: `
            rotateZ(${rotationZ}deg) 
            rotateX(${rotationX}deg) 
            rotateY(${rotationY}deg) 
            scale(${scale})
          `,
          transformStyle: 'preserve-3d',
          filter: `hue-rotate(${frame * 3}deg)`,
          ...(style || {}),
        }}
      >
        {text}
      </span>
    </span>
  );
}

RotateEffect.key = 'RotateEffect';
RotateEffect.description = 'rotate effect';
RotateEffect.showName = '旋转';
