import React from 'react';
import { useCurrentFrame } from 'remotion';

interface FlickerLoopEffectProps {
  text: string | React.ReactNode[];
  style: React.CSSProperties;
}

export default function FlickerLoopEffect({
  text = '展示文本',
  style = {},
}: FlickerLoopEffectProps) {
  const frame = useCurrentFrame();

  // 闪烁效果
  const flicker = Math.random() > 0.1 ? 1 : 0.3;
  const baseOpacity = 0.8 + Math.sin(frame * 0.3) * 0.2;

  return (
    <span
      style={{
        display: 'inline-flex',
        alignItems: 'center',
        justifyContent: 'center',
        height: '100%',
      }}
    >
      <span
        style={{
          opacity: baseOpacity * flicker,
          color: '#ffffff',
          textAlign: 'center',
          textShadow: `0 0 ${8 * flicker}px #ffffff`,
          ...style,
        }}
      >
        {text}
      </span>
    </span>
  );
}

FlickerLoopEffect.key = 'FlickerLoopEffect';
FlickerLoopEffect.description = 'flicker loop text effect';
FlickerLoopEffect.showName = '闪烁循环'; 