import React from 'react';
import { useCurrentFrame } from 'remotion';

interface GlowPulseLoopEffectProps {
  text: string | React.ReactNode[];
  style: React.CSSProperties;
}

export default function GlowPulseLoopEffect({
  text = '展示文本',
  style = {},
}: GlowPulseLoopEffectProps) {
  const frame = useCurrentFrame();

  // 发光脉冲效果
  const glowIntensity = 0.5 + Math.sin(frame * 0.15) * 0.5;
  const shadowBlur = 8 + Math.sin(frame * 0.1) * 12;

  return (
    <span
      style={{
        display: 'inline-flex',
        alignItems: 'center',
        justifyContent: 'center',
        height: '100%',
      }}
    >
      <span
        style={{
          color: '#ffffff',
          textAlign: 'center',
          textShadow: `0 0 ${shadowBlur}px rgba(255, 255, 255, ${glowIntensity})`,
          filter: `brightness(${0.8 + glowIntensity * 0.4})`,
          ...style,
        }}
      >
        {text}
      </span>
    </span>
  );
}

GlowPulseLoopEffect.key = 'GlowPulseLoopEffect';
GlowPulseLoopEffect.description = 'glow pulse loop text effect';
GlowPulseLoopEffect.showName = '发光脉冲'; 