import React from 'react';
import { useCurrentFrame, interpolate, useVideoConfig } from 'remotion';

interface ElectricSurgeEffectProps {
  text: string;
  style?: React.CSSProperties;
}

const ElectricSurgeEffect: React.FC<ElectricSurgeEffectProps> & { showName: string } = ({
  text,
  style
}) => {
  const frame = useCurrentFrame();
  const { fps } = useVideoConfig();
  
  const arcSpeed = 120; // 120px/s电弧速度
  const nodeCount = 8; // 8个节点
  
  const chars = text.split('');
  
  return (
    <div style={{
      display: 'inline-flex',
      alignItems: 'center',
      justifyContent: 'center',
      position: 'relative',
      backgroundColor: 'transparent',
      gap: '4px',
      ...style
    }}>
      {chars.map((char, index) => {
        const charOffset = index * 0.5;
        
        return (
          <div key={index} style={{
            position: 'relative',
            display: 'inline-block'
          }}>
            {/* 主文字 */}
            <span style={{
              fontSize: '60px',
              fontWeight: 'bold',
              color: 'white',
              display: 'inline-block',
              textShadow: `
                0 0 5px rgba(100, 200, 255, 0.8),
                0 0 10px rgba(100, 200, 255, 0.6),
                0 0 15px rgba(100, 200, 255, 0.4)
              `
            }}>
              {char}
            </span>
            
            {/* 电弧节点 */}
            {Array.from({ length: nodeCount }, (_, nodeIndex) => {
              const nodeAngle = (nodeIndex / nodeCount) * Math.PI * 2;
              const nodeRadius = 35;
              const nodeX = Math.cos(nodeAngle) * nodeRadius;
              const nodeY = Math.sin(nodeAngle) * nodeRadius;
              
              // 节点闪烁
              const nodeFlicker = Math.sin(frame * 0.3 + nodeIndex + charOffset) > 0.5 ? 1 : 0.3;
              
              return (
                <div
                  key={`node-${nodeIndex}`}
                  style={{
                    position: 'absolute',
                    left: '50%',
                    top: '50%',
                    width: '4px',
                    height: '4px',
                    background: `rgba(100, 200, 255, ${nodeFlicker})`,
                    borderRadius: '50%',
                    transform: `translate(-50%, -50%) translate(${nodeX}px, ${nodeY}px)`,
                    boxShadow: `0 0 8px rgba(100, 200, 255, ${nodeFlicker})`,
                    pointerEvents: 'none'
                  }}
                />
              );
            })}
            
            {/* 游走的电弧 */}
            {Array.from({ length: 3 }, (_, arcIndex) => {
              const arcProgress = ((frame * arcSpeed / fps / 100) + (arcIndex * 0.33) + charOffset) % 1;
              const arcAngle = arcProgress * Math.PI * 2;
              const arcRadius = 30 + Math.sin(frame * 0.1 + arcIndex) * 5;
              const arcX = Math.cos(arcAngle) * arcRadius;
              const arcY = Math.sin(arcAngle) * arcRadius;
              
              // 电弧强度变化
              const arcIntensity = Math.abs(Math.sin(frame * 0.2 + arcIndex));
              
              return (
                <div
                  key={`arc-${arcIndex}`}
                  style={{
                    position: 'absolute',
                    left: '50%',
                    top: '50%',
                    width: '3px',
                    height: '8px',
                    background: `linear-gradient(180deg, 
                      rgba(255, 255, 255, ${arcIntensity}) 0%,
                      rgba(100, 200, 255, ${arcIntensity * 0.8}) 50%,
                      transparent 100%)`,
                    borderRadius: '2px',
                    transform: `translate(-50%, -50%) translate(${arcX}px, ${arcY}px) rotate(${arcAngle}rad)`,
                    boxShadow: `0 0 6px rgba(100, 200, 255, ${arcIntensity})`,
                    pointerEvents: 'none'
                  }}
                />
              );
            })}
            
            {/* 电弧连接线 */}
            {Array.from({ length: 2 }, (_, lineIndex) => {
              const lineProgress = ((frame * 0.05) + (lineIndex * 0.5) + charOffset) % 1;
              const startAngle = lineProgress * Math.PI * 2;
              const endAngle = startAngle + Math.PI * 0.8;
              
              const startRadius = 32;
              const startX = Math.cos(startAngle) * startRadius;
              const startY = Math.sin(startAngle) * startRadius;
              const endX = Math.cos(endAngle) * startRadius;
              const endY = Math.sin(endAngle) * startRadius;
              
              const lineLength = Math.sqrt((endX - startX) ** 2 + (endY - startY) ** 2);
              const lineAngle = Math.atan2(endY - startY, endX - startX);
              
              const lineOpacity = Math.abs(Math.sin(frame * 0.15 + lineIndex));
              
              return (
                <div
                  key={`line-${lineIndex}`}
                  style={{
                    position: 'absolute',
                    left: '50%',
                    top: '50%',
                    width: `${lineLength}px`,
                    height: '1px',
                    background: `linear-gradient(90deg, 
                      rgba(100, 200, 255, ${lineOpacity}) 0%,
                      rgba(255, 255, 255, ${lineOpacity * 0.8}) 50%,
                      rgba(100, 200, 255, ${lineOpacity}) 100%)`,
                    transform: `translate(-50%, -50%) translate(${startX}px, ${startY}px) rotate(${lineAngle}rad)`,
                    transformOrigin: '0 50%',
                    boxShadow: `0 0 3px rgba(100, 200, 255, ${lineOpacity})`,
                    pointerEvents: 'none'
                  }}
                />
              );
            })}
            
            {/* 随机电火花 */}
            {frame % 8 < 2 && (
              <>
                {Array.from({ length: 4 }, (_, sparkIndex) => {
                  const sparkAngle = Math.random() * Math.PI * 2;
                  const sparkDistance = 25 + Math.random() * 15;
                  const sparkX = Math.cos(sparkAngle) * sparkDistance;
                  const sparkY = Math.sin(sparkAngle) * sparkDistance;
                  
                  return (
                    <div
                      key={`spark-${sparkIndex}`}
                      style={{
                        position: 'absolute',
                        left: '50%',
                        top: '50%',
                        width: '2px',
                        height: '6px',
                        background: 'rgba(255, 255, 255, 0.9)',
                        borderRadius: '1px',
                        transform: `translate(-50%, -50%) translate(${sparkX}px, ${sparkY}px) rotate(${sparkAngle}rad)`,
                        boxShadow: '0 0 4px rgba(255, 255, 255, 0.8)',
                        pointerEvents: 'none'
                      }}
                    />
                  );
                })}
              </>
            )}
          </div>
        );
      })}
    </div>
  );
};

ElectricSurgeEffect.showName = "电流涌动";

export default ElectricSurgeEffect; 