import React from 'react';
import { useCurrentFrame, interpolate, useVideoConfig } from 'remotion';

interface BreathingPulseEffectProps {
  text: string;
  style?: React.CSSProperties;
}

const BreathingPulseEffect: React.FC<BreathingPulseEffectProps> & { showName: string } = ({
  text,
  style
}) => {
  const frame = useCurrentFrame();
  const { fps } = useVideoConfig();
  
  const cycleDuration = 2 * fps; // 2秒周期
  const cycleProgress = (frame % cycleDuration) / cycleDuration;
  
  // 使用正弦波实现ease-in-out效果
  const breathingPhase = Math.sin(cycleProgress * Math.PI * 2);
  
  // 缩放范围95%~105%
  const scale = interpolate(breathingPhase, [-1, 1], [0.95, 1.05]);
  
  // 透明度微幅变化
  const opacity = interpolate(breathingPhase, [-1, 1], [0.85, 1]);
  
  return (
    <div style={{
      display: 'inline-flex',
      alignItems: 'center',
      justifyContent: 'center',
      position: 'relative',
      backgroundColor: 'transparent',
      ...style
    }}>
      <span style={{
        fontSize: '60px',
        fontWeight: 'bold',
        color: 'white',
        display: 'inline-block',
        transform: `scale(${scale})`,
        opacity: opacity,
        transition: 'none',
        textShadow: `0 0 ${interpolate(breathingPhase, [-1, 1], [8, 16])}px rgba(255,255,255,0.4)`
      }}>
        {text}
      </span>
    </div>
  );
};

BreathingPulseEffect.showName = "呼吸脉动";

export default BreathingPulseEffect; 