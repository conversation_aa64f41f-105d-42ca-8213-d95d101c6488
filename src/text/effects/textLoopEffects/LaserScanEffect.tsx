import React from 'react';
import { interpolate, useCurrentFrame } from 'remotion';

interface LaserScanEffectProps {
  text: string;
  color?: string;
  fontSize?: number;
  style?: React.CSSProperties;
}

export default function LaserScanEffect({
  text = '展示文本',
  color = '#fff',
  fontSize = 48,
  style = {},
}: LaserScanEffectProps) {
  const frame = useCurrentFrame();

  // 扫描线角度
  const scanAngle = (frame * 12) % 360;

  // 雷达波纹
  const radarPulse = (frame * 3) % 60;
  const radarOpacity = interpolate(radarPulse, [0, 60], [0.8, 0]);
  const radarScale = interpolate(radarPulse, [0, 60], [0.5, 2]);

  // 激光网格
  const gridOpacity = interpolate(Math.sin(frame * 0.4), [-1, 1], [0.2, 0.6]);

  // 文字发现效果
  const discoveryScale = interpolate(
    Math.sin(frame * 0.3),
    [-1, 1],
    [0.95, 1.05],
  );

  return (
    <span
      style={{
        display: 'inline-flex',
        alignItems: 'center',
        justifyContent: 'center',
        height: '100%',
        fontSize: `${fontSize}px`,
        position: 'relative',
        overflow: 'hidden',
      }}
    >
      {/* 雷达圆圈 */}
      {[1, 2, 3].map((ring) => (
        <div
          key={ring}
          style={{
            position: 'absolute',
            width: `${ring * 80}px`,
            height: `${ring * 80}px`,
            border: `1px solid ${color}`,
            borderRadius: '50%',
            opacity: 0.3,
          }}
        />
      ))}

      {/* 雷达脉冲 */}
      <div
        style={{
          position: 'absolute',
          width: '200px',
          height: '200px',
          border: `2px solid ${color}`,
          borderRadius: '50%',
          transform: `scale(${radarScale})`,
          opacity: radarOpacity,
          boxShadow: `0 0 20px ${color}`,
        }}
      />

      {/* 扫描线 */}
      <div
        style={{
          position: 'absolute',
          width: '150px',
          height: '2px',
          background: `linear-gradient(90deg, transparent, ${color}, transparent)`,
          transformOrigin: 'left center',
          transform: `rotate(${scanAngle}deg)`,
          boxShadow: `0 0 10px ${color}`,
          left: '50%',
          top: '50%',
        }}
      />

      {/* 激光网格 */}
      <div
        style={{
          position: 'absolute',
          width: '300px',
          height: '300px',
          backgroundImage: `
            linear-gradient(${color}40 1px, transparent 1px),
            linear-gradient(90deg, ${color}40 1px, transparent 1px)
          `,
          backgroundSize: '20px 20px',
          opacity: gridOpacity,
        }}
      />

      {/* 扫描目标框 */}
      <div
        style={{
          position: 'absolute',
          width: '120px',
          height: '80px',
          border: `2px solid ${color}`,
          borderRadius: '4px',
          opacity: 0.6,
          transform: `scale(${discoveryScale})`,
          boxShadow: `0 0 15px ${color}`,
        }}
      />

      {/* 角落标记 */}
      {[
        { top: -10, left: -10 },
        { top: -10, right: -10 },
        { bottom: -10, left: -10 },
        { bottom: -10, right: -10 },
      ].map((pos, i) => (
        <div
          key={i}
          style={{
            position: 'absolute',
            width: '20px',
            height: '20px',
            border: `2px solid ${color}`,
            ...pos,
            borderColor: i % 2 === 0 ? color : 'transparent',
            borderTopColor: i < 2 ? color : 'transparent',
            borderBottomColor: i >= 2 ? color : 'transparent',
            borderLeftColor: i % 2 === 0 ? color : 'transparent',
            borderRightColor: i % 2 === 1 ? color : 'transparent',
          }}
        />
      ))}

      {/* 主文字 */}
      <span
        style={{
          color: '#ffffff',

          zIndex: 10,
          position: 'relative',
          letterSpacing: '2px',
          transform: `scale(${discoveryScale})`,
          ...(style || {}),
        }}
      >
        {text}
      </span>

      {/* 扫描信息 */}
      <div
        style={{
          position: 'absolute',
          top: '20px',
          right: '20px',
          fontSize: '12px',
          color: color,
          fontFamily: 'monospace',
          opacity: 0.7,
        }}
      >
        SCAN: {Math.floor(frame % 100)}%
      </div>
    </span>
  );
}

LaserScanEffect.key = 'LaserScanEffect';
LaserScanEffect.description = 'laser scan effect';
LaserScanEffect.showName = '激光扫描';
