import React from 'react';
import { useCurrentFrame, interpolate, useVideoConfig } from 'remotion';

interface HeartbeatSensingEffectProps {
  text: string;
  style?: React.CSSProperties;
}

const HeartbeatSensingEffect: React.FC<HeartbeatSensingEffectProps> & { showName: string } = ({
  text,
  style
}) => {
  const frame = useCurrentFrame();
  const { fps } = useVideoConfig();
  
  const heartRate = 72; // 72bpm心率
  const pulseIntensity = 5; // 5%搏动强度
  
  // 心跳周期
  const beatPeriod = 60 / heartRate; // 秒
  const beatProgress = (frame / fps / beatPeriod) % 1;
  
  // 心跳搏动效果
  let heartbeatScale = 1;
  if (beatProgress < 0.1) {
    heartbeatScale = 1 + (pulseIntensity / 100) * Math.sin(beatProgress * Math.PI / 0.1);
  } else if (beatProgress < 0.2) {
    heartbeatScale = 1 + (pulseIntensity / 100) * Math.sin((beatProgress - 0.1) * Math.PI / 0.1) * 0.5;
  }
  
  return (
    <div style={{
      display: 'inline-flex',
      alignItems: 'center',
      justifyContent: 'center',
      position: 'relative',
      backgroundColor: 'transparent',
      ...style
    }}>
      {/* 主文字 */}
      <span style={{
        fontSize: '60px',
        fontWeight: 'bold',
        color: '#FF4444',
        display: 'inline-block',
        transform: `scale(${heartbeatScale})`,
        textShadow: `
          0 0 5px #FF4444,
          0 0 10px #FF4444,
          0 0 15px rgba(255, 68, 68, 0.5)
        `,
        filter: `brightness(${1 + (heartbeatScale - 1) * 1.5})`
      }}>
        {text}
      </span>
      
      {/* 心跳指示灯 */}
      <div style={{
        position: 'absolute',
        top: -30,
        left: -50,
        width: '8px',
        height: '8px',
        background: beatProgress < 0.1 ? '#FF4444' : '#444444',
        borderRadius: '50%',
        boxShadow: beatProgress < 0.1 ? '0 0 10px #FF4444' : 'none',
        pointerEvents: 'none'
      }} />
    </div>
  );
};

HeartbeatSensingEffect.showName = "心跳感应";

export default HeartbeatSensingEffect; 