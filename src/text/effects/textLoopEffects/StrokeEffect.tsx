import React from 'react';
import { interpolate, useCurrentFrame, useVideoConfig } from 'remotion';

interface StrokeEffectProps {
  text: string;
  color?: string;
  fontSize?: number;
  style?: React.CSSProperties;
}

export default function StrokeEffect({
  text = '展示文本',
  color = '#fff',
  fontSize = 48,
  style = {},
}: StrokeEffectProps) {
  const frame = useCurrentFrame();
  const { fps } = useVideoConfig();

  // 描边动画进度
  const strokeProgress = interpolate(frame, [0, 12], [0, 100], {
    extrapolateRight: 'clamp',
  });

  // 填充动画进度
  const fillProgress = interpolate(frame, [8, 15], [0, 100], {
    extrapolateRight: 'clamp',
  });

  // 描边宽度变化
  const strokeWidth = interpolate(Math.sin(frame * 0.3), [-1, 1], [2, 6]);

  return (
    <span
      style={{
        display: 'inline-flex',
        alignItems: 'center',
        justifyContent: 'center',
        height: '100%',

        fontSize: `${fontSize}px`,
       
      }}
    >
      <span
        style={{
          color: fillProgress > 50 ? color : 'transparent',
          WebkitTextStroke: `${strokeWidth}px ${color}`,
          opacity: strokeProgress / 100,
          animation: fillProgress > 0 ? 'none' : `dash ${1.5}s linear infinite`,
          ...(style || {}),
        }}
      >
        {text}
      </span>

      <style>{`
        @keyframes dash {
          0% {
            stroke-dasharray: 0, 100;
          }
          50% {
            stroke-dasharray: 50, 50;
          }
          100% {
            stroke-dasharray: 100, 0;
          }
        }
      `}</style>
    </span>
  );
}

StrokeEffect.key = 'StrokeEffect';
StrokeEffect.description = 'stroke effect';
StrokeEffect.showName = '描边';
