import React from 'react';
import { interpolate, useCurrentFrame } from 'remotion';

interface EnergyShieldEffectProps {
  text: string;
  color?: string;
  fontSize?: number;
  style?: React.CSSProperties;
}

export default function EnergyShieldEffect({
  text = '展示文本',
  color = '#fff',
  fontSize = 48,
  style = {},
}: EnergyShieldEffectProps) {
  const frame = useCurrentFrame();

  // 能量波纹
  const rippleScale = interpolate(frame % 20, [0, 20], [0.8, 2], {
    extrapolateRight: 'clamp',
  });

  const rippleOpacity = interpolate(frame % 20, [0, 20], [0.8, 0], {
    extrapolateRight: 'clamp',
  });

  // 护盾强度
  const shieldIntensity = interpolate(Math.sin(frame * 0.3), [-1, 1], [0.3, 1]);

  // 六边形护盾动画
  const hexRotation = (frame * 2) % 360;

  // 能量粒子
  const particleCount = 8;
  const particles = Array.from({ length: particleCount }, (_, i) => {
    const angle = (i / particleCount) * Math.PI * 2 + frame * 0.05;
    const radius = 100 + Math.sin(frame * 0.1 + i) * 20;
    const x = Math.cos(angle) * radius;
    const y = Math.sin(angle) * radius;

    return { x, y, angle: angle * (180 / Math.PI) };
  });

  return (
    <span
      style={{
        display: 'inline-flex',
        alignItems: 'center',
        justifyContent: 'center',
        height: '100%',
        fontSize: `${fontSize}px`,
        position: 'relative',
      }}
    >
      {/* 护盾波纹 */}
      <div
        style={{
          position: 'absolute',
          width: '200px',
          height: '200px',
          border: `2px solid ${color}`,
          borderRadius: '50%',
          transform: `scale(${rippleScale})`,
          opacity: rippleOpacity,
          boxShadow: `0 0 20px ${color}`,
        }}
      />

      {/* 六边形护盾 */}
      <div
        style={{
          position: 'absolute',
          width: '160px',
          height: '160px',
          border: `2px solid ${color}`,
          clipPath:
            'polygon(50% 0%, 93.3% 25%, 93.3% 75%, 50% 100%, 6.7% 75%, 6.7% 25%)',
          transform: `rotate(${hexRotation}deg)`,
          opacity: shieldIntensity * 0.6,
          boxShadow: `
            inset 0 0 20px ${color},
            0 0 30px ${color}
          `,
        }}
      />

      {/* 能量粒子 */}
      {particles.map((particle, index) => (
        <div
          key={index}
          style={{
            position: 'absolute',
            left: '50%',
            top: '50%',
            width: '4px',
            height: '4px',
            backgroundColor: color,
            borderRadius: '50%',
            transform: `translate(${particle.x - 2}px, ${particle.y - 2}px)`,
            boxShadow: `0 0 10px ${color}`,
            opacity: 0.8,
          }}
        />
      ))}

      {/* 护盾内层 */}
      <div
        style={{
          position: 'absolute',
          width: '120px',
          height: '120px',
          background: `radial-gradient(circle, transparent 60%, ${color}20 80%, transparent 100%)`,
          borderRadius: '50%',
          opacity: shieldIntensity,
        }}
      />

      {/* 主文字 */}
      <span
        style={{
          color: '#ffffff',
          zIndex: 10,
          position: 'relative',
          ...(style || {}),
        }}
      >
        {text}
      </span>

      {/* 护盾反射光 */}
      <div
        style={{
          position: 'absolute',
          width: '300px',
          height: '2px',
          background: `linear-gradient(90deg, transparent, ${color}, transparent)`,
          transform: `rotate(${frame * 4}deg)`,
          opacity: 0.4,
        }}
      />
    </span>
  );
}

EnergyShieldEffect.key = 'EnergyShieldEffect';
EnergyShieldEffect.description = 'energy shield effect';
EnergyShieldEffect.showName = '能量护盾';
