import React from 'react';
import { useCurrentFrame } from 'remotion';

interface HeartBeatLoopEffectProps {
  text: string | React.ReactNode[];
  style: React.CSSProperties;
}

export default function HeartBeatLoopEffect({
  text = '展示文本',
  style = {},
}: HeartBeatLoopEffectProps) {
  const frame = useCurrentFrame();

  // 心跳效果（持续循环）
  const heartBeat = 1 + Math.sin(frame * 0.3) * 0.15;

  // 爱心环绕
  const hearts = Array.from({ length: 4 }, (_, i) => {
    const angle = (frame * 0.05) + (i * Math.PI / 2);
    const radius = 60;
    const x = 50 + Math.cos(angle) * radius;
    const y = 50 + Math.sin(angle) * radius;
    const heartScale = 0.8 + Math.sin(frame * 0.2 + i) * 0.3;
    
    return (
      <div
        key={i}
        style={{
          position: 'absolute',
          left: `${x}%`,
          top: `${y}%`,
          fontSize: '16px',
          opacity: 0.7,
          transform: `translate(-50%, -50%) scale(${heartScale})`,
        }}
      >
        ❤️
      </div>
    );
  });

  return (
    <span
      style={{
        display: 'inline-flex',
        alignItems: 'center',
        justifyContent: 'center',
        height: '100%',
        position: 'relative',
      }}
    >
      <span
        style={{
          transform: `scale(${heartBeat})`,
          color: '#ffffff',
          textAlign: 'center',
          textShadow: '0 0 10px #ffffff',
          ...style,
        }}
      >
        {text}
      </span>
      {hearts}
    </span>
  );
}

HeartBeatLoopEffect.key = 'HeartBeatLoopEffect';
HeartBeatLoopEffect.description = 'heartbeat loop text effect with circling hearts';
HeartBeatLoopEffect.showName = '心跳循环'; 