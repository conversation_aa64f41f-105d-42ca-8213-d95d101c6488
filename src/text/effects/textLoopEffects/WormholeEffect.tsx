import React from 'react';
import { interpolate, useCurrentFrame } from 'remotion';

interface WormholeEffectProps {
  text: string;
  color?: string;
  fontSize?: number;
  style?: React.CSSProperties;
}

export default function WormholeEffect({
  text = '展示文本',
  color = '#fff',
  fontSize = 48,
  style = {},
}: WormholeEffectProps) {
  const frame = useCurrentFrame();

  const tunnelRings = Array.from({ length: 8 }, (_, i) => {
    const distance = i * 30 + ((frame * 2) % 240);
    const scale = interpolate(distance, [0, 240], [3, 0.2]);
    const opacity = interpolate(distance, [0, 240], [0.2, 0.8]);
    return { scale, opacity, rotation: (frame + i * 45) % 360 };
  });

  return (
    <span
      style={{
        display: 'inline-flex',
        alignItems: 'center',
        justifyContent: 'center',
        height: '100%',
        fontSize: `${fontSize}px`,
        position: 'relative',
        background: 'radial-gradient(circle, transparent 30%, #000000 100%)',
      }}
    >
      {tunnelRings.map((ring, index) => (
        <div
          key={index}
          style={{
            position: 'absolute',
            width: '200px',
            height: '200px',
            border: `2px solid ${color}`,
            borderRadius: '50%',
            transform: `scale(${ring.scale}) rotate(${ring.rotation}deg)`,
            opacity: ring.opacity,
            borderStyle: index % 2 === 0 ? 'solid' : 'dashed',
          }}
        />
      ))}

      <span
        style={{
          color: '#ffffff',
          zIndex: 10,
          transform: `perspective(1000px) rotateY(${
            Math.sin(frame * 0.1) * 10
          }deg)`,
          ...(style || {}),
        }}
      >
        {text}
      </span>
    </span>
  );
}

WormholeEffect.key = 'WormholeEffect';
WormholeEffect.description = 'wormhole effect';
WormholeEffect.showName = '虫洞';
