import React from 'react';
import { useCurrentFrame, interpolate, useVideoConfig } from 'remotion';

interface HologramWaveEffectProps {
  text: string;
  style?: React.CSSProperties;
}

const HologramWaveEffect: React.FC<HologramWaveEffectProps> & { showName: string } = ({
  text,
  style
}) => {
  const frame = useCurrentFrame();
  const { fps } = useVideoConfig();
  
  const colorSeparation = 1.5; // 色散强度
  
  // 全息抖动
  const hologramJitter = Math.sin(frame * 0.3) * 2;
  const hologramFlicker = Math.abs(Math.sin(frame * 0.1)) * 0.3 + 0.7;
  
  // 色彩通道分离
  const redOffset = Math.sin(frame * 0.2) * colorSeparation;
  const blueOffset = Math.sin(frame * 0.2 + Math.PI) * colorSeparation;
  
  return (
    <div style={{
      display: 'inline-flex',
      alignItems: 'center',
      justifyContent: 'center',
      position: 'relative',
      backgroundColor: 'transparent',
      ...style
    }}>
      {/* 红色通道 */}
      <span style={{
        fontSize: '60px',
        fontWeight: 'bold',
        color: '#FF0000',
        display: 'inline-block',
        position: 'absolute',
        opacity: hologramFlicker * 0.6,
        transform: `translate(${redOffset}px, ${hologramJitter * 0.5}px)`,
        mixBlendMode: 'screen'
      }}>
        {text}
      </span>
      
      {/* 绿色通道 (主体) */}
      <span style={{
        fontSize: '60px',
        fontWeight: 'bold',
        color: '#00FF88',
        display: 'inline-block',
        opacity: hologramFlicker,
        transform: `translate(0px, ${hologramJitter}px)`,
        textShadow: `
          0 0 10px #00FF88,
          0 0 20px #00FF88,
          0 0 30px #00FF88
        `
      }}>
        {text}
      </span>
      
      {/* 蓝色通道 */}
      <span style={{
        fontSize: '60px',
        fontWeight: 'bold',
        color: '#0088FF',
        display: 'inline-block',
        position: 'absolute',
        opacity: hologramFlicker * 0.6,
        transform: `translate(${blueOffset}px, ${hologramJitter * -0.5}px)`,
        mixBlendMode: 'screen'
      }}>
        {text}
      </span>
      
      {/* 全息网格 */}
      <div style={{
        position: 'absolute',
        left: -40,
        right: -40,
        top: -40,
        bottom: -40,
        background: `
          linear-gradient(90deg, transparent 0px, rgba(0, 255, 170, 0.1) 1px, transparent 2px),
          linear-gradient(0deg, transparent 0px, rgba(0, 255, 170, 0.1) 1px, transparent 2px)
        `,
        backgroundSize: '20px 20px',
        opacity: hologramFlicker * 0.3,
        pointerEvents: 'none'
      }} />
      
      {/* 数据点 */}
      {Array.from({ length: 12 }, (_, i) => {
        const dotAngle = (i / 12) * Math.PI * 2 + frame * 0.02;
        const dotRadius = 50 + Math.sin(frame * 0.1 + i) * 10;
        const dotX = Math.cos(dotAngle) * dotRadius;
        const dotY = Math.sin(dotAngle) * dotRadius;
        const dotOpacity = Math.abs(Math.sin(frame * 0.15 + i)) * hologramFlicker;
        
        return (
          <div
            key={`dot-${i}`}
            style={{
              position: 'absolute',
              left: '50%',
              top: '50%',
              width: '3px',
              height: '3px',
              background: '#00FFAA',
              borderRadius: '50%',
              transform: `translate(-50%, -50%) translate(${dotX}px, ${dotY}px)`,
              opacity: dotOpacity,
              boxShadow: `0 0 4px #00FFAA`,
              pointerEvents: 'none'
            }}
          />
        );
      })}
      
      {/* 信号干扰线 */}
      {frame % 20 < 3 && (
        <>
          {Array.from({ length: 5 }, (_, lineIndex) => {
            const lineY = -30 + lineIndex * 15 + Math.random() * 10;
            const lineWidth = 80 + Math.random() * 40;
            const lineOpacity = Math.random() * 0.5;
            
            return (
              <div
                key={`interference-${lineIndex}`}
                style={{
                  position: 'absolute',
                  left: '50%',
                  top: '50%',
                  width: `${lineWidth}px`,
                  height: '1px',
                  background: '#00FFAA',
                  transform: `translate(-50%, -50%) translateY(${lineY}px)`,
                  opacity: lineOpacity,
                  pointerEvents: 'none'
                }}
              />
            );
          })}
        </>
      )}
    </div>
  );
};

HologramWaveEffect.showName = "全息波动";

export default HologramWaveEffect; 