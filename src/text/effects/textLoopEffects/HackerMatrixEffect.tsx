import React from 'react';
import { random, useCurrentFrame } from 'remotion';

interface HackerMatrixEffectProps {
  text: string;
  color?: string;
  fontSize?: number;
  style?: React.CSSProperties;
}

export default function HackerMatrixEffect({
  text = '展示文本',
  color = '#fff',
  fontSize = 48,
  style = {},
}: HackerMatrixEffectProps) {
  const frame = useCurrentFrame();

  const matrixChars =
    '01アイウエオカキクケコサシスセソタチツテトナニヌネノハヒフヘホマミムメモヤユヨラリルレロワヲン';

  const codeRain = Array.from({ length: 20 }, (_, i) => {
    const x = i * 60 - 600;
    const speed = 3 + random(`speed-${i}`) * 4;
    const y = ((frame * speed) % 800) - 400;
    const char =
      matrixChars[
        Math.floor(
          random(`char-${i}-${Math.floor(frame / 5)}`) * matrixChars.length,
        )
      ];
    return { x, y, char };
  });

  return (
    <span
      style={{
        display: 'inline-flex',
        alignItems: 'center',
        justifyContent: 'center',
        height: '100%',
        fontSize: `${fontSize}px`,
        position: 'relative',
        backgroundColor: '#000000',
        overflow: 'hidden',
      }}
    >
      {/* 代码雨 */}
      {codeRain.map((drop, index) => (
        <div
          key={index}
          style={{
            position: 'absolute',
            left: '50%',
            top: '50%',
            color: color,
            fontSize: '14px',
            opacity: 0.6,
            transform: `translate(${drop.x}px, ${drop.y}px)`,
            textShadow: `0 0 5px ${color}`,
          }}
        >
          {drop.char}
        </div>
      ))}

      {/* 终端框 */}
      <div
        style={{
          border: `1px solid ${color}`,
          backgroundColor: 'rgba(0, 0, 0, 0.9)',
          padding: '20px',
          borderRadius: '4px',
          boxShadow: `0 0 20px ${color}40`,
        }}
      >
        <div
          style={{
            color: color,
            fontSize: '12px',
            marginBottom: '10px',
            fontFamily: 'monospace',
          }}
        >
          
        </div>

        <span
          style={{
            color: '#ffffff',
            ...(style || {}),
          }}
        >
          {text}
        </span>
      </div>
    </span>
  );
}

HackerMatrixEffect.key = 'HackerMatrixEffect';
HackerMatrixEffect.description = 'hacker matrix effect';
HackerMatrixEffect.showName = '黑客矩阵';
