import React from 'react';
import { useCurrentFrame } from 'remotion';

interface NanoParticleEffectProps {
  text: string;
  color?: string;
  fontSize?: number;
  style?: React.CSSProperties;
}

export default function NanoParticleEffect({
  text = '展示文本',
  color = '#fff',
  fontSize = 48,
  style = {},
}: NanoParticleEffectProps) {
  const frame = useCurrentFrame();

  const nanoParticles = Array.from({ length: 100 }, (_, i) => {
    const angle = (i / 100) * Math.PI * 2;
    const distance = 50 + Math.sin(frame * 0.1 + i * 0.1) * 30;
    const x = Math.cos(angle + frame * 0.02) * distance;
    const y = Math.sin(angle + frame * 0.02) * distance;
    return { x, y };
  });

  return (
    <span
      style={{
        display: 'inline-flex',
        alignItems: 'center',
        justifyContent: 'center',
        height: '100%',
        fontSize: `${fontSize}px`,
        position: 'relative',
      }}
    >
      {nanoParticles.map((particle, index) => (
        <div
          key={index}
          style={{
            position: 'absolute',
            left: '50%',
            top: '50%',
            width: '1px',
            height: '1px',
            backgroundColor: color,
            transform: `translate(${particle.x}px, ${particle.y}px)`,
            opacity: 0.8,
            boxShadow: `0 0 2px ${color}`,
          }}
        />
      ))}

      <span
        style={{
          color: '#ffffff',
          zIndex: 10,
          ...(style || {}),
        }}
      >
        {text}
      </span>
    </span>
  );
}

NanoParticleEffect.key = 'NanoParticleEffect';
NanoParticleEffect.description = 'nano particle effect';
NanoParticleEffect.showName = '纳米粒子';
