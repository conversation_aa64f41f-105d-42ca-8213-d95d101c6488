import React from 'react';
import { useCurrentFrame } from 'remotion';

interface WobbleLoopEffectProps {
  text: string | React.ReactNode[];
  style: React.CSSProperties;
}

export default function WobbleLoopEffect({
  text = '展示文本',
  style = {},
}: WobbleLoopEffectProps) {
  const frame = useCurrentFrame();

  // 摇摆效果
  const wobbleX = Math.sin(frame * 0.2) * 8;
  const wobbleRotate = Math.sin(frame * 0.15) * 5;

  return (
    <span
      style={{
        display: 'inline-flex',
        alignItems: 'center',
        justifyContent: 'center',
        height: '100%',
      }}
    >
      <span
        style={{
          transform: `translateX(${wobbleX}px) rotate(${wobbleRotate}deg)`,
          color: '#ffffff',
          textAlign: 'center',
          textShadow: '0 0 8px #ffffff',
          ...style,
        }}
      >
        {text}
      </span>
    </span>
  );
}

WobbleLoopEffect.key = 'WobbleLoopEffect';
WobbleLoopEffect.description = 'wobble loop text effect';
WobbleLoopEffect.showName = '摇摆循环'; 