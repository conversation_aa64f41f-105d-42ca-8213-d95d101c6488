import React from 'react';
import { useCurrentFrame } from 'remotion';

interface ElasticLoopEffectProps {
  text: string | React.ReactNode[];
  style: React.CSSProperties;
}

export default function ElasticLoopEffect({
  text = '展示文本',
  style = {},
}: ElasticLoopEffectProps) {
  const frame = useCurrentFrame();

  // 弹性效果
  const elasticX = 1 + Math.sin(frame * 0.1) * 0.15;
  const elasticY = 1 + Math.sin(frame * 0.13) * 0.1;

  return (
    <span
      style={{
        display: 'inline-flex',
        alignItems: 'center',
        justifyContent: 'center',
        height: '100%',
      }}
    >
      <span
        style={{
          transform: `scaleX(${elasticX}) scaleY(${elasticY})`,
          color: '#ffffff',
          textAlign: 'center',
          textShadow: '0 0 8px #ffffff',
          ...style,
        }}
      >
        {text}
      </span>
    </span>
  );
}

ElasticLoopEffect.key = 'ElasticLoopEffect';
ElasticLoopEffect.description = 'elastic loop text effect';
ElasticLoopEffect.showName = '弹性循环'; 