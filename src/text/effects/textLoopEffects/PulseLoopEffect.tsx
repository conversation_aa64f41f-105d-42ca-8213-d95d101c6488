import React from 'react';
import { useCurrentFrame } from 'remotion';

interface PulseLoopEffectProps {
  text: string | React.ReactNode[];
  style: React.CSSProperties;
}

export default function PulseLoopEffect({
  text = '展示文本',
  style = {},
}: PulseLoopEffectProps) {
  const frame = useCurrentFrame();

  // 脉冲效果
  const pulse = 1 + Math.sin(frame * 0.2) * 0.2;

  return (
    <span
      style={{
        display: 'inline-flex',
        alignItems: 'center',
        justifyContent: 'center',
        height: '100%',
      }}
    >
      <span
        style={{
          transform: `scale(${pulse})`,
          color: '#ffffff',
          textAlign: 'center',
          textShadow: '0 0 10px #ffffff',
          ...style,
        }}
      >
        {text}
      </span>
    </span>
  );
}

PulseLoopEffect.key = 'PulseLoopEffect';
PulseLoopEffect.description = 'pulse loop text effect';
PulseLoopEffect.showName = '脉冲循环'; 