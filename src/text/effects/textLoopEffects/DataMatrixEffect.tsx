import React from 'react';
import { useCurrentFrame, interpolate, useVideoConfig } from 'remotion';

interface DataMatrixEffectProps {
  text: string;
  style?: React.CSSProperties;
}

const DataMatrixEffect: React.FC<DataMatrixEffectProps> & { showName: string } = ({
  text,
  style
}) => {
  const frame = useCurrentFrame();
  const { fps } = useVideoConfig();
  
  const codeFlowSpeed = 200; // 200px/s代码流速
  const highlightFreq = 1; // 1Hz高亮频率
  
  // 生成随机字符
  const generateRandomChar = () => {
    const chars = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz!@#$%^&*()_+-=[]{}|;:,.<>?';
    return chars[Math.floor(Math.random() * chars.length)];
  };
  
  // 高亮效果
  const highlightIntensity = Math.abs(Math.sin(frame * highlightFreq * 2 * Math.PI / fps));
  
  const chars = text.split('');
  
  return (
    <div style={{
      display: 'inline-flex',
      alignItems: 'center',
      justifyContent: 'center',
      position: 'relative',
      backgroundColor: 'transparent',
      gap: '2px',
      ...style
    }}>
      {chars.map((char, index) => {
        const charOffset = index * 0.3;
        
        // 字符高亮强度
        const charHighlight = Math.abs(Math.sin(frame * highlightFreq * 2 * Math.PI / fps + charOffset));
        
        return (
          <div key={index} style={{
            position: 'relative',
            display: 'inline-block'
          }}>
            {/* 数据流文字表面 */}
            <div style={{
              position: 'absolute',
              left: 0,
              top: 0,
              right: 0,
              bottom: 0,
              overflow: 'hidden',
              pointerEvents: 'none'
            }}>
              {Array.from({ length: 6 }, (_, streamIndex) => {
                const streamY = ((frame * (codeFlowSpeed * 0.3) / fps + streamIndex * 10) % 60) - 10;
                const streamOpacity = Math.sin(frame * 0.1 + streamIndex + charOffset) * 0.4 + 0.4;
                const streamX = (streamIndex % 3) * 20;
                
                return (
                  <div
                    key={`stream-${streamIndex}`}
                    style={{
                      position: 'absolute',
                      left: `${streamX}%`,
                      top: `${streamY}px`,
                      fontSize: '8px',
                      color: '#44FF44',
                      fontFamily: 'monospace',
                      opacity: streamOpacity,
                      transform: 'rotate(-15deg)'
                    }}
                  >
                    {generateRandomChar()}
                  </div>
                );
              })}
            </div>
            
            {/* 主文字 */}
            <span style={{
              fontSize: '60px',
              fontWeight: 'bold',
              color: '#00FF88',
              display: 'inline-block',
              position: 'relative',
              textShadow: `
                0 0 10px #00FF88,
                0 0 20px #00FF88,
                0 0 30px rgba(0, 255, 136, 0.5)
              `,
              filter: `brightness(${1 + charHighlight * 0.5})`,
              zIndex: 10
            }}>
              {char}
            </span>
            
            {/* 数据碎片效果 */}
            {charHighlight > 0.7 && (
              <>
                {Array.from({ length: 3 }, (_, fragmentIndex) => {
                  const fragmentX = (fragmentIndex - 1) * 15 + Math.sin(frame * 0.1 + fragmentIndex + charOffset) * 5;
                  const fragmentY = -20 + Math.cos(frame * 0.08 + fragmentIndex + charOffset) * 15;
                  const fragmentOpacity = Math.abs(Math.sin(frame * 0.15 + fragmentIndex + charOffset)) * charHighlight;
                  
                  return (
                    <div
                      key={`fragment-${fragmentIndex}`}
                      style={{
                        position: 'absolute',
                        left: '50%',
                        top: '50%',
                        transform: `translate(-50%, -50%) translate(${fragmentX}px, ${fragmentY}px)`,
                        fontSize: '10px',
                        color: '#FFFF00',
                        fontFamily: 'monospace',
                        opacity: fragmentOpacity,
                        textShadow: '0 0 8px #FFFF00',
                        pointerEvents: 'none'
                      }}
                    >
                      {('0000' + Math.floor(Math.random() * 9999).toString()).slice(-4)}
                    </div>
                  );
                })}
              </>
            )}
          </div>
        );
      })}
    </div>
  );
};

DataMatrixEffect.showName = "数据矩阵";

export default DataMatrixEffect; 