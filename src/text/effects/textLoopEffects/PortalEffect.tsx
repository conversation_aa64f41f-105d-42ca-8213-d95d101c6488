import React from 'react';
import { interpolate, useCurrentFrame } from 'remotion';

interface PortalEffectProps {
  text: string;
  color?: string;
  fontSize?: number;
  style?: React.CSSProperties;
}

export default function PortalEffect({
  text = '展示文本',
  color = '#fff',
  fontSize = 48,
  style = {},
}: PortalEffectProps) {
  const frame = useCurrentFrame();

  // 传送门旋转
  const portalRotation = (frame * 8) % 360;

  // 旋涡层数
  const spiralLayers = 5;
  const spirals = Array.from({ length: spiralLayers }, (_, i) => {
    const radius = 80 + i * 20;
    const rotation = portalRotation + i * 20;
    const opacity = 1 - (i / spiralLayers) * 0.6;

    return { radius, rotation, opacity };
  });

  // 文字传送效果
  const teleportScale = interpolate(Math.sin(frame * 0.4), [-1, 1], [0.8, 1.2]);

  // 能量波动
  const energyPulse = interpolate(Math.sin(frame * 0.6), [-1, 1], [0.5, 1]);

  return (
    <span
      style={{
        display: 'inline-flex',
        alignItems: 'center',
        justifyContent: 'center',
        height: '100%',
        fontSize: `${fontSize}px`,
        position: 'relative',
      }}
    >
      {/* 传送门旋涡 */}
      {spirals.map((spiral, index) => (
        <div
          key={index}
          style={{
            position: 'absolute',
            width: `${spiral.radius * 2}px`,
            height: `${spiral.radius * 2}px`,
            border: `2px solid ${color}`,
            borderRadius: '50%',
            transform: `rotate(${spiral.rotation}deg)`,
            opacity: spiral.opacity,
            borderLeftColor: 'transparent',
            borderBottomColor: 'transparent',
            boxShadow: `0 0 20px ${color}`,
          }}
        />
      ))}

      {/* 中心能量核心 */}
      <div
        style={{
          position: 'absolute',
          width: '40px',
          height: '40px',
          background: `radial-gradient(circle, ${color}, transparent)`,
          borderRadius: '50%',
          transform: `scale(${energyPulse})`,
          opacity: 0.8,
          boxShadow: `0 0 30px ${color}`,
        }}
      />

      {/* 传送粒子 */}
      {Array.from({ length: 12 }, (_, i) => {
        const angle = (i / 12) * Math.PI * 2;
        const distance = 60 + Math.sin(frame * 0.2 + i) * 30;
        const x = Math.cos(angle + frame * 0.1) * distance;
        const y = Math.sin(angle + frame * 0.1) * distance;

        return (
          <div
            key={i}
            style={{
              position: 'absolute',
              left: '50%',
              top: '50%',
              width: '3px',
              height: '3px',
              backgroundColor: color,
              borderRadius: '50%',
              transform: `translate(${x - 1.5}px, ${y - 1.5}px)`,
              opacity: Math.sin(frame * 0.3 + i) * 0.5 + 0.5,
              boxShadow: `0 0 8px ${color}`,
            }}
          />
        );
      })}

      {/* 主文字 */}
      <span
        style={{
          color: '#ffffff',
          transform: `scale(${teleportScale})`,
          zIndex: 10,
          position: 'relative',
          ...(style || {}),
        }}
      >
        {text}
      </span>

      {/* 传送门边缘光效 */}
      <div
        style={{
          position: 'absolute',
          width: '200px',
          height: '200px',
          border: `1px solid ${color}`,
          borderRadius: '50%',
          opacity: 0.3,
          transform: `rotate(${-portalRotation}deg)`,
          borderTopColor: 'transparent',
          borderRightColor: 'transparent',
        }}
      />
    </span>
  );
}

PortalEffect.key = 'PortalEffect';
PortalEffect.description = 'portal effect';
PortalEffect.showName = '传送门';
