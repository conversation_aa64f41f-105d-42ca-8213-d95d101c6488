import React from 'react';
import { useCurrentFrame } from 'remotion';

interface JelloLoopEffectProps {
  text: string | React.ReactNode[];
  style: React.CSSProperties;
}

export default function JelloLoopEffect({
  text = '展示文本',
  style = {},
}: JelloLoopEffectProps) {
  const frame = useCurrentFrame();

  // 果冻摇摆效果
  const skewX = Math.sin(frame * 0.15) * 8;
  const skewY = Math.sin(frame * 0.12) * 2;

  return (
    <span
      style={{
        display: 'inline-flex',
        alignItems: 'center',
        justifyContent: 'center',
        height: '100%',
      }}
    >
      <span
        style={{
          transform: `skewX(${skewX}deg) skewY(${skewY}deg)`,
          color: '#ffffff',
          textAlign: 'center',
          textShadow: '0 0 8px #ffffff',
          ...style,
        }}
      >
        {text}
      </span>
    </span>
  );
}

JelloLoopEffect.key = 'JelloLoopEffect';
JelloLoopEffect.description = 'jello loop text effect';
JelloLoopEffect.showName = '果冻循环'; 