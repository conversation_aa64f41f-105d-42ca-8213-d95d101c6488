import React from 'react';
import { interpolate, random, useCurrentFrame } from 'remotion';

interface MachineCodeEffectProps {
  text: string;
  color?: string;
  fontSize?: number;
  style?: React.CSSProperties;
}

export default function MachineCodeEffect({
  text = '展示文本',
  color = '#fff',
  fontSize = 48,
  style = {},
}: MachineCodeEffectProps) {
  const frame = useCurrentFrame();

  // 二进制码流
  const binaryStreams = Array.from({ length: 30 }, (_, i) => {
    const code = Array.from({ length: 8 }, () =>
      Math.floor(random(`bin-${i}-${Math.floor(frame / 3)}`) * 2),
    ).join('');
    const x = i * 25 - 300;
    const y = ((frame * (1 + i * 0.1)) % 400) - 200;
    return { code, x, y };
  });

  // 文字编译进度
  const compileProgress = interpolate(frame, [0, 30], [0, 100], {
    extrapolateRight: 'clamp',
  });

  return (
    <span
      style={{
        display: 'inline-flex',
        alignItems: 'center',
        justifyContent: 'center',
        height: '100%',
        fontSize: `${fontSize}px`,
        position: 'relative',
        backgroundColor: '#0a0a0a',
        overflow: 'hidden',
      }}
    >
      {/* 二进制码流 */}
      {binaryStreams.map((stream, index) => (
        <div
          key={index}
          style={{
            position: 'absolute',
            left: '50%',
            top: '50%',
            color: color,
            fontSize: '10px',
            opacity: 0.6,
            transform: `translate(${stream.x}px, ${stream.y}px)`,
            textShadow: `0 0 5px ${color}`,
          }}
        >
          {stream.code}
        </div>
      ))}

      {/* 编译框 */}
      <div
        style={{
          position: 'absolute',
          top: '20px',
          left: '20px',
          color: color,
          fontSize: '12px',
          fontFamily: 'monospace',
        }}
      >
        COMPILING... {Math.floor(compileProgress)}%
      </div>

      {/* 主文字 */}
      <span
        style={{
          color: '#ffffff',
          zIndex: 10,
          letterSpacing: '2px',
          ...(style || {}),
        }}
      >
        {text}
      </span>
    </span>
  );
}

MachineCodeEffect.key = 'MachineCodeEffect';
MachineCodeEffect.description = 'machine code effect';
MachineCodeEffect.showName = '机器码';
