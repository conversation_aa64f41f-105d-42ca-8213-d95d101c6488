import React from 'react';
import { useCurrentFrame } from 'remotion';

interface BreatheLoopEffectProps {
  text: string | React.ReactNode[];
  style: React.CSSProperties;
}

export default function BreatheLoopEffect({
  text = '展示文本',
  style = {},
}: BreatheLoopEffectProps) {
  const frame = useCurrentFrame();

  // 呼吸效果
  const breathe = 1 + Math.sin(frame * 0.08) * 0.2;
  const opacity = 0.7 + Math.sin(frame * 0.08) * 0.3;

  return (
    <span
      style={{
        display: 'inline-flex',
        alignItems: 'center',
        justifyContent: 'center',
        height: '100%',
      }}
    >
      <span
        style={{
          transform: `scale(${breathe})`,
          opacity: opacity,
          color: '#ffffff',
          textAlign: 'center',
          textShadow: '0 0 10px #ffffff',
          ...style,
        }}
      >
        {text}
      </span>
    </span>
  );
}

BreatheLoopEffect.key = 'BreatheLoopEffect';
BreatheLoopEffect.description = 'breathing loop text effect';
BreatheLoopEffect.showName = '呼吸循环'; 