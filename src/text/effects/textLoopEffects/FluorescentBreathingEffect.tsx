import React from 'react';
import { useCurrentFrame, interpolate, useVideoConfig } from 'remotion';

interface FluorescentBreathingEffectProps {
  text: string;
  style?: React.CSSProperties;
}

const FluorescentBreathingEffect: React.FC<FluorescentBreathingEffectProps> & { showName: string } = ({
  text,
  style
}) => {
  const frame = useCurrentFrame();
  const { fps } = useVideoConfig();
  
  const breathingPeriod = 4; // 4s周期
  
  // 呼吸周期
  const breathingProgress = (frame / fps / breathingPeriod) % 1;
  
  // 明暗变化 - 由淡入深再由深变淡
  const brightness = Math.sin(breathingProgress * Math.PI * 2) * 0.4 + 0.6; // 0.2-1.0之间变化
  const saturation = Math.sin(breathingProgress * Math.PI * 2) * 30 + 70; // 40-100之间变化
  const lightness = Math.sin(breathingProgress * Math.PI * 2) * 20 + 60; // 40-80之间变化
  
  const chars = text.split('');
  
  return (
    <div style={{
      display: 'inline-flex',
      alignItems: 'center',
      justifyContent: 'center',
      position: 'relative',
      backgroundColor: 'transparent',
      gap: '2px',
      ...style
    }}>
      {chars.map((char, index) => {
        const charHue = 120 + index * 20; // 每个字符不同色相
        const charOffset = index * 0.1; // 字符间的相位差
        const charBrightness = Math.sin((breathingProgress + charOffset) * Math.PI * 2) * 0.4 + 0.6;
        const charSaturation = Math.sin((breathingProgress + charOffset) * Math.PI * 2) * 30 + 70;
        const charLightness = Math.sin((breathingProgress + charOffset) * Math.PI * 2) * 20 + 60;
        
        // 呼吸缩放效果
        const breathingScale = 1 + Math.sin((breathingProgress + charOffset) * Math.PI * 2) * 0.05;
        
        return (
          <div key={index} style={{
            position: 'relative',
            display: 'inline-block'
          }}>
            {/* 主文字 */}
            <span style={{
              fontSize: '60px',
              fontWeight: 'bold',
              color: `hsl(${charHue}, ${charSaturation}%, ${charLightness}%)`,
              display: 'inline-block',
              textShadow: `
                0 0 ${5 + charBrightness * 10}px hsla(${charHue}, ${charSaturation}%, ${charLightness}%, ${charBrightness * 0.8}),
                0 0 ${10 + charBrightness * 15}px hsla(${charHue}, ${charSaturation}%, ${charLightness}%, ${charBrightness * 0.6}),
                0 0 ${15 + charBrightness * 20}px hsla(${charHue}, ${charSaturation}%, ${charLightness}%, ${charBrightness * 0.4})
              `,
              filter: `brightness(${charBrightness}) saturate(${1 + charBrightness * 0.5})`,
              transform: `scale(${breathingScale})`
            }}>
              {char}
            </span>
          </div>
        );
      })}
    </div>
  );
};

FluorescentBreathingEffect.showName = "荧光呼吸";

export default FluorescentBreathingEffect; 