import React from 'react';
import { useCurrentFrame } from 'remotion';

interface SwingLoopEffectProps {
  text: string | React.ReactNode[];
  style: React.CSSProperties;
}

export default function SwingLoopEffect({
  text = '展示文本',
  style = {},
}: SwingLoopEffectProps) {
  const frame = useCurrentFrame();

  // 摆动效果
  const swingAngle = Math.sin(frame * 0.1) * 15;

  return (
    <span
      style={{
        display: 'inline-flex',
        alignItems: 'center',
        justifyContent: 'center',
        height: '100%',
      }}
    >
      <span
        style={{
          transform: `rotate(${swingAngle}deg)`,
          transformOrigin: 'center top',
          color: '#ffffff',
          textAlign: 'center',
          textShadow: '0 0 8px #ffffff',
          ...style,
        }}
      >
        {text}
      </span>
    </span>
  );
}

SwingLoopEffect.key = 'SwingLoopEffect';
SwingLoopEffect.description = 'swinging loop text effect';
SwingLoopEffect.showName = '摆动循环'; 