import React from 'react';
import { interpolate, random, useCurrentFrame, useVideoConfig } from 'remotion';

interface GlitchEffectProps {
  text: string;
  color?: string;
  fontSize?: number;
  style?: React.CSSProperties;
}

export default function GlitchEffect({
  text = '展示文本',
  color = '#ffffff',
  fontSize = 48,
  style = {},
}: GlitchEffectProps) {
  const frame = useCurrentFrame();
  const { fps } = useVideoConfig();

  // 随机故障强度
  const glitchIntensity = interpolate(Math.sin(frame * 0.3), [-1, 1], [0, 1]);

  // 垂直位移
  const verticalShift = random(`vertical-${frame}`) * glitchIntensity * 20 - 10;

  // 水平位移
  const horizontalShift =
    random(`horizontal-${frame}`) * glitchIntensity * 15 - 7.5;

  return (
    <span
      style={{
        position: 'relative',
        display: 'inline-flex',
        alignItems: 'center',
        justifyContent: 'center',
        height: '100%',
        fontSize: `${fontSize}px`,
      }}
    >
      {/* 主文字 */}
      <span
        style={{
          color,
          transform: `translate(${horizontalShift * 0.3}px, ${
            verticalShift * 0.3
          }px)`,
          ...(style || {}),
        }}
      >
        {text}
      </span>
    </span>
  );
}

GlitchEffect.key = 'GlitchEffect';
GlitchEffect.description = 'glitch effect';
GlitchEffect.showName = '故障';
