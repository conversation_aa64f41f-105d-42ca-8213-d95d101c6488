import React from 'react';
import { interpolate, useCurrentFrame } from 'remotion';

interface AntiGravityEffectProps {
  text: string | React.ReactNode[];
  color?: string;
  fontSize?: number;
  style?: React.CSSProperties;
  speed?: number;
}

export default function AntiGravityEffect({
  text = '展示文本',
  color = '#fff',
  fontSize = 48,
  style = {},
  speed = 1,
}: AntiGravityEffectProps) {
  const frame = useCurrentFrame();

  const floatY = interpolate(Math.sin(frame * 0.1 * speed), [-1, 1], [-20, 20]);
  const particles = Array.from({ length: 15 }, (_, i) => {
    const y = interpolate(frame * speed + i * 5, [0, 100], [100, -100], {
      extrapolateRight: 'wrap',
    });
    const x = Math.sin(frame * 0.05 * speed + i) * 50;
    return { x, y };
  });

  return (
    <span
      style={{
        display: 'inline-flex',
        alignItems: 'center',
        justifyContent: 'center',
        height: '100%',
        fontSize: `${fontSize}px`,
        position: 'relative',
      }}
    >
      {particles.map((particle, index) => (
        <span
          key={index}
          style={{
            position: 'absolute',
            left: '50%',
            top: '50%',
            width: '3px',
            height: '3px',
            backgroundColor: color,
            borderRadius: '50%',
            transform: `translate(${particle.x}px, ${particle.y}px)`,
            opacity: 0.7,
            boxShadow: `0 0 8px ${color}`,
          }}
        />
      ))}

      <span
        style={{
          color: '#ffffff',
          transform: `translateY(${floatY}px)`,
          zIndex: 10,
          ...(style || {}),
        }}
      >
        {text}
      </span>
    </span>
  );
}

AntiGravityEffect.key = 'AntiGravityEffect';
AntiGravityEffect.description = 'anti gravity effect';
AntiGravityEffect.showName = '反重力';
