import React from 'react';
import { interpolate, useCurrentFrame, useVideoConfig } from 'remotion';

interface PulseEffectProps {
  text: string;
  color?: string;
  fontSize?: number;
  style?: React.CSSProperties;
}

export default function PulseEffect({
  text = '展示文本  ',
  color = 'white',
  fontSize = 48,
  style = {},
}: PulseEffectProps) {
  const frame = useCurrentFrame();
  const { fps } = useVideoConfig();

  // 脉冲缩放
  const pulseScale = interpolate(Math.sin(frame * 0.6), [-1, 1], [0.85, 1.15]);

  // 脉冲透明度
  const pulseOpacity = interpolate(Math.sin(frame * 0.6), [-1, 1], [0.7, 1]);

  // 脉冲发光
  const glowRadius = interpolate(Math.sin(frame * 0.6), [-1, 1], [10, 40]);

  return (
    <span
      style={{
        display: 'inline-flex',
        alignItems: 'center',
        justifyContent: 'center',
        height: '100%',
        fontFamily: 'Arial, sans-serif',
        fontSize: `${fontSize}px`,
        fontWeight: 'bold',
      }}
    >
      {/* 主文字 */}
      <span
        style={{
          color,
          transform: `scale(${pulseScale})`,
          opacity: pulseOpacity,
          ...(style || {}),
        }}
      >
        {text}
      </span>
    </span>
  );
}

PulseEffect.key = 'PulseEffect';
PulseEffect.description = 'pulse effect';
PulseEffect.showName = '脉冲';
