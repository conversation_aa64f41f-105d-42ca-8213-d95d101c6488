import React from 'react';
import { Easing, interpolate, useCurrentFrame, useVideoConfig } from 'remotion';

interface BounceEffectProps {
  text: string;
  color?: string;
  fontSize?: number;
  style?: React.CSSProperties;
}

export default function BounceEffect({
  text = '展示文本',
  color = '#fff',
  fontSize = 48,
  style = {},
}: BounceEffectProps) {
  const frame = useCurrentFrame();
  const { fps } = useVideoConfig();

  // 弹跳动画
  const bounceY = interpolate(frame, [0, 8, 12, 15], [-100, 20, -5, 0], {
    easing: Easing.bounce,
    extrapolateRight: 'clamp',
  });

  // 缩放效果
  const scale = interpolate(frame, [0, 8, 12, 15], [0.5, 1.2, 0.95, 1], {
    easing: Easing.elastic(1),
    extrapolateRight: 'clamp',
  });

  return (
    <span
      style={{
        display: 'inline-flex',
        alignItems: 'center',
        justifyContent: 'center',
        height: '100%',
        fontSize: `${fontSize}px`,
      }}
    >
      <span
        style={{
          color,
          transform: `translateY(${bounceY}px) scale(${scale})`,
          ...(style || {}),
        }}
      >
        {text}
      </span>
    </span>
  );
}

BounceEffect.key = 'BounceEffect';
BounceEffect.description = 'bounce effect';
BounceEffect.showName = '弹跳';
