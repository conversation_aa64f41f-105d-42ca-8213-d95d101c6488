import React from 'react';
import { interpolate, useCurrentFrame, useVideoConfig } from 'remotion';

interface FlipEffectProps {
  text: string;
  color?: string;
  fontSize?: number;
  style?: React.CSSProperties;
}

export default function FlipEffect({
  text = '展示文本',
  color = '#fff',
  fontSize = 48,
  style = {},
}: FlipEffectProps) {
  const frame = useCurrentFrame();
  const { fps } = useVideoConfig();

  // 翻转角度
  const rotationY = interpolate(frame, [0, 15], [0, 360], {
    extrapolateRight: 'clamp',
  });

  const rotationX = Math.sin(frame * 0.2) * 20;

  return (
    <span
      style={{
        display: 'inline-flex',
        alignItems: 'center',
        justifyContent: 'center',
        height: '100%',
        perspective: '1000px',
      }}
    >
      <span
        style={{
          fontSize: `${fontSize}px`,
          color,
          transform: `rotateY(${rotationY}deg) rotateX(${rotationX}deg)`,
          transformStyle: 'preserve-3d',
          ...(style || {}),
        }}
      >
        {text}
      </span>
    </span>
  );
}

FlipEffect.key = 'FlipEffect';
FlipEffect.description = 'flip effect';
FlipEffect.showName = '翻转';
