import React from 'react';
import { useCurrentFrame } from 'remotion';

interface StardustEffectProps {
  text: string;
  color?: string;
  fontSize?: number;
  style?: React.CSSProperties;
}

export default function StardustEffect({
  text = '展示文本',
  color = '#fff',
  fontSize = 48,
  style = {},
}: StardustEffectProps) {
  const frame = useCurrentFrame();

  const stars = Array.from({ length: 50 }, (_, i) => {
    const x = Math.sin(frame * 0.01 + i) * 200;
    const y = Math.cos(frame * 0.01 + i * 1.3) * 150;
    const twinkle = Math.sin(frame * 0.1 + i) * 0.5 + 0.5;
    return { x, y, twinkle };
  });

  return (
    <span
      style={{
        display: 'inline-flex',
        alignItems: 'center',
        justifyContent: 'center',
        height: '100%',
        fontSize: `${fontSize}px`,
        position: 'relative',
        background: 'radial-gradient(circle, #000033, #000000)',
      }}
    >
      {stars.map((star, index) => (
        <div
          key={index}
          style={{
            position: 'absolute',
            left: '50%',
            top: '50%',
            width: '2px',
            height: '2px',
            backgroundColor: color,
            borderRadius: '50%',
            transform: `translate(${star.x}px, ${star.y}px)`,
            opacity: star.twinkle,
            boxShadow: `0 0 ${star.twinkle * 10}px ${color}`,
          }}
        />
      ))}

      <span
        style={{
          color: '#ffffff',
          zIndex: 10,
          ...(style || {}),
        }}
      >
        {text}
      </span>
    </span>
  );
}

StardustEffect.key = 'StardustEffect';
StardustEffect.description = 'stardust effect';
StardustEffect.showName = '星尘';
