import React from 'react';
import { Easing, interpolate, useCurrentFrame, useVideoConfig } from 'remotion';

interface BlurEffectProps {
  text: string;
  color?: string;
  fontSize?: number;
  style?: React.CSSProperties;
}

export default function BlurEffect({
  text = '展示文本',
  color = '#fff',
  fontSize = 48,
  style = {},
}: BlurEffectProps) {
  const frame = useCurrentFrame();
  const { fps } = useVideoConfig();

  // 模糊度变化
  const blurAmount = interpolate(frame, [0, 8, 15], [20, 0, 0], {
    easing: Easing.out(Easing.cubic),
    extrapolateRight: 'clamp',
  });

  // 透明度变化
  const opacity = interpolate(frame, [0, 5, 15], [0.3, 1, 1], {
    extrapolateRight: 'clamp',
  });

  // 缩放效果
  const scale = interpolate(frame, [0, 8, 15], [1.2, 1, 1], {
    easing: Easing.out(Easing.cubic),
    extrapolateRight: 'clamp',
  });

  return (
    <span
      style={{
        display: 'inline-flex',
        alignItems: 'center',
        justifyContent: 'center',
        height: '100%',
        fontSize: `${fontSize}px`,
      }}
    >
      {/* 背景模糊层 */}
      <div
        style={{
          position: 'absolute',
          color,
          opacity: 0.3,
          filter: `blur(${blurAmount + 5}px)`,
          transform: `scale(${scale * 1.1})`,
          ...(style || {}),
        }}
      >
        {text}
      </div>

      {/* 主文字层 */}
      <span
        style={{
          color,
          opacity,
          filter: `blur(${blurAmount}px)`,
          transform: `scale(${scale})`,
          ...(style || {}),
        }}
      >
        {text}
      </span>
    </span>
  );
}

BlurEffect.key = 'BlurEffect';
BlurEffect.description = 'blur effect';
BlurEffect.showName = '模糊';
