import React from 'react';
import { useCurrentFrame, interpolate, useVideoConfig } from 'remotion';

interface GoldParticleEffectProps {
  text: string;
  style?: React.CSSProperties;
}

const GoldParticleEffect: React.FC<GoldParticleEffectProps> & { showName: string } = ({
  text,
  style
}) => {
  const frame = useCurrentFrame();
  const { fps } = useVideoConfig();
  
  const particleSize = { min: 2, max: 5 }; // 2~5px粒子大小
  const generationRate = 50; // 50个/s生成率
  
  return (
    <div style={{
      display: 'inline-flex',
      alignItems: 'center',
      justifyContent: 'center',
      position: 'relative',
      backgroundColor: 'transparent',
      ...style
    }}>
      {/* 主文字 */}
      <span style={{
        fontSize: '60px',
        fontWeight: 'bold',
        background: `
          linear-gradient(135deg, 
            #FFD700 0%, 
            #FFA500 25%, 
            #FFFF00 50%, 
            #FFD700 75%, 
            #DAA520 100%)
        `,
        backgroundClip: 'text',
        WebkitBackgroundClip: 'text',
        WebkitTextFillColor: 'transparent',
        display: 'inline-block',
        position: 'relative',
        textShadow: `
          0 0 10px rgba(255, 215, 0, 0.6),
          0 0 20px rgba(255, 215, 0, 0.4),
          0 0 30px rgba(255, 215, 0, 0.2)
        `,
        filter: 'brightness(1.2)'
      }}>
        {text}
        
        {/* 金粉粒子效果 */}
        <div style={{
          position: 'absolute',
          left: -20,
          top: -20,
          right: -20,
          bottom: -20,
          overflow: 'hidden',
          pointerEvents: 'none'
        }}>
          {Array.from({ length: 30 }, (_, particleIndex) => {
            const particleLife = (frame + particleIndex * 3) % (3 * fps);
            const particleProgress = particleLife / (3 * fps);
            
            // 粒子初始位置（文字表面）
            const startX = 10 + (particleIndex % 8) * 12 + Math.sin(particleIndex) * 5;
            const startY = 20 + Math.floor(particleIndex / 8) * 15;
            
            // 重力下落
            const fallDistance = particleProgress * particleProgress * 100; // 重力加速
            const particleX = startX + Math.sin(frame * 0.02 + particleIndex) * 3; // 轻微飘动
            const particleY = startY + fallDistance;
            
            const size = particleSize.min + Math.sin(particleIndex) * (particleSize.max - particleSize.min);
            const opacity = Math.max(0, 1 - particleProgress * 1.5); // 逐渐消失
            
            // 金色变化
            const goldHue = 45 + Math.sin(frame * 0.05 + particleIndex) * 15;
            
            return (
              <div
                key={`particle-${particleIndex}`}
                style={{
                  position: 'absolute',
                  left: `${particleX}%`,
                  top: `${particleY}px`,
                  width: `${size}px`,
                  height: `${size}px`,
                  background: `radial-gradient(circle, 
                    hsl(${goldHue}, 80%, 60%) 0%, 
                    hsl(${goldHue}, 70%, 50%) 70%,
                    transparent 100%)`,
                  borderRadius: '50%',
                  opacity: opacity,
                  boxShadow: `0 0 ${size}px hsla(${goldHue}, 80%, 60%, ${opacity * 0.6})`,
                  pointerEvents: 'none'
                }}
              />
            );
          })}
          
          {/* 表面金粉闪烁 */}
          {Array.from({ length: 15 }, (_, sparkleIndex) => {
            const sparkleLife = (frame + sparkleIndex * 5) % (1.5 * fps);
            const sparkleProgress = sparkleLife / (1.5 * fps);
            const sparkleOpacity = Math.sin(sparkleProgress * Math.PI) * 0.8;
            
            const sparkleX = 15 + (sparkleIndex % 5) * 20;
            const sparkleY = 25 + Math.floor(sparkleIndex / 5) * 20;
            
            return (
              <div
                key={`sparkle-${sparkleIndex}`}
                style={{
                  position: 'absolute',
                  left: `${sparkleX}%`,
                  top: `${sparkleY}%`,
                  width: '3px',
                  height: '3px',
                  background: 'radial-gradient(circle, rgba(255, 255, 255, 0.9) 0%, transparent 70%)',
                  borderRadius: '50%',
                  opacity: sparkleOpacity,
                  boxShadow: `0 0 6px rgba(255, 255, 255, ${sparkleOpacity})`,
                  pointerEvents: 'none'
                }}
              />
            );
          })}
        </div>
      </span>
      
      {/* 金粉堆积效果 */}
      <div style={{
        position: 'absolute',
        left: -10,
        right: -10,
        bottom: -5,
        height: '8px',
        background: `
          linear-gradient(90deg, 
            transparent 0%, 
            rgba(255, 215, 0, 0.3) 20%, 
            rgba(255, 215, 0, 0.5) 50%, 
            rgba(255, 215, 0, 0.3) 80%, 
            transparent 100%)
        `,
        borderRadius: '4px',
        opacity: Math.abs(Math.sin(frame * 0.03)) * 0.6 + 0.4,
        pointerEvents: 'none'
      }} />
    </div>
  );
};

GoldParticleEffect.showName = "氪金粒子";

export default GoldParticleEffect; 