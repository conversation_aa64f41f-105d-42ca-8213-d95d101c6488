import React from 'react';
import { useCurrentFrame } from 'remotion';

interface BouncyLoopEffectProps {
  text: string | React.ReactNode[];
  style: React.CSSProperties;
}

export default function BouncyLoopEffect({
  text = '展示文本',
  style = {},
}: BouncyLoopEffectProps) {
  const frame = useCurrentFrame();

  // 弹跳效果
  const bounce = Math.abs(Math.sin(frame * 0.15)) * 15;
  const squash = 1 - Math.abs(Math.sin(frame * 0.15)) * 0.1;

  return (
    <span
      style={{
        display: 'inline-flex',
        alignItems: 'center',
        justifyContent: 'center',
        height: '100%',
      }}
    >
      <span
        style={{
          transform: `translateY(-${bounce}px) scaleY(${squash})`,
          color: '#ffffff',
          textAlign: 'center',
          textShadow: '0 0 8px #ffffff',
          ...style,
        }}
      >
        {text}
      </span>
    </span>
  );
}

BouncyLoopEffect.key = 'BouncyLoopEffect';
BouncyLoopEffect.description = 'bouncy loop text effect';
BouncyLoopEffect.showName = '弹跳循环'; 