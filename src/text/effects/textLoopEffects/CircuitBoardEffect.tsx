import React from 'react';
import { interpolate, useCurrentFrame } from 'remotion';

interface CircuitBoardEffectProps {
  text: string;
  color?: string;
  fontSize?: number;
  style?: React.CSSProperties;
}

export default function CircuitBoardEffect({
  text = '展示文本',
  color = '#fff',
  fontSize = 48,
  style = {},
}: CircuitBoardEffectProps) {
  const frame = useCurrentFrame();

  const pulseIntensity = interpolate(Math.sin(frame * 0.4), [-1, 1], [0.5, 1]);

  return (
    <span
      style={{
        display: 'inline-flex',
        alignItems: 'center',
        justifyContent: 'center',
        height: '100%',
        fontSize: `${fontSize}px`,
        position: 'relative',
        backgroundImage: `
          linear-gradient(${color}40 1px, transparent 1px),
          linear-gradient(90deg, ${color}40 1px, transparent 1px)
        `,
        backgroundSize: '20px 20px',
      }}
    >
      <span
        style={{
          color: '#ffffff',

          filter: `brightness(${pulseIntensity})`,
          border: `2px solid ${color}`,
          padding: '10px 20px',
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          ...(style || {}),
        }}
      >
        {text}
      </span>
    </span>
  );
}

CircuitBoardEffect.key = 'CircuitBoardEffect';
CircuitBoardEffect.description = 'circuit board effect';
CircuitBoardEffect.showName = '电路板';
