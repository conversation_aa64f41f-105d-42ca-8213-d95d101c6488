import React from 'react';
import { interpolate, useCurrentFrame } from 'remotion';

interface DimensionRiftEffectProps {
  text: string;
  color?: string;
  fontSize?: number;
  style?: React.CSSProperties;
}

export default function DimensionRiftEffect({
  text = '展示文本',
  color = '#fff',
  fontSize = 48,
  style = {},
}: DimensionRiftEffectProps) {
  const frame = useCurrentFrame();

  const riftIntensity = interpolate(Math.sin(frame * 0.3), [-1, 1], [0.5, 1]);

  const distortionX = Math.sin(frame * 0.1) * 10;
  const distortionY = Math.cos(frame * 0.15) * 5;

  return (
    <span
      style={{
        display: 'inline-flex',
        alignItems: 'center',
        justifyContent: 'center',
        height: '100%',
        fontSize: `${fontSize}px`,
        position: 'relative',
        background: 'linear-gradient(45deg, #000000, #330066)',
      }}
    >
      {/* 维度裂缝 */}
      <div
        style={{
          position: 'absolute',
          width: '300px',
          height: '2px',
          background: `linear-gradient(90deg, transparent, ${color}, transparent)`,
          transform: `rotate(${frame}deg)`,
          opacity: riftIntensity,
          boxShadow: `0 0 20px ${color}`,
        }}
      />

      <div
        style={{
          position: 'absolute',
          width: '300px',
          height: '2px',
          background: `linear-gradient(90deg, transparent, ${color}, transparent)`,
          transform: `rotate(${frame + 90}deg)`,
          opacity: riftIntensity * 0.7,
          boxShadow: `0 0 20px ${color}`,
        }}
      />

      <span
        style={{
          color: '#ffffff',
          transform: `translate(${distortionX}px, ${distortionY}px) scale(${riftIntensity})`,

          zIndex: 10,
          ...(style || {}),
        }}
      >
        {text}
      </span>
    </span>
  );
}

DimensionRiftEffect.key = 'DimensionRiftEffect';
DimensionRiftEffect.description = 'dimension rift effect';
DimensionRiftEffect.showName = '维度裂缝';
