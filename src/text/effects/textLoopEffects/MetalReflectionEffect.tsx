import React from 'react';
import { useCurrentFrame, interpolate, useVideoConfig } from 'remotion';

interface MetalReflectionEffectProps {
  text: string;
  style?: React.CSSProperties;
}

const MetalReflectionEffect: React.FC<MetalReflectionEffectProps> & { showName: string } = ({
  text,
  style
}) => {
  const frame = useCurrentFrame();
  const { fps } = useVideoConfig();
  
  const scanPeriod = 3; // 3s扫光周期
  const glossWidth = 30; // 30%光泽宽度
  
  // 扫光位置
  const scanProgress = (frame / fps / scanPeriod) % 1;
  const scanPosition = interpolate(scanProgress, [0, 1], [-50, 150]);
  
  const chars = text.split('');
  
  return (
    <div style={{
      display: 'inline-flex',
      alignItems: 'center',
      justifyContent: 'center',
      position: 'relative',
      backgroundColor: 'transparent',
      gap: '2px',
      ...style
    }}>
      {chars.map((char, index) => {
        const charScanOffset = index * 10; // 每个字符的扫光延迟
        const charScanPosition = (scanPosition + charScanOffset) % 200 - 50;
        
        // 判断扫光是否经过当前字符
        const isScanning = charScanPosition >= -20 && charScanPosition <= 120;
        const scanFactor = isScanning 
          ? Math.max(0, 1 - Math.abs(charScanPosition - 50) / 50) 
          : 0;
        
        // 金属基础渐变
        const metalGradient = `
          linear-gradient(135deg, 
            #C0C0C0 0%, 
            #E8E8E8 25%, 
            #A8A8A8 50%, 
            #D8D8D8 75%, 
            #B8B8B8 100%)
        `;
        
        // 反光渐变 - 增强亮度
        const reflectionGradient = `
          linear-gradient(135deg, 
            rgba(255, 255, 255, ${scanFactor * 1.2}) 0%, 
            rgba(255, 255, 255, ${scanFactor * 1.8}) 50%, 
            rgba(255, 255, 255, ${scanFactor * 1.2}) 100%)
        `;
        
        return (
          <div key={index} style={{
            position: 'relative',
            display: 'inline-block'
          }}>
            {/* 金属文字 */}
            <span style={{
              fontSize: '60px',
              fontWeight: 'bold',
              background: metalGradient,
              backgroundClip: 'text',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
              display: 'inline-block',
              position: 'relative',
              filter: `brightness(${1.2 + scanFactor * 1.2}) contrast(${1.1 + scanFactor * 0.5})`, // 增强反光亮度
              textShadow: `
                2px 2px 4px rgba(0,0,0,0.3),
                0 0 ${5 + scanFactor * 20}px rgba(255,255,255,${scanFactor * 1.0})
              `
            }}>
              {char}
              
              {/* 反光叠加层 */}
              {isScanning && (
                <span style={{
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  fontSize: '60px',
                  fontWeight: 'bold',
                  background: reflectionGradient,
                  backgroundClip: 'text',
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent',
                  display: 'inline-block',
                  mixBlendMode: 'overlay',
                  pointerEvents: 'none'
                }}>
                  {char}
                </span>
              )}
            </span>
          </div>
        );
      })}
    </div>
  );
};

MetalReflectionEffect.showName = "金属反光";

export default MetalReflectionEffect; 