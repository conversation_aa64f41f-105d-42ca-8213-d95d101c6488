import React from 'react';
import { useCurrentFrame, interpolate, useVideoConfig } from 'remotion';
import { ANIMATION_CYCLE_DURATION } from './data';

interface ParticleVortexEffectProps {
  text: string | React.ReactNode[];
  style?: React.CSSProperties;
  speed?: number;
}

export default function ParticleVortexEffect({
  text = "ABCD123+粒子漩涡",
  style = {},
  speed = 1,
}: ParticleVortexEffectProps) {
  const frame = useCurrentFrame();
  const { fps } = useVideoConfig();
  
  const rotationSpeed = 1 * speed; // 旋转速度受speed控制
  const particleCount = 200; // 200个粒子
  
  // 根据文字长度计算容器尺寸
  const textLength = Array.from(text).length;
  const containerWidth = Math.max(200, textLength * 40); // 最小200px，每个字符40px
  const maxRadius = containerWidth * 0.4; // 最大半径为容器宽度的40%
  const minRadius = 20; // 最小半径
  
  return (
    <span style={{
      // display: 'inline-flex',
      // alignItems: 'center',
      // justifyContent: 'center',
      position: 'relative',
      backgroundColor: 'transparent',
      minWidth: `${containerWidth}px`,
    }}>
      {/* 粒子漩涡 */}
      {Array.from({ length: particleCount }, (_, particleIndex) => {
        const spiralTurns = 3; // 螺旋圈数
        
        // 粒子在螺旋中的位置
        const spiralProgress = (particleIndex / particleCount + frame * rotationSpeed / fps) % 1;
        const radius = interpolate(spiralProgress, [0, 1], [maxRadius, minRadius]);
        const angle = spiralProgress * spiralTurns * Math.PI * 2;
        
        const particleX = Math.cos(angle) * radius;
        const particleY = Math.sin(angle) * radius;
        
        // 粒子大小和透明度
        const particleSize = interpolate(radius, [minRadius, maxRadius], [4, 1]);
        const particleOpacity = interpolate(radius, [minRadius, maxRadius], [0.2, 0.8]);
        
        // 粒子颜色基于位置
        const hue = (particleIndex * 3 + frame * 2 * speed) % 360;
        
        return (
          <div
            key={`particle-${particleIndex}`}
            style={{
              position: 'absolute',
              left: '50%',
              top: '50%',
              width: `${particleSize}px`,
              height: `${particleSize}px`,
              background: `radial-gradient(circle, 
                hsl(${hue}, 70%, 60%) 0%, 
                hsl(${hue}, 70%, 40%) 70%, 
                transparent 100%)`,
              borderRadius: '50%',
              transform: `translate(-50%, -50%) translate(${particleX}px, ${particleY}px)`,
              opacity: particleOpacity,
              pointerEvents: 'none'
            }}
          />
        );
      })}
      
      {/* 引力中心效果 */}
      <div style={{
        position: 'absolute',
        left: '50%',
        top: '50%',
        width: '100px',
        height: '100px',
        background: `radial-gradient(circle, 
          rgba(255, 255, 255, 0.3) 0%, 
          rgba(255, 255, 255, 0.1) 30%, 
          rgba(100, 200, 255, 0.2) 60%, 
          transparent 100%)`,
        borderRadius: '50%',
        transform: 'translate(-50%, -50%)',
        opacity: Math.abs(Math.sin(frame * 0.05 * speed)) * 0.5 + 0.3,
        pointerEvents: 'none'
      }} />
      
      {/* 主文字 */}
      <span style={{
        fontSize: '60px',
        fontWeight: 'bold',
        color: 'white',
        display: 'inline-block',
        textShadow: `
          0 0 10px rgba(100, 200, 255, 0.8),
          0 0 20px rgba(100, 200, 255, 0.6),
          0 0 30px rgba(100, 200, 255, 0.4)
        `,
        filter: `brightness(${1 + Math.abs(Math.sin(frame * 0.08 * speed)) * 0.3})`,
        zIndex: 10,
        ...style
      }}>
        {text}
      </span>
      
      {/* 自适应引力波纹 */}
      {Array.from({ length: 4 }, (_, rippleIndex) => {
        const rippleProgress = ((frame + rippleIndex * 15) * speed / fps) % (2 * ANIMATION_CYCLE_DURATION);
        const rippleRadius = rippleProgress * maxRadius * 0.8; // 波纹半径根据容器大小调整
        const rippleOpacity = Math.sin(rippleProgress * Math.PI) * 0.4;
        
        return (
          <div
            key={`ripple-${rippleIndex}`}
            style={{
              position: 'absolute',
              left: '50%',
              top: '50%',
              width: `${rippleRadius * 2}px`,
              height: `${rippleRadius * 2}px`,
              border: '1px solid rgba(100, 200, 255, 0.6)',
              borderRadius: '50%',
              transform: 'translate(-50%, -50%)',
              opacity: rippleOpacity,
              pointerEvents: 'none'
            }}
          />
        );
      })}
      
      {/* 椭圆形能量束 - 适应文字宽度 */}
      {Array.from({ length: 8 }, (_, beamIndex) => {
        const beamAngle = (beamIndex / 8) * Math.PI * 2 + frame * 0.02 * speed;
        const beamLength = maxRadius * 0.7 + Math.sin(frame * 0.1 * speed + beamIndex) * 20;
        const beamOpacity = Math.abs(Math.sin(frame * 0.15 * speed + beamIndex)) * 0.5;
        
        return (
          <div
            key={`beam-${beamIndex}`}
            style={{
              position: 'absolute',
              left: '50%',
              top: '50%',
              width: `${beamLength}px`,
              height: '2px',
              background: `linear-gradient(90deg, 
                rgba(100, 200, 255, ${beamOpacity}) 0%, 
                transparent 100%)`,
              transform: `translate(-50%, -50%) rotate(${beamAngle}rad)`,
              transformOrigin: 'left center',
              pointerEvents: 'none'
            }}
          />
        );
      })}
      
      {/* 核心脉冲 */}
      <div style={{
        position: 'absolute',
        left: '50%',
        top: '50%',
        width: '20px',
        height: '20px',
        background: `radial-gradient(circle, 
          rgba(255, 255, 255, ${Math.abs(Math.sin(frame * 0.2)) * 0.8 + 0.2}) 0%, 
          rgba(100, 200, 255, 0.4) 50%, 
          transparent 100%)`,
        borderRadius: '50%',
        transform: `translate(-50%, -50%) scale(${1 + Math.abs(Math.sin(frame * 0.2 * speed)) * 0.5})`,
        pointerEvents: 'none'
      }} />
    </span>
  );
}

ParticleVortexEffect.key = 'ParticleVortexEffect';
ParticleVortexEffect.description = 'particle vortex effect with spiral motion';
ParticleVortexEffect.showName = '粒子漩涡'; 