import React from 'react';
import { useCurrentFrame, useVideoConfig } from 'remotion';
import { ANIMATION_CYCLE_DURATION } from './data';

interface CodeRefactorEffectProps {
  text: string | React.ReactNode[];
  style?: React.CSSProperties;
  speed?: number;
}

export default function CodeRefactorEffect({
  text = "ABCD123+语法高亮",
  style = {},
  speed = 1,
}: CodeRefactorEffectProps) {
  const frame = useCurrentFrame();
  const { fps } = useVideoConfig();
  
  const chars = Array.from(text);
  const syntaxColors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7']; // 固定语法高亮颜色
  
  return (
    <span style={{
      // display: 'inline-flex',
      // alignItems: 'center',
      // justifyContent: 'center',
      position: 'relative',
      fontFamily: 'monospace',
      // gap: '1px',
    }}>
      {chars.map((char, index) => {
        // 语法高亮颜色
        const colorIndex = (index + Math.floor(frame / (10 / speed))) % syntaxColors.length;
        const currentColor = syntaxColors[colorIndex];
        
        // 扫描高亮
        const scanProgress = (frame / fps * (2 * ANIMATION_CYCLE_DURATION) * speed) % (chars.length + 2);
        const isScanning = Math.abs(scanProgress - index) < 1;
        
        return (
          <span key={index} style={{
            fontSize: '60px',
            fontWeight: 'bold',
            color: isScanning ? '#FFFFFF' : currentColor,
            display: 'inline-block',
            textShadow: isScanning 
              ? `0 0 10px ${currentColor}, 0 0 20px ${currentColor}`
              : `0 0 5px ${currentColor}`,
            transition: 'all 0.2s ease',
            ...style
          }}>
            {char}
          </span>
        );
      })}
    </span>
  );
}

CodeRefactorEffect.key = 'CodeRefactorEffect';
CodeRefactorEffect.description = 'syntax highlighting refactor effect';
CodeRefactorEffect.showName = "语法高亮"; 