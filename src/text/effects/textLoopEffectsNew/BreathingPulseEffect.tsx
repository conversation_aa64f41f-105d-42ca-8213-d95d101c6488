import React from 'react';
import { useCurrentFrame, interpolate, useVideoConfig } from 'remotion';
import { ANIMATION_CYCLE_DURATION } from './data';

interface BreathingPulseEffectProps {
  text: string | React.ReactNode[];
  style?: React.CSSProperties;
  speed?: number;
}

export default function BreathingPulseEffect({
  text = "ABCD123+呼吸脉动",
  style = {},
  speed = 1,
}: BreathingPulseEffectProps) {
  const frame = useCurrentFrame();
  const { fps } = useVideoConfig();
  
  const cycleDuration = (fps * ANIMATION_CYCLE_DURATION) / speed; // 实际周期时间 = 基础时间 / 速度
  const cycleProgress = (frame % cycleDuration) / cycleDuration;
  
  // 使用正弦波实现ease-in-out效果
  const breathingPhase = Math.sin(cycleProgress * Math.PI * 2);
  
  // 缩放范围95%~105%
  const scale = interpolate(breathingPhase, [-1, 1], [0.95, 1.05]);
  
  // 透明度微幅变化
  const opacity = interpolate(breathingPhase, [-1, 1], [0.85, 1]);
  
  return (
    <span style={{
      // display: 'inline-flex',
      // alignItems: 'center',
      // justifyContent: 'center',
      position: 'relative',
      backgroundColor: 'transparent',
    }}>
      <span style={{
        fontSize: '60px',
        fontWeight: 'bold',
        color: 'white',
        display: 'inline-block',
        transform: `scale(${scale})`,
        opacity: opacity,
        transition: 'none',
        textShadow: `0 0 ${interpolate(breathingPhase, [-1, 1], [8, 16])}px rgba(255,255,255,0.4)`,
        ...style
      }}>
        {text}
      </span>
    </span>
  );
}

BreathingPulseEffect.key = 'BreathingPulseEffect';
BreathingPulseEffect.description = 'breathing pulse effect with scale and opacity';
BreathingPulseEffect.showName = '呼吸脉动'; 