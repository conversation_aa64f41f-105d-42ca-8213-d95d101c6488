import React from 'react';
import { useCurrentFrame, interpolate, useVideoConfig } from 'remotion';
import { ANIMATION_CYCLE_DURATION } from './data';

interface LavaCrackEffectProps {
  text: string | React.ReactNode[];
  style?: React.CSSProperties;
  speed?: number;
}

export default function LavaCrackEffect({
  text = "ABCD123+熔岩裂缝",
  style = {},
  speed = 1,
}: LavaCrackEffectProps) {
  const frame = useCurrentFrame();
  const { fps } = useVideoConfig();
  
  const chars = Array.from(text);
  
  return (
    <span style={{
      // display: 'inline-flex',
      // alignItems: 'center',
      // justifyContent: 'center',
      position: 'relative',
    }}>
      {chars.map((char, index) => {
        // 熔岩脉动效果
        const lavaPulse = Math.abs(Math.sin(frame * 0.1 * speed + index * 0.4)) * 0.6 + 0.4;
        const crackGlow = Math.sin(frame * 0.15 * speed + index * 0.3) * 0.5 + 0.5;
        
        return (
          <span key={index} style={{
            position: 'relative',
            display: 'inline-block',
            margin: '0 2px'
          }}>
            {/* 裂缝纹理 */}
            {Array.from({ length: 8 }, (_, i) => {
              const crackAngle = (i / 8) * Math.PI * 2 + Math.sin(frame * 0.05 * speed) * 0.3;
              const crackLength = 20 + i * 3 + Math.sin(frame * 0.08 * speed + i) * 5;
              const crackWidth = Math.max(1, 3 - i * 0.3);
              const crackOpacity = lavaPulse * (0.8 - i * 0.1);
              
              // 裂缝内部熔岩光芒
              const lavaGlow = crackGlow;
              
              return (
                <span
                  key={`crack-${i}`}
                  style={{
                    position: 'absolute',
                    left: '50%',
                    top: '50%',
                    width: `${crackLength}px`,
                    height: `${crackWidth}px`,
                    background: `linear-gradient(90deg, 
                      rgba(255, 0, 0, ${crackOpacity}) 0%,
                      rgba(255, 100, 0, ${crackOpacity * lavaGlow}) 30%,
                      rgba(255, 200, 0, ${crackOpacity * lavaGlow * 0.8}) 50%,
                      rgba(255, 100, 0, ${crackOpacity * lavaGlow}) 70%,
                      rgba(255, 0, 0, ${crackOpacity}) 100%)`,
                    borderRadius: '1px',
                    transform: `translate(-50%, -50%) rotate(${crackAngle}rad)`,
                    transformOrigin: '0 50%',
                    pointerEvents: 'none',
                    boxShadow: `0 0 ${crackWidth * 2}px rgba(255, 100, 0, ${crackOpacity * lavaGlow * 0.6})`
                  }}
                />
              );
            })}
            
            {/* 熔岩气泡 */}
            {Array.from({ length: 6 }, (_, i) => {
              const bubbleAngle = (i / 6) * Math.PI * 2 + frame * 0.03 * speed;
              const bubbleDistance = 25 + Math.sin(frame * 0.1 * speed + i) * 8;
              const bubbleX = Math.cos(bubbleAngle) * bubbleDistance;
              const bubbleY = Math.sin(bubbleAngle) * bubbleDistance;
              const bubbleSize = 3 + Math.sin(frame * 0.2 * speed + i) * 2;
              const bubbleOpacity = lavaPulse * (0.6 + Math.sin(frame * 0.12 * speed + i) * 0.4);
              
              return (
                <span
                  key={`bubble-${i}`}
                  style={{
                    position: 'absolute',
                    left: '50%',
                    top: '50%',
                    width: `${bubbleSize}px`,
                    height: `${bubbleSize}px`,
                    background: `radial-gradient(circle, 
                      rgba(255, 200, 0, ${bubbleOpacity}) 0%,
                      rgba(255, 100, 0, ${bubbleOpacity * 0.8}) 50%,
                      rgba(255, 0, 0, ${bubbleOpacity * 0.4}) 100%)`,
                    borderRadius: '50%',
                    transform: `translate(-50%, -50%) translate(${bubbleX}px, ${bubbleY}px) scale(${1 + Math.sin(frame * 0.25 * speed + i) * 0.3})`,
                    pointerEvents: 'none',
                    boxShadow: `0 0 ${bubbleSize * 2}px rgba(255, 165, 0, ${bubbleOpacity * 0.5})`
                  }}
                />
              );
            })}
            
            {/* 岩浆飞溅 */}
            {Array.from({ length: 4 }, (_, i) => {
              const splatProgress = ((frame + i * 15) * speed) % 60 / 60; // 1秒周期
              if (splatProgress > 0.8) return null;
              
              const splatAngle = (i / 4) * Math.PI * 2 + Math.sin(frame * 0.1 * speed) * 0.5;
              const splatDistance = splatProgress * 30;
              const splatX = Math.cos(splatAngle) * splatDistance;
              const splatY = Math.sin(splatAngle) * splatDistance - splatProgress * splatProgress * 20; // 重力效果
              const splatOpacity = interpolate(splatProgress, [0, 0.3, 0.8], [0, 1, 0]);
              const splatSize = 2 + splatProgress * 3;
              
              return (
                <span
                  key={`splat-${i}`}
                  style={{
                    position: 'absolute',
                    left: '50%',
                    top: '50%',
                    width: `${splatSize}px`,
                    height: `${splatSize}px`,
                    background: `radial-gradient(circle, 
                      rgba(255, 150, 0, ${splatOpacity}) 0%,
                      rgba(255, 50, 0, ${splatOpacity * 0.8}) 70%,
                      transparent 100%)`,
                    borderRadius: '50%',
                    transform: `translate(-50%, -50%) translate(${splatX}px, ${splatY}px)`,
                    pointerEvents: 'none'
                  }}
                />
              );
            })}
            
            {/* 热浪扭曲 */}
            <span style={{
              position: 'absolute',
              left: '50%',
              top: '50%',
              width: '80px',
              height: '80px',
              background: `radial-gradient(circle, 
                transparent 40%,
                rgba(255, 100, 0, ${lavaPulse * 0.1}) 60%,
                transparent 100%)`,
              borderRadius: '50%',
              transform: `translate(-50%, -50%) scale(${1 + lavaPulse * 0.2})`,
              pointerEvents: 'none',
              filter: 'blur(4px)'
            }} />
            
            {/* 主文字 */}
            <span style={{
              fontSize: '60px',
              fontWeight: 'bold',
              color: `rgba(255, ${Math.floor(50 + lavaPulse * 150)}, 0, 1)`,
              display: 'inline-block',
              textShadow: `
                0 0 ${5 + lavaPulse * 15}px rgba(255, 100, 0, ${lavaPulse * 0.8}),
                0 0 ${10 + lavaPulse * 25}px rgba(255, 0, 0, ${lavaPulse * 0.4}),
                0 2px 4px rgba(0, 0, 0, 0.5)
              `,
              filter: `brightness(${0.8 + lavaPulse * 0.4}) contrast(${1 + lavaPulse * 0.3})`,
              transform: `scale(${1 + crackGlow * 0.03})`,
              ...style
            }}>
              {char}
            </span>
          </span>
        );
      })}
    </span>
  );
}

LavaCrackEffect.key = 'LavaCrackEffect';
LavaCrackEffect.description = 'lava crack eruption effect';
LavaCrackEffect.showName = "熔岩裂缝"; 