import React from 'react';
import { useCurrentFrame, interpolate, useVideoConfig } from 'remotion';
import { ANIMATION_CYCLE_DURATION } from './data';

interface MetalReflectionEffectProps {
  text: string | React.ReactNode[];
  style?: React.CSSProperties;
  speed?: number;
}

export default function MetalReflectionEffect({
  text = "ABCD123+金属反射",
  style = {},
  speed = 1,
}: MetalReflectionEffectProps) {
  const frame = useCurrentFrame();
  const { fps } = useVideoConfig();
  
  const chars = Array.from(text);
  const reflectionCount = 4; // 固定反射光条数量
  
  return (
    <span style={{
      // display: 'inline-flex',
      // alignItems: 'center',
      // justifyContent: 'center',
      position: 'relative',
    }}>
      {chars.map((char, index) => {
        // 金属反射光扫过
        const reflectionCycle = (frame * speed) / fps * (1.5 * ANIMATION_CYCLE_DURATION); // 1.5倍基础周期
        const reflectionPosition = (reflectionCycle % 2) - 1; // -1到1之间
        const charPosition = (index / chars.length) * 2 - 1; // 字符在-1到1之间的位置
        
        // 反射光条经过该字符的强度
        const reflectionDistance = Math.abs(reflectionPosition - charPosition);
        const reflectionIntensity = Math.max(0, 1 - reflectionDistance * 3);
        
        // 金属基础色
        const baseMetalness = 0.7;
        const brightness = baseMetalness + reflectionIntensity * 0.8;
        
        return (
          <span key={index} style={{
            position: 'relative',
            display: 'inline-block',
            margin: '0 1px'
          }}>
            {/* 反射光条 */}
            {reflectionIntensity > 0.3 && Array.from({ length: reflectionCount }, (_, i) => (
              <span
                key={`reflection-${i}`}
                style={{
                  position: 'absolute',
                  left: `${20 + i * 15}%`,
                  top: -10,
                  width: '2px',
                  height: 80,
                  background: `linear-gradient(0deg, 
                    transparent 0%, 
                    rgba(255,255,255,${reflectionIntensity * 0.8}) 50%, 
                    transparent 100%)`,
                  transform: `skewX(${-20 + i * 5}deg)`,
                  filter: 'blur(1px)',
                  pointerEvents: 'none'
                }}
              />
            ))}
            
            {/* 高光点 */}
            {reflectionIntensity > 0.5 && (
              <span style={{
                position: 'absolute',
                left: '50%',
                top: '30%',
                width: '8px',
                height: '8px',
                background: `radial-gradient(circle, 
                  rgba(255,255,255,${reflectionIntensity}) 0%, 
                  transparent 100%)`,
                borderRadius: '50%',
                transform: 'translateX(-50%)',
                filter: 'blur(2px)',
                pointerEvents: 'none'
              }} />
            )}
            
            {/* 主文字 */}
            <span style={{
              fontSize: '60px',
              fontWeight: 'bold',
              color: `rgba(${Math.floor(200 * brightness)}, ${Math.floor(200 * brightness)}, ${Math.floor(220 * brightness)}, 1)`,
              display: 'inline-block',
              textShadow: `
                0 0 5px rgba(255,255,255,${reflectionIntensity * 0.5}),
                0 2px 4px rgba(0,0,0,0.3)
              `,
              filter: `brightness(${brightness}) contrast(1.2)`,
              ...style
            }}>
              {char}
            </span>
          </span>
        );
      })}
    </span>
  );
}

MetalReflectionEffect.key = 'MetalReflectionEffect';
MetalReflectionEffect.description = 'metal reflection scanning effect';
MetalReflectionEffect.showName = "金属反射"; 