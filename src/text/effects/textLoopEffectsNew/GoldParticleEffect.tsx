import React from 'react';
import { useCurrentFrame, interpolate, useVideoConfig } from 'remotion';
import { ANIMATION_CYCLE_DURATION } from './data';

interface GoldParticleEffectProps {
  text: string | React.ReactNode[];
  style?: React.CSSProperties;
  particleCount?: number;
  speed?: number;
}

export default function GoldParticleEffect({
  text = "ABCD123+金粒飞舞",
  style = {},
  particleCount = 20,
  speed = 1,
}: GoldParticleEffectProps) {
  const frame = useCurrentFrame();
  const { fps } = useVideoConfig();
  
  const chars = Array.from(text);
  
  return (
    <span style={{
      // display: 'inline-flex',
      // alignItems: 'center',
      // justifyContent: 'center',
      position: 'relative',
    }}>
      {chars.map((char, index) => {
        // 金粒子环绕动画
        const orbitRadius = 30 + Math.sin(frame * 0.01 * speed + index) * 5;
        const orbitSpeed = speed * 0.05;
        
        return (
          <span key={index} style={{
            position: 'relative',
            display: 'inline-block',
            margin: '0 2px'
          }}>
            {/* 金粒子环绕 */}
            {Array.from({ length: particleCount }, (_, i) => {
              const particleAngle = (i / particleCount) * Math.PI * 2 + frame * orbitSpeed;
              const particleRadius = orbitRadius + Math.sin(frame * 0.03 * speed + i) * 8;
              const particleX = Math.cos(particleAngle) * particleRadius;
              const particleY = Math.sin(particleAngle) * particleRadius;
              
              // 粒子大小变化
              const particleSize = 2 + Math.sin(frame * 0.08 * speed + i) * 1.5;
              const particleOpacity = 0.6 + Math.sin(frame * 0.04 * speed + i) * 0.4;
              
              // 金色渐变
              const goldIntensity = 0.7 + Math.sin(frame * 0.06 * speed + i) * 0.3;
              
              return (
                <span
                  key={`particle-${i}`}
                  style={{
                    position: 'absolute',
                    left: '50%',
                    top: '50%',
                    width: `${particleSize}px`,
                    height: `${particleSize}px`,
                    background: `radial-gradient(circle, 
                      rgba(255, ${Math.floor(215 * goldIntensity)}, ${Math.floor(0 * goldIntensity)}, ${particleOpacity}) 0%, 
                      rgba(255, ${Math.floor(165 * goldIntensity)}, ${Math.floor(0 * goldIntensity)}, ${particleOpacity * 0.8}) 50%, 
                      transparent 100%)`,
                    borderRadius: '50%',
                    transform: `translate(-50%, -50%) translate(${particleX}px, ${particleY}px)`,
                    pointerEvents: 'none',
                    boxShadow: `0 0 ${particleSize * 2}px rgba(255, 215, 0, ${particleOpacity * 0.5})`
                  }}
                />
              );
            })}
            
            {/* 金色光环 */}
            <span style={{
              position: 'absolute',
              left: '50%',
              top: '50%',
              width: `${orbitRadius * 2 + 20}px`,
              height: `${orbitRadius * 2 + 20}px`,
              border: `1px solid rgba(255, 215, 0, ${0.2 + Math.sin(frame * 0.02 * speed) * 0.1})`,
              borderRadius: '50%',
              transform: 'translate(-50%, -50%)',
              pointerEvents: 'none'
            }} />
            
            {/* 主文字 */}
            <span style={{
              fontSize: '60px',
              fontWeight: 'bold',
              color: `rgba(255, ${Math.floor(215 + Math.sin(frame * 0.05 * speed) * 40)}, ${Math.floor(Math.sin(frame * 0.05 * speed) * 100)}, 1)`,
              display: 'inline-block',
              textShadow: `
                0 0 10px rgba(255, 215, 0, 0.8),
                0 0 20px rgba(255, 165, 0, 0.4),
                0 2px 4px rgba(0, 0, 0, 0.3)
              `,
              filter: `brightness(${1 + Math.sin(frame * 0.03 * speed) * 0.2})`,
              ...style
            }}>
              {char}
            </span>
          </span>
        );
      })}
    </span>
  );
}

GoldParticleEffect.key = 'GoldParticleEffect';
GoldParticleEffect.description = 'golden particles orbiting effect';
GoldParticleEffect.showName = "金粒飞舞"; 