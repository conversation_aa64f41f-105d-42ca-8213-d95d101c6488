import React from 'react';
import { useCurrentFrame, interpolate, useVideoConfig } from 'remotion';
import { ANIMATION_CYCLE_DURATION } from './data';

interface DataMatrixEffectProps {
  text: string | React.ReactNode[];
  style?: React.CSSProperties;
  matrixChars?: string;
  speed?: number;
}

export default function DataMatrixEffect({
  text = "ABCD123+数字矩阵",
  style = {},
  matrixChars = "01",
  speed = 1,
}: DataMatrixEffectProps) {
  const frame = useCurrentFrame();
  const { fps } = useVideoConfig();
  
  const chars = Array.from(text);
  const matrixCharArray = matrixChars.split('');
  
  return (
    <span style={{
      // display: 'inline-flex',
      // alignItems: 'center',
      // justifyContent: 'center',
      position: 'relative',
      fontFamily: 'monospace',
    }}>
      {chars.map((char, index) => {
        // 数字雨效果
        const columnCount = 8;
        const rainSpeed = speed * 2;
        
        return (
          <span key={index} style={{
            position: 'relative',
            display: 'inline-block',
            margin: '0 2px'
          }}>
            {/* 数字雨背景 */}
            <span style={{
              position: 'absolute',
              left: '50%',
              top: '50%',
              width: '80px',
              height: '120px',
              transform: 'translate(-50%, -50%)',
              overflow: 'hidden',
              pointerEvents: 'none'
            }}>
              {Array.from({ length: columnCount }, (_, col) => {
                const columnX = (col / columnCount) * 80;
                
                return Array.from({ length: 12 }, (_, row) => {
                  const rainY = ((frame * rainSpeed + row * 10 + col * 5) % 140) - 20;
                  const charIndex = Math.floor((frame * speed * 0.1 + row + col) % matrixCharArray.length);
                  const rainChar = matrixCharArray[charIndex];
                  const opacity = interpolate(rainY, [-20, 0, 100, 120], [0, 0.8, 0.8, 0]);
                  
                  return (
                    <span
                      key={`rain-${col}-${row}`}
                      style={{
                        position: 'absolute',
                        left: `${columnX}px`,
                        top: `${rainY}px`,
                        fontSize: '12px',
                        color: `rgba(0, 255, 0, ${opacity})`,
                        textShadow: `0 0 5px rgba(0, 255, 0, ${opacity * 0.5})`,
                        pointerEvents: 'none'
                      }}
                    >
                      {rainChar}
                    </span>
                  );
                });
              })}
            </span>
            
            {/* 主文字 */}
            <span style={{
              fontSize: '60px',
              fontWeight: 'bold',
              color: '#00FF00',
              display: 'inline-block',
              textShadow: `
                0 0 10px #00FF00,
                0 0 20px #00FF00,
                0 0 30px rgba(0, 255, 0, 0.5)
              `,
              filter: `brightness(${1 + Math.sin(frame * 0.05 + index) * 0.2})`,
              animation: `matrix-flicker ${(2 * ANIMATION_CYCLE_DURATION) / speed}s infinite alternate`,
              ...style
            }}>
              {char}
            </span>
          </span>
        );
      })}
    </span>
  );
}

DataMatrixEffect.key = 'DataMatrixEffect';
DataMatrixEffect.description = 'digital matrix rain effect';
DataMatrixEffect.showName = "数字矩阵"; 