import React from 'react';
import { useCurrentFrame, interpolate, useVideoConfig } from 'remotion';
import { ANIMATION_CYCLE_DURATION } from './data';

interface StarSparkleEffectProps {
  text: string | React.ReactNode[];
  style?: React.CSSProperties;
  speed?: number;
}

export default function StarSparkleEffect({
  text = "ABCD123+星星闪烁",
  style = {},
  speed = 1,
}: StarSparkleEffectProps) {
  const frame = useCurrentFrame();
  const { fps } = useVideoConfig();
  
  const sparkleFreq = 2 * speed; // 闪光频率
  const starCount = 16; // 16个星点
  
  const chars = Array.from(text);
  
  return (
    <span style={{
      // display: 'inline-flex',
      // alignItems: 'center',
      // justifyContent: 'center',
      position: 'relative',
      backgroundColor: 'transparent',
      gap: '2px',
    }}>
      {chars.map((char, index) => {
        const charOffset = index * 0.4;
        
        return (
          <div key={index} style={{
            position: 'relative',
            display: 'inline-block'
          }}>
            {/* 主文字 */}
            <span style={{
              fontSize: '60px',
              fontWeight: 'bold',
              color: 'white',
              display: 'inline-block',
              textShadow: `
                0 0 10px rgba(255, 255, 200, 0.6),
                0 0 20px rgba(255, 255, 150, 0.4),
                0 0 30px rgba(255, 255, 100, 0.2)
              `,
              ...style
            }}>
              {char}
            </span>
            
            {/* 星点分布 */}
            {Array.from({ length: starCount }, (_, starIndex) => {
              const starAngle = (starIndex / starCount) * Math.PI * 2 + frame * 0.02 * speed;
              const starRadius = 20 + Math.sin(frame * 0.08 * speed + starIndex) * 15;
              const starX = Math.cos(starAngle) * starRadius;
              const starY = Math.sin(starAngle) * starRadius;
              
              // 星点闪烁
              const sparklePhase = (frame * sparkleFreq * 2 * Math.PI / fps) + (starIndex * Math.PI / 4);
              const sparkleIntensity = Math.max(0.2, Math.abs(Math.sin(sparklePhase)));
              
                              // 随机爆发
                const burstTrigger = Math.sin(frame * 0.1 * speed + starIndex * 2) > 0.8;
              const burstIntensity = burstTrigger ? Math.random() * 2 + 1 : 1;
              
              return (
                <div
                  key={`star-${starIndex}`}
                  style={{
                    position: 'absolute',
                    left: '50%',
                    top: '50%',
                    width: '3px',
                    height: '3px',
                    background: `rgba(255, 255, 200, ${sparkleIntensity * burstIntensity})`,
                    borderRadius: '50%',
                    transform: `translate(-50%, -50%) translate(${starX}px, ${starY}px)`,
                    boxShadow: `0 0 ${6 * burstIntensity}px rgba(255, 255, 200, ${sparkleIntensity})`,
                    pointerEvents: 'none'
                  }}
                />
              );
            })}
            
            {/* 爆发闪光 */}
            {Array.from({ length: 6 }, (_, burstIndex) => {
              const burstLife = (frame + burstIndex * 15) % 45;
              if (burstLife > 20) return null;
              
              const burstProgress = burstLife / 20;
              const burstAngle = (burstIndex / 6) * Math.PI * 2 + Math.sin(frame * 0.05 * speed) * 0.5;
              const burstDistance = 25 + Math.sin(burstIndex) * 10;
              const burstX = Math.cos(burstAngle) * burstDistance;
              const burstY = Math.sin(burstAngle) * burstDistance;
              const burstOpacity = interpolate(burstProgress, [0, 0.3, 1], [0, 1, 0]);
              const burstSize = interpolate(burstProgress, [0, 0.5, 1], [2, 8, 2]);
              
              return (
                <div
                  key={`burst-${burstIndex}`}
                  style={{
                    position: 'absolute',
                    left: '50%',
                    top: '50%',
                    width: `${burstSize}px`,
                    height: `${burstSize}px`,
                    background: `radial-gradient(circle, 
                      rgba(255, 255, 255, ${burstOpacity}) 0%, 
                      rgba(255, 255, 200, ${burstOpacity * 0.8}) 50%, 
                      transparent 100%)`,
                    borderRadius: '50%',
                    transform: `translate(-50%, -50%) translate(${burstX}px, ${burstY}px)`,
                    pointerEvents: 'none'
                  }}
                />
              );
            })}
            
            {/* 拖尾粒子 */}
            {Array.from({ length: 8 }, (_, trailIndex) => {
              const trailLife = (frame + trailIndex * 8) % 60;
              const trailProgress = trailLife / 60;
              
              const trailAngle = trailProgress * Math.PI * 4 + trailIndex;
              const trailRadius = 30 + Math.sin(trailProgress * Math.PI * 2) * 10;
              const trailX = Math.cos(trailAngle) * trailRadius;
              const trailY = Math.sin(trailAngle) * trailRadius;
              const trailOpacity = interpolate(trailProgress, [0, 0.3, 0.7, 1], [0, 0.8, 0.8, 0]);
              const trailSize = 2 + Math.sin(trailProgress * Math.PI) * 2;
              
              return (
                <div
                  key={`trail-${trailIndex}`}
                  style={{
                    position: 'absolute',
                    left: '50%',
                    top: '50%',
                    width: `${trailSize}px`,
                    height: `${trailSize}px`,
                    background: `rgba(255, 255, 150, ${trailOpacity})`,
                    borderRadius: '50%',
                    transform: `translate(-50%, -50%) translate(${trailX}px, ${trailY}px)`,
                    boxShadow: `0 0 4px rgba(255, 255, 150, ${trailOpacity})`,
                    pointerEvents: 'none'
                  }}
                />
              );
            })}
            
            {/* 星座连线 */}
            {Array.from({ length: 4 }, (_, lineIndex) => {
              const lineProgress = (frame * 0.02 * speed + lineIndex * 0.25) % 1;
              const startAngle = lineProgress * Math.PI * 2;
              const endAngle = startAngle + Math.PI * 0.6;
              
              const lineRadius = 35;
              const startX = Math.cos(startAngle) * lineRadius;
              const startY = Math.sin(startAngle) * lineRadius;
              const endX = Math.cos(endAngle) * lineRadius;
              const endY = Math.sin(endAngle) * lineRadius;
              
              const lineLength = Math.sqrt((endX - startX) ** 2 + (endY - startY) ** 2);
              const lineAngle = Math.atan2(endY - startY, endX - startX);
              const lineOpacity = Math.abs(Math.sin(frame * 0.1 + lineIndex)) * 0.3;
              
              return (
                <div
                  key={`line-${lineIndex}`}
                  style={{
                    position: 'absolute',
                    left: '50%',
                    top: '50%',
                    width: `${lineLength}px`,
                    height: '1px',
                    background: `linear-gradient(90deg, 
                      rgba(255, 255, 200, ${lineOpacity}) 0%,
                      rgba(255, 255, 150, ${lineOpacity * 0.8}) 50%,
                      rgba(255, 255, 200, ${lineOpacity}) 100%)`,
                    transform: `translate(-50%, -50%) translate(${startX}px, ${startY}px) rotate(${lineAngle}rad)`,
                    transformOrigin: '0 50%',
                    pointerEvents: 'none'
                  }}
                />
              );
            })}
            
            {/* 十字星光 */}
            {Array.from({ length: 3 }, (_, crossIndex) => {
              const crossTrigger = Math.sin(frame * 0.15 + crossIndex * 2) > 0.7;
              if (!crossTrigger) return null;
              
              const crossAngle = (crossIndex / 3) * Math.PI * 2;
              const crossDistance = 20 + crossIndex * 8;
              const crossX = Math.cos(crossAngle) * crossDistance;
              const crossY = Math.sin(crossAngle) * crossDistance;
              const crossOpacity = Math.random() * 0.8 + 0.2;
              
              return (
                <div
                  key={`cross-${crossIndex}`}
                  style={{
                    position: 'absolute',
                    left: '50%',
                    top: '50%',
                    width: '2px',
                    height: '16px',
                    background: `rgba(255, 255, 255, ${crossOpacity})`,
                    transform: `translate(-50%, -50%) translate(${crossX}px, ${crossY}px) rotate(${crossAngle}rad)`,
                    pointerEvents: 'none'
                  }}
                />
              );
            })}
          </div>
        );
      })}
    </span>
  );
}

StarSparkleEffect.key = 'StarSparkleEffect';
StarSparkleEffect.description = 'star sparkle effect with twinkling lights';
StarSparkleEffect.showName = '星星闪烁'; 