import React from 'react';
import { useCurrentFrame, interpolate, useVideoConfig } from 'remotion';
import { ANIMATION_CYCLE_DURATION } from './data';

interface FlameFlickerEffectProps {
  text: string | React.ReactNode[];
  style?: React.CSSProperties;
  speed?: number;
}

export default function FlameFlickerEffect({
  text = "ABCD123+火焰闪烁",
  style = {},
  speed = 1,
}: FlameFlickerEffectProps) {
  const frame = useCurrentFrame();
  const { fps } = useVideoConfig();
  
  const chars = Array.from(text);
  
  return (
    <span style={{
      // display: 'inline-flex',
      // alignItems: 'center',
      // justifyContent: 'center',
      position: 'relative',
    }}>
      {chars.map((char, index) => {
        // 火焰闪烁效果
        const flicker = Math.abs(Math.sin(frame * 0.15 * speed + index * 0.5)) * 0.6 + 0.4;
        const flameHeight = flicker * 1;
        const emberCount = Math.floor(flameHeight * 8);
        
        return (
          <span key={index} style={{
            position: 'relative',
            display: 'inline-block',
            margin: '0 2px'
          }}>
            {/* 火焰层 */}
            {Array.from({ length: 5 }, (_, layer) => {
              const layerHeight = (layer + 1) * 15 * flameHeight;
              const layerWidth = Math.max(1, (5 - layer) * 8 * flameHeight);
              const layerOpacity = (5 - layer) / 5 * flicker * 1;
              const layerColor = layer < 2 ? 
                `rgba(255, ${Math.floor(100 + layer * 50)}, 0, ${layerOpacity})` :
                `rgba(255, ${Math.floor(200 + layer * 20)}, ${Math.floor(layer * 50)}, ${layerOpacity})`;
              
              return (
                <span
                  key={`flame-${layer}`}
                  style={{
                    position: 'absolute',
                    left: '50%',
                    bottom: '80%',
                    width: `${layerWidth}px`,
                    height: `${layerHeight}px`,
                    background: `radial-gradient(ellipse at 50% 100%, 
                      ${layerColor} 0%, 
                      transparent 70%)`,
                    borderRadius: '50% 50% 50% 50% / 100% 100% 0% 0%',
                    transform: `translateX(-50%) scale(${1 + Math.sin(frame * 0.2 * speed + layer) * 0.2})`,
                    pointerEvents: 'none',
                    filter: 'blur(1px)'
                  }}
                />
              );
            })}
            
            {/* 火星粒子 */}
            {Array.from({ length: emberCount }, (_, i) => {
              const emberAge = (frame + i * 5) * speed;
              const emberLife = (emberAge % 60) / 60; // 1秒生命周期
              const emberX = (Math.sin(emberAge * 0.1) + Math.cos(emberAge * 0.07)) * 15;
              const emberY = -emberLife * 40 - 20;
              const emberOpacity = interpolate(emberLife, [0, 0.3, 0.7, 1], [0, 1, 0.8, 0]) * 1;
              const emberSize = 1 + Math.sin(emberAge * 0.3) * 1;
              
              return (
                <span
                  key={`ember-${i}`}
                  style={{
                    position: 'absolute',
                    left: '50%',
                    top: '50%',
                    width: `${emberSize}px`,
                    height: `${emberSize}px`,
                    background: `rgba(255, ${Math.floor(100 + emberLife * 155)}, 0, ${emberOpacity})`,
                    borderRadius: '50%',
                    transform: `translate(-50%, -50%) translate(${emberX}px, ${emberY}px)`,
                    pointerEvents: 'none',
                    boxShadow: `0 0 ${emberSize * 2}px rgba(255, 165, 0, ${emberOpacity * 0.6})`
                  }}
                />
              );
            })}
            
            {/* 热浪扭曲效果 */}
            <span style={{
              position: 'absolute',
              left: '50%',
              top: '0%',
              width: '60px',
              height: '80px',
              background: `linear-gradient(0deg, 
                transparent 0%, 
                rgba(255, 100, 0, ${flicker * 0.1}) 30%, 
                transparent 100%)`,
              transform: `translateX(-50%) scaleX(${1 + Math.sin(frame * 0.3 * speed) * 0.3})`,
              pointerEvents: 'none',
              filter: 'blur(8px)'
            }} />
            
            {/* 主文字 */}
            <span style={{
              fontSize: '60px',
              fontWeight: 'bold',
              color: `rgba(255, ${Math.floor(100 + flicker * 155)}, ${Math.floor(flicker * 100)}, 1)`,
              display: 'inline-block',
              textShadow: `
                0 0 ${5 + flicker * 15}px rgba(255, 100, 0, ${flicker * 0.8}),
                0 0 ${10 + flicker * 25}px rgba(255, 165, 0, ${flicker * 0.4}),
                0 2px 4px rgba(0, 0, 0, 0.3)
              `,
              filter: `brightness(${0.8 + flicker * 0.4}) contrast(${1 + flicker * 0.3})`,
              transform: `scale(${1 + flicker * 0.03}) rotate(${Math.sin(frame * 0.1 * speed) * 0.5}deg)`,
              ...style
            }}>
              {char}
            </span>
          </span>
        );
      })}
    </span>
  );
}

FlameFlickerEffect.key = 'FlameFlickerEffect';
FlameFlickerEffect.description = 'flame flicker burning effect';
FlameFlickerEffect.showName = "火焰闪烁"; 