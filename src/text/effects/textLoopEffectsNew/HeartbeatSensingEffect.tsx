import React from 'react';
import { useCurrentFrame, interpolate, useVideoConfig } from 'remotion';
import { ANIMATION_CYCLE_DURATION } from './data';

interface HeartbeatSensingEffectProps {
  text: string | React.ReactNode[];
  style?: React.CSSProperties;
  speed?: number;
}

export default function HeartbeatSensingEffect({
  text = "ABCD123+心跳感应",
  style = {},
  speed = 1,
}: HeartbeatSensingEffectProps) {
  const frame = useCurrentFrame();
  const { fps } = useVideoConfig();
  
  const chars = Array.from(text);
  
  // 心跳周期计算，基于速度调整
  const bpm = 72 * speed; // 基础心跳频率 * 速度
  const heartbeatPeriod = (60 / bpm) * fps; // 转换为帧数
  const heartbeatPhase = (frame % heartbeatPeriod) / heartbeatPeriod;
  
  // 心跳脉冲模式 (模拟真实心跳的双重跳动)
  const getHeartbeatPulse = (phase: number) => {
    if (phase < 0.1) return interpolate(phase, [0, 0.1], [0, 1]); // 第一次跳动上升
    if (phase < 0.15) return interpolate(phase, [0.1, 0.15], [1, 0.3]); // 第一次跳动下降
    if (phase < 0.25) return interpolate(phase, [0.15, 0.25], [0.3, 0.8]); // 第二次跳动
    if (phase < 0.35) return interpolate(phase, [0.25, 0.35], [0.8, 0]); // 第二次跳动下降
    return 0; // 静息期
  };
  
  const pulseStrength = getHeartbeatPulse(heartbeatPhase);
  
  return (
    <span style={{
      // display: 'inline-flex',
      // alignItems: 'center',
      // justifyContent: 'center',
      position: 'relative',
    }}>
      {chars.map((char, index) => {
        // 每个字符的脉冲延迟
        const charDelay = index * 0.05;
        const charPhase = ((frame - charDelay * fps) % heartbeatPeriod) / heartbeatPeriod;
        const charPulse = getHeartbeatPulse(charPhase > 0 ? charPhase : 0);
        
        return (
          <span key={index} style={{
            fontSize: '60px',
            fontWeight: 'bold',
            color: `rgba(255, ${Math.floor(100 + charPulse * 155)}, ${Math.floor(100 + charPulse * 155)}, 1)`,
            display: 'inline-block',
            margin: '0 1px',
            transform: `scale(${1 + charPulse * 0.15})`,
            textShadow: `
              0 0 ${5 + charPulse * 15}px rgba(255, 100, 100, ${charPulse * 0.8}),
              0 0 ${10 + charPulse * 25}px rgba(255, 50, 50, ${charPulse * 0.4})
            `,
            filter: `brightness(${1 + charPulse * 0.5})`,
            ...style
          }}>
            {char}
          </span>
        );
      })}
      
      {/* 心跳波形背景 */}
      {pulseStrength > 0.3 && (
        <span style={{
          position: 'absolute',
          left: '50%',
          top: '50%',
          width: '200px',
          height: '100px',
          transform: 'translate(-50%, -50%)',
          pointerEvents: 'none'
        }}>
          {/* 脉冲环 */}
          <span style={{
            position: 'absolute',
            left: '50%',
            top: '50%',
            width: `${50 + pulseStrength * 100}px`,
            height: `${50 + pulseStrength * 100}px`,
            border: `2px solid rgba(255, 100, 100, ${pulseStrength * 0.5})`,
            borderRadius: '50%',
            transform: 'translate(-50%, -50%)',
            opacity: 1 - pulseStrength * 0.5
          }} />
        </span>
      )}
    </span>
  );
}

HeartbeatSensingEffect.key = 'HeartbeatSensingEffect';
HeartbeatSensingEffect.description = 'heartbeat sensing pulse effect';
HeartbeatSensingEffect.showName = "心跳感应"; 