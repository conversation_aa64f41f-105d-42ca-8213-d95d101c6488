import React from 'react';
import { useCurrentFrame, interpolate, useVideoConfig } from 'remotion';
import { ANIMATION_CYCLE_DURATION } from './data';

interface ElectricSurgeEffectProps {
  text: string | React.ReactNode[];
  style?: React.CSSProperties;
  lightningCount?: number;
  speed?: number;
}

export default function ElectricSurgeEffect({
  text = "ABCD123+电流涌动",
  style = {},
  lightningCount = 6,
  speed = 1,
}: ElectricSurgeEffectProps) {
  const frame = useCurrentFrame();
  const { fps } = useVideoConfig();
  
  const chars = Array.from(text);
  
  return (
    <span style={{
      // display: 'inline-flex',
      // alignItems: 'center',
      // justifyContent: 'center',
      position: 'relative',
    }}>
      {chars.map((char, index) => {
        // 电流脉冲效果
        const surgeIntensity = Math.abs(Math.sin(frame * 0.1 * speed + index * 0.3)) * 0.8 + 0.2;
        const electricPulse = Math.sin(frame * 0.2 * speed + index * 0.5) * 0.5 + 0.5;
        
        return (
          <span key={index} style={{
            position: 'relative',
            display: 'inline-block',
            margin: '0 2px'
          }}>
            {/* 闪电效果 */}
            {Array.from({ length: lightningCount }, (_, i) => {
              const lightningAngle = (i / lightningCount) * Math.PI * 2 + frame * 0.05 * speed;
              const lightningDistance = 25 + Math.sin(frame * 0.08 * speed + i) * 10;
              const lightningX = Math.cos(lightningAngle) * lightningDistance;
              const lightningY = Math.sin(lightningAngle) * lightningDistance;
              const lightningOpacity = surgeIntensity * (0.6 + Math.sin(frame * 0.15 * speed + i) * 0.4);
              
              // 闪电路径点
              const pathPoints = Array.from({ length: 4 }, (_, p) => {
                const progress = p / 3;
                const zigzag = Math.sin(progress * Math.PI * 3 + frame * 0.3 * speed) * 5;
                return {
                  x: lightningX * progress + zigzag,
                  y: lightningY * progress + Math.sin(progress * Math.PI + frame * 0.2 * speed) * 3
                };
              });
              
              return (
                <span key={`lightning-${i}`}>
                  {pathPoints.map((point, p) => (
                    <span
                      key={`point-${p}`}
                      style={{
                        position: 'absolute',
                        left: '50%',
                        top: '50%',
                        width: '2px',
                        height: '8px',
                        background: `linear-gradient(0deg, 
                          rgba(100, 150, 255, ${lightningOpacity}) 0%, 
                          rgba(255, 255, 255, ${lightningOpacity * 0.8}) 50%, 
                          rgba(100, 150, 255, ${lightningOpacity}) 100%)`,
                        borderRadius: '1px',
                        transform: `translate(-50%, -50%) translate(${point.x}px, ${point.y}px) rotate(${Math.atan2(point.y, point.x)}rad)`,
                        pointerEvents: 'none',
                        boxShadow: `0 0 4px rgba(100, 150, 255, ${lightningOpacity * 0.6})`
                      }}
                    />
                  ))}
                </span>
              );
            })}
            
            {/* 电弧环 */}
            <span style={{
              position: 'absolute',
              left: '50%',
              top: '50%',
              width: `${40 + surgeIntensity * 20}px`,
              height: `${40 + surgeIntensity * 20}px`,
              border: `1px solid rgba(100, 150, 255, ${surgeIntensity * 0.3})`,
              borderRadius: '50%',
              transform: 'translate(-50%, -50%)',
              pointerEvents: 'none'
            }} />
            
            {/* 主文字 */}
            <span style={{
              fontSize: '60px',
              fontWeight: 'bold',
              color: `rgba(${Math.floor(100 + surgeIntensity * 155)}, ${Math.floor(150 + surgeIntensity * 105)}, 255, 1)`,
              display: 'inline-block',
              textShadow: `
                0 0 ${5 + surgeIntensity * 15}px rgba(100, 150, 255, ${surgeIntensity * 0.8}),
                0 0 ${10 + surgeIntensity * 25}px rgba(255, 255, 255, ${surgeIntensity * 0.4})
              `,
              filter: `brightness(${1 + surgeIntensity * 0.5}) contrast(${1 + surgeIntensity * 0.3})`,
              transform: `scale(${1 + electricPulse * 0.05})`,
              ...style
            }}>
              {char}
            </span>
          </span>
        );
      })}
    </span>
  );
}

ElectricSurgeEffect.key = 'ElectricSurgeEffect';
ElectricSurgeEffect.description = 'electric surge lightning effect';
ElectricSurgeEffect.showName = "电流涌动"; 