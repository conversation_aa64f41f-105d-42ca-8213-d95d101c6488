import React from 'react';
import { useCurrentFrame, interpolate, useVideoConfig } from 'remotion';

interface HourglassEmptyEffectProps {
  text: string;
  style?: React.CSSProperties;
}

const HourglassEmptyEffect: React.FC<HourglassEmptyEffectProps> & { showName: string } = ({
  text,
  style
}) => {
  const frame = useCurrentFrame();
  const { fps } = useVideoConfig();
  
  const chars = text.split('');
  const duration = 1.7 * fps; // 1.7秒
  const flowSpeed = 80; // 流沙速度80px/s
  
  return (
    <div style={{
      display: 'inline-flex',
      alignItems: 'center',
      justifyContent: 'center',
      position: 'relative',
      backgroundColor: 'transparent',
      ...style
    }}>
      {chars.map((char, index) => {
        const charDelay = index * 3;
        const charProgress = Math.max(0, Math.min(1, (frame - charDelay) / (duration * 0.8)));
        
        // 沙漏阶段
        const hourglassPhase = interpolate(charProgress, [0, 0.3, 0.7, 1], [0, 1, 2, 3]);
        
        // 文字透明度
        const opacity = interpolate(charProgress, [0, 0.6, 1], [1, 0.3, 0]);
        
        return (
          <div key={index} style={{
            position: 'relative',
            display: 'inline-block',
            margin: '0 2px'
          }}>

            
            {/* 沙粒分解 */}
            {hourglassPhase >= 1 && (
              <>
                {Array.from({ length: 20 }, (_, i) => {
                  const sandDelay = i * 2;
                  const sandProgress = Math.max(0, Math.min(1, (hourglassPhase - 1 - sandDelay * 0.01) / 1.5));
                  
                  if (sandProgress <= 0) return null;
                  
                  // 沙粒初始位置（围绕文字）
                  const initialAngle = (i / 20) * Math.PI * 2;
                  const initialRadius = 15 + Math.random() * 10;
                  const initialX = Math.cos(initialAngle) * initialRadius;
                  const initialY = Math.sin(initialAngle) * initialRadius;
                  
                  // 流向中心狭缝
                  const centerX = 0;
                  const centerY = 0;
                  const currentX = interpolate(sandProgress, [0, 0.6], [initialX, centerX]);
                  const currentY = interpolate(sandProgress, [0, 0.6], [initialY, centerY]);
                  
                  // 下漏效果
                  const fallY = sandProgress > 0.6 
                    ? interpolate(sandProgress, [0.6, 1], [0, flowSpeed])
                    : 0;
                  
                  const sandSize = 2 + Math.random() * 2;
                  const sandOpacity = interpolate(sandProgress, [0, 0.3, 0.8, 1], [0, 1, 1, 0]);
                  
                  return (
                    <div
                      key={`sand-${i}`}
                      style={{
                        position: 'absolute',
                        left: '50%',
                        top: '50%',
                        width: `${sandSize}px`,
                        height: `${sandSize}px`,
                        backgroundColor: `rgba(194,154,108,${sandOpacity})`,
                        borderRadius: '50%',
                        transform: `translate(-50%, -50%) translate(${currentX}px, ${currentY + fallY}px)`,
                        pointerEvents: 'none'
                      }}
                    />
                  );
                })}
              </>
            )}
            
            {/* 沙流加速效果 */}
            {hourglassPhase >= 1.5 && hourglassPhase < 2.5 && (
              <div style={{
                position: 'absolute',
                left: '50%',
                top: '50%',
                width: '4px',
                height: `${interpolate(hourglassPhase, [1.5, 2], [10, 40])}px`,
                background: `linear-gradient(180deg, 
                  rgba(194,154,108,${interpolate(hourglassPhase, [1.5, 2, 2.5], [0, 0.8, 0])}) 0%, 
                  rgba(160,120,80,${interpolate(hourglassPhase, [1.5, 2, 2.5], [0, 0.6, 0])}) 100%)`,
                transform: 'translateX(-50%)',
                pointerEvents: 'none'
              }} />
            )}
            
            {/* 底部堆积 */}
            {hourglassPhase >= 2 && opacity > 0 && (
              <div style={{
                position: 'absolute',
                left: '50%',
                bottom: '-40px',
                width: `${interpolate(hourglassPhase, [2, 2.8], [20, 35])}px`,
                height: `${interpolate(hourglassPhase, [2, 2.8], [8, 15])}px`,
                background: `linear-gradient(135deg, 
                  rgba(194,154,108,${interpolate(hourglassPhase, [2, 2.8, 3], [0, 0.8, 0.3]) * opacity}) 0%, 
                  rgba(160,120,80,${interpolate(hourglassPhase, [2, 2.8, 3], [0, 0.6, 0.2]) * opacity}) 100%)`,
                borderRadius: '50% 50% 0 0',
                transform: 'translateX(-50%)',
                pointerEvents: 'none'
              }} />
            )}
            
            {/* 沙堆塌陷 */}
            {hourglassPhase >= 2.8 && opacity > 0 && (
              <>
                {Array.from({ length: 8 }, (_, i) => {
                  const collapseProgress = (hourglassPhase - 2.8) / 0.2;
                  const collapseAngle = (i / 8) * Math.PI * 2;
                  const collapseDistance = interpolate(collapseProgress, [0, 1], [0, 15]);
                  const collapseX = Math.cos(collapseAngle) * collapseDistance;
                  const collapseY = Math.sin(collapseAngle) * collapseDistance + 20;
                  const collapseOpacity = interpolate(collapseProgress, [0, 0.5, 1], [1, 0.5, 0]) * opacity;
                  
                  return (
                    <div
                      key={`collapse-${i}`}
                      style={{
                        position: 'absolute',
                        left: '50%',
                        bottom: '-40px',
                        width: '3px',
                        height: '3px',
                        backgroundColor: `rgba(194,154,108,${collapseOpacity})`,
                        borderRadius: '50%',
                        transform: `translate(-50%, 0) translate(${collapseX}px, ${collapseY}px)`,
                        pointerEvents: 'none'
                      }}
                    />
                  );
                })}
              </>
            )}
            
            {/* 主文字 */}
            <span style={{
              fontSize: '60px',
              fontWeight: 'bold',
              color: 'white',
              display: 'inline-block',
              opacity: opacity,
              filter: hourglassPhase >= 1 
                ? `blur(${interpolate(hourglassPhase, [1, 2], [0, 2])}px)` 
                : 'none',
              textShadow: hourglassPhase < 1 
                ? 'none' 
                : `0 0 5px rgba(194,154,108,0.5)`
            }}>
              {char}
            </span>
          </div>
        );
      })}
    </div>
  );
};

HourglassEmptyEffect.showName = "沙漏流尽";

export default HourglassEmptyEffect; 