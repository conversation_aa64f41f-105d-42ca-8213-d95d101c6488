import React from 'react';
import { interpolate, useCurrentFrame } from 'remotion';

interface TypewriterOutEffectProps {
  text: string | React.ReactNode[];
  style: React.CSSProperties;
  durationFrames?: number;
}

export default function TypewriterOutEffect({
  text = '展示文本',
  style = {},
  durationFrames = 90,
}: TypewriterOutEffectProps) {
  const frame = useCurrentFrame();

  const textString = typeof text === 'string' ? text : text.join('');
  const charsToShow = interpolate(
    frame,
    [0, durationFrames],
    [textString.length, 0],
    {
      extrapolateLeft: 'clamp',
      extrapolateRight: 'clamp',
    }
  );

  const showCursor = Math.floor(frame / 20) % 2 === 0;

  return (
    <span
      style={{
        display: 'inline-flex',
        alignItems: 'center',
        justifyContent: 'center',
        height: '100%',
        position: 'relative',
      }}
    >
      <span
        style={{
          color: '#ffffff',
          textAlign: 'center',
          ...style,
        }}
      >
        {textString.slice(0, Math.floor(charsToShow))}
        {frame < durationFrames && showCursor && '|'}
      </span>
    </span>
  );
}

TypewriterOutEffect.key = 'TypewriterOutEffect';
TypewriterOutEffect.description = 'typewriter out text effect';
TypewriterOutEffect.showName = '打字机退场'; 