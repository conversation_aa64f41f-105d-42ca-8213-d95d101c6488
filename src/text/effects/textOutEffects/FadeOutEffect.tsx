import React from 'react';
import { interpolate, useCurrentFrame } from 'remotion';

interface FadeOutEffectProps {
  text: string | React.ReactNode[];
  style: React.CSSProperties;
  durationFrames?: number;
}

export default function FadeOutEffect({
  text = '展示文本',
  style = {},
  durationFrames = 45,
}: FadeOutEffectProps) {
  const frame = useCurrentFrame();

  // 淡出动画
  const opacity = interpolate(
    frame,
    [0, durationFrames],
    [1, 0],
    {
      extrapolateLeft: 'clamp',
      extrapolateRight: 'clamp',
    }
  );

  return (
    <span
      style={{
        display: 'inline-flex',
        alignItems: 'center',
        justifyContent: 'center',
        height: '100%',
      }}
    >
      <span
        style={{
          opacity: opacity,
          color: '#ffffff',
          textAlign: 'center',
          textShadow: '0 0 8px #ffffff',
          ...style,
        }}
      >
        {text}
      </span>
    </span>
  );
}

FadeOutEffect.key = 'FadeOutEffect';
FadeOutEffect.description = 'fade out text effect';
FadeOutEffect.showName = '淡出'; 