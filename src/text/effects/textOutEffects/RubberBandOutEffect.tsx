import React from 'react';
import { interpolate, useCurrentFrame } from 'remotion';

interface RubberBandOutEffectProps {
  text: string | React.ReactNode[];
  style: React.CSSProperties;
  durationFrames?: number;
}

export default function RubberBandOutEffect({
  text = '展示文本',
  style = {},
  durationFrames = 60,
}: RubberBandOutEffectProps) {
  const frame = useCurrentFrame();

  // 橡皮筋拉伸效果
  const scaleX = interpolate(
    frame,
    [0, 15, 30, 45, 60],
    [1, 1.25, 0.75, 1.15, 0.1],
    {
      extrapolateLeft: 'clamp',
      extrapolateRight: 'clamp',
    }
  );

  const scaleY = interpolate(
    frame,
    [0, 15, 30, 45, 60],
    [1, 0.75, 1.25, 0.85, 0.1],
    {
      extrapolateLeft: 'clamp',
      extrapolateRight: 'clamp',
    }
  );

  // 透明度
  const opacity = interpolate(
    frame,
    [durationFrames - 15, durationFrames],
    [1, 0],
    {
      extrapolateLeft: 'clamp',
      extrapolateRight: 'clamp',
    }
  );

  return (
    <span
      style={{
        display: 'inline-flex',
        alignItems: 'center',
        justifyContent: 'center',
        height: '100%',
      }}
    >
      <span
        style={{
          opacity: opacity,
          transform: `scaleX(${scaleX}) scaleY(${scaleY})`,
          color: '#ffffff',
          textAlign: 'center',
          textShadow: '0 0 8px #ffffff',
          ...style,
        }}
      >
        {text}
      </span>
    </span>
  );
}

RubberBandOutEffect.key = 'RubberBandOutEffect';
RubberBandOutEffect.description = 'rubber band out text effect';
RubberBandOutEffect.showName = '橡皮筋出场'; 