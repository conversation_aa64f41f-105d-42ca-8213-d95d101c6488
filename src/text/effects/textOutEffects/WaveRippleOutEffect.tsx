import React from 'react';
import { interpolate, useCurrentFrame } from 'remotion';

interface WaveRippleOutEffectProps {
  text: string | React.ReactNode[];
  style: React.CSSProperties;
  durationFrames?: number;
}

export default function WaveRippleOutEffect({
  text = '展示文本',
  style = {},
  durationFrames = 60,
}: WaveRippleOutEffectProps) {
  const frame = useCurrentFrame();

  // 文字淡出
  const opacity = interpolate(
    frame,
    [durationFrames - 20, durationFrames],
    [1, 0],
    {
      extrapolateLeft: 'clamp',
      extrapolateRight: 'clamp',
    }
  );

  // 波纹效果
  const waveScale = 1 - Math.sin(frame * 0.3) * 0.1;

  // 背景波纹扩散
  const ripples = Array.from({ length: 3 }, (_, i) => {
    const rippleFrame = frame + i * 10;
    if (rippleFrame > durationFrames + 20) return null;
    
    const rippleOpacity = interpolate(
      frame,
      [0, durationFrames],
      [0.3, 0],
      { extrapolateLeft: 'clamp', extrapolateRight: 'clamp' }
    );
    
    const rippleScale = rippleFrame * 3;
    
    return (
      <div
        key={i}
        style={{
          position: 'absolute',
          left: '50%',
          top: '50%',
          width: `${rippleScale}px`,
          height: `${rippleScale}px`,
          borderRadius: '50%',
          border: '2px solid rgba(255, 255, 255, 0.3)',
          opacity: rippleOpacity,
          transform: 'translate(-50%, -50%)',
          pointerEvents: 'none',
        }}
      />
    );
  }).filter(Boolean);

  return (
    <span
      style={{
        display: 'inline-flex',
        alignItems: 'center',
        justifyContent: 'center',
        height: '100%',
        position: 'relative',
      }}
    >
      {ripples}
      <span
        style={{
          opacity: opacity,
          transform: `scale(${waveScale})`,
          color: '#ffffff',
          textAlign: 'center',
          textShadow: '0 0 8px #ffffff',
          ...style,
        }}
      >
        {text}
      </span>
    </span>
  );
}

WaveRippleOutEffect.key = 'WaveRippleOutEffect';
WaveRippleOutEffect.description = 'wave ripple out text effect';
WaveRippleOutEffect.showName = '波纹扩散'; 