import React from 'react';
import { interpolate, useCurrentFrame } from 'remotion';

interface ElasticOutEffectProps {
  text: string | React.ReactNode[];
  style: React.CSSProperties;
  durationFrames?: number;
}

export default function ElasticOutEffect({
  text = '展示文本',
  style = {},
  durationFrames = 70,
}: ElasticOutEffectProps) {
  const frame = useCurrentFrame();

  // 弹性缩放动画
  const elasticScale = interpolate(
    frame,
    [0, 20, 40, 60, 70],
    [1, 1.2, 0.8, 0.3, 0],
    {
      extrapolateLeft: 'clamp',
      extrapolateRight: 'clamp',
    }
  );

  // 透明度
  const opacity = interpolate(
    frame,
    [durationFrames - 15, durationFrames],
    [1, 0],
    {
      extrapolateLeft: 'clamp',
      extrapolateRight: 'clamp',
    }
  );

  return (
    <span
      style={{
        display: 'inline-flex',
        alignItems: 'center',
        justifyContent: 'center',
        height: '100%',
      }}
    >
      <span
        style={{
          opacity: opacity,
          transform: `scale(${elasticScale})`,
          color: '#ffffff',
          textAlign: 'center',
          textShadow: '0 0 8px #ffffff',
          ...style,
        }}
      >
        {text}
      </span>
    </span>
  );
}

ElasticOutEffect.key = 'ElasticOutEffect';
ElasticOutEffect.description = 'elastic out text effect';
ElasticOutEffect.showName = '弹性出场'; 