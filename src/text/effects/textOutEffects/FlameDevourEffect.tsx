import React from 'react';
import { useCurrentFrame, interpolate, useVideoConfig } from 'remotion';

interface FlameDevourEffectProps {
  text: string;
  style?: React.CSSProperties;
}

const FlameDevourEffect: React.FC<FlameDevourEffectProps> & { showName: string } = ({
  text,
  style
}) => {
  const frame = useCurrentFrame();
  const { fps } = useVideoConfig();
  
  const chars = text.split('');
  const duration = 1.1 * fps; // 1.1秒
  const burnSpeed = 200; // 燃烧速度200px/s
  
  return (
    <div style={{
      display: 'inline-flex',
      alignItems: 'center',
      justifyContent: 'center',
      position: 'relative',
      backgroundColor: 'transparent',
      ...style
    }}>
      {chars.map((char, index) => {
        const charDelay = index * 2;
        const charProgress = Math.max(0, Math.min(1, (frame - charDelay) / (duration * 0.8)));
        
        // 燃烧阶段
        const burnPhase = interpolate(charProgress, [0, 0.3, 0.7, 1], [0, 1, 2, 3]);
        
        // 文字透明度
        const opacity = interpolate(charProgress, [0, 0.7, 1], [1, 0.2, 0]);
        
        return (
          <div key={index} style={{
            position: 'relative',
            display: 'inline-block',
            margin: '0 2px'
          }}>
            {/* 底部火星点燃 */}
            {burnPhase >= 0.2 && burnPhase < 1.5 && (
              <>
                {Array.from({ length: 6 }, (_, i) => {
                  const sparkDelay = i * 3;
                  const sparkProgress = Math.max(0, Math.min(1, (burnPhase - 0.2 - sparkDelay * 0.01) / 0.8));
                  
                  if (sparkProgress <= 0) return null;
                  
                  const sparkX = (Math.random() - 0.5) * 30;
                  const sparkY = interpolate(sparkProgress, [0, 1], [60, 40]);
                  const sparkSize = 2 + Math.random() * 3;
                  const sparkOpacity = interpolate(sparkProgress, [0, 0.5, 1], [0, 1, 0]);
                  
                  return (
                    <div
                      key={`spark-${i}`}
                      style={{
                        position: 'absolute',
                        left: '50%',
                        bottom: 0,
                        width: `${sparkSize}px`,
                        height: `${sparkSize}px`,
                        background: `radial-gradient(circle, 
                          rgba(255,200,0,${sparkOpacity}) 0%, 
                          rgba(255,100,0,${sparkOpacity * 0.8}) 50%, 
                          transparent 100%)`,
                        borderRadius: '50%',
                        transform: `translate(-50%, 0) translate(${sparkX}px, ${sparkY}px)`,
                        pointerEvents: 'none'
                      }}
                    />
                  );
                })}
              </>
            )}
            
            {/* 烈焰向上蔓延 */}
            {burnPhase >= 1 && (
              <>
                {Array.from({ length: 8 }, (_, i) => {
                  const flameDelay = i * 2;
                  const flameProgress = Math.max(0, Math.min(1, (burnPhase - 1 - flameDelay * 0.01) / 1.2));
                  
                  if (flameProgress <= 0) return null;
                  
                  const flameHeight = interpolate(flameProgress, [0, 1], [0, burnSpeed * 0.3]);
                  const flameX = (Math.random() - 0.5) * 25;
                  const flameWidth = 8 + Math.random() * 12;
                  const flameOpacity = interpolate(flameProgress, [0, 0.3, 0.8, 1], [0, 1, 0.8, 0]);
                  
                  return (
                    <div
                      key={`flame-${i}`}
                      style={{
                        position: 'absolute',
                        left: '50%',
                        bottom: 0,
                        width: `${flameWidth}px`,
                        height: `${flameHeight}px`,
                        background: `linear-gradient(180deg, 
                          rgba(255,0,0,${flameOpacity}) 0%, 
                          rgba(255,100,0,${flameOpacity * 0.8}) 30%, 
                          rgba(255,200,0,${flameOpacity * 0.6}) 70%, 
                          transparent 100%)`,
                        borderRadius: '50% 50% 0 0',
                        transform: `translateX(-50%) translateX(${flameX}px)`,
                        filter: 'blur(1px)',
                        pointerEvents: 'none'
                      }}
                    />
                  );
                })}
              </>
            )}
            

            
            {/* 灰烬飘飞 */}
            {burnPhase >= 1.8 && (
              <>
                {Array.from({ length: 12 }, (_, i) => {
                  const ashProgress = Math.max(0, (burnPhase - 1.8) / 1.2);
                  const ashAngle = (Math.random() - 0.5) * Math.PI * 0.5;
                  const ashDistance = interpolate(ashProgress, [0, 1], [0, 40 + Math.random() * 30]);
                  const ashX = Math.cos(ashAngle) * ashDistance;
                  const ashY = Math.sin(ashAngle) * ashDistance - ashProgress * 30;
                  const ashSize = 1 + Math.random() * 2;
                  const ashOpacity = interpolate(ashProgress, [0, 0.5, 1], [0, 0.8, 0]);
                  
                  return (
                    <div
                      key={`ash-${i}`}
                      style={{
                        position: 'absolute',
                        left: '50%',
                        top: '50%',
                        width: `${ashSize}px`,
                        height: `${ashSize}px`,
                        backgroundColor: `rgba(100,100,100,${ashOpacity})`,
                        borderRadius: '50%',
                        transform: `translate(-50%, -50%) translate(${ashX}px, ${ashY}px)`,
                        pointerEvents: 'none'
                      }}
                    />
                  );
                })}
              </>
            )}
            
            {/* 火焰骤熄效果 */}
            {burnPhase >= 2.5 && (
              <div style={{
                position: 'absolute',
                left: '50%',
                bottom: 0,
                width: '40px',
                height: '60px',
                background: `radial-gradient(ellipse, 
                  rgba(100,100,100,${interpolate(burnPhase, [2.5, 3], [0.3, 0])}) 0%, 
                  transparent 100%)`,
                transform: 'translateX(-50%)',
                pointerEvents: 'none'
              }} />
            )}
            
            {/* 主文字 */}
            <span style={{
              fontSize: '60px',
              fontWeight: 'bold',
              color: burnPhase < 1.5 ? 'white' : `rgba(50,25,0,${1 - (burnPhase - 1.5) / 1.5})`,
              display: 'inline-block',
              opacity: opacity,
              textShadow: burnPhase >= 1 && burnPhase < 2 
                ? `0 0 10px rgba(255,100,0,${interpolate(burnPhase, [1, 2], [0, 0.8])})` 
                : 'none',
              filter: burnPhase >= 1.5 
                ? `blur(${interpolate(burnPhase, [1.5, 2.5], [0, 2])}px)` 
                : 'none'
            }}>
              {char}
            </span>
          </div>
        );
      })}
    </div>
  );
};

FlameDevourEffect.showName = "火焰吞噬";

export default FlameDevourEffect; 