import React from 'react';
import { interpolate, useCurrentFrame } from 'remotion';

interface FlickerOutEffectProps {
  text: string | React.ReactNode[];
  style: React.CSSProperties;
  durationFrames?: number;
}

export default function FlickerOutEffect({
  text = '展示文本',
  style = {},
  durationFrames = 60,
}: FlickerOutEffectProps) {
  const frame = useCurrentFrame();

  // 闪烁效果（频率递增）
  const flickerSpeed = 0.5 + (frame / durationFrames) * 2;
  const flicker = Math.sin(frame * flickerSpeed) > 0.3 ? 1 : 0.2;

  // 整体透明度
  const opacity = interpolate(
    frame,
    [durationFrames - 20, durationFrames],
    [1, 0],
    {
      extrapolateLeft: 'clamp',
      extrapolateRight: 'clamp',
    }
  );

  return (
    <span
      style={{
        display: 'inline-flex',
        alignItems: 'center',
        justifyContent: 'center',
        height: '100%',
      }}
    >
      <span
        style={{
          opacity: opacity * flicker,
          color: '#ffffff',
          textAlign: 'center',
          textShadow: `0 0 ${8 * flicker}px #ffffff`,
          ...style,
        }}
      >
        {text}
      </span>
    </span>
  );
}

FlickerOutEffect.key = 'FlickerOutEffect';
FlickerOutEffect.description = 'flicker out text effect';
FlickerOutEffect.showName = '闪烁消失'; 