import React from 'react';
import { interpolate, useCurrentFrame } from 'remotion';

interface ShakeOutEffectProps {
  text: string | React.ReactNode[];
  style: React.CSSProperties;
  durationFrames?: number;
}

export default function ShakeOutEffect({
  text = '展示文本',
  style = {},
  durationFrames = 45,
}: ShakeOutEffectProps) {
  const frame = useCurrentFrame();

  // 文字淡出
  const opacity = interpolate(
    frame,
    [durationFrames - 15, durationFrames],
    [1, 0],
    {
      extrapolateLeft: 'clamp',
      extrapolateRight: 'clamp',
    }
  );

  // 抖动强度逐渐增强
  const shakeIntensity = interpolate(
    frame,
    [0, durationFrames],
    [0, 15],
    {
      extrapolateLeft: 'clamp',
      extrapolateRight: 'clamp',
    }
  );

  // 随机抖动偏移
  const shakeX = (Math.random() - 0.5) * shakeIntensity;
  const shakeY = (Math.random() - 0.5) * shakeIntensity;

  return (
    <span
      style={{
        display: 'inline-flex',
        alignItems: 'center',
        justifyContent: 'center',
        height: '100%',
        position: 'relative',
      }}
    >
      <span
        style={{
          opacity: opacity,
          transform: `translate(${shakeX}px, ${shakeY}px)`,
          color: '#ffffff',
          textAlign: 'center',
          textShadow: '0 0 8px #ffffff',
          ...style,
        }}
      >
        {text}
      </span>
    </span>
  );
}

ShakeOutEffect.key = 'ShakeOutEffect';
ShakeOutEffect.description = 'shake out text effect with increasing intensity';
ShakeOutEffect.showName = '抖动出场'; 