import React from 'react';
import { interpolate, useCurrentFrame } from 'remotion';

interface GlowPulseOutEffectProps {
  text: string | React.ReactNode[];
  style: React.CSSProperties;
  durationFrames?: number;
}

export default function GlowPulseOutEffect({
  text = '展示文本',
  style = {},
  durationFrames = 60,
}: GlowPulseOutEffectProps) {
  const frame = useCurrentFrame();

  // 文字淡出
  const opacity = interpolate(
    frame,
    [durationFrames - 20, durationFrames],
    [1, 0],
    {
      extrapolateLeft: 'clamp',
      extrapolateRight: 'clamp',
    }
  );

  // 发光脉冲效果（逐渐减弱）
  const baseIntensity = 1 - frame / durationFrames;
  const glowIntensity = Math.sin(frame * 0.2) * 0.5 * baseIntensity + baseIntensity;
  const shadowBlur = (10 + Math.sin(frame * 0.15) * 8) * baseIntensity;

  return (
    <span
      style={{
        display: 'inline-flex',
        alignItems: 'center',
        justifyContent: 'center',
        height: '100%',
        position: 'relative',
      }}
    >
      <span
        style={{
          opacity: opacity,
          color: '#ffffff',
          textAlign: 'center',
          textShadow: `0 0 ${shadowBlur}px rgba(255, 255, 255, ${glowIntensity})`,
          filter: `brightness(${glowIntensity})`,
          ...style,
        }}
      >
        {text}
      </span>
    </span>
  );
}

GlowPulseOutEffect.key = 'GlowPulseOutEffect';
GlowPulseOutEffect.description = 'glow pulse out text effect';
GlowPulseOutEffect.showName = '发光消退'; 