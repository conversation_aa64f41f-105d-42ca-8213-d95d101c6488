import React from 'react';
import { useCurrentFrame, interpolate, useVideoConfig } from 'remotion';

interface MagneticRepelEffectProps {
  text: string;
  style?: React.CSSProperties;
}

const MagneticRepelEffect: React.FC<MagneticRepelEffectProps> & { showName: string } = ({
  text,
  style
}) => {
  const frame = useCurrentFrame();
  const { fps } = useVideoConfig();
  
  const chars = text.split('');
  const duration = 1 * fps; // 1秒
  const repelForce = 1.8; // 排斥力1.8
  
  return (
    <div style={{
      display: 'inline-flex',
      alignItems: 'center',
      justifyContent: 'center',
      position: 'relative',
      backgroundColor: 'transparent',
      ...style
    }}>
      {chars.map((char, index) => {
        const charDelay = index * 2;
        const charProgress = Math.max(0, Math.min(1, (frame - charDelay) / (duration * 0.8)));
        
        // 磁力阶段
        const magneticPhase = interpolate(charProgress, [0, 0.3, 0.7, 1], [0, 1, 2, 3]);
        
        // 文字透明度
        const opacity = interpolate(charProgress, [0, 0.6, 1], [1, 0.3, 0]);
        
        return (
          <div key={index} style={{
            position: 'relative',
            display: 'inline-block',
            margin: '0 2px'
          }}>
            {/* 磁场线可见效果 */}
            {magneticPhase >= 0.2 && magneticPhase < 2.5 && (
              <>
                {Array.from({ length: 8 }, (_, i) => {
                  const fieldAngle = (i / 8) * Math.PI * 2;
                  const fieldRadius = interpolate(magneticPhase, [0.2, 1.5], [10, 40]);
                  const fieldOpacity = interpolate(magneticPhase, [0.2, 1, 2, 2.5], [0, 0.6, 0.4, 0]);
                  
                  return (
                    <div
                      key={`field-${i}`}
                      style={{
                        position: 'absolute',
                        left: '50%',
                        top: '50%',
                        width: `${fieldRadius * 2}px`,
                        height: '1px',
                        background: `linear-gradient(90deg, 
                          transparent 0%, 
                          rgba(255,100,100,${fieldOpacity}) 20%, 
                          rgba(100,100,255,${fieldOpacity}) 80%, 
                          transparent 100%)`,
                        transform: `translate(-50%, -50%) rotate(${fieldAngle}rad)`,
                        transformOrigin: '50% 50%',
                        pointerEvents: 'none'
                      }}
                    />
                  );
                })}
              </>
            )}
            
            {/* 文字分裂碎片 */}
            {magneticPhase >= 0.8 && (
              <>
                {Array.from({ length: 16 }, (_, i) => {
                  const fragmentProgress = Math.max(0, (magneticPhase - 0.8) / 2.2);
                  const fragmentAngle = (i / 16) * Math.PI * 2 + (Math.random() - 0.5) * 0.5;
                  
                  // 磁力排斥加速度计算
                  const acceleration = repelForce * fragmentProgress * 15; // 15px/frame²
                  const fragmentDistance = acceleration * fragmentProgress;
                  const fragmentX = Math.cos(fragmentAngle) * fragmentDistance;
                  const fragmentY = Math.sin(fragmentAngle) * fragmentDistance;
                  
                  // 碎片旋转加速
                  const fragmentRotation = interpolate(fragmentProgress, [0, 1], [0, 360 + Math.random() * 720]);
                  const fragmentOpacity = interpolate(fragmentProgress, [0, 0.5, 1], [1, 0.8, 0]);
                  
                  const fragmentSize = 3 + Math.random() * 5;
                  const isPositive = i % 2 === 0;
                  
                  return (
                    <div
                      key={`fragment-${i}`}
                      style={{
                        position: 'absolute',
                        left: '50%',
                        top: '50%',
                        width: `${fragmentSize}px`,
                        height: `${fragmentSize}px`,
                        background: isPositive 
                          ? `radial-gradient(circle, 
                              rgba(255,100,100,${fragmentOpacity}) 0%, 
                              rgba(255,150,150,${fragmentOpacity * 0.8}) 50%, 
                              transparent 100%)`
                          : `radial-gradient(circle, 
                              rgba(100,100,255,${fragmentOpacity}) 0%, 
                              rgba(150,150,255,${fragmentOpacity * 0.8}) 50%, 
                              transparent 100%)`,
                        border: `1px solid ${isPositive ? `rgba(255,100,100,${fragmentOpacity * 0.5})` : `rgba(100,100,255,${fragmentOpacity * 0.5})`}`,
                        borderRadius: '2px',
                        transform: `translate(-50%, -50%) translate(${fragmentX}px, ${fragmentY}px) rotate(${fragmentRotation}deg)`,
                        pointerEvents: 'none'
                      }}
                    />
                  );
                })}
              </>
            )}
            
            {/* 撞击边缘闪光 */}
            {magneticPhase >= 2 && (
              <>
                {Array.from({ length: 6 }, (_, i) => {
                  const sparkProgress = Math.max(0, (magneticPhase - 2 - i * 0.05) / 0.3);
                  if (sparkProgress <= 0) return null;
                  
                  const sparkAngle = (i / 6) * Math.PI * 2;
                  const sparkDistance = 35 + Math.random() * 15;
                  const sparkX = Math.cos(sparkAngle) * sparkDistance;
                  const sparkY = Math.sin(sparkAngle) * sparkDistance;
                  const sparkOpacity = interpolate(sparkProgress, [0, 0.3, 1], [0, 1, 0]);
                  
                  return (
                    <div
                      key={`spark-${i}`}
                      style={{
                        position: 'absolute',
                        left: '50%',
                        top: '50%',
                        width: '8px',
                        height: '2px',
                        background: `linear-gradient(90deg, 
                          transparent 0%, 
                          rgba(255,255,255,${sparkOpacity}) 50%, 
                          transparent 100%)`,
                        transform: `translate(-50%, -50%) translate(${sparkX}px, ${sparkY}px) rotate(${sparkAngle}rad)`,
                        pointerEvents: 'none',
                        boxShadow: `0 0 4px rgba(255,255,255,${sparkOpacity * 0.8})`
                      }}
                    />
                  );
                })}
              </>
            )}
            
            {/* 磁极指示器 */}
            {magneticPhase >= 0.5 && magneticPhase < 2 && (
              <>
                {/* 正极 */}
                <div style={{
                  position: 'absolute',
                  left: '25%',
                  top: '25%',
                  width: '8px',
                  height: '8px',
                  background: `rgba(255,100,100,${interpolate(magneticPhase, [0.5, 1.5, 2], [0, 0.8, 0])})`,
                  border: `1px solid rgba(255,100,100,${interpolate(magneticPhase, [0.5, 1.5, 2], [0, 1, 0])})`,
                  borderRadius: '50%',
                  transform: 'translate(-50%, -50%)',
                  pointerEvents: 'none'
                }} />
                {/* 负极 */}
                <div style={{
                  position: 'absolute',
                  right: '25%',
                  bottom: '25%',
                  width: '8px',
                  height: '8px',
                  background: `rgba(100,100,255,${interpolate(magneticPhase, [0.5, 1.5, 2], [0, 0.8, 0])})`,
                  border: `1px solid rgba(100,100,255,${interpolate(magneticPhase, [0.5, 1.5, 2], [0, 1, 0])})`,
                  borderRadius: '50%',
                  transform: 'translate(50%, 50%)',
                  pointerEvents: 'none'
                }} />
              </>
            )}
            
            {/* 磁力波动 */}
            {magneticPhase >= 1 && magneticPhase < 2.5 && (
              <div style={{
                position: 'absolute',
                left: '50%',
                top: '50%',
                width: '50px',
                height: '50px',
                background: `radial-gradient(circle, 
                  transparent 30%, 
                  rgba(255,100,255,${interpolate(magneticPhase, [1, 2, 2.5], [0, 0.3, 0])}) 50%, 
                  transparent 70%)`,
                borderRadius: '50%',
                transform: `translate(-50%, -50%) scale(${1 + Math.sin(frame * 0.3) * 0.1})`,
                pointerEvents: 'none'
              }} />
            )}
            
            {/* 主文字 */}
            <span style={{
              fontSize: '60px',
              fontWeight: 'bold',
              color: 'white',
              display: 'inline-block',
              opacity: opacity,
              textShadow: magneticPhase >= 0.3 && magneticPhase < 2 
                ? `0 0 8px rgba(255,100,255,0.6)` 
                : 'none',
              filter: magneticPhase >= 1.5 
                ? `blur(${interpolate(magneticPhase, [1.5, 3], [0, 2])}px)` 
                : 'none',
              transform: magneticPhase >= 0.8 
                ? `scale(${interpolate(magneticPhase, [0.8, 2], [1, 0.2])})` 
                : 'scale(1)'
            }}>
              {char}
            </span>
          </div>
        );
      })}
    </div>
  );
};

MagneticRepelEffect.showName = "磁力排斥";

export default MagneticRepelEffect; 