import React from 'react';
import { interpolate, useCurrentFrame } from 'remotion';

interface DissolveOutEffectProps {
  text: string | React.ReactNode[];
  style: React.CSSProperties;
  durationFrames?: number;
}

export default function DissolveOutEffect({
  text = '展示文本',
  style = {},
  durationFrames = 80,
}: DissolveOutEffectProps) {
  const frame = useCurrentFrame();

  // 溶解效果
  const dissolveIntensity = interpolate(
    frame,
    [0, durationFrames],
    [0, 10],
    {
      extrapolateLeft: 'clamp',
      extrapolateRight: 'clamp',
    }
  );

  // 透明度
  const opacity = interpolate(
    frame,
    [durationFrames - 30, durationFrames],
    [1, 0],
    {
      extrapolateLeft: 'clamp',
      extrapolateRight: 'clamp',
    }
  );

  // 溶解粒子
  const particles = Array.from({ length: 15 }, (_, i) => {
    const particleFrame = frame + i * 3;
    if (particleFrame > durationFrames + 20) return null;
    
    const particleOpacity = interpolate(
      frame,
      [0, 40, durationFrames],
      [1, 0.5, 0],
      { extrapolateLeft: 'clamp', extrapolateRight: 'clamp' }
    );
    
    const x = 30 + (i % 5) * 15 + Math.random() * 10 + frame * 0.5;
    const y = 30 + Math.floor(i / 5) * 20 + Math.random() * 10 + frame * 0.3;
    
    return (
      <div
        key={i}
        style={{
          position: 'absolute',
          left: `${x}%`,
          top: `${y}%`,
          fontSize: '6px',
          opacity: particleOpacity,
          transform: 'translate(-50%, -50%)',
          color: 'rgba(255, 255, 255, 0.7)',
        }}
      >
        •
      </div>
    );
  }).filter(Boolean);

  return (
    <span
      style={{
        display: 'inline-flex',
        alignItems: 'center',
        justifyContent: 'center',
        height: '100%',
        position: 'relative',
      }}
    >
      <span
        style={{
          opacity: opacity,
          filter: `blur(${dissolveIntensity * 0.3}px) contrast(${1 - dissolveIntensity * 0.1})`,
          color: '#ffffff',
          textAlign: 'center',
          textShadow: '0 0 8px #ffffff',
          ...style,
        }}
      >
        {text}
      </span>
      {particles}
    </span>
  );
}

DissolveOutEffect.key = 'DissolveOutEffect';
DissolveOutEffect.description = 'dissolve out text effect';
DissolveOutEffect.showName = '溶解消失'; 