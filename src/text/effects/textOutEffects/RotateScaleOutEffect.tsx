import React, { useRef, useEffect, useState } from 'react';
import { interpolate, useCurrentFrame, useVideoConfig } from 'remotion';

interface RotateScaleOutEffectProps {
  text: string | React.ReactNode[];
  style: React.CSSProperties;
  durationFrames: number;
}

export default function RotateScaleOutEffect({
  text = '展示文本',
  style = {},
  durationFrames = 50, // 出场动画持续帧数
}: RotateScaleOutEffectProps) {
  const frame = useCurrentFrame();
  const { durationInFrames } = useVideoConfig();
  const textRef = useRef<HTMLDivElement>(null);
  const [elementSize, setElementSize] = useState({ width: 96, height: 48 }); // 默认值基于fontSize 48px

  useEffect(() => {
    if (textRef.current) {
      const rect = textRef.current.getBoundingClientRect();
      setElementSize({ width: rect.width, height: rect.height });
    }
  }, [text, style.fontSize]); // 只监听text和fontSize，避免无限循环

  // 出场动画进度
  const animateOutProgress = interpolate(
    frame,
    [durationInFrames - durationFrames, durationInFrames],
    [0, 1],
    {
      extrapolateLeft: 'clamp',
      extrapolateRight: 'clamp',
    }
  );

  // 旋转动画 - 出场时从正常到旋转
  const rotation = 360 * animateOutProgress;

  // 缩放动画 - 出场时从正常到最大缩放然后到最小缩放
  const getScale = () => {
    if (animateOutProgress < 0.5) {
      return interpolate(animateOutProgress, [0, 0.5], [1, 1.5]);
    } else {
      return interpolate(animateOutProgress, [0.5, 1], [1.5, 0]);
    }
  };

  const scale = getScale();

  // 透明度动画
  const opacity = interpolate(
    frame,
    [durationInFrames - durationFrames * 0.6, durationInFrames],
    [1, 0],
    {
      extrapolateLeft: 'clamp',
      extrapolateRight: 'clamp',
    }
  );

  // 添加发光效果，强度随动画进度变化
  const glowIntensity = interpolate(
    animateOutProgress,
    [0, 1],
    [5, 25]
  );

  // 添加背景光圈效果
  const ringScale = interpolate(
    animateOutProgress,
    [0, 1],
    [0.5, 3]
  );

  const ringOpacity = interpolate(
    animateOutProgress,
    [0, 0.5, 1],
    [0, 0.9, 0]
  );

  // 使用元素实际大小计算光圈尺寸
  const ringSize = Math.max(elementSize.width, elementSize.height) * 1.5;

  return (
    <span
      style={{
        display: 'inline-flex',
        alignItems: 'center',
        justifyContent: 'center',
        height: '100%',
        position: 'relative',
      }}
    >
      {/* 背景光圈效果 */}
      <div
        style={{
          position: 'absolute',
          width: `${ringSize}px`,
          height: `${ringSize}px`,
          borderRadius: '50%',
          border: `2px solid ${style.color}`,
          opacity: ringOpacity,
          transform: `scale(${ringScale})`,
          boxShadow: `0 0 30px ${style.color}`,
        }}
      />

      {/* 主文字 */}
      <span
        ref={textRef}
        style={{
          color: style.color,
          opacity: opacity,
          transform: `rotate(${rotation}deg) scale(${scale})`,
          textShadow: `0 0 ${glowIntensity}px ${style.color}`,
          fontWeight: 'bold',
          textAlign: 'center',
          transformOrigin: 'center',
          transition: 'all 0.1s ease',
          ...style,
        }}
      >
        {text}
      </span>

      {/* 添加粒子效果 */}
      {animateOutProgress > 0.3 && (
        <>
          {Array.from({ length: 12 }, (_, i) => {
            const angle = (i * 30) + rotation;
            const distance = Math.max(elementSize.width, elementSize.height) * scale * (0.8 + animateOutProgress * 0.5);
            const x = Math.cos((angle * Math.PI) / 180) * distance;
            const y = Math.sin((angle * Math.PI) / 180) * distance;
            
            return (
              <div
                key={i}
                style={{
                  position: 'absolute',
                  left: '50%',
                  top: '50%',
                  width: '3px',
                  height: '3px',
                  backgroundColor: style.color,
                  borderRadius: '50%',
                  transform: `translate(-50%, -50%) translate(${x}px, ${y}px)`,
                  opacity: opacity * 0.8,
                  boxShadow: `0 0 12px ${style.color}`,
                }}
              />
            );
          })}
        </>
      )}
    </span>
  );
}

RotateScaleOutEffect.key = 'RotateScaleOutEffect';
RotateScaleOutEffect.description = 'rotate and scale out text effect with particle animation';
RotateScaleOutEffect.showName = '旋转缩放出场'; 