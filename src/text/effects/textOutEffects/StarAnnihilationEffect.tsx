import React from 'react';
import { useCurrentFrame, interpolate, useVideoConfig } from 'remotion';

interface StarAnnihilationEffectProps {
  text: string;
  style?: React.CSSProperties;
}

const StarAnnihilationEffect: React.FC<StarAnnihilationEffectProps> & { showName: string } = ({
  text,
  style
}) => {
  const frame = useCurrentFrame();
  const { fps } = useVideoConfig();
  
  const chars = text.split('');
  const duration = 2 * fps; // 2秒
  const explosionInterval = 0.2 * fps; // 爆发间隔0.2s
  
  return (
    <div style={{
      display: 'inline-flex',
      alignItems: 'center',
      justifyContent: 'center',
      position: 'relative',
      backgroundColor: 'transparent',
      ...style
    }}>
      {chars.map((char, index) => {
        const charDelay = index * explosionInterval;
        const charProgress = Math.max(0, Math.min(1, (frame - charDelay) / (duration * 0.8)));
        
        // 星座阶段
        const constellationPhase = interpolate(charProgress, [0, 0.3, 0.6, 1], [0, 1, 2, 3]);
        
        // 文字透明度
        const opacity = interpolate(charProgress, [0, 0.8, 1], [1, 0.2, 0]);
        
        return (
          <div key={index} style={{
            position: 'relative',
            display: 'inline-block',
            margin: '0 2px'
          }}>
            {/* 星座连线 */}
            {constellationPhase >= 1 && constellationPhase < 2 && (
              <>
                {Array.from({ length: 5 }, (_, i) => {
                  const lineAngle = (i * 72) * Math.PI / 180;
                  const lineLength = 25 + i * 5;
                  const lineOpacity = interpolate(constellationPhase, [1, 1.5, 2], [0, 0.8, 0]);
                  
                  return (
                    <div
                      key={`line-${i}`}
                      style={{
                        position: 'absolute',
                        left: '50%',
                        top: '50%',
                        width: `${lineLength}px`,
                        height: '1px',
                        background: `linear-gradient(90deg, 
                          rgba(200,200,255,${lineOpacity}) 0%, 
                          transparent 100%)`,
                        transform: `translate(-50%, -50%) rotate(${lineAngle}rad)`,
                        transformOrigin: '0 50%',
                        pointerEvents: 'none'
                      }}
                    />
                  );
                })}
              </>
            )}
            
            {/* 恒星节点 */}
            {Array.from({ length: 8 }, (_, i) => {
              const starAngle = (i * 45) * Math.PI / 180;
              const starDistance = 20 + i * 8;
              const starX = Math.cos(starAngle) * starDistance;
              const starY = Math.sin(starAngle) * starDistance;
              
              const starDelay = i * 3;
              const starProgress = Math.max(0, Math.min(1, (charProgress - starDelay * 0.01) / 0.8));
              
              // 超新星爆发
              const isExploding = constellationPhase >= 2 && starProgress > 0.5;
              const explosionScale = isExploding 
                ? interpolate(starProgress, [0.5, 1], [1, 15])
                : 1;
              const explosionOpacity = isExploding 
                ? interpolate(starProgress, [0.5, 0.8, 1], [1, 0.5, 0])
                : interpolate(constellationPhase, [0, 1, 2], [0, 1, 1]);
              
              return (
                <div key={`star-${i}`}>
                  {/* 恒星核心 */}
                  <div style={{
                    position: 'absolute',
                    left: '50%',
                    top: '50%',
                    width: isExploding ? '20px' : '4px',
                    height: isExploding ? '20px' : '4px',
                    background: isExploding 
                      ? `radial-gradient(circle, 
                          rgba(255,255,255,${explosionOpacity}) 0%, 
                          rgba(255,200,100,${explosionOpacity * 0.8}) 30%, 
                          rgba(255,100,0,${explosionOpacity * 0.4}) 70%, 
                          transparent 100%)`
                      : `rgba(255,255,255,${explosionOpacity})`,
                    borderRadius: '50%',
                    transform: `translate(-50%, -50%) translate(${starX}px, ${starY}px) scale(${explosionScale})`,
                    boxShadow: isExploding 
                      ? `0 0 ${explosionScale * 2}px rgba(255,255,255,${explosionOpacity})`
                      : `0 0 4px rgba(255,255,255,${explosionOpacity})`,
                    pointerEvents: 'none'
                  }} />
                  
                  {/* 强光脉冲 */}
                  {isExploding && (
                    <div style={{
                      position: 'absolute',
                      left: '50%',
                      top: '50%',
                      width: `${explosionScale * 3}px`,
                      height: `${explosionScale * 3}px`,
                      background: `radial-gradient(circle, 
                        rgba(255,255,255,${explosionOpacity * 0.3}) 0%, 
                        transparent 100%)`,
                      borderRadius: '50%',
                      transform: `translate(-50%, -50%) translate(${starX}px, ${starY}px)`,
                      pointerEvents: 'none'
                    }} />
                  )}
                </div>
              );
            })}
            
            {/* 黑洞形成 - 文字消失后立即结束 */}
            {constellationPhase >= 2.5 && opacity > 0.1 && (
              <div style={{
                position: 'absolute',
                left: '50%',
                top: '50%',
                width: `${interpolate(constellationPhase, [2.5, 3], [10, 30])}px`,
                height: `${interpolate(constellationPhase, [2.5, 3], [10, 30])}px`,
                background: `radial-gradient(circle, 
                  rgba(0,0,0,1) 0%, 
                  rgba(100,0,200,${interpolate(constellationPhase, [2.5, 3], [0.8, 0]) * opacity}) 70%, 
                  transparent 100%)`,
                borderRadius: '50%',
                transform: 'translate(-50%, -50%)',
                border: `2px solid rgba(200,0,255,${interpolate(constellationPhase, [2.5, 3], [0.8, 0]) * opacity})`,
                boxShadow: `0 0 20px rgba(200,0,255,${interpolate(constellationPhase, [2.5, 3], [0.5, 0]) * opacity})`,
                pointerEvents: 'none'
              }} />
            )}
            
            {/* 空间扭曲效果 - 文字消失后立即结束 */}
            {constellationPhase >= 2.8 && opacity > 0.1 && (
              <>
                {Array.from({ length: 4 }, (_, i) => {
                  const warpRadius = 40 + i * 15;
                  const warpOpacity = interpolate(constellationPhase, [2.8, 3], [0.6 - i * 0.1, 0]) * opacity;
                  
                  return (
                    <div
                      key={`warp-${i}`}
                      style={{
                        position: 'absolute',
                        left: '50%',
                        top: '50%',
                        width: `${warpRadius}px`,
                        height: `${warpRadius}px`,
                        border: `1px solid rgba(100,0,200,${warpOpacity})`,
                        borderRadius: '50%',
                        transform: `translate(-50%, -50%) scale(${interpolate(frame % 30, [0, 15, 30], [1, 1.1, 1])})`,
                        pointerEvents: 'none'
                      }}
                    />
                  );
                })}
              </>
            )}
            
            {/* 主文字 */}
            <span style={{
              fontSize: '60px',
              fontWeight: 'bold',
              color: constellationPhase < 2 ? 'white' : 'transparent',
              display: 'inline-block',
              opacity: opacity,
              textShadow: constellationPhase < 2 
                ? `0 0 10px rgba(255,255,255,0.8)` 
                : 'none',
              filter: constellationPhase >= 2.5 
                ? `blur(${interpolate(constellationPhase, [2.5, 3], [0, 10])}px)` 
                : 'none'
            }}>
              {char}
            </span>
          </div>
        );
      })}
    </div>
  );
};

StarAnnihilationEffect.showName = "星空湮灭";

export default StarAnnihilationEffect; 