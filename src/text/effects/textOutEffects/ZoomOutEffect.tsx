import React from 'react';
import { interpolate, useCurrentFrame } from 'remotion';

interface ZoomOutEffectProps {
  text: string | React.ReactNode[];
  style: React.CSSProperties;
  durationFrames?: number;
}

export default function ZoomOutEffect({
  text = '展示文本',
  style = {},
  durationFrames = 35,
}: ZoomOutEffectProps) {
  const frame = useCurrentFrame();

  // 缩小动画
  const scale = interpolate(
    frame,
    [0, durationFrames],
    [1, 0],
    {
      extrapolateLeft: 'clamp',
      extrapolateRight: 'clamp',
    }
  );

  // 透明度
  const opacity = interpolate(
    frame,
    [durationFrames - 10, durationFrames],
    [1, 0],
    {
      extrapolateLeft: 'clamp',
      extrapolateRight: 'clamp',
    }
  );

  return (
    <span
      style={{
        display: 'inline-flex',
        alignItems: 'center',
        justifyContent: 'center',
        height: '100%',
      }}
    >
      <span
        style={{
          opacity: opacity,
          transform: `scale(${scale})`,
          color: '#ffffff',
          textAlign: 'center',
          textShadow: '0 0 8px #ffffff',
          ...style,
        }}
      >
        {text}
      </span>
    </span>
  );
}

ZoomOutEffect.key = 'ZoomOutEffect';
ZoomOutEffect.description = 'zoom out text effect';
ZoomOutEffect.showName = '缩小出场'; 