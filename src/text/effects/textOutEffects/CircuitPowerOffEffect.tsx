import React from 'react';
import { useCurrentFrame, interpolate, useVideoConfig } from 'remotion';

interface CircuitPowerOffEffectProps {
  text: string;
  style?: React.CSSProperties;
}

const CircuitPowerOffEffect: React.FC<CircuitPowerOffEffectProps> & { showName: string } = ({
  text,
  style
}) => {
  const frame = useCurrentFrame();
  const { fps } = useVideoConfig();
  
  const chars = text.split('');
  const duration = 1.5 * fps; // 1.5秒
  const powerOffDelay = 0.1 * fps; // 断电延迟0.1s
  
  return (
    <div style={{
      display: 'inline-flex',
      alignItems: 'center',
      justifyContent: 'center',
      position: 'relative',
      backgroundColor: 'transparent',
      ...style
    }}>
      {chars.map((char, index) => {
        const charDelay = index * powerOffDelay;
        const charProgress = Math.max(0, Math.min(1, (frame - charDelay) / (duration * 0.8)));
        
        // 电路状态
        const circuitActive = charProgress < 0.6;
        const powerLevel = interpolate(charProgress, [0, 0.6, 1], [1, 0.3, 0]);
        
        // 文字透明度
        const opacity = interpolate(charProgress, [0, 0.8, 1], [1, 0.2, 0]);
        
        return (
          <div key={index} style={{
            position: 'relative',
            display: 'inline-block',
            margin: '0 2px'
          }}>
            {/* 电路板背景 */}
            <div style={{
              position: 'absolute',
              left: -5,
              top: -5,
              right: -5,
              bottom: -5,
              background: `linear-gradient(45deg, 
                rgba(0,100,0,${powerLevel * 0.1}) 0%, 
                rgba(0,50,0,${powerLevel * 0.05}) 100%)`,
              border: `1px solid rgba(0,255,0,${powerLevel * 0.3})`,
              pointerEvents: 'none'
            }} />
            
            {/* 电流纹路 */}
            {circuitActive && Array.from({ length: 6 }, (_, i) => {
              const circuitDelay = i * 5;
              const circuitProgress = Math.max(0, Math.min(1, (frame - charDelay - circuitDelay) / 20));
              const isActive = circuitProgress > 0;
              
              const pathLength = 30 + i * 8;
              const angle = (i * 60) * Math.PI / 180;
              const endX = Math.cos(angle) * pathLength;
              const endY = Math.sin(angle) * pathLength;
              
              return (
                <div key={`circuit-${i}`}>
                  {/* 电路线 */}
                  <div style={{
                    position: 'absolute',
                    left: '50%',
                    top: '50%',
                    width: `${pathLength}px`,
                    height: '2px',
                    background: `linear-gradient(90deg, 
                        rgba(0,255,0,${powerLevel}) 0%, 
                        rgba(0,150,0,${powerLevel * 0.5}) 100%)`,
                    transform: `translate(-50%, -50%) rotate(${angle}rad)`,
                    transformOrigin: '0 50%',
                    pointerEvents: 'none'
                  }} />
                  
                  {/* 电路节点 */}
                  <div style={{
                    position: 'absolute',
                    left: '50%',
                    top: '50%',
                    width: '4px',
                    height: '4px',
                    backgroundColor: `rgba(0,255,0,${powerLevel})`,
                    borderRadius: '50%',
                    transform: `translate(-50%, -50%) translate(${endX}px, ${endY}px)`,
                    boxShadow: `0 0 8px rgba(0,255,0,${powerLevel})`,
                    pointerEvents: 'none'
                  }} />
                </div>
              );
            })}
            
            {/* 熄灭火花 */}
            {charProgress > 0.5 && charProgress < 0.8 && (
              <>
                {Array.from({ length: 4 }, (_, i) => {
                  const sparkProgress = (charProgress - 0.5) / 0.3;
                  const sparkAngle = (i / 4) * Math.PI * 2;
                  const sparkDistance = interpolate(sparkProgress, [0, 1], [5, 15]);
                  const sparkX = Math.cos(sparkAngle) * sparkDistance;
                  const sparkY = Math.sin(sparkAngle) * sparkDistance;
                  const sparkOpacity = interpolate(sparkProgress, [0, 0.5, 1], [0, 1, 0]);
                  
                  return (
                    <div
                      key={`spark-${i}`}
                      style={{
                        position: 'absolute',
                        left: '50%',
                        top: '50%',
                        width: '3px',
                        height: '3px',
                        background: `radial-gradient(circle, 
                          rgba(255,100,0,${sparkOpacity}) 0%, 
                          transparent 100%)`,
                        borderRadius: '50%',
                        transform: `translate(-50%, -50%) translate(${sparkX}px, ${sparkY}px)`,
                        pointerEvents: 'none'
                      }}
                    />
                  );
                })}
              </>
            )}
            
            {/* 主芯片爆裂效果 */}
            {charProgress > 0.8 && (
              <div style={{
                position: 'absolute',
                left: '50%',
                top: '50%',
                width: '20px',
                height: '20px',
                background: `radial-gradient(circle, 
                  rgba(255,0,0,${interpolate(charProgress, [0.8, 1], [0.8, 0])}) 0%, 
                  rgba(255,100,0,${interpolate(charProgress, [0.8, 1], [0.4, 0])}) 50%, 
                  transparent 100%)`,
                borderRadius: '50%',
                transform: `translate(-50%, -50%) scale(${interpolate(charProgress, [0.8, 1], [1, 3])})`,
                pointerEvents: 'none'
              }} />
            )}
            
            {/* 主文字 */}
            <span style={{
              fontSize: '60px',
              fontWeight: 'bold',
              color: circuitActive ? '#00FF00' : '#666666',
              display: 'inline-block',
              opacity: opacity,
              textShadow: circuitActive 
                ? `0 0 10px rgba(0,255,0,${powerLevel * 0.8})` 
                : 'none',
              filter: circuitActive ? 'none' : 'grayscale(1)',
              transition: 'color 0.2s ease-out'
            }}>
              {char}
            </span>
          </div>
        );
      })}
    </div>
  );
};

CircuitPowerOffEffect.showName = "电路断电";

export default CircuitPowerOffEffect; 