import React from 'react';
import { interpolate, useCurrentFrame } from 'remotion';

interface FloatDriftOutEffectProps {
  text: string | React.ReactNode[];
  style: React.CSSProperties;
  durationFrames?: number;
}

export default function FloatDriftOutEffect({
  text = '展示文本',
  style = {},
  durationFrames = 80,
}: FloatDriftOutEffectProps) {
  const frame = useCurrentFrame();

  // 文字淡出
  const opacity = interpolate(
    frame,
    [durationFrames - 30, durationFrames],
    [1, 0],
    {
      extrapolateLeft: 'clamp',
      extrapolateRight: 'clamp',
    }
  );

  // 漂浮动画（逐渐飘远）
  const floatY = Math.sin(frame * 0.08) * 3 - frame * 0.3;
  const floatX = Math.sin(frame * 0.06) * 2 + frame * 0.2;

  return (
    <span
      style={{
        display: 'inline-flex',
        alignItems: 'center',
        justifyContent: 'center',
        height: '100%',
        position: 'relative',
      }}
    >
      <span
        style={{
          opacity: opacity,
          transform: `translate(${floatX}px, ${floatY}px)`,
          color: '#ffffff',
          textAlign: 'center',
          textShadow: '0 0 6px #ffffff',
          ...style,
        }}
      >
        {text}
      </span>
    </span>
  );
}

FloatDriftOutEffect.key = 'FloatDriftOutEffect';
FloatDriftOutEffect.description = 'floating drift out text effect';
FloatDriftOutEffect.showName = '漂浮飘散'; 