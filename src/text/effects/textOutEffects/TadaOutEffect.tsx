import React from 'react';
import { interpolate, useCurrentFrame } from 'remotion';

interface TadaOutEffectProps {
  text: string | React.ReactNode[];
  style: React.CSSProperties;
  durationFrames?: number;
}

export default function TadaOutEffect({
  text = '展示文本',
  style = {},
  durationFrames = 60,
}: TadaOutEffectProps) {
  const frame = useCurrentFrame();

  // Ta-da效果（戏剧性的缩放和旋转）
  const scale = interpolate(
    frame,
    [0, 10, 20, 30, 40, 50, 60],
    [1, 0.9, 1.1, 0.95, 1.05, 0.8, 0],
    {
      extrapolateLeft: 'clamp',
      extrapolateRight: 'clamp',
    }
  );

  const rotation = interpolate(
    frame,
    [0, 10, 20, 30, 40, 50, 60],
    [0, -3, 3, -3, 3, -10, 180],
    {
      extrapolateLeft: 'clamp',
      extrapolateRight: 'clamp',
    }
  );

  // 透明度
  const opacity = interpolate(
    frame,
    [durationFrames - 15, durationFrames],
    [1, 0],
    {
      extrapolateLeft: 'clamp',
      extrapolateRight: 'clamp',
    }
  );

  return (
    <span
      style={{
        display: 'inline-flex',
        alignItems: 'center',
        justifyContent: 'center',
        height: '100%',
      }}
    >
      <span
        style={{
          opacity: opacity,
          transform: `scale(${scale}) rotate(${rotation}deg)`,
          color: '#ffffff',
          textAlign: 'center',
          textShadow: '0 0 12px #ffffff',
          ...style,
        }}
      >
        {text}
      </span>
    </span>
  );
}

TadaOutEffect.key = 'TadaOutEffect';
TadaOutEffect.description = 'ta-da out text effect';
TadaOutEffect.showName = 'Ta-da出场'; 