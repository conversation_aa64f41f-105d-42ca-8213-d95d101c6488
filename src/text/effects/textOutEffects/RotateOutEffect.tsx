import React from 'react';
import { interpolate, useCurrentFrame } from 'remotion';

interface RotateOutEffectProps {
  text: string | React.ReactNode[];
  style: React.CSSProperties;
  durationFrames?: number;
}

export default function RotateOutEffect({
  text = '展示文本',
  style = {},
  durationFrames = 60,
}: RotateOutEffectProps) {
  const frame = useCurrentFrame();

  // 旋转动画
  const rotation = interpolate(
    frame,
    [0, durationFrames],
    [0, 360],
    {
      extrapolateLeft: 'clamp',
      extrapolateRight: 'clamp',
    }
  );

  // 透明度
  const opacity = interpolate(
    frame,
    [durationFrames - 20, durationFrames],
    [1, 0],
    {
      extrapolateLeft: 'clamp',
      extrapolateRight: 'clamp',
    }
  );

  return (
    <span
      style={{
        display: 'inline-flex',
        alignItems: 'center',
        justifyContent: 'center',
        height: '100%',
      }}
    >
      <span
        style={{
          opacity: opacity,
          transform: `rotate(${rotation}deg)`,
          color: '#ffffff',
          textAlign: 'center',
          textShadow: '0 0 8px #ffffff',
          ...style,
        }}
      >
        {text}
      </span>
    </span>
  );
}

RotateOutEffect.key = 'RotateOutEffect';
RotateOutEffect.description = 'rotate out text effect';
RotateOutEffect.showName = '旋转出场'; 