import React from 'react';
import { interpolate, useCurrentFrame } from 'remotion';

interface SpiralOutEffectProps {
  text: string | React.ReactNode[];
  style: React.CSSProperties;
  durationFrames?: number;
}

export default function SpiralOutEffect({
  text = '展示文本',
  style = {},
  durationFrames = 80,
}: SpiralOutEffectProps) {
  const frame = useCurrentFrame();

  // 螺旋移动
  const angle = frame * 0.3;
  const radius = frame * 2;
  const spiralX = Math.cos(angle) * radius;
  const spiralY = Math.sin(angle) * radius;

  // 螺旋缩放
  const scale = interpolate(
    frame,
    [0, durationFrames],
    [1, 0.1],
    {
      extrapolateLeft: 'clamp',
      extrapolateRight: 'clamp',
    }
  );

  // 透明度
  const opacity = interpolate(
    frame,
    [durationFrames - 20, durationFrames],
    [1, 0],
    {
      extrapolateLeft: 'clamp',
      extrapolateRight: 'clamp',
    }
  );

  return (
    <span
      style={{
        display: 'inline-flex',
        alignItems: 'center',
        justifyContent: 'center',
        height: '100%',
      }}
    >
      <span
        style={{
          opacity: opacity,
          transform: `translate(${spiralX}px, ${spiralY}px) scale(${scale}) rotate(${angle * 2}deg)`,
          color: '#ffffff',
          textAlign: 'center',
          textShadow: '0 0 8px #ffffff',
          ...style,
        }}
      >
        {text}
      </span>
    </span>
  );
}

SpiralOutEffect.key = 'SpiralOutEffect';
SpiralOutEffect.description = 'spiral out text effect';
SpiralOutEffect.showName = '螺旋消失'; 