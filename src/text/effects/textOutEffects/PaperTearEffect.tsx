import React from 'react';
import { useCurrentFrame, interpolate, useVideoConfig } from 'remotion';

interface PaperTearEffectProps {
  text: string;
  style?: React.CSSProperties;
}

const PaperTearEffect: React.FC<PaperTearEffectProps> & { showName: string } = ({
  text,
  style
}) => {
  const frame = useCurrentFrame();
  const { fps } = useVideoConfig();
  
  const chars = text.split('');
  const duration = 1 * fps; // 1秒
  const fragmentCount = 12; // 碎片数量
  
  return (
    <div style={{
      display: 'inline-flex',
      alignItems: 'center',
      justifyContent: 'center',
      position: 'relative',
      backgroundColor: 'transparent',
      ...style
    }}>
      {chars.map((char, index) => {
        const charDelay = index * 3;
        const charProgress = Math.max(0, Math.min(1, (frame - charDelay) / (duration * 0.8)));
        
        // 撕裂阶段
        const tearPhase = interpolate(charProgress, [0, 0.3, 0.7, 1], [0, 1, 2, 3]);
        
        // 文字透明度
        const opacity = interpolate(charProgress, [0, 0.6, 1], [1, 0.3, 0]);
        
        return (
          <div key={index} style={{
            position: 'relative',
            display: 'inline-block',
            margin: '0 2px'
          }}>

            
            {/* 撕裂线 */}
            {tearPhase >= 1 && tearPhase < 2 && (
              <>
                {Array.from({ length: 6 }, (_, i) => {
                  const tearAngle = (Math.random() - 0.5) * Math.PI;
                  const tearLength = 20 + Math.random() * 30;
                  const tearProgress = interpolate(tearPhase, [1, 1.5], [0, 1]);
                  const tearOpacity = interpolate(tearPhase, [1, 1.8, 2], [0, 1, 0]);
                  
                  return (
                    <div
                      key={`tear-${i}`}
                      style={{
                        position: 'absolute',
                        left: '50%',
                        top: '50%',
                        width: `${tearLength * tearProgress}px`,
                        height: '2px',
                        background: `linear-gradient(90deg, 
                          rgba(139,69,19,${tearOpacity}) 0%, 
                          rgba(160,82,45,${tearOpacity * 0.5}) 100%)`,
                        transform: `translate(-50%, -50%) rotate(${tearAngle}rad)`,
                        transformOrigin: '0 50%',
                        pointerEvents: 'none',
                        filter: 'blur(0.5px)'
                      }}
                    />
                  );
                })}
              </>
            )}
            
            {/* 纸片碎片 */}
            {tearPhase >= 2 && (
              <>
                {Array.from({ length: fragmentCount }, (_, i) => {
                  const fragmentAngle = (Math.random() - 0.5) * Math.PI * 2;
                  const fragmentDistance = interpolate(tearPhase, [2, 3], [0, 60 + Math.random() * 40]);
                  const fragmentX = Math.cos(fragmentAngle) * fragmentDistance;
                  const fragmentY = Math.sin(fragmentAngle) * fragmentDistance + (tearPhase - 2) * 80; // 重力下落
                  
                  const fragmentRotation = interpolate(tearPhase, [2, 3], [0, (Math.random() - 0.5) * 720]); // 旋转
                  const fragmentOpacity = interpolate(tearPhase, [2, 2.5, 3], [1, 0.6, 0]);
                  
                  const fragmentWidth = 8 + Math.random() * 12;
                  const fragmentHeight = 6 + Math.random() * 10;
                  
                  return (
                    <div
                      key={`fragment-${i}`}
                      style={{
                        position: 'absolute',
                        left: '50%',
                        top: '50%',
                        width: `${fragmentWidth}px`,
                        height: `${fragmentHeight}px`,
                        background: `linear-gradient(45deg, 
                          rgba(255,248,220,${fragmentOpacity}) 0%, 
                          rgba(245,245,220,${fragmentOpacity * 0.8}) 100%)`,
                        border: `1px solid rgba(139,69,19,${fragmentOpacity * 0.3})`,
                        transform: `translate(-50%, -50%) translate(${fragmentX}px, ${fragmentY}px) rotate(${fragmentRotation}deg)`,
                        pointerEvents: 'none',
                        boxShadow: `2px 2px 4px rgba(0,0,0,${fragmentOpacity * 0.2})`
                      }}
                    />
                  );
                })}
              </>
            )}
            
            {/* 纤维细节 */}
            {tearPhase >= 1.5 && tearPhase < 2.5 && (
              <>
                {Array.from({ length: 10 }, (_, i) => {
                  const fiberAngle = (Math.random() - 0.5) * Math.PI;
                  const fiberLength = 3 + Math.random() * 8;
                  const fiberX = (Math.random() - 0.5) * 40;
                  const fiberY = (Math.random() - 0.5) * 40;
                  const fiberOpacity = interpolate(tearPhase, [1.5, 2, 2.5], [0, 0.8, 0]);
                  
                  return (
                    <div
                      key={`fiber-${i}`}
                      style={{
                        position: 'absolute',
                        left: '50%',
                        top: '50%',
                        width: `${fiberLength}px`,
                        height: '1px',
                        backgroundColor: `rgba(139,69,19,${fiberOpacity})`,
                        transform: `translate(-50%, -50%) translate(${fiberX}px, ${fiberY}px) rotate(${fiberAngle}rad)`,
                        pointerEvents: 'none'
                      }}
                    />
                  );
                })}
              </>
            )}
            

            
            {/* 主文字 */}
            <span style={{
              fontSize: '60px',
              fontWeight: 'bold',
              color: 'white',
              display: 'inline-block',
              opacity: opacity,
              textShadow: `0 0 1px rgba(139,69,19,0.3)`,
              filter: tearPhase >= 1.5 
                ? `blur(${interpolate(tearPhase, [1.5, 2.5], [0, 2])}px)` 
                : 'none'
            }}>
              {char}
            </span>
          </div>
        );
      })}
    </div>
  );
};

PaperTearEffect.showName = "纸片撕碎";

export default PaperTearEffect; 