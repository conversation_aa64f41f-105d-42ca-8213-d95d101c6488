import React from 'react';
import { interpolate, useCurrentFrame } from 'remotion';

interface ImplodeOutEffectProps {
  text: string | React.ReactNode[];
  style: React.CSSProperties;
  durationFrames?: number;
}

export default function ImplodeOutEffect({
  text = '展示文本',
  style = {},
  durationFrames = 60,
}: ImplodeOutEffectProps) {
  const frame = useCurrentFrame();

  // 内爆缩放
  const scale = interpolate(
    frame,
    [0, durationFrames * 0.8, durationFrames],
    [1, 0.5, 0],
    {
      extrapolateLeft: 'clamp',
      extrapolateRight: 'clamp',
    }
  );

  // 内爆收缩效果
  const letterSpacing = interpolate(
    frame,
    [0, durationFrames],
    [0, -10],
    {
      extrapolateLeft: 'clamp',
      extrapolateRight: 'clamp',
    }
  );

  // 透明度
  const opacity = interpolate(
    frame,
    [durationFrames - 15, durationFrames],
    [1, 0],
    {
      extrapolateLeft: 'clamp',
      extrapolateRight: 'clamp',
    }
  );

  return (
    <span
      style={{
        display: 'inline-flex',
        alignItems: 'center',
        justifyContent: 'center',
        height: '100%',
      }}
    >
      <span
        style={{
          opacity: opacity,
          transform: `scale(${scale})`,
          letterSpacing: `${letterSpacing}px`,
          color: '#ffffff',
          textAlign: 'center',
          textShadow: '0 0 8px #ffffff',
          ...style,
        }}
      >
        {text}
      </span>
    </span>
  );
}

ImplodeOutEffect.key = 'ImplodeOutEffect';
ImplodeOutEffect.description = 'implode out text effect';
ImplodeOutEffect.showName = '内爆消失'; 