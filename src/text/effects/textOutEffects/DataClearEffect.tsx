import React from 'react';
import { useCurrentFrame, interpolate, useVideoConfig } from 'remotion';

interface DataClearEffectProps {
  text: string;
  style?: React.CSSProperties;
}

const DataClearEffect: React.FC<DataClearEffectProps> & { showName: string } = ({
  text,
  style
}) => {
  const frame = useCurrentFrame();
  const { fps } = useVideoConfig();
  
  const chars = text.split('');
  const duration = 1.8 * fps; // 1.8秒
  const clearSpeed = 60; // 清空速度60字/秒
  
  return (
    <div style={{
      display: 'inline-flex',
      alignItems: 'center',
      justifyContent: 'center',
      position: 'relative',
      backgroundColor: 'transparent',
      ...style
    }}>
      {chars.map((char, index) => {
        const charDelay = index * 2;
        const charProgress = Math.max(0, Math.min(1, (frame - charDelay) / (duration * 0.8)));
        
        // 数据清空阶段
        const clearPhase = interpolate(charProgress, [0, 0.3, 0.6, 1], [0, 1, 2, 3]);
        
        // 文字透明度
        const opacity = interpolate(charProgress, [0, 0.7, 1], [1, 0.3, 0]);
        
        return (
          <div key={index} style={{
            position: 'relative',
            display: 'inline-block',
            margin: '0 2px'
          }}>
            {/* 二进制代码瀑布流 */}
            {clearPhase >= 1 && (
              <>
                {Array.from({ length: 8 }, (_, i) => {
                  const streamDelay = i * 3;
                  const streamProgress = Math.max(0, Math.min(1, (charProgress - streamDelay * 0.01) / 0.8));
                  
                  // 反向消除效果
                  const reverseProgress = clearPhase >= 2 ? (clearPhase - 2) : 0;
                  const streamOpacity = interpolate(reverseProgress, [0, 1], [1, 0]);
                  
                  const binaryChar = Math.random() > 0.5 ? '1' : '0';
                  const streamY = -streamProgress * 100 + reverseProgress * 50;
                  
                  return (
                    <div
                      key={`stream-${i}`}
                      style={{
                        position: 'absolute',
                        left: '50%',
                        top: '50%',
                        fontSize: '12px',
                        color: `rgba(0,255,0,${streamOpacity})`,
                        transform: `translate(-50%, -50%) translateY(${streamY}px)`,
                        pointerEvents: 'none',
                        fontFamily: 'monospace'
                      }}
                    >
                      {binaryChar}
                    </div>
                  );
                })}
              </>
            )}
            
            {/* 删除线横扫 */}
            {clearPhase >= 1.5 && clearPhase < 2.5 && (
              <div style={{
                position: 'absolute',
                left: 0,
                top: '50%',
                width: `${interpolate(clearPhase, [1.5, 2], [0, 100])}%`,
                height: '2px',
                backgroundColor: `rgba(255,0,0,${interpolate(clearPhase, [1.5, 2, 2.5], [0, 1, 0])})`,
                transform: 'translateY(-50%)',
                pointerEvents: 'none'
              }} />
            )}
            

            
            {/* 数据流消除粒子 */}
            {clearPhase >= 2.2 && (
              <>
                {Array.from({ length: 12 }, (_, i) => {
                  const particleAngle = (i / 12) * Math.PI * 2;
                  const particleDistance = interpolate(clearPhase, [2.2, 3], [0, 40]);
                  const particleX = Math.cos(particleAngle) * particleDistance;
                  const particleY = Math.sin(particleAngle) * particleDistance;
                  const particleOpacity = interpolate(clearPhase, [2.2, 2.8, 3], [0, 1, 0]);
                  
                  return (
                    <div
                      key={`particle-${i}`}
                      style={{
                        position: 'absolute',
                        left: '50%',
                        top: '50%',
                        width: '2px',
                        height: '2px',
                        backgroundColor: `rgba(255,0,0,${particleOpacity})`,
                        borderRadius: '50%',
                        transform: `translate(-50%, -50%) translate(${particleX}px, ${particleY}px)`,
                        pointerEvents: 'none'
                      }}
                    />
                  );
                })}
              </>
            )}
            
            {/* 空白光标 */}
            {clearPhase >= 2.8 && (
              <div style={{
                position: 'absolute',
                left: '50%',
                top: '50%',
                width: '2px',
                height: '40px',
                backgroundColor: `rgba(255,255,255,${interpolate(clearPhase, [2.8, 3], [1, 0])})`,
                transform: 'translate(-50%, -50%)',
                opacity: frame % 30 < 15 ? 1 : 0.3, // 闪烁光标
                pointerEvents: 'none'
              }} />
            )}
            
            {/* 主文字 */}
            <span style={{
              fontSize: '60px',
              fontWeight: 'bold',
              color: clearPhase < 1 ? 'white' : '#00FF00',
              display: 'inline-block',
              opacity: opacity,
              fontFamily: clearPhase >= 1 ? 'monospace' : 'inherit',
              textShadow: clearPhase >= 1 
                ? `0 0 5px rgba(0,255,0,0.5)` 
                : 'none',
              textDecoration: clearPhase >= 1.5 && clearPhase < 2.5 ? 'line-through' : 'none',
              textDecorationColor: 'rgba(255,0,0,1)'
            }}>
              {clearPhase >= 1 && clearPhase < 2 
                ? (Math.random() > 0.7 ? (Math.random() > 0.5 ? '1' : '0') : char)
                : char}
            </span>
          </div>
        );
      })}
    </div>
  );
};

DataClearEffect.showName = "数据清空";

export default DataClearEffect; 