import React from 'react';
import { interpolate, useCurrentFrame, useVideoConfig } from 'remotion';

interface SlideOutEffectProps {
  text: string | React.ReactNode[];
  style: React.CSSProperties;
  durationFrames: number;
  slideDirection: 'left' | 'right' | 'top' | 'bottom';
}

export default function SlideOutEffect({
  text = '展示文本',
  style = {},
  durationFrames = 40,
  slideDirection = 'right', // 滑动方向
}: SlideOutEffectProps) {
  const frame = useCurrentFrame();
  const { durationInFrames } = useVideoConfig();

  // 滑出动画进度
  const slideOutProgress = interpolate(
    frame,
    [durationInFrames - durationFrames, durationInFrames],
    [0, 1],
    {
      extrapolateLeft: 'clamp',
      extrapolateRight: 'clamp',
    }
  );

  // 根据方向计算位移
  const getTransform = () => {
    let x = 0;
    let y = 0;

    // 出场位移
    const outDistance = 200 * slideOutProgress;
    switch (slideDirection) {
      case 'left':
        x = -outDistance;
        break;
      case 'right':
        x = outDistance;
        break;
      case 'top':
        y = -outDistance;
        break;
      case 'bottom':
        y = outDistance;
        break;
    }

    return `translate(${x}px, ${y}px)`;
  };

  // 透明度动画
  const opacity = interpolate(
    frame,
    [durationInFrames - durationFrames * 0.7, durationInFrames],
    [1, 0],
    {
      extrapolateLeft: 'clamp',
      extrapolateRight: 'clamp',
    }
  );

  // 添加运动模糊效果的模拟
  const blurAmount = Math.abs(slideOutProgress - 0.5) * 6;

  return (
    <span
      style={{
        display: 'inline-flex',
        alignItems: 'center',
        justifyContent: 'center',
        height: '100%',
        position: 'relative',
        overflow: 'hidden', // 防止文字滑出可视区域时显示
      }}
    >
      <span
        style={{
          opacity: opacity,
          transform: getTransform(),
          filter: `blur(${blurAmount}px)`,
          fontWeight: 'bold',
          textAlign: 'center',
          textShadow: `0 0 15px ${style.color}`,
          transition: 'filter 0.1s ease',
          ...style,
        }}
      >
        {text}
      </span>

      {/* 添加运动轨迹效果 */}
      <span
        style={{
          position: 'absolute',
          opacity: opacity * 0.4,
          transform: getTransform(),
          fontWeight: 'bold',
          textAlign: 'center',
          filter: 'blur(3px)',
          zIndex: -1,
          ...style,
        }}
      >
        {text}
      </span>
    </span>
  );
}

SlideOutEffect.key = 'SlideOutEffect';
SlideOutEffect.description = 'slide out text effect with directional motion';
SlideOutEffect.showName = '滑出'; 