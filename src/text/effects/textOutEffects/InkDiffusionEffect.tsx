import React from 'react';
import { useCurrentFrame, interpolate, useVideoConfig } from 'remotion';

interface InkDiffusionEffectProps {
  text: string;
  style?: React.CSSProperties;
}

const InkDiffusionEffect: React.FC<InkDiffusionEffectProps> & { showName: string } = ({
  text,
  style
}) => {
  const frame = useCurrentFrame();
  const { fps } = useVideoConfig();
  
  const chars = text.split('');
  const duration = 1.2 * fps; // 1.2秒
  const diffusionRange = 1.5; // 扩散范围150%
  
  return (
    <div style={{
      display: 'inline-flex',
      alignItems: 'center',
      justifyContent: 'center',
      position: 'relative',
      backgroundColor: 'transparent',
      ...style
    }}>
      {chars.map((char, index) => {
        const charDelay = index * 3;
        const charProgress = Math.max(0, Math.min(1, (frame - charDelay) / (duration * 0.9)));
        
        // 模糊程度
        const blurAmount = interpolate(charProgress, [0, 0.3, 1], [0, 2, 15]);
        
        // 扩散尺寸
        const diffusionScale = interpolate(charProgress, [0, 1], [1, diffusionRange]);
        
        // 透明度降低
        const opacity = interpolate(charProgress, [0, 0.4, 1], [1, 0.6, 0]);
        
        // 墨迹浓度
        const inkDensity = interpolate(charProgress, [0, 0.5, 1], [1, 0.4, 0.1]);
        
        return (
          <div key={index} style={{
            position: 'relative',
            display: 'inline-block',
            margin: '0 2px'
          }}>

            
            {/* 毛刺边缘效果 */}
            {charProgress > 0.2 && (
              <>
                {Array.from({ length: 12 }, (_, i) => {
                  const angle = (i / 12) * Math.PI * 2;
                  const distance = interpolate(charProgress, [0.2, 1], [0, 25]);
                  const bristleX = Math.cos(angle) * distance;
                  const bristleY = Math.sin(angle) * distance;
                  const bristleOpacity = interpolate(charProgress, [0.2, 0.6, 1], [0, 0.8, 0]);
                  
                  return (
                    <div
                      key={`bristle-${i}`}
                      style={{
                        position: 'absolute',
                        left: '50%',
                        top: '50%',
                        width: '2px',
                        height: `${8 + Math.random() * 12}px`,
                        background: `linear-gradient(${angle}rad, 
                          rgba(255,255,255,${bristleOpacity}) 0%, 
                          transparent 100%)`,
                        transform: `translate(-50%, -50%) translate(${bristleX}px, ${bristleY}px) rotate(${angle}rad)`,
                        pointerEvents: 'none'
                      }}
                    />
                  );
                })}
              </>
            )}
            
            {/* 墨滴飞溅 */}
            {charProgress > 0.3 && (
              <>
                {Array.from({ length: 8 }, (_, i) => {
                  const splashAngle = (Math.random() - 0.5) * Math.PI;
                  const splashDistance = interpolate(charProgress, [0.3, 1], [0, 40 + Math.random() * 20]);
                  const splashX = Math.cos(splashAngle) * splashDistance;
                  const splashY = Math.sin(splashAngle) * splashDistance;
                  const splashOpacity = interpolate(charProgress, [0.3, 0.7, 1], [0, 0.6, 0]);
                  
                  return (
                    <div
                      key={`splash-${i}`}
                      style={{
                        position: 'absolute',
                        left: '50%',
                        top: '50%',
                        width: `${3 + Math.random() * 4}px`,
                        height: `${3 + Math.random() * 4}px`,
                        backgroundColor: `rgba(255,255,255,${splashOpacity})`,
                        borderRadius: '50%',
                        transform: `translate(-50%, -50%) translate(${splashX}px, ${splashY}px)`,
                        filter: `blur(${Math.random() * 2}px)`,
                        pointerEvents: 'none'
                      }}
                    />
                  );
                })}
              </>
            )}
            
            {/* 主文字 */}
            <span style={{
              fontSize: '60px',
              fontWeight: 'bold',
              color: 'white',
              display: 'inline-block',
              opacity: opacity,
              filter: `blur(${blurAmount}px)`,
              transform: `scale(${diffusionScale})`,
              textShadow: `0 0 ${blurAmount * 2}px rgba(255,255,255,${opacity * 0.5})`
            }}>
              {char}
            </span>
          </div>
        );
      })}
    </div>
  );
};

InkDiffusionEffect.showName = "墨水扩散";

export default InkDiffusionEffect; 