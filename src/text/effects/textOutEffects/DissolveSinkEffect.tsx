import React from 'react';
import { useCurrentFrame, interpolate, useVideoConfig } from 'remotion';

interface DissolveSinkEffectProps {
  text: string;
  style?: React.CSSProperties;
}

const DissolveSinkEffect: React.FC<DissolveSinkEffectProps> & { showName: string } = ({
  text,
  style
}) => {
  const frame = useCurrentFrame();
  const { fps } = useVideoConfig();
  
  const chars = text.split('');
  const duration = 1.2 * fps; // 1.2秒
  const dissolveSpeed = 90; // 溶解速度90px/s
  
  return (
    <div style={{
      display: 'inline-flex',
      alignItems: 'center',
      justifyContent: 'center',
      position: 'relative',
      backgroundColor: 'transparent',
      ...style
    }}>
      {chars.map((char, index) => {
        const charDelay = index * 3;
        const charProgress = Math.max(0, Math.min(1, (frame - charDelay) / (duration * 0.8)));
        
        // 溶解阶段
        const dissolvePhase = interpolate(charProgress, [0, 0.3, 0.7, 1], [0, 1, 2, 3]);
        
        // 文字透明度
        const opacity = interpolate(charProgress, [0, 0.8, 1], [1, 0.2, 0]);
        
        // 下沉距离
        const sinkDistance = interpolate(dissolvePhase, [1, 3], [0, dissolveSpeed * (duration / fps)]);
        
        return (
          <div key={index} style={{
            position: 'relative',
            display: 'inline-block',
            margin: '0 2px'
          }}>
            {/* 底部液化效果 - 文字接触时消失 */}
            {dissolvePhase >= 0.5 && dissolvePhase < 2.5 && sinkDistance < 15 && (
              <div style={{
                position: 'absolute',
                left: -2,
                bottom: -10,
                right: -2,
                height: `${interpolate(dissolvePhase, [0.5, 2], [0, 20])}px`,
                background: `linear-gradient(180deg, 
                  rgba(100,150,200,${interpolate(dissolvePhase, [0.5, 1.5, 2.5], [0, 0.8, 0])}) 0%, 
                  rgba(120,170,220,${interpolate(dissolvePhase, [0.5, 1.5, 2.5], [0, 0.6, 0])}) 50%, 
                  rgba(80,130,180,${interpolate(dissolvePhase, [0.5, 1.5, 2.5], [0, 0.4, 0])}) 100%)`,
                borderRadius: '0 0 50% 50%',
                pointerEvents: 'none',
                filter: 'blur(1px)',
                opacity: sinkDistance > 10 ? interpolate(sinkDistance, [10, 15], [1, 0]) : 1
              }} />
            )}
            
            {/* 气泡上浮 */}
            {dissolvePhase >= 1 && opacity > 0 && (
              <>
                {Array.from({ length: 6 }, (_, i) => {
                  const bubbleDelay = i * 0.2;
                  const bubbleProgress = Math.max(0, (dissolvePhase - 1 - bubbleDelay) / 1.5);
                  
                  if (bubbleProgress <= 0) return null;
                  
                  const bubbleX = (Math.random() - 0.5) * 30;
                  const bubbleY = interpolate(bubbleProgress, [0, 1], [20, -40]);
                  const bubbleSize = 2 + Math.random() * 4;
                  const bubbleOpacity = interpolate(bubbleProgress, [0, 0.5, 1], [0, 0.8, 0]) * opacity;
                  
                  return (
                    <div
                      key={`bubble-${i}`}
                      style={{
                        position: 'absolute',
                        left: '50%',
                        bottom: '0%',
                        width: `${bubbleSize}px`,
                        height: `${bubbleSize}px`,
                        background: `radial-gradient(circle, 
                          rgba(200,230,255,${bubbleOpacity}) 0%, 
                          rgba(150,200,255,${bubbleOpacity * 0.6}) 70%, 
                          transparent 100%)`,
                        borderRadius: '50%',
                        transform: `translate(-50%, 0) translate(${bubbleX}px, ${bubbleY}px)`,
                        pointerEvents: 'none'
                      }}
                    />
                  );
                })}
              </>
            )}
            
            {/* 液体浓度递减效果 */}
            {dissolvePhase >= 1.5 && opacity > 0 && (
              <>
                {Array.from({ length: 4 }, (_, layerIndex) => {
                  const layerY = layerIndex * 15;
                  const layerOpacity = interpolate(dissolvePhase, [1.5, 2.5, 3], [0, 0.6 - layerIndex * 0.1, 0]) * opacity;
                  const layerHeight = 12 - layerIndex * 2;
                  
                  return (
                    <div
                      key={`layer-${layerIndex}`}
                      style={{
                        position: 'absolute',
                        left: -3,
                        right: -3,
                        bottom: `-${layerY}px`,
                        height: `${layerHeight}px`,
                        background: `linear-gradient(180deg, 
                          rgba(100,150,200,${layerOpacity}) 0%, 
                          rgba(120,170,220,${layerOpacity * 0.8}) 50%, 
                          rgba(80,130,180,${layerOpacity * 0.6}) 100%)`,
                        borderRadius: `0 0 ${50 - layerIndex * 10}% ${50 - layerIndex * 10}%`,
                        pointerEvents: 'none',
                        filter: 'blur(0.5px)'
                      }}
                    />
                  );
                })}
              </>
            )}
            
            {/* 溶解粒子 */}
            {dissolvePhase >= 1.2 && opacity > 0 && (
              <>
                {Array.from({ length: 12 }, (_, i) => {
                  const particleProgress = Math.max(0, (dissolvePhase - 1.2) / 1.8);
                  const particleAngle = (i / 12) * Math.PI * 2;
                  const particleDistance = 5 + Math.random() * 15;
                  const particleX = Math.cos(particleAngle) * particleDistance;
                  const particleY = Math.sin(particleAngle) * particleDistance + particleProgress * 30;
                  
                  const particleSize = 1 + Math.random() * 2;
                  const particleOpacity = interpolate(particleProgress, [0, 0.5, 1], [0.8, 0.6, 0]) * opacity;
                  
                  return (
                    <div
                      key={`particle-${i}`}
                      style={{
                        position: 'absolute',
                        left: '50%',
                        top: '50%',
                        width: `${particleSize}px`,
                        height: `${particleSize}px`,
                        background: `rgba(120,170,220,${particleOpacity})`,
                        borderRadius: '50%',
                        transform: `translate(-50%, -50%) translate(${particleX}px, ${particleY}px)`,
                        pointerEvents: 'none'
                      }}
                    />
                  );
                })}
              </>
            )}
            
            {/* 水面波纹 */}
            {dissolvePhase >= 2 && opacity > 0 && (
              <>
                {Array.from({ length: 3 }, (_, i) => {
                  const rippleDelay = i * 8;
                  const rippleProgress = Math.max(0, Math.min(1, (frame - charDelay - rippleDelay - 2 * fps) / 20));
                  const rippleRadius = interpolate(rippleProgress, [0, 1], [0, 25 + i * 8]);
                  const rippleOpacity = interpolate(rippleProgress, [0, 0.5, 1], [0, 0.6, 0]) * opacity;
                  
                  return (
                    <div
                      key={`ripple-${i}`}
                      style={{
                        position: 'absolute',
                        left: '50%',
                        bottom: '-10px',
                        width: `${rippleRadius * 2}px`,
                        height: `${rippleRadius}px`,
                        border: `1px solid rgba(120,170,220,${rippleOpacity})`,
                        borderRadius: '50%',
                        borderTop: 'none',
                        transform: 'translateX(-50%)',
                        pointerEvents: 'none'
                      }}
                    />
                  );
                })}
              </>
            )}
            
            {/* 主文字 */}
            <span style={{
              fontSize: '60px',
              fontWeight: 'bold',
              color: 'white',
              display: 'inline-block',
              opacity: opacity,
              transform: `translateY(${sinkDistance}px)`,
              textShadow: dissolvePhase >= 0.5 && dissolvePhase < 2 
                ? `0 0 8px rgba(120,170,220,0.6)` 
                : 'none',
              filter: dissolvePhase >= 2 
                ? `blur(${interpolate(dissolvePhase, [2, 3], [0, 2])}px)` 
                : 'none'
            }}>
              {char}
            </span>
          </div>
        );
      })}
    </div>
  );
};

DissolveSinkEffect.showName = "溶解下沉";

export default DissolveSinkEffect; 