import React from 'react';
import { interpolate, useCurrentFrame } from 'remotion';

interface SlideDownOutEffectProps {
  text: string | React.ReactNode[];
  style: React.CSSProperties;
  durationFrames?: number;
}

export default function SlideDownOutEffect({
  text = '展示文本',
  style = {},
  durationFrames = 40,
}: SlideDownOutEffectProps) {
  const frame = useCurrentFrame();

  // 下滑动画
  const translateY = interpolate(
    frame,
    [0, durationFrames],
    [0, 50],
    {
      extrapolateLeft: 'clamp',
      extrapolateRight: 'clamp',
    }
  );

  // 透明度
  const opacity = interpolate(
    frame,
    [durationFrames - 20, durationFrames],
    [1, 0],
    {
      extrapolateLeft: 'clamp',
      extrapolateRight: 'clamp',
    }
  );

  return (
    <span
      style={{
        display: 'inline-flex',
        alignItems: 'center',
        justifyContent: 'center',
        height: '100%',
      }}
    >
      <span
        style={{
          opacity: opacity,
          transform: `translateY(${translateY}px)`,
          color: '#ffffff',
          textAlign: 'center',
          textShadow: '0 0 8px #ffffff',
          ...style,
        }}
      >
        {text}
      </span>
    </span>
  );
}

SlideDownOutEffect.key = 'SlideDownOutEffect';
SlideDownOutEffect.description = 'slide down out text effect';
SlideDownOutEffect.showName = '下滑出场'; 