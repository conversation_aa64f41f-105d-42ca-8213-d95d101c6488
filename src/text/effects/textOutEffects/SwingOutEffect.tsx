import React from 'react';
import { interpolate, useCurrentFrame } from 'remotion';

interface SwingOutEffectProps {
  text: string | React.ReactNode[];
  style: React.CSSProperties;
  durationFrames?: number;
}

export default function SwingOutEffect({
  text = '展示文本',
  style = {},
  durationFrames = 60,
}: SwingOutEffectProps) {
  const frame = useCurrentFrame();

  // 摆动角度（逐渐增大）
  const swingAngle = Math.sin(frame * 0.3) * (frame / 2);

  // 透明度
  const opacity = interpolate(
    frame,
    [durationFrames - 15, durationFrames],
    [1, 0],
    {
      extrapolateLeft: 'clamp',
      extrapolateRight: 'clamp',
    }
  );

  return (
    <span
      style={{
        display: 'inline-flex',
        alignItems: 'center',
        justifyContent: 'center',
        height: '100%',
      }}
    >
      <span
        style={{
          opacity: opacity,
          transform: `rotate(${swingAngle}deg)`,
          transformOrigin: 'center top',
          color: '#ffffff',
          textAlign: 'center',
          textShadow: '0 0 8px #ffffff',
          ...style,
        }}
      >
        {text}
      </span>
    </span>
  );
}

SwingOutEffect.key = 'SwingOutEffect';
SwingOutEffect.description = 'swing out text effect';
SwingOutEffect.showName = '摆动出场'; 