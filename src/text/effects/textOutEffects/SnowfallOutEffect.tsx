import React from 'react';
import { interpolate, useCurrentFrame } from 'remotion';

interface SnowfallOutEffectProps {
  text: string | React.ReactNode[];
  style: React.CSSProperties;
  durationFrames?: number;
}

export default function SnowfallOutEffect({
  text = '展示文本',
  style = {},
  durationFrames = 80,
}: SnowfallOutEffectProps) {
  const frame = useCurrentFrame();

  // 文字淡出
  const opacity = interpolate(
    frame,
    [durationFrames - 30, durationFrames],
    [1, 0],
    {
      extrapolateLeft: 'clamp',
      extrapolateRight: 'clamp',
    }
  );

  // 雪花飞散
  const snowflakes = Array.from({ length: 8 }, (_, i) => {
    const snowFrame = frame + i * 6;
    if (snowFrame > durationFrames + 30) return null;
    
    const snowOpacity = interpolate(
      frame,
      [0, 20, durationFrames],
      [1, 0.8, 0],
      { extrapolateLeft: 'clamp', extrapolateRight: 'clamp' }
    );
    
    const x = 20 + i * 10 + Math.sin(snowFrame * 0.05 + i) * (15 + frame * 0.5);
    const y = (snowFrame * 1.2) % 120;
    
    return (
      <div
        key={i}
        style={{
          position: 'absolute',
          left: `${x}%`,
          top: `${y}%`,
          fontSize: '16px',
          opacity: snowOpacity,
          transform: 'translate(-50%, -50%)',
        }}
      >
        ❄️
      </div>
    );
  }).filter(Boolean);

  return (
    <span
      style={{
        display: 'inline-flex',
        alignItems: 'center',
        justifyContent: 'center',
        height: '100%',
        position: 'relative',
      }}
    >
      <span
        style={{
          opacity: opacity,
          color: '#ffffff',
          textAlign: 'center',
          textShadow: '0 0 12px #ffffff',
          filter: `brightness(${1 - Math.sin(frame * 0.1) * 0.2})`,
          ...style,
        }}
      >
        {text}
      </span>
      {snowflakes}
    </span>
  );
}

SnowfallOutEffect.key = 'SnowfallOutEffect';
SnowfallOutEffect.description = 'snowfall out text effect with dispersing snowflakes';
SnowfallOutEffect.showName = '雪花飞散'; 