import React from 'react';
import { useCurrentFrame, interpolate, useVideoConfig } from 'remotion';

interface TimeReverseEffectProps {
  text: string;
  style?: React.CSSProperties;
}

const TimeReverseEffect: React.FC<TimeReverseEffectProps> & { showName: string } = ({
  text,
  style
}) => {
  const frame = useCurrentFrame();
  const { fps } = useVideoConfig();
  
  const chars = text.split('');
  const duration = 1.4 * fps; // 1.4秒
  const reverseSpeed = 1.5; // 倒流速度1.5x
  
  return (
    <div style={{
      display: 'inline-flex',
      alignItems: 'center',
      justifyContent: 'center',
      position: 'relative',
      backgroundColor: 'transparent',
      ...style
    }}>
      {chars.map((char, index) => {
        const charDelay = index * 3;
        const charProgress = Math.max(0, Math.min(1, (frame - charDelay) / (duration * 0.8)));
        
        // 倒流阶段
        const reversePhase = interpolate(charProgress, [0, 0.3, 0.7, 1], [0, 1, 2, 3]);
        
        // 文字透明度
        const opacity = interpolate(charProgress, [0, 0.8, 1], [1, 0.2, 0]);
        
        return (
          <div key={index} style={{
            position: 'relative',
            display: 'inline-block',
            margin: '0 2px'
          }}>
            {/* 时光波纹 */}
            {reversePhase >= 0.2 && reversePhase < 2 && (
              <>
                {Array.from({ length: 4 }, (_, i) => {
                  const rippleDelay = i * 5;
                  const rippleProgress = Math.max(0, Math.min(1, (frame - charDelay - rippleDelay) / 20));
                  const rippleRadius = interpolate(rippleProgress, [0, 1], [0, 30 + i * 10]);
                  const rippleOpacity = interpolate(rippleProgress, [0, 0.5, 1], [0, 0.6, 0]);
                  
                  return (
                    <div
                      key={`ripple-${i}`}
                      style={{
                        position: 'absolute',
                        left: '50%',
                        top: '50%',
                        width: `${rippleRadius * 2}px`,
                        height: `${rippleRadius * 2}px`,
                        border: `1px solid rgba(100,200,255,${rippleOpacity})`,
                        borderRadius: '50%',
                        transform: 'translate(-50%, -50%)',
                        pointerEvents: 'none'
                      }}
                    />
                  );
                })}
              </>
            )}
            
            {/* 粒子倒飞轨迹 */}
            {reversePhase >= 1 && (
              <>
                {Array.from({ length: 12 }, (_, i) => {
                  const particleAngle = (i / 12) * Math.PI * 2;
                  const particleProgress = Math.max(0, (reversePhase - 1) / 2);
                  
                  // 倒飞效果：从远处飞回中心
                  const startDistance = 40 + Math.random() * 20;
                  const currentDistance = interpolate(particleProgress, [0, 1], [startDistance, 0]);
                  const particleX = Math.cos(particleAngle) * currentDistance;
                  const particleY = Math.sin(particleAngle) * currentDistance;
                  
                  const particleOpacity = interpolate(particleProgress, [0, 0.5, 1], [0.8, 1, 0]);
                  const particleSize = 2 + Math.random() * 3;
                  
                  return (
                    <div
                      key={`particle-${i}`}
                      style={{
                        position: 'absolute',
                        left: '50%',
                        top: '50%',
                        width: `${particleSize}px`,
                        height: `${particleSize}px`,
                        background: `radial-gradient(circle, 
                          rgba(100,200,255,${particleOpacity}) 0%, 
                          rgba(150,220,255,${particleOpacity * 0.8}) 50%, 
                          transparent 100%)`,
                        borderRadius: '50%',
                        transform: `translate(-50%, -50%) translate(${particleX}px, ${particleY}px)`,
                        pointerEvents: 'none'
                      }}
                    />
                  );
                })}
              </>
            )}
            
            {/* 建筑拆除过程 */}
            {reversePhase >= 1.5 && reversePhase < 2.5 && (
              <>
                {Array.from({ length: 6 }, (_, i) => {
                  const blockProgress = Math.max(0, (reversePhase - 1.5 - i * 0.1) / 0.8);
                  if (blockProgress <= 0) return null;
                  
                  const blockY = interpolate(blockProgress, [0, 1], [0, -30 - i * 8]);
                  const blockOpacity = interpolate(blockProgress, [0, 0.8, 1], [1, 0.6, 0]);
                  const blockWidth = 20 - i * 2;
                  const blockHeight = 8;
                  
                  return (
                    <div
                      key={`block-${i}`}
                      style={{
                        position: 'absolute',
                        left: '50%',
                        top: '50%',
                        width: `${blockWidth}px`,
                        height: `${blockHeight}px`,
                        background: `linear-gradient(135deg, 
                          rgba(150,150,150,${blockOpacity}) 0%, 
                          rgba(100,100,100,${blockOpacity * 0.8}) 100%)`,
                        border: `1px solid rgba(120,120,120,${blockOpacity})`,
                        transform: `translate(-50%, -50%) translateY(${blockY}px)`,
                        pointerEvents: 'none'
                      }}
                    />
                  );
                })}
              </>
            )}
            
            {/* 原始材料回归 */}
            {reversePhase >= 2.5 && (
              <>
                {Array.from({ length: 8 }, (_, i) => {
                  const materialProgress = Math.max(0, (reversePhase - 2.5) / 0.5);
                  const materialAngle = (i / 8) * Math.PI * 2;
                  const materialDistance = interpolate(materialProgress, [0, 1], [25, 0]);
                  const materialX = Math.cos(materialAngle) * materialDistance;
                  const materialY = Math.sin(materialAngle) * materialDistance;
                  const materialOpacity = interpolate(materialProgress, [0, 0.8, 1], [0.6, 1, 0]);
                  
                  return (
                    <div
                      key={`material-${i}`}
                      style={{
                        position: 'absolute',
                        left: '50%',
                        top: '50%',
                        width: '3px',
                        height: '3px',
                        background: `rgba(200,180,150,${materialOpacity})`,
                        borderRadius: '50%',
                        transform: `translate(-50%, -50%) translate(${materialX}px, ${materialY}px)`,
                        pointerEvents: 'none'
                      }}
                    />
                  );
                })}
              </>
            )}
            
            {/* 时间扭曲效果 */}
            {reversePhase >= 0.5 && reversePhase < 2.5 && (
              <div style={{
                position: 'absolute',
                left: '50%',
                top: '50%',
                width: '60px',
                height: '60px',
                background: `conic-gradient(
                  from ${-frame * reverseSpeed * 5}deg,
                  transparent 0deg,
                  rgba(100,200,255,${interpolate(reversePhase, [0.5, 1.5, 2.5], [0, 0.2, 0])}) 90deg,
                  transparent 180deg,
                  rgba(100,200,255,${interpolate(reversePhase, [0.5, 1.5, 2.5], [0, 0.2, 0])}) 270deg,
                  transparent 360deg
                )`,
                borderRadius: '50%',
                transform: 'translate(-50%, -50%)',
                pointerEvents: 'none'
              }} />
            )}
            
            {/* 主文字 */}
            <span style={{
              fontSize: '60px',
              fontWeight: 'bold',
              color: 'white',
              display: 'inline-block',
              opacity: opacity,
              textShadow: reversePhase >= 0.5 && reversePhase < 2 
                ? `0 0 8px rgba(100,200,255,0.6)` 
                : 'none',
              filter: reversePhase >= 2 
                ? `blur(${interpolate(reversePhase, [2, 3], [0, 3])}px)` 
                : 'none',
              transform: reversePhase >= 1.5 
                ? `scale(${interpolate(reversePhase, [1.5, 3], [1, 0.3])})` 
                : 'scale(1)'
            }}>
              {char}
            </span>
          </div>
        );
      })}
    </div>
  );
};

TimeReverseEffect.showName = "时光倒流";

export default TimeReverseEffect; 