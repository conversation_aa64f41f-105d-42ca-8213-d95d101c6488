import React from 'react';
import { interpolate, useCurrentFrame } from 'remotion';

interface WobbleOutEffectProps {
  text: string | React.ReactNode[];
  style: React.CSSProperties;
  durationFrames?: number;
}

export default function WobbleOutEffect({
  text = '展示文本',
  style = {},
  durationFrames = 60,
}: WobbleOutEffectProps) {
  const frame = useCurrentFrame();

  // 摇摆动画（逐渐减弱）
  const wobbleIntensity = 1 - frame / durationFrames;
  const wobbleX = Math.sin(frame * 0.5) * 15 * wobbleIntensity;
  const wobbleRotate = Math.sin(frame * 0.3) * 5 * wobbleIntensity;

  // 透明度
  const opacity = interpolate(
    frame,
    [durationFrames - 15, durationFrames],
    [1, 0],
    {
      extrapolateLeft: 'clamp',
      extrapolateRight: 'clamp',
    }
  );

  return (
    <span
      style={{
        display: 'inline-flex',
        alignItems: 'center',
        justifyContent: 'center',
        height: '100%',
      }}
    >
      <span
        style={{
          opacity: opacity,
          transform: `translateX(${wobbleX}px) rotate(${wobbleRotate}deg)`,
          color: '#ffffff',
          textAlign: 'center',
          textShadow: '0 0 8px #ffffff',
          ...style,
        }}
      >
        {text}
      </span>
    </span>
  );
}

WobbleOutEffect.key = 'WobbleOutEffect';
WobbleOutEffect.description = 'wobble out text effect';
WobbleOutEffect.showName = '摇摆出场'; 