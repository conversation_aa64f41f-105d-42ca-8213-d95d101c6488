import React from 'react';
import { interpolate, useCurrentFrame } from 'remotion';

interface BlurOutEffectProps {
  text: string | React.ReactNode[];
  style: React.CSSProperties;
  durationFrames?: number;
}

export default function BlurOutEffect({
  text = '展示文本',
  style = {},
  durationFrames = 50,
}: BlurOutEffectProps) {
  const frame = useCurrentFrame();

  // 清晰到模糊
  const blur = interpolate(
    frame,
    [0, durationFrames],
    [0, 10],
    {
      extrapolateLeft: 'clamp',
      extrapolateRight: 'clamp',
    }
  );

  // 透明度
  const opacity = interpolate(
    frame,
    [durationFrames - 20, durationFrames],
    [1, 0],
    {
      extrapolateLeft: 'clamp',
      extrapolateRight: 'clamp',
    }
  );

  return (
    <span
      style={{
        display: 'inline-flex',
        alignItems: 'center',
        justifyContent: 'center',
        height: '100%',
      }}
    >
      <span
        style={{
          opacity: opacity,
          filter: `blur(${blur}px)`,
          color: '#ffffff',
          textAlign: 'center',
          textShadow: '0 0 8px #ffffff',
          ...style,
        }}
      >
        {text}
      </span>
    </span>
  );
}

BlurOutEffect.key = 'BlurOutEffect';
BlurOutEffect.description = 'blur out text effect';
BlurOutEffect.showName = '模糊出场'; 