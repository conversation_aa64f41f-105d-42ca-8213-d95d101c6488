import React from 'react';
import { useCurrentFrame, interpolate, useVideoConfig } from 'remotion';

interface CrystalCollapseEffectProps {
  text: string;
  style?: React.CSSProperties;
}

const CrystalCollapseEffect: React.FC<CrystalCollapseEffectProps> & { showName: string } = ({
  text,
  style
}) => {
  const frame = useCurrentFrame();
  const { fps } = useVideoConfig();
  
  const chars = text.split('');
  const duration = 1.3 * fps; // 1.3秒
  const collapseDelay = 0.2 * fps; // 崩解延迟0.2s
  
  return (
    <div style={{
      display: 'inline-flex',
      alignItems: 'center',
      justifyContent: 'center',
      position: 'relative',
      backgroundColor: 'transparent',
      ...style
    }}>
      {chars.map((char, index) => {
        const charDelay = index * collapseDelay;
        const charProgress = Math.max(0, Math.min(1, (frame - charDelay) / (duration * 0.8)));
        
        // 崩解阶段
        const collapsePhase = interpolate(charProgress, [0, 0.3, 0.7, 1], [0, 1, 2, 3]);
        
        // 文字透明度
        const opacity = interpolate(charProgress, [0, 0.8, 1], [1, 0.1, 0]);
        
        return (
          <div key={index} style={{
            position: 'relative',
            display: 'inline-block',
            margin: '0 2px'
          }}>

            
            {/* 内部裂缝 */}
            {collapsePhase >= 0.5 && collapsePhase < 2.5 && (
              <>
                {Array.from({ length: 8 }, (_, i) => {
                  const crackAngle = (i / 8) * Math.PI * 2 + (Math.random() - 0.5) * 0.3;
                  const crackLength = interpolate(collapsePhase, [0.5, 1.5], [0, 20 + Math.random() * 15]);
                  const crackOpacity = interpolate(collapsePhase, [0.5, 1, 2, 2.5], [0, 1, 0.8, 0]);
                  
                  return (
                    <div
                      key={`crack-${i}`}
                      style={{
                        position: 'absolute',
                        left: '50%',
                        top: '50%',
                        width: `${crackLength}px`,
                        height: '2px',
                        background: `linear-gradient(90deg, 
                          rgba(80,80,80,${crackOpacity}) 0%, 
                          rgba(60,60,60,${crackOpacity * 0.8}) 50%, 
                          rgba(40,40,40,${crackOpacity * 0.6}) 100%)`,
                        transform: `translate(-50%, -50%) rotate(${crackAngle}rad)`,
                        transformOrigin: '0 50%',
                        pointerEvents: 'none',
                        boxShadow: `0 0 2px rgba(0,0,0,${crackOpacity * 0.5})`
                      }}
                    />
                  );
                })}
              </>
            )}
            
            {/* 碎片剥落 */}
            {collapsePhase >= 1.2 && (
              <>
                {Array.from({ length: 16 }, (_, i) => {
                  const fragmentProgress = Math.max(0, (collapsePhase - 1.2) / 1.8);
                  const fragmentAngle = (i / 16) * Math.PI * 2 + (Math.random() - 0.5) * 0.5;
                  const fragmentDistance = fragmentProgress * (25 + Math.random() * 15);
                  const fragmentX = Math.cos(fragmentAngle) * fragmentDistance;
                  const fragmentY = Math.sin(fragmentAngle) * fragmentDistance + fragmentProgress * 20; // 重力
                  
                  const fragmentRotation = fragmentProgress * (180 + Math.random() * 360);
                  const fragmentSize = 3 + Math.random() * 5;
                  const fragmentOpacity = interpolate(fragmentProgress, [0, 0.5, 1], [0.8, 0.6, 0]);
                  
                  return (
                    <div
                      key={`fragment-${i}`}
                      style={{
                        position: 'absolute',
                        left: '50%',
                        top: '50%',
                        width: `${fragmentSize}px`,
                        height: `${fragmentSize}px`,
                        background: `linear-gradient(45deg, 
                          rgba(200,230,255,${fragmentOpacity}) 0%, 
                          rgba(150,200,255,${fragmentOpacity * 0.8}) 50%, 
                          rgba(100,170,255,${fragmentOpacity * 0.6}) 100%)`,
                        border: `1px solid rgba(180,220,255,${fragmentOpacity * 0.5})`,
                        borderRadius: '20%',
                        transform: `translate(-50%, -50%) translate(${fragmentX}px, ${fragmentY}px) rotate(${fragmentRotation}deg)`,
                        pointerEvents: 'none',
                        boxShadow: `0 0 3px rgba(150,200,255,${fragmentOpacity * 0.4})`
                      }}
                    />
                  );
                })}
              </>
            )}
            
            {/* 折射光消失 */}
            {collapsePhase >= 0.8 && collapsePhase < 2.2 && (
              <>
                {Array.from({ length: 6 }, (_, i) => {
                  const lightAngle = (i / 6) * Math.PI * 2;
                  const lightLength = interpolate(collapsePhase, [0.8, 1.5, 2.2], [0, 30, 0]);
                  const lightOpacity = interpolate(collapsePhase, [0.8, 1.2, 1.8, 2.2], [0, 0.8, 0.6, 0]);
                  
                  return (
                    <div
                      key={`light-${i}`}
                      style={{
                        position: 'absolute',
                        left: '50%',
                        top: '50%',
                        width: `${lightLength}px`,
                        height: '1px',
                        background: `linear-gradient(90deg, 
                          rgba(255,255,255,${lightOpacity}) 0%, 
                          rgba(200,230,255,${lightOpacity * 0.8}) 50%, 
                          transparent 100%)`,
                        transform: `translate(-50%, -50%) rotate(${lightAngle}rad)`,
                        transformOrigin: '0 50%',
                        pointerEvents: 'none',
                        boxShadow: `0 0 3px rgba(255,255,255,${lightOpacity * 0.6})`
                      }}
                    />
                  );
                })}
              </>
            )}
            
            {/* 整体坍塌成粉末 */}
            {collapsePhase >= 2.5 && (
              <>
                {Array.from({ length: 30 }, (_, i) => {
                  const powderProgress = Math.max(0, (collapsePhase - 2.5) / 0.5);
                  const powderAngle = (Math.random() - 0.5) * Math.PI * 2;
                  const powderDistance = powderProgress * (15 + Math.random() * 10);
                  const powderX = Math.cos(powderAngle) * powderDistance;
                  const powderY = Math.sin(powderAngle) * powderDistance + powderProgress * 25;
                  
                  const powderSize = 1 + Math.random() * 2;
                  const powderOpacity = interpolate(powderProgress, [0, 0.5, 1], [0.6, 0.4, 0]);
                  
                  return (
                    <div
                      key={`powder-${i}`}
                      style={{
                        position: 'absolute',
                        left: '50%',
                        top: '50%',
                        width: `${powderSize}px`,
                        height: `${powderSize}px`,
                        background: `rgba(180,220,255,${powderOpacity})`,
                        borderRadius: '50%',
                        transform: `translate(-50%, -50%) translate(${powderX}px, ${powderY}px)`,
                        pointerEvents: 'none',
                        filter: 'blur(0.5px)'
                      }}
                    />
                  );
                })}
              </>
            )}
            
            {/* 水晶光环 */}
            {collapsePhase >= 0.3 && collapsePhase < 2 && (
              <div style={{
                position: 'absolute',
                left: '50%',
                top: '50%',
                width: '50px',
                height: '50px',
                border: `2px solid rgba(200,230,255,${interpolate(collapsePhase, [0.3, 1, 2], [0, 0.6, 0])})`,
                borderRadius: '50%',
                transform: `translate(-50%, -50%) rotate(${frame * 2}deg)`,
                pointerEvents: 'none',
                boxShadow: `0 0 10px rgba(200,230,255,${interpolate(collapsePhase, [0.3, 1, 2], [0, 0.4, 0])})`
              }} />
            )}
            
            {/* 主文字 */}
            <span style={{
              fontSize: '60px',
              fontWeight: 'bold',
              color: 'white',
              display: 'inline-block',
              opacity: opacity,
              textShadow: collapsePhase >= 0.3 && collapsePhase < 2 
                ? `0 0 8px rgba(200,230,255,0.8)` 
                : 'none',
              filter: collapsePhase >= 2 
                ? `blur(${interpolate(collapsePhase, [2, 3], [0, 2])}px)` 
                : 'none',
              background: collapsePhase >= 0.2 && collapsePhase < 2.5
                ? `linear-gradient(135deg, 
                    rgba(200,230,255,0.3) 0%, 
                    rgba(150,200,255,0.2) 50%, 
                    rgba(100,170,255,0.3) 100%)`
                : 'transparent',
              WebkitBackgroundClip: 'text',
              backgroundClip: 'text'
            }}>
              {char}
            </span>
          </div>
        );
      })}
    </div>
  );
};

CrystalCollapseEffect.showName = "水晶崩解";

export default CrystalCollapseEffect; 