import React from 'react';
import { interpolate, useCurrentFrame } from 'remotion';

interface JelloOutEffectProps {
  text: string | React.ReactNode[];
  style: React.CSSProperties;
  durationFrames?: number;
}

export default function JelloOutEffect({
  text = '展示文本',
  style = {},
  durationFrames = 70,
}: JelloOutEffectProps) {
  const frame = useCurrentFrame();

  // 果冻摇摆效果（逐渐减弱）
  const intensity = 1 - frame / durationFrames;
  const skewX = Math.sin(frame * 0.8) * 12.5 * intensity;
  const skewY = Math.sin(frame * 0.6) * 2.5 * intensity;

  // 透明度
  const opacity = interpolate(
    frame,
    [durationFrames - 15, durationFrames],
    [1, 0],
    {
      extrapolateLeft: 'clamp',
      extrapolateRight: 'clamp',
    }
  );

  return (
    <span
      style={{
        display: 'inline-flex',
        alignItems: 'center',
        justifyContent: 'center',
        height: '100%',
      }}
    >
      <span
        style={{
          opacity: opacity,
          transform: `skewX(${skewX}deg) skewY(${skewY}deg)`,
          color: '#ffffff',
          textAlign: 'center',
          textShadow: '0 0 8px #ffffff',
          ...style,
        }}
      >
        {text}
      </span>
    </span>
  );
}

JelloOutEffect.key = 'JelloOutEffect';
JelloOutEffect.description = 'jello out text effect';
JelloOutEffect.showName = '果冻出场'; 