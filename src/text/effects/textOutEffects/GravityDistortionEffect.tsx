import React from 'react';
import { useCurrentFrame, interpolate, useVideoConfig } from 'remotion';

interface GravityDistortionEffectProps {
  text: string;
  color?: string;
  fontSize?: number;
  style?: React.CSSProperties;
}

const GravityDistortionEffect: React.FC<GravityDistortionEffectProps> & { showName: string } = ({
  text = "ABCD123",
  color = "#FFFFFF",
  fontSize = 60,
  style = {}
}) => {
  const frame = useCurrentFrame();
  const { fps } = useVideoConfig();
  
  const duration = 0.9 * fps; // 0.9秒
  const progress = Math.min(1, frame / duration);
  
  // 扭曲强度1.8
  const distortionIntensity = 1.8;
  
  return (
    <div style={{
      display: 'inline-flex',
      alignItems: 'center',
      justifyContent: 'center',
      position: 'relative',
      ...style
    }}>
      {text.split('').map((char, index) => {
        const charCenterX = index * (fontSize * 0.6 + 4) + fontSize * 0.3;
        const charCenterY = fontSize * 0.5;
        
        // 黑洞中心位置（文字中央）
        const blackHoleX = (text.length * (fontSize * 0.6 + 4)) / 2;
        const blackHoleY = fontSize * 0.5;
        
        // 距离黑洞的距离
        const distanceToBlackHole = Math.sqrt(
          (charCenterX - blackHoleX) ** 2 + (charCenterY - blackHoleY) ** 2
        );
        
        // 扭曲效果
        const maxDistance = fontSize * text.length * 0.5;
        const distortionFactor = Math.max(0, 1 - (distanceToBlackHole / maxDistance));
        const warpIntensity = progress * distortionIntensity * distortionFactor;
        
        // 拉伸变形
        const stretchX = interpolate(warpIntensity, [0, 1], [1, 0.1 + distortionFactor * 0.2]);
        const stretchY = interpolate(warpIntensity, [0, 1], [1, 2 + warpIntensity]);
        
        // 向中心移动
        const pullX = (blackHoleX - charCenterX) * warpIntensity * 0.8;
        const pullY = (blackHoleY - charCenterY) * warpIntensity * 0.8;
        
        // 透明度变化
        const opacity = interpolate(progress, [0, 0.8, 1], [1, 0.3, 0]);
        
        return (
          <span
            key={index}
            style={{
              fontSize,
              color,
              display: 'inline-block',
              margin: '0 2px',
              opacity,
              transform: `
                translate(${pullX}px, ${pullY}px)
                scaleX(${stretchX})
                scaleY(${stretchY})
              `,
              filter: `blur(${warpIntensity * 3}px)`,
              textShadow: warpIntensity > 0.5 ? 
                `0 0 ${warpIntensity * 20}px ${color}` : 'none'
            }}
          >
            {char}
          </span>
        );
      })}
    </div>
  );
};

GravityDistortionEffect.showName = "引力扭曲";

export default GravityDistortionEffect; 