import React from 'react';
import { useCurrentFrame, interpolate, useVideoConfig } from 'remotion';

interface PixelEvaporationEffectProps {
  text: string;
  style?: React.CSSProperties;
}

const PixelEvaporationEffect: React.FC<PixelEvaporationEffectProps> & { showName: string } = ({
  text,
  style
}) => {
  const frame = useCurrentFrame();
  const { fps } = useVideoConfig();
  
  const chars = text.split('');
  const duration = 0.8 * fps; // 0.8秒
  const evaporationSpeed = 120; // 蒸发速度 120px/s
  
  return (
    <div style={{
      display: 'inline-flex',
      alignItems: 'center',
      justifyContent: 'center',
      position: 'relative',
      backgroundColor: 'transparent',
      ...style
    }}>
      {/* 热浪扭曲效果 */}
      {frame > duration * 0.3 && (
        <div style={{
          position: 'absolute',
          top: -30,
          left: 0,
          right: 0,
          height: 40,
          background: `linear-gradient(180deg, 
            rgba(255,100,0,${interpolate(frame, [duration * 0.3, duration], [0.1, 0])}) 0%, 
            transparent 100%)`,
          filter: `blur(${interpolate(frame, [duration * 0.3, duration], [2, 8])}px)`,
          transform: `scaleY(${interpolate(frame, [duration * 0.3, duration], [1, 2])})`,
          pointerEvents: 'none'
        }} />
      )}
      
      {chars.map((char, index) => {
        const charDelay = index * 2;
        const charProgress = Math.max(0, Math.min(1, (frame - charDelay) / (duration * 0.8)));
        
        // 像素化进度（从底部向上）
        const pixelProgress = charProgress;
        const pixelSize = interpolate(pixelProgress, [0, 0.5, 1], [1, 4, 12]);
        
        // 蒸发高度
        const evaporationHeight = interpolate(pixelProgress, [0, 1], [0, evaporationSpeed]);
        
        // 透明度消失
        const opacity = interpolate(pixelProgress, [0, 0.7, 1], [1, 0.3, 0]);
        
        return (
          <div key={index} style={{
            position: 'relative',
            display: 'inline-block',
            margin: '0 2px'
          }}>
            {/* 像素块升腾效果 */}
            {pixelProgress > 0.2 && (
              <>
                {Array.from({ length: 8 }, (_, i) => {
                  const blockDelay = i * 3;
                  const blockProgress = Math.max(0, Math.min(1, (frame - charDelay - blockDelay) / 20));
                  const blockX = (Math.random() - 0.5) * 40;
                  const blockY = -blockProgress * 80;
                  
                  return (
                    <div
                      key={`pixel-${i}`}
                      style={{
                        position: 'absolute',
                        left: `50%`,
                        top: `100%`,
                        width: `${4 + Math.random() * 6}px`,
                        height: `${4 + Math.random() * 6}px`,
                        backgroundColor: `rgba(255,255,255,${1 - blockProgress})`,
                        transform: `translate(${blockX}px, ${blockY}px)`,
                        opacity: 1 - blockProgress,
                        pointerEvents: 'none'
                      }}
                    />
                  );
                })}
              </>
            )}
            
            {/* 蒸汽粒子 */}
            {pixelProgress > 0.4 && (
              <>
                {Array.from({ length: 6 }, (_, i) => {
                  const steamProgress = Math.max(0, (pixelProgress - 0.4) / 0.6);
                  const steamX = (Math.random() - 0.5) * 30;
                  const steamY = -steamProgress * 60 - i * 8;
                  
                  return (
                    <div
                      key={`steam-${i}`}
                      style={{
                        position: 'absolute',
                        left: '50%',
                        top: '50%',
                        width: '3px',
                        height: '8px',
                        background: `linear-gradient(180deg, 
                          rgba(200,200,255,${0.6 - steamProgress}) 0%, 
                          transparent 100%)`,
                        transform: `translate(${steamX}px, ${steamY}px)`,
                        borderRadius: '50%',
                        pointerEvents: 'none'
                      }}
                    />
                  );
                })}
              </>
            )}
            
            {/* 主文字 */}
            <span style={{
              fontSize: '60px',
              fontWeight: 'bold',
              color: 'white',
              display: 'inline-block',
              opacity: opacity,
              filter: `blur(${pixelSize * 0.3}px)`,
              transform: `translateY(${-evaporationHeight * 0.1}px)`,
              textShadow: `0 0 ${pixelSize}px rgba(255,255,255,0.5)`,
              imageRendering: pixelSize > 2 ? 'pixelated' : 'auto'
            }}>
              {char}
            </span>
          </div>
        );
      })}
    </div>
  );
};

PixelEvaporationEffect.showName = "像素蒸发";

export default PixelEvaporationEffect; 