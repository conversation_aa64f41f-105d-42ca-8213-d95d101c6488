import React from 'react';
import { interpolate, useCurrentFrame } from 'remotion';

interface FlipOutEffectProps {
  text: string | React.ReactNode[];
  style: React.CSSProperties;
  durationFrames?: number;
}

export default function FlipOutEffect({
  text = '展示文本',
  style = {},
  durationFrames = 50,
}: FlipOutEffectProps) {
  const frame = useCurrentFrame();

  // 翻转角度
  const rotateY = interpolate(
    frame,
    [0, durationFrames],
    [0, 90],
    {
      extrapolateLeft: 'clamp',
      extrapolateRight: 'clamp',
    }
  );

  // 文字透明度
  const opacity = interpolate(
    frame,
    [durationFrames - 15, durationFrames],
    [1, 0],
    {
      extrapolateLeft: 'clamp',
      extrapolateRight: 'clamp',
    }
  );

  return (
    <span
      style={{
        display: 'inline-flex',
        alignItems: 'center',
        justifyContent: 'center',
        height: '100%',
        perspective: '1000px',
      }}
    >
      <span
        style={{
          opacity: opacity,
          transform: `rotateY(${rotateY}deg)`,
          color: '#ffffff',
          textAlign: 'center',
          textShadow: '0 0 8px #ffffff',
          ...style,
        }}
      >
        {text}
      </span>
    </span>
  );
}

FlipOutEffect.key = 'FlipOutEffect';
FlipOutEffect.description = '3D flip out text effect';
FlipOutEffect.showName = '翻转出场'; 