import React from 'react';
import { useCurrentFrame, interpolate, useVideoConfig } from 'remotion';

interface PixelRetractEffectProps {
  text: string;
  style?: React.CSSProperties;
}

const PixelRetractEffect: React.FC<PixelRetractEffectProps> & { showName: string } = ({
  text,
  style
}) => {
  const frame = useCurrentFrame();
  const { fps } = useVideoConfig();
  
  const chars = text.split('');
  const duration = 0.6 * fps; // 0.6秒
  const retractStep = 8; // 收缩步长8px
  
  return (
    <div style={{
      display: 'inline-flex',
      alignItems: 'center',
      justifyContent: 'center',
      position: 'relative',
      backgroundColor: 'transparent',
      ...style
    }}>
      {chars.map((char, index) => {
        const charDelay = index * 2;
        const charProgress = Math.max(0, Math.min(1, (frame - charDelay) / (duration * 0.8)));
        
        // 像素化阶段
        const pixelPhase = interpolate(charProgress, [0, 0.4, 0.8, 1], [0, 1, 2, 3]);
        
        // 文字透明度
        const opacity = interpolate(charProgress, [0, 0.8, 1], [1, 0.2, 0]);
        
        return (
          <div key={index} style={{
            position: 'relative',
            display: 'inline-block',
            margin: '0 2px'
          }}>
            {/* 像素化边缘效果 */}
            {pixelPhase >= 0.3 && pixelPhase < 2.5 && opacity > 0 && (
              <>
                {Array.from({ length: 16 }, (_, i) => {
                  const pixelAngle = (i / 16) * Math.PI * 2;
                  const pixelDistance = 25 + Math.random() * 10;
                  const pixelX = Math.cos(pixelAngle) * pixelDistance;
                  const pixelY = Math.sin(pixelAngle) * pixelDistance;
                  
                  const pixelSize = retractStep - Math.random() * 2;
                  const pixelOpacity = interpolate(pixelPhase, [0.3, 1, 2, 2.5], [0, 0.8, 0.6, 0]) * opacity;
                  
                  return (
                    <div
                      key={`pixel-${i}`}
                      style={{
                        position: 'absolute',
                        left: '50%',
                        top: '50%',
                        width: `${pixelSize}px`,
                        height: `${pixelSize}px`,
                        background: `rgba(200,200,200,${pixelOpacity})`,
                        border: `1px solid rgba(150,150,150,${pixelOpacity * 0.5})`,
                        transform: `translate(-50%, -50%) translate(${pixelX}px, ${pixelY}px)`,
                        pointerEvents: 'none'
                      }}
                    />
                  );
                })}
              </>
            )}
            
            {/* 向中心收缩效果 */}
            {pixelPhase >= 1 && opacity > 0 && (
              <>
                {Array.from({ length: 12 }, (_, ringIndex) => {
                  const ringDelay = ringIndex * 0.1;
                  const ringProgress = Math.max(0, (pixelPhase - 1 - ringDelay) / 1.5);
                  
                  if (ringProgress <= 0) return null;
                  
                  const ringRadius = 30 - ringIndex * 2;
                  const contractedRadius = interpolate(ringProgress, [0, 1], [ringRadius, 0]);
                  
                  return Array.from({ length: 8 }, (_, pixelIndex) => {
                    const pixelAngle = (pixelIndex / 8) * Math.PI * 2;
                    const pixelX = Math.cos(pixelAngle) * contractedRadius;
                    const pixelY = Math.sin(pixelAngle) * contractedRadius;
                    
                    const pixelOpacity = interpolate(ringProgress, [0, 0.5, 1], [0.8, 0.6, 0]) * opacity;
                    const pixelSize = retractStep - ringIndex * 0.3;
                    
                    return (
                      <div
                        key={`contract-${ringIndex}-${pixelIndex}`}
                        style={{
                          position: 'absolute',
                          left: '50%',
                          top: '50%',
                          width: `${pixelSize}px`,
                          height: `${pixelSize}px`,
                          background: `rgba(180,180,180,${pixelOpacity})`,
                          border: `1px solid rgba(120,120,120,${pixelOpacity * 0.5})`,
                          transform: `translate(-50%, -50%) translate(${pixelX}px, ${pixelY}px)`,
                          pointerEvents: 'none'
                        }}
                      />
                    );
                  });
                })}
              </>
            )}
            
            {/* 扫描线效果 */}
            {pixelPhase >= 0.5 && pixelPhase < 2.5 && opacity > 0 && (
              <>
                {Array.from({ length: 5 }, (_, i) => {
                  const lineDelay = i * 0.15;
                  const lineProgress = Math.max(0, Math.min(1, (pixelPhase - 0.5 - lineDelay) / 1.5));
                  
                  const lineY = interpolate(lineProgress, [0, 1], [40, -40]);
                  const lineOpacity = interpolate(lineProgress, [0, 0.3, 0.7, 1], [0, 0.8, 0.8, 0]) * opacity;
                  const lineWidth = 60 - i * 8;
                  
                  return (
                    <div
                      key={`scanline-${i}`}
                      style={{
                        position: 'absolute',
                        left: '50%',
                        top: '50%',
                        width: `${lineWidth}px`,
                        height: '2px',
                        background: `linear-gradient(90deg, 
                          transparent 0%, 
                          rgba(100,200,100,${lineOpacity}) 50%, 
                          transparent 100%)`,
                        transform: `translate(-50%, -50%) translateY(${lineY}px)`,
                        pointerEvents: 'none',
                        boxShadow: `0 0 4px rgba(100,200,100,${lineOpacity * 0.8})`
                      }}
                    />
                  );
                })}
              </>
            )}
            
            {/* 旧电视关闭效果 */}
            {pixelPhase >= 2 && opacity > 0 && (
              <div style={{
                position: 'absolute',
                left: '50%',
                top: '50%',
                width: `${interpolate(pixelPhase, [2, 2.8], [60, 2])}px`,
                height: `${interpolate(pixelPhase, [2, 2.8], [60, 2])}px`,
                background: `radial-gradient(circle, 
                  rgba(255,255,255,${interpolate(pixelPhase, [2, 2.5, 2.8], [0, 0.8, 0]) * opacity}) 0%, 
                  rgba(200,200,255,${interpolate(pixelPhase, [2, 2.5, 2.8], [0, 0.6, 0]) * opacity}) 30%, 
                  transparent 70%)`,
                borderRadius: '50%',
                transform: 'translate(-50%, -50%)',
                pointerEvents: 'none',
                filter: 'blur(1px)'
              }} />
            )}
            
            {/* 最后光点熄灭 */}
            {pixelPhase >= 2.8 && opacity > 0 && (
              <div style={{
                position: 'absolute',
                left: '50%',
                top: '50%',
                width: '2px',
                height: '2px',
                background: `rgba(255,255,255,${interpolate(pixelPhase, [2.8, 3], [1, 0]) * opacity})`,
                borderRadius: '50%',
                transform: 'translate(-50%, -50%)',
                pointerEvents: 'none',
                boxShadow: `0 0 8px rgba(255,255,255,${interpolate(pixelPhase, [2.8, 3], [0.8, 0]) * opacity})`
              }} />
            )}
            
            {/* 像素网格效果 */}
            {pixelPhase >= 0.8 && pixelPhase < 2.2 && opacity > 0 && (
              <div style={{
                position: 'absolute',
                left: -30,
                top: -30,
                right: -30,
                bottom: -30,
                background: `
                  repeating-linear-gradient(
                    0deg,
                    rgba(100,100,100,${interpolate(pixelPhase, [0.8, 1.5, 2.2], [0, 0.3, 0]) * opacity}) 0px,
                    rgba(100,100,100,${interpolate(pixelPhase, [0.8, 1.5, 2.2], [0, 0.3, 0]) * opacity}) 1px,
                    transparent 1px,
                    transparent ${retractStep}px
                  ),
                  repeating-linear-gradient(
                    90deg,
                    rgba(100,100,100,${interpolate(pixelPhase, [0.8, 1.5, 2.2], [0, 0.3, 0]) * opacity}) 0px,
                    rgba(100,100,100,${interpolate(pixelPhase, [0.8, 1.5, 2.2], [0, 0.3, 0]) * opacity}) 1px,
                    transparent 1px,
                    transparent ${retractStep}px
                  )
                `,
                pointerEvents: 'none'
              }} />
            )}
            
            {/* 主文字 */}
            <span style={{
              fontSize: '60px',
              fontWeight: 'bold',
              color: 'white',
              display: 'inline-block',
              opacity: opacity,
              textShadow: pixelPhase >= 0.5 && pixelPhase < 2 
                ? `0 0 6px rgba(100,200,100,0.6)` 
                : 'none',
              filter: pixelPhase >= 1.5 
                ? `blur(${interpolate(pixelPhase, [1.5, 2.5], [0, 1])}px)` 
                : 'none',
              transform: pixelPhase >= 2 
                ? `scale(${interpolate(pixelPhase, [2, 3], [1, 0])})` 
                : 'scale(1)'
            }}>
              {char}
            </span>
          </div>
        );
      })}
    </div>
  );
};

PixelRetractEffect.showName = "像素撤退";

export default PixelRetractEffect; 