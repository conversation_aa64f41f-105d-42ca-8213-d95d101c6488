import React from 'react';
import { interpolate, useCurrentFrame } from 'remotion';

interface BounceOutEffectProps {
  text: string | React.ReactNode[];
  style: React.CSSProperties;
  durationFrames?: number;
}

export default function BounceOutEffect({
  text = '展示文本',
  style = {},
  durationFrames = 60,
}: BounceOutEffectProps) {
  const frame = useCurrentFrame();

  // 弹跳动画（向下弹出）
  const bounceY = interpolate(
    frame,
    [0, 15, 30, 45, 60],
    [0, -5, 10, 20, 50],
    {
      extrapolateLeft: 'clamp',
      extrapolateRight: 'clamp',
    }
  );

  // 透明度
  const opacity = interpolate(
    frame,
    [durationFrames - 15, durationFrames],
    [1, 0],
    {
      extrapolateLeft: 'clamp',
      extrapolateRight: 'clamp',
    }
  );

  return (
    <span
      style={{
        display: 'inline-flex',
        alignItems: 'center',
        justifyContent: 'center',
        height: '100%',
      }}
    >
      <span
        style={{
          opacity: opacity,
          transform: `translateY(${bounceY}px)`,
          color: '#ffffff',
          textAlign: 'center',
          textShadow: '0 0 8px #ffffff',
          ...style,
        }}
      >
        {text}
      </span>
    </span>
  );
}

BounceOutEffect.key = 'BounceOutEffect';
BounceOutEffect.description = 'bounce out text effect';
BounceOutEffect.showName = '弹跳出场'; 