import React from 'react';
import { useCurrentFrame, interpolate, useVideoConfig } from 'remotion';

interface PixelDisintegrateEffectProps {
  text: string;
  color?: string;
  fontSize?: number;
  style?: React.CSSProperties;
}

const PixelDisintegrateEffect: React.FC<PixelDisintegrateEffectProps> & { showName: string } = ({
  text = "ABCD123",
  color = "#FFFFFF",
  fontSize = 60,
  style = {}
}) => {
  const frame = useCurrentFrame();
  const { fps } = useVideoConfig();
  
  const duration = 0.8 * fps; // 0.8秒
  const progress = Math.min(1, frame / duration);
  
  // 随机函数
  const random = (seed: number) => {
    const x = Math.sin(seed * 12.9898) * 43758.5453;
    return x - Math.floor(x);
  };
  
  // 像素块大小16x16
  const pixelSize = 16;
  
  return (
    <div style={{
      display: 'inline-flex',
      alignItems: 'center',
      justifyContent: 'center',
      position: 'relative',
      ...style
    }}>
      {text.split('').map((char, index) => {
        const charProgress = Math.max(0, Math.min(1, (progress - index * 0.05) / 0.6));
        
        // 出场开始前显示完整文字
        if (charProgress === 0) {
          return (
            <span
              key={index}
              style={{
                fontSize,
                color,
                display: 'inline-block',
                margin: '0 2px'
              }}
            >
              {char}
            </span>
          );
        }
        
        // 计算字符区域的像素块数量
        const charWidth = fontSize * 0.6;
        const charHeight = fontSize;
        const pixelsX = Math.ceil(charWidth / pixelSize);
        const pixelsY = Math.ceil(charHeight / pixelSize);
        
        return (
          <div
            key={index}
            style={{
              position: 'relative',
              display: 'inline-block',
              width: charWidth,
              height: charHeight,
              margin: '0 2px'
            }}
          >
            {/* 生成像素块，向外溅射 */}
            {Array.from({ length: pixelsX * pixelsY }, (_, pixelIndex) => {
              const pixelX = pixelIndex % pixelsX;
              const pixelY = Math.floor(pixelIndex / pixelsX);
              
              // 每个像素块的随机参数
              const pixelSeed = index * 1000 + pixelIndex;
              const splashAngle = random(pixelSeed) * Math.PI * 2;
              const splashDistance = 200 * (0.5 + random(pixelSeed + 100) * 0.5);
              const rotationSpeed = (random(pixelSeed + 200) - 0.5) * 720; // 旋转角度
              
              // 溅射动画（向外飞出）
              const splashX = Math.cos(splashAngle) * splashDistance * charProgress;
              const splashY = Math.sin(splashAngle) * splashDistance * charProgress;
              const rotation = rotationSpeed * charProgress;
              
              // 像素块透明度（出场时逐渐淡出）
              const opacity = interpolate(charProgress, [0, 0.3, 1], [1, 0.8, 0]);
              
              return (
                <div
                  key={pixelIndex}
                  style={{
                    position: 'absolute',
                    left: pixelX * pixelSize,
                    top: pixelY * pixelSize,
                    width: pixelSize,
                    height: pixelSize,
                    backgroundColor: color,
                    transform: `translate(${splashX}px, ${splashY}px) rotate(${rotation}deg)`,
                    opacity,
                    // 拖尾效果
                    boxShadow: charProgress > 0.2 ? `
                      ${-splashX * 0.3}px ${-splashY * 0.3}px 0px rgba(${color === '#FFFFFF' ? '255,255,255' : '255,255,255'}, ${opacity * 0.5}),
                      ${-splashX * 0.6}px ${-splashY * 0.6}px 0px rgba(${color === '#FFFFFF' ? '255,255,255' : '255,255,255'}, ${opacity * 0.3})
                    ` : 'none'
                  }}
                />
              );
            })}
          </div>
        );
      })}
    </div>
  );
};

PixelDisintegrateEffect.showName = "像素解体";

export default PixelDisintegrateEffect; 