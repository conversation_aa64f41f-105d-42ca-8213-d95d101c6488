import React from 'react';
import { useCurrentFrame, interpolate, useVideoConfig } from 'remotion';

interface StarConstellationEffectProps {
  text: string;
  color?: string;
  fontSize?: number;
  style?: React.CSSProperties;
}

const StarConstellationEffect: React.FC<StarConstellationEffectProps> & { showName: string } = ({
  text = "ABCD123",
  color = "#FFFFFF",
  fontSize = 60,
  style = {}
}) => {
  const frame = useCurrentFrame();
  const { fps } = useVideoConfig();
  
  const duration = 1.8 * fps; // 1.8秒
  const progress = Math.min(1, frame / duration);
  
  // 24个星点
  const starCount = 24;
  
  return (
    <div style={{
      display: 'inline-flex',
      alignItems: 'center',
      justifyContent: 'center',
      position: 'relative',
      ...style
    }}>
      {text.split('').map((char, index) => {
        const charStartTime = index * 0.08; // 更快开始，减少延迟
        const charProgress = Math.max(0, Math.min(1, (progress - charStartTime) / 0.8));
        
        // 星座连线形成进度
        const constellationProgress = charProgress;
        
        // 恒星闪烁
        const twinklePhase = (frame + index * 10) % 60;
        const twinkle = Math.sin(twinklePhase * 0.2) * 0.3 + 0.7;
        
        // 星云色彩填充 - 为最后字符优化填充时机
        const nebulaStartDelay = Math.max(0, 0.4 - index * 0.05); // 最后的字符更早开始填充
        const nebulaProgress = Math.max(0, (charProgress - nebulaStartDelay) / (1 - nebulaStartDelay));
        
        // 入场完成后的循环效果
        const isComplete = progress >= 1;
        const meteorPhase = isComplete ? (frame % (fps * 4)) / fps : 0;
        const showMeteor = meteorPhase > 3 && meteorPhase < 3.5;
        
        return (
          <div
            key={index}
            style={{
              position: 'relative',
              display: 'inline-block',
              margin: '0 2px'
            }}
          >
            {/* 主文字 - 星座形成 */}
            <span
              style={{
                fontSize,
                color: 'transparent',
                display: 'inline-block',
                opacity: constellationProgress,
                WebkitTextStroke: `2px #87CEEB`,
                textShadow: `
                  0 0 10px #87CEEB,
                  0 0 20px rgba(135, 206, 235, 0.5)
                `
              }}
            >
              {char}
            </span>
            
            {/* 星云色彩填充 */}
            {nebulaProgress > 0 && (
              <span
                style={{
                  position: 'absolute',
                  left: 0,
                  top: 0,
                  fontSize,
                  color: '#DDA0DD', // 星云紫
                  display: 'inline-block',
                  opacity: nebulaProgress * 0.6,
                  clipPath: `inset(${100 - nebulaProgress * 100}% 0 0 0)`,
                  textShadow: `
                    0 0 15px #DDA0DD,
                    0 0 30px rgba(221, 160, 221, 0.4)
                  `
                }}
              >
                {char}
              </span>
            )}
            
            {/* 星点 - 形成文字轮廓 */}
            {constellationProgress > 0.2 && Array.from({ length: 6 }, (_, starIndex) => {
              const starProgress = Math.max(0, Math.min(1, 
                (constellationProgress - 0.2 - starIndex * 0.05) / 0.3));
              
              if (starProgress === 0) return null;
              
              const angle = (starIndex * 60) * (Math.PI / 180);
              const radius = fontSize * 0.4;
              const starX = Math.cos(angle) * radius;
              const starY = Math.sin(angle) * radius;
              
              return (
                <div
                  key={`star-${starIndex}`}
                  style={{
                    position: 'absolute',
                    left: starX + fontSize * 0.5,
                    top: starY + fontSize * 0.5,
                    width: 3,
                    height: 3,
                    backgroundColor: '#FFFFFF',
                    borderRadius: '50%',
                    opacity: starProgress * twinkle,
                    boxShadow: `0 0 ${twinkle * 8}px #FFFFFF`,
                    transform: `scale(${0.5 + starProgress * 0.5})`
                  }}
                />
              );
            })}
            
            {/* 星座连线 */}
            {constellationProgress > 0.4 && Array.from({ length: 3 }, (_, lineIndex) => {
              const lineProgress = Math.max(0, Math.min(1, 
                (constellationProgress - 0.4 - lineIndex * 0.1) / 0.2));
              
              if (lineProgress === 0) return null;
              
              const lineLength = fontSize * 0.6 * lineProgress;
              const lineAngle = lineIndex * 45;
              
              return (
                <div
                  key={`line-${lineIndex}`}
                  style={{
                    position: 'absolute',
                    left: fontSize * 0.5,
                    top: fontSize * 0.5,
                    width: lineLength,
                    height: 1,
                    backgroundColor: '#87CEEB',
                    opacity: 0.6,
                    transform: `rotate(${lineAngle}deg)`,
                    transformOrigin: '0 50%'
                  }}
                />
              );
            })}
            
            {/* 流星划过收尾 */}
            {showMeteor && (
              <div
                style={{
                  position: 'absolute',
                  left: -50,
                  top: -20,
                  width: 60,
                  height: 2,
                  background: `linear-gradient(90deg, 
                    transparent 0%, 
                    #FFFFFF 50%, 
                    transparent 100%)`,
                  opacity: 0.8,
                  transform: `rotate(-30deg) translateX(${(meteorPhase - 3) * 200}px)`
                }}
              />
            )}
          </div>
        );
      })}
    </div>
  );
};

StarConstellationEffect.showName = "星空浮现";

export default StarConstellationEffect; 