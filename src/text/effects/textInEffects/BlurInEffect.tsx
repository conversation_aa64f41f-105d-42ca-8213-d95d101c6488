import React from 'react';
import { interpolate, useCurrentFrame } from 'remotion';

interface BlurInEffectProps {
  text: string | React.ReactNode[];
  style: React.CSSProperties;
  durationFrames?: number;
}

export default function BlurInEffect({
  text = '展示文本',
  style = {},
  durationFrames = 50,
}: BlurInEffectProps) {
  const frame = useCurrentFrame();

  // 模糊到清晰
  const blur = interpolate(
    frame,
    [0, durationFrames],
    [10, 0],
    {
      extrapolateLeft: 'clamp',
      extrapolateRight: 'clamp',
    }
  );

  // 透明度
  const opacity = interpolate(
    frame,
    [0, 20],
    [0, 1],
    {
      extrapolateLeft: 'clamp',
      extrapolateRight: 'clamp',
    }
  );

  return (
    <span
      style={{
        display: 'inline-flex',
        alignItems: 'center',
        justifyContent: 'center',
        height: '100%',
      }}
    >
      <span
        style={{
          opacity: opacity,
          filter: `blur(${blur}px)`,
          color: '#ffffff',
          textAlign: 'center',
          textShadow: '0 0 8px #ffffff',
          ...style,
        }}
      >
        {text}
      </span>
    </span>
  );
}

BlurInEffect.key = 'BlurInEffect';
BlurInEffect.description = 'blur to focus text effect';
BlurInEffect.showName = '聚焦'; 