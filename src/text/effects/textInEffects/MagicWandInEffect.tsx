import React from 'react';
import { interpolate, useCurrentFrame } from 'remotion';

interface MagicWandInEffectProps {
  text: string | React.ReactNode[];
  style: React.CSSProperties;
  durationFrames?: number;
}

export default function MagicWandInEffect({
  text = '展示文本',
  style = {},
  durationFrames = 70,
}: MagicWandInEffectProps) {
  const frame = useCurrentFrame();

  // 文字透明度和变换
  const opacity = interpolate(
    frame,
    [20, 40],
    [0, 1],
    {
      extrapolateLeft: 'clamp',
      extrapolateRight: 'clamp',
    }
  );

  const scale = interpolate(
    frame,
    [20, 40, 60],
    [0.5, 1.2, 1],
    {
      extrapolateLeft: 'clamp',
      extrapolateRight: 'clamp',
    }
  );

  // 魔法棒轨迹
  const wandPosition = interpolate(
    frame,
    [0, 20],
    [0, 100],
    {
      extrapolateLeft: 'clamp',
      extrapolateRight: 'clamp',
    }
  );

  // 魔法粒子
  const particles = Array.from({ length: 12 }, (_, i) => {
    const particleFrame = frame - i * 3;
    if (particleFrame < 0 || particleFrame > 60) return null;
    
    const particleOpacity = interpolate(
      particleFrame,
      [0, 20, 40],
      [0, 1, 0],
      { extrapolateLeft: 'clamp', extrapolateRight: 'clamp' }
    );
    
    const angle = (i / 12) * Math.PI * 2;
    const radius = particleFrame * 1.5;
    const x = 50 + Math.cos(angle) * radius;
    const y = 50 + Math.sin(angle) * radius;
    
    return (
      <div
        key={i}
        style={{
          position: 'absolute',
          left: `${x}%`,
          top: `${y}%`,
          fontSize: '14px',
          opacity: particleOpacity,
          transform: 'translate(-50%, -50%)',
        }}
      >
        ⭐
      </div>
    );
  }).filter(Boolean);

  return (
    <span
      style={{
        display: 'inline-flex',
        alignItems: 'center',
        justifyContent: 'center',
        height: '100%',
        position: 'relative',
      }}
    >
      {/* 魔法棒 */}
      {frame < 20 && (
        <div
          style={{
            position: 'absolute',
            left: `${wandPosition}%`,
            top: '20%',
            fontSize: '24px',
            transform: 'translate(-50%, -50%) rotate(45deg)',
          }}
        >
          🪄
        </div>
      )}
      
      <span
        style={{
          opacity: opacity,
          transform: `scale(${scale})`,
          color: '#ffffff',
          textAlign: 'center',
          textShadow: '0 0 12px #ffffff',
          ...style,
        }}
      >
        {text}
      </span>
      {particles}
    </span>
  );
}

MagicWandInEffect.key = 'MagicWandInEffect';
MagicWandInEffect.description = 'magic wand text effect with stars';
MagicWandInEffect.showName = '魔法棒'; 