import React from 'react';
import { interpolate, useCurrentFrame } from 'remotion';

interface FadeInEffectProps {
  text: string | React.ReactNode[];
  style: React.CSSProperties;
  durationFrames?: number;
}

export default function FadeInEffect({
  text = '展示文本',
  style = {},
  durationFrames = 45,
}: FadeInEffectProps) {
  const frame = useCurrentFrame();

  // 淡入动画
  const opacity = interpolate(
    frame,
    [0, durationFrames],
    [0, 1],
    {
      extrapolateLeft: 'clamp',
      extrapolateRight: 'clamp',
    }
  );

  return (
    <span
      style={{
        display: 'inline-flex',
        alignItems: 'center',
        justifyContent: 'center',
        height: '100%',
      }}
    >
      <span
        style={{
          opacity: opacity,
          color: '#ffffff',
          textAlign: 'center',
          textShadow: '0 0 8px #ffffff',
          ...style,
        }}
      >
        {text}
      </span>
    </span>
  );
}

FadeInEffect.key = 'FadeInEffect';
FadeInEffect.description = 'fade in text effect';
FadeInEffect.showName = '淡入'; 