import React from 'react';
import { useCurrentFrame, interpolate, useVideoConfig } from 'remotion';

interface CalligraphyEffectProps {
  text: string;
  color?: string;
  fontSize?: number;
  style?: React.CSSProperties;
}

const CalligraphyEffect: React.FC<CalligraphyEffectProps> & { showName: string } = ({
  text = "ABCD123",
  color = "#FFFFFF",
  fontSize = 60,
  style = {}
}) => {
  const frame = useCurrentFrame();
  const { fps } = useVideoConfig();
  
  const duration = 1.5 * fps; // 1.5秒
  const chars = text.split('');
  const charDuration = duration / chars.length;
  
  // 生成噪声函数用于飞白效果
  const noise = (x: number, y: number) => {
    const n = Math.sin(x * 12.9898 + y * 78.233) * 43758.5453;
    return n - Math.floor(n);
  };
  
  return (
    <div style={{
      display: 'inline-flex',
      alignItems: 'center',
      justifyContent: 'center',
      position: 'relative',
      ...style
    }}>
      {chars.map((char, index) => {
        const charStartFrame = index * charDuration;
        const charEndFrame = charStartFrame + charDuration;
        const charProgress = Math.max(0, Math.min(1, (frame - charStartFrame) / charDuration));
        
        // 笔画绘制进度
        const strokeProgress = charProgress;
        
        // 墨迹扩散效果
        const inkSpread = interpolate(charProgress, [0.8, 1], [0, 1]);
        
        // 飞白效果强度
        const flyWhiteIntensity = Math.sin(frame * 0.3 + index) * 0.1 + 0.1;
        
        return (
          <div
            key={index}
            style={{
              position: 'relative',
              display: 'inline-block',
              margin: '0 2px'
            }}
          >
            {/* 主要笔画 */}
            <span
              style={{
                fontSize,
                color: frame >= charStartFrame ? color : 'transparent',
                display: 'inline-block',
                                 position: 'relative',
                 opacity: strokeProgress,
                 filter: `
                   drop-shadow(0 0 ${inkSpread * 2}px rgba(0,0,0,0.3))
                   contrast(${1 + flyWhiteIntensity})
                 `
              }}
            >
              {char}
            </span>
            
            {/* 笔尖位置指示器 */}
            {strokeProgress > 0 && strokeProgress < 1 && (
              <div
                style={{
                  position: 'absolute',
                  left: `${strokeProgress * 100}%`,
                  top: `${50 + Math.sin(strokeProgress * Math.PI * 2) * 20}%`,
                  width: 4,
                  height: 4,
                  borderRadius: '50%',
                  backgroundColor: color,
                  opacity: 0.6,
                  transform: 'translate(-50%, -50%)'
                }}
              />
            )}
            
            {/* 墨迹扩散粒子 */}
            {inkSpread > 0 && Array.from({ length: 8 }, (_, i) => {
              const angle = (i / 8) * Math.PI * 2;
              const distance = inkSpread * 20;
              const particleX = Math.cos(angle) * distance;
              const particleY = Math.sin(angle) * distance;
              const particleSize = (1 - i / 8) * 3;
              
              return (
                <div
                  key={`ink-${i}`}
                  style={{
                    position: 'absolute',
                    left: '50%',
                    top: '50%',
                    width: particleSize,
                    height: particleSize,
                    borderRadius: '50%',
                    backgroundColor: color,
                    opacity: (1 - inkSpread) * 0.5,
                    transform: `translate(${particleX}px, ${particleY}px)`,
                    pointerEvents: 'none'
                  }}
                />
              );
            })}
            
            
          </div>
        );
      })}
      

      

    </div>
  );
};

CalligraphyEffect.showName = "书法笔触";

export default CalligraphyEffect; 