import React from 'react';
import { useCurrentFrame, interpolate, useVideoConfig } from 'remotion';

interface TimeRewindEffectProps {
  text: string;
  color?: string;
  fontSize?: number;
  style?: React.CSSProperties;
}

const TimeRewindEffect: React.FC<TimeRewindEffectProps> & { showName: string } = ({
  text = "ABCD123",
  color = "#FFFFFF",
  fontSize = 60,
  style = {}
}) => {
  const frame = useCurrentFrame();
  const { fps } = useVideoConfig();
  
  const duration = 1.3 * fps; // 1.3秒
  const progress = Math.min(1, frame / duration);
  
  // 倒流速度1.2x
  const rewindSpeed = 1.2;
  
  return (
    <div style={{
      display: 'inline-flex',
      alignItems: 'center',
      justifyContent: 'center',
      position: 'relative',
      ...style
    }}>
      {text.split('').map((char, index) => {
        const charStartTime = index * 0.08;
        const charProgress = Math.max(0, Math.min(1, (progress - charStartTime) / 0.8));
        
        // 时光倒流的逆向进度（从分解到完整）
        const rewindProgress = charProgress * rewindSpeed;
        
        // 建筑拆除效果的逆向 - 从散落到聚合
        const assemblyPhase = Math.min(1, rewindProgress / 0.6);
        const materialPhase = Math.max(0, (rewindProgress - 0.6) / 0.4);
        
        // 粒子倒流轨迹
        const particleCount = 8;
        const particleRadius = fontSize * (1 - assemblyPhase) * 2;
        
        // 时间扭曲视觉效果
        const timeWarp = Math.sin(frame * 0.3 + index) * (1 - materialPhase) * 3;
        
        // 入场完成后的循环效果
        const isComplete = progress >= 1;
        const loopPhase = isComplete ? (frame % (fps * 3)) / fps : 0;
        const timeFlicker = loopPhase > 2.5 && frame % 15 < 3; // 时间残影
        
        return (
          <div
            key={index}
            style={{
              position: 'relative',
              display: 'inline-block',
              margin: '0 2px'
            }}
          >
            {/* 主文字 - 从原始材料重构 */}
            <span
              style={{
                fontSize,
                color: materialPhase > 0.5 ? color : '#8B4513', // 原始材料色 -> 最终颜色
                display: 'inline-block',
                opacity: interpolate(materialPhase, [0, 0.3, 1], [0.4, 0.7, 1]),
                transform: `
                  translateX(${timeWarp}px)
                  scale(${interpolate(materialPhase, [0, 0.5, 1], [0.6, 1.2, 1])})
                `,
                filter: materialPhase < 0.8 ? 
                  `blur(${(1 - materialPhase) * 4}px) 
                   sepia(${(1 - materialPhase) * 0.8})` : 
                  (timeFlicker ? 'brightness(1.5)' : 'none'),
                textShadow: materialPhase > 0.6 ? 
                  `0 0 10px ${color}` : 'none'
              }}
            >
              {char}
            </span>
            
            {/* 粒子倒流轨迹 */}
            {assemblyPhase < 1 && Array.from({ length: particleCount }, (_, particleIndex) => {
              const angle = (particleIndex * 45) * (Math.PI / 180);
              const particleX = Math.cos(angle) * particleRadius;
              const particleY = Math.sin(angle) * particleRadius;
              
              const particleProgress = Math.max(0, Math.min(1, 
                (assemblyPhase - particleIndex * 0.05) / 0.3));
              
              if (particleProgress === 0) return null;
              
              return (
                <div
                  key={`particle-${particleIndex}`}
                  style={{
                    position: 'absolute',
                    left: particleX + fontSize * 0.5,
                    top: particleY + fontSize * 0.5,
                    width: 3,
                    height: 3,
                    backgroundColor: '#8B4513',
                    borderRadius: '50%',
                    opacity: (1 - particleProgress) * 0.8,
                    transform: `scale(${1 - particleProgress * 0.5})`,
                    // 倒流运动轨迹
                    boxShadow: `0 0 ${(1 - particleProgress) * 8}px #8B4513`
                  }}
                />
              );
            })}
            
            {/* 建筑拆除的逆向效果 - 结构重组 */}
            {assemblyPhase > 0.3 && assemblyPhase < 0.9 && Array.from({ length: 3 }, (_, structIndex) => {
              const structProgress = Math.max(0, Math.min(1, 
                (assemblyPhase - 0.3 - structIndex * 0.1) / 0.3));
              
              if (structProgress === 0) return null;
              
              const structY = [fontSize * 0.2, fontSize * 0.5, fontSize * 0.8][structIndex];
              
              return (
                <div
                  key={`struct-${structIndex}`}
                  style={{
                    position: 'absolute',
                    left: '10%',
                    top: structY,
                    width: `${structProgress * 80}%`,
                    height: 2,
                    backgroundColor: '#696969',
                    opacity: structProgress * 0.6,
                    transform: `scaleX(${structProgress})`
                  }}
                />
              );
            })}
            
            {/* 时间涟漪效果 */}
            {materialPhase > 0.8 && (
              <div
                style={{
                  position: 'absolute',
                  left: '50%',
                  top: '50%',
                  width: fontSize * 1.5,
                  height: fontSize * 1.5,
                  border: `1px solid ${color}`,
                  borderRadius: '50%',
                  opacity: 0.3,
                  transform: `translate(-50%, -50%) scale(${Math.sin(frame * 0.2) * 0.2 + 1})`,
                  pointerEvents: 'none'
                }}
              />
            )}
          </div>
        );
      })}
    </div>
  );
};

TimeRewindEffect.showName = "时光倒流";

export default TimeRewindEffect; 