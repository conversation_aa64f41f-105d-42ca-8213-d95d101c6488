import React from 'react';
import { interpolate, useCurrentFrame } from 'remotion';

interface ElasticInEffectProps {
  text: string | React.ReactNode[];
  style: React.CSSProperties;
  durationFrames?: number;
}

export default function ElasticInEffect({
  text = '展示文本',
  style = {},
  durationFrames = 80,
}: ElasticInEffectProps) {
  const frame = useCurrentFrame();

  // 弹性动画
  const elasticScale = interpolate(
    frame,
    [0, 20, 40, 60, 80],
    [0, 1.3, 0.9, 1.1, 1],
    {
      extrapolateLeft: 'clamp',
      extrapolateRight: 'clamp',
    }
  );

  // 透明度
  const opacity = interpolate(
    frame,
    [0, 20],
    [0, 1],
    {
      extrapolateLeft: 'clamp',
      extrapolateRight: 'clamp',
    }
  );

  return (
    <span
      style={{
        display: 'inline-flex',
        alignItems: 'center',
        justifyContent: 'center',
        height: '100%',
      }}
    >
      <span
        style={{
          opacity: opacity,
          transform: `scale(${elasticScale})`,
          color: '#ffffff',
          textAlign: 'center',
          textShadow: '0 0 8px #ffffff',
          ...style,
        }}
      >
        {text}
      </span>
    </span>
  );
}

ElasticInEffect.key = 'ElasticInEffect';
ElasticInEffect.description = 'elastic text effect';
ElasticInEffect.showName = '弹性'; 