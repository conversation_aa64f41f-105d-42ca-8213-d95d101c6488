import React from 'react';
import { useCurrentFrame, interpolate, useVideoConfig } from 'remotion';

interface CellFusionEffectProps {
  text: string;
  color?: string;
  fontSize?: number;
  style?: React.CSSProperties;
}

const CellFusionEffect: React.FC<CellFusionEffectProps> & { showName: string } = ({
  text = "ABCD123",
  color = "#FFFFFF",
  fontSize = 60,
  style = {}
}) => {
  const frame = useCurrentFrame();
  const { fps } = useVideoConfig();
  
  const chars = text.split('');
  const duration = 1.6 * fps; // 1.6秒
  const halfLength = Math.ceil(chars.length / 2);
  const topChars = chars.slice(0, halfLength);
  const bottomChars = chars.slice(halfLength);
  
  const fusionTemperature = 37; // 融合温度37℃
  
  return (
    <div style={{
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      position: 'relative',
      backgroundColor: 'transparent',
      ...style
    }}>
      {/* 上半文字 - 细胞下探 */}
      <div style={{
        display: 'flex',
        position: 'relative',
        marginBottom: '4px'
      }}>
        {topChars.map((char, index) => {
          const charDelay = index * 4;
          const charProgress = Math.max(0, Math.min(1, (frame - charDelay) / (duration * 0.7)));
          
          // 细胞膜伸缩
          const membraneY = interpolate(charProgress, [0, 0.6, 1], [-fontSize * 1.2, 0, 0]);
          const pseudopodExtension = Math.sin(frame * 0.3 + index) * 5 * charProgress;
          
          return (
            <div key={`top-${index}`} style={{ position: 'relative' }}>
              {/* 细胞膜轮廓 */}
              <div style={{
                position: 'absolute',
                left: '50%',
                top: membraneY - 10,
                transform: 'translateX(-50%)',
                width: fontSize * 0.8,
                height: fontSize * 0.8,
                border: '2px solid rgba(0,255,128,0.4)',
                borderRadius: '50%',
                opacity: charProgress > 0 ? 0.6 : 0,
                pointerEvents: 'none'
              }} />
              
              {/* 伪足 */}
              {Array.from({ length: 4 }, (_, i) => (
                <div
                  key={`pseudopod-${i}`}
                  style={{
                    position: 'absolute',
                    left: '50%',
                    top: membraneY + fontSize * 0.3,
                    width: '3px',
                    height: 15 + pseudopodExtension,
                    background: `linear-gradient(180deg, 
                      rgba(0,255,128,0.6) 0%, 
                      transparent 100%)`,
                    transform: `translateX(-50%) rotate(${(i - 1.5) * 20}deg)`,
                    transformOrigin: 'top center',
                    opacity: charProgress > 0.3 ? 1 : 0,
                    pointerEvents: 'none'
                  }}
                />
              ))}
              
              {/* 细胞核 */}
              <div style={{
                position: 'absolute',
                left: '50%',
                top: membraneY - 5,
                transform: 'translateX(-50%)',
                width: '8px',
                height: '8px',
                backgroundColor: 'rgba(255,100,100,0.8)',
                borderRadius: '50%',
                opacity: charProgress > 0 ? 1 : 0,
                pointerEvents: 'none'
              }} />
              
              <span style={{
                fontSize,
                color,
                transform: `translateY(${membraneY}px)`,
                display: 'inline-block',
                margin: '0 2px',
                opacity: charProgress > 0 ? 0.8 : 0,
                filter: `drop-shadow(0 0 3px rgba(0,255,128,0.3))`
              }}>
                {char}
              </span>
            </div>
          );
        })}
      </div>
      
      {/* 下半文字 - 细胞上迎 */}
      <div style={{
        display: 'flex',
        position: 'relative'
      }}>
        {bottomChars.map((char, index) => {
          const charDelay = index * 4;
          const charProgress = Math.max(0, Math.min(1, (frame - charDelay) / (duration * 0.7)));
          
          // 细胞膜上升
          const membraneY = interpolate(charProgress, [0, 0.6, 1], [fontSize * 1.2, 0, 0]);
          
          return (
            <div key={`bottom-${index}`} style={{ position: 'relative' }}>
              {/* 细胞膜轮廓 */}
              <div style={{
                position: 'absolute',
                left: '50%',
                top: membraneY + 10,
                transform: 'translateX(-50%)',
                width: fontSize * 0.8,
                height: fontSize * 0.8,
                border: '2px solid rgba(128,0,255,0.4)',
                borderRadius: '50%',
                opacity: charProgress > 0 ? 0.6 : 0,
                pointerEvents: 'none'
              }} />
              
              {/* 细胞核 */}
              <div style={{
                position: 'absolute',
                left: '50%',
                top: membraneY + fontSize + 5,
                transform: 'translateX(-50%)',
                width: '8px',
                height: '8px',
                backgroundColor: 'rgba(100,100,255,0.8)',
                borderRadius: '50%',
                opacity: charProgress > 0 ? 1 : 0,
                pointerEvents: 'none'
              }} />
              
              <span style={{
                fontSize,
                color,
                transform: `translateY(${membraneY}px)`,
                display: 'inline-block',
                margin: '0 2px',
                opacity: charProgress > 0 ? 0.8 : 0,
                filter: `drop-shadow(0 0 3px rgba(128,0,255,0.3))`
              }}>
                {char}
              </span>
            </div>
          );
        })}
      </div>
      
      {/* 细胞膜融合效果 */}
      {frame > duration * 0.6 && frame < duration * 0.9 && (
        <div style={{
          position: 'absolute',
          left: '50%',
          top: '50%',
          transform: 'translate(-50%, -50%)',
          pointerEvents: 'none'
        }}>
          {/* 脂质双分子层波动 */}
          {Array.from({ length: 6 }, (_, i) => (
            <div
              key={`lipid-${i}`}
              style={{
                position: 'absolute',
                left: (i - 2.5) * 8,
                top: 0,
                width: '4px',
                height: '2px',
                background: `linear-gradient(90deg, 
                  rgba(255,255,0,0.6) 0%, 
                  rgba(255,128,0,0.6) 100%)`,
                transform: `translateY(${Math.sin(frame * 0.5 + i) * 3}px)`,
                borderRadius: '1px'
              }}
            />
          ))}
          
          {/* 细胞质混合 */}
          <div style={{
            position: 'absolute',
            left: -15,
            top: -15,
            width: '30px',
            height: '30px',
            background: `radial-gradient(circle, 
              rgba(255,255,255,0.2) 0%, 
              rgba(128,255,128,0.1) 50%, 
              transparent 100%)`,
            borderRadius: '50%',
            opacity: interpolate(frame, [duration * 0.6, duration * 0.8], [0, 1])
          }} />
        </div>
      )}
      

    </div>
  );
};

CellFusionEffect.showName = "细胞融合";

export default CellFusionEffect; 