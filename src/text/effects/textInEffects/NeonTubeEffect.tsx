import React from 'react';
import { useCurrentFrame, interpolate, useVideoConfig } from 'remotion';

interface NeonTubeEffectProps {
  text: string;
  color?: string;
  fontSize?: number;
  style?: React.CSSProperties;
}

const NeonTubeEffect: React.FC<NeonTubeEffectProps> & { showName: string } = ({
  text = "ABCD123",
  color = "#FFFFFF",
  fontSize = 60,
  style = {}
}) => {
  const frame = useCurrentFrame();
  const { fps } = useVideoConfig();
  
  const duration = 1 * fps; // 1秒
  const progress = Math.min(1, frame / duration);
  const chars = text.split('');
  
  // 点亮进度
  const lightingProgress = interpolate(progress, [0, 0.8], [0, chars.length]);
  
  // 完全点亮后的闪烁效果
  const blinkPhase = progress > 0.8 ? (frame - duration * 0.8) : 0;
  const blink1 = blinkPhase > 0 && blinkPhase < 3 ? Math.abs(Math.sin(blinkPhase * 2)) : 1;
  const blink2 = blinkPhase > 10 && blinkPhase < 13 ? Math.abs(Math.sin(blinkPhase * 2)) : 1;
  const finalBrightness = Math.min(blink1, blink2);
  
  return (
    <div style={{
      display: 'inline-flex',
      alignItems: 'center',
      justifyContent: 'center',
      position: 'relative',
      gap: '4px', // 增加字符间距
      ...style
    }}>
      {chars.map((char, index) => {
        const isLit = lightingProgress > index;
        const lightIntensity = isLit ? 
          (progress > 0.8 ? finalBrightness : 1) : 0;
        
        return (
          <div
            key={index}
            style={{
              position: 'relative',
              display: 'inline-block',
              minWidth: `${fontSize * 0.6}px`, // 确保每个字符有足够的空间
              textAlign: 'center'
            }}
          >
            {/* 暗色轮廓 - 仅在未点亮时显示 */}
            {!isLit && (
              <span
                style={{
                  fontSize,
                  fontWeight: 'bold',
                  color: '#333',
                  display: 'inline-block',
                  opacity: 0.3,
                  width: '100%'
                }}
              >
                {char}
              </span>
            )}
            
            {/* 霓虹发光文字 */}
            {isLit && (
              <span
                style={{
                  position: isLit && !isLit ? 'absolute' : 'relative',
                  top: 0,
                  left: 0,
                  right: 0,
                  fontSize,
                  fontWeight: 'bold',
                  color: '#00FFFF',
                  display: 'inline-block',
                  textShadow: `
                    0 0 5px #00FFFF,
                    0 0 10px #00FFFF,
                    0 0 15px #00FFFF,
                    0 0 20px #00FFFF,
                    0 0 25px #00FFFF
                  `,
                  opacity: lightIntensity,
                  filter: `brightness(${1 + lightIntensity * 0.5})`,
                  width: '100%',
                  textAlign: 'center'
                }}
              >
                {char}
              </span>
            )}
            
            {/* 连接点电火花 - 随机闪烁 */}
            {isLit && index < chars.length - 1 && lightingProgress > index + 0.5 && frame % 6 < 2 && (
              <div
                style={{
                  position: 'absolute',
                  right: -2,
                  top: '50%',
                  width: 4,
                  height: 1,
                  background: '#00FFFF',
                  opacity: 0.8,
                  boxShadow: '0 0 6px #00FFFF',
                  transform: 'translateY(-50%)',
                  borderRadius: '1px'
                }}
              />
            )}
          </div>
        );
      })}
      
      {/* 背景光晕扩散 */}
      {progress > 0.8 && (
        <div
          style={{
            position: 'absolute',
            left: -30,
            right: -30,
            top: -30,
            bottom: -30,
            background: `radial-gradient(ellipse, 
              rgba(0, 255, 255, ${finalBrightness * 0.1}) 0%, 
              transparent 70%)`,
            pointerEvents: 'none',
            borderRadius: '50%'
          }}
        />
      )}
    </div>
  );
};

NeonTubeEffect.showName = "霓虹灯管";

export default NeonTubeEffect; 