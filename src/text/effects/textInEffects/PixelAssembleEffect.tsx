import React from 'react';
import { useCurrentFrame, interpolate, useVideoConfig } from 'remotion';

interface PixelAssembleEffectProps {
  text: string;
  color?: string;
  fontSize?: number;
  style?: React.CSSProperties;
}

const PixelAssembleEffect: React.FC<PixelAssembleEffectProps> & { showName: string } = ({
  text = "ABCD123",
  color = "#FFFFFF",
  fontSize = 60,
  style = {}
}) => {
  const frame = useCurrentFrame();
  const { fps } = useVideoConfig();
  
  const duration = 1.0 * fps; // 1秒入场动画
  const progress = Math.min(1, frame / duration);
  
  // 3个组装阶梯：噪点 -> 4bit -> 8bit -> 16bit (正常)
  const getPixelSize = () => {
    if (progress < 0.25) return 12; // 噪点
    if (progress < 0.5) return 8;   // 4bit
    if (progress < 0.75) return 4;  // 8bit
    return 0; // 正常清晰度
  };
  
  const pixelSize = getPixelSize();
  
  return (
    <div style={{
      display: 'inline-flex',
      alignItems: 'center',
      justifyContent: 'center',
      position: 'relative',
      ...style
    }}>
      {text.split('').map((char, index) => {
        const charProgress = Math.max(0, Math.min(1, (progress - index * 0.05) / 0.8));
        
        // 入场完成后的循环效果
        const isEntranceComplete = progress >= 1;
        const loopPhase = isEntranceComplete ? (frame % (fps * 3)) / fps : 0;
        const loopGlitch = loopPhase > 2.5 && frame % 30 < 5; // 偶尔闪烁
        
        return (
          <span
            key={index}
            style={{
              fontSize,
              color,
              display: 'inline-block',
              margin: '0 2px',
              opacity: interpolate(charProgress, [0, 0.2, 1], [0, 0.5, 1]),
              filter: pixelSize > 0 || loopGlitch ? 
                `blur(${(pixelSize * 0.3) + (loopGlitch ? 2 : 0)}px) 
                 contrast(${1 + pixelSize * 0.1}) 
                 saturate(${Math.max(0, 1 - pixelSize * 0.1)})` : 'none',
              transform: `
                scale(${interpolate(charProgress, [0, 0.5, 1], [0.8, 1.1, 1])})
                rotate(${Math.sin(frame * 0.5) * charProgress * 2}deg)
              `,
              // 组装过程的抖动效果
              textShadow: pixelSize > 0 ? `
                ${Math.sin(frame * 0.3) * pixelSize * 0.5}px 
                ${Math.cos(frame * 0.3) * pixelSize * 0.5}px 
                0 ${color}
              ` : 'none'
            }}
          >
            {char}
          </span>
        );
      })}
    </div>
  );
};

PixelAssembleEffect.showName = "像素组装";

export default PixelAssembleEffect; 