import React from 'react';
import { interpolate, useCurrentFrame } from 'remotion';

interface SwingInEffectProps {
  text: string | React.ReactNode[];
  style: React.CSSProperties;
  durationFrames?: number;
}

export default function SwingInEffect({
  text = '展示文本',
  style = {},
  durationFrames = 80,
}: SwingInEffectProps) {
  const frame = useCurrentFrame();

  // 摆动动画
  const swing = interpolate(
    frame,
    [0, 20, 40, 60, 80],
    [15, -10, 6, -3, 0],
    {
      extrapolateLeft: 'clamp',
      extrapolateRight: 'clamp',
    }
  );

  // 透明度
  const opacity = interpolate(
    frame,
    [0, 20],
    [0, 1],
    {
      extrapolateLeft: 'clamp',
      extrapolateRight: 'clamp',
    }
  );

  return (
    <span
      style={{
        display: 'inline-flex',
        alignItems: 'center',
        justifyContent: 'center',
        height: '100%',
      }}
    >
      <span
        style={{
          opacity: opacity,
          transform: `rotate(${swing}deg)`,
          transformOrigin: 'top center',
          color: '#ffffff',
          textAlign: 'center',
          textShadow: '0 0 8px #ffffff',
          ...style,
        }}
      >
        {text}
      </span>
    </span>
  );
}

SwingInEffect.key = 'SwingInEffect';
SwingInEffect.description = 'swing text effect';
SwingInEffect.showName = '摆动'; 