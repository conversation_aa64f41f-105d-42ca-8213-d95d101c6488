import React, { useRef, useEffect, useState } from 'react';
import { interpolate, useCurrentFrame, useCurrentScale } from 'remotion';

interface StarJumpEffectProps {
  text: string | React.ReactNode[];
  style: React.CSSProperties;
  durationFrames: number;
  starSizeRatio?: number; // 星星相对于字符的大小比例
  compressionRatio?: number; // 压缩比例
  lineDelay?: number; // 多行时每行之间的延迟帧数
}

interface CharPosition {
  x: number;
  y: number;
  width: number;
  height: number;
  index: number;
  char: string | React.ReactNode;
}

interface LineInfo {
  chars: CharPosition[];
  y: number;
  startX: number;
  endX: number;
}

export default function StarJumpEffect({
  text = '展示文本',
  style = {},
  durationFrames = 120, // 默认8秒循环
  starSizeRatio = 0.6, // 星星大小为字符大小的60%
  compressionRatio = 0.7, // 压缩到70%
  lineDelay = 20, // 每行延迟20帧开始
}: StarJumpEffectProps) {
  const frame = useCurrentFrame();
  const scale = useCurrentScale();
  const containerRef = useRef<HTMLDivElement>(null);
  const [charPositions, setCharPositions] = useState<CharPosition[]>([]);
  const [lines, setLines] = useState<LineInfo[]>([]);
  const [containerWidth, setContainerWidth] = useState(0);

  // 将text转换为字符数组
  const characters = typeof text === 'string' ? text.split('') : text;

  useEffect(() => {
    if (containerRef.current) {
      const container = containerRef.current;
      const containerRect = container.getBoundingClientRect();
      setContainerWidth(containerRect.width / scale);

      // 获取每个字符的位置和大小
      const positions: CharPosition[] = [];
      const charElements = container.querySelectorAll('.char-element');
      
      charElements.forEach((element, index) => {
        const rect = element.getBoundingClientRect();
        const containerRect = container.getBoundingClientRect();
        positions.push({
          x: (rect.left - containerRect.left + rect.width / 2) / scale,
          y: (rect.top - containerRect.top + rect.height / 2) / scale,
          width: rect.width / scale,
          height: rect.height / scale,
          index,
          char: characters[index],
        });
      });

      setCharPositions(positions);

      // 计算平均字符大小，用于后续动态计算
      const avgCharHeight = positions.length > 0 ? 
        positions.reduce((sum, pos) => sum + pos.height, 0) / positions.length : 48;

      // 按Y坐标分组，识别不同的行
      const groupedByLine: LineInfo[] = [];
      positions.forEach((pos) => {
        // Y坐标容差基于字符高度计算
        const tolerance = avgCharHeight * 0.2; // 字符高度的20%作为容差
        let existingLine = groupedByLine.find(line => 
          Math.abs(line.y - pos.y) < tolerance
        );

        if (existingLine) {
          existingLine.chars.push(pos);
        } else {
          groupedByLine.push({
            chars: [pos],
            y: pos.y,
            startX: -50,
            endX: (containerWidth || 800) + 50,
          });
        }
      });

      // 按Y坐标排序行，并为每行计算范围
      groupedByLine.sort((a, b) => a.y - b.y);
      groupedByLine.forEach(line => {
        line.chars.sort((a, b) => a.x - b.x); // 每行内按X坐标排序
        if (line.chars.length > 0) {
          const minX = Math.min(...line.chars.map(c => c.x));
          const maxX = Math.max(...line.chars.map(c => c.x));
          const avgCharHeight = line.chars.reduce((sum, c) => sum + c.height, 0) / line.chars.length;
          // 起始和结束位置扩展距离基于字符高度计算，保持相对比例一致
          const extension = avgCharHeight * 1.5; // 扩展距离：字符高度的1.5倍
          line.startX = minX - extension;
          line.endX = maxX + extension;
        }
      });

      setLines(groupedByLine);
    }
  }, [text, style.fontSize, containerWidth]);

  // 计算星星大小 - 基于字符大小
  const getStarSize = () => {
    if (charPositions.length === 0) return 20;
    
    const firstChar = charPositions.find(pos => pos.width > 0 && pos.height > 0);
    if (!firstChar) return 20;
    
    const charSize = Math.max(firstChar.height, firstChar.width);
    return Math.max(charSize * starSizeRatio, charSize * 0.15); // 最小为字符大小的15%
  };

  const starSize = getStarSize();

  // 为每行创建星星动画
  const renderStarsForLines = () => {
    return lines.map((line, lineIndex) => {
      // 根据这行的字符数量调整动画持续时间
      const lineCharCount = line.chars.length;
      const baseDurationPerChar = durationFrames / Math.max(...lines.map(l => l.chars.length));
      const lineDuration = Math.max(baseDurationPerChar * lineCharCount, durationFrames * 0.5);

      // 计算这行星星的动画进度（考虑延迟）
      const lineStartFrame = lineIndex * lineDelay;
      const adjustedFrame = Math.max(0, frame - lineStartFrame);
      const cycleProgress = adjustedFrame % lineDuration;
      const normalizedProgress = cycleProgress / lineDuration;

      // 如果还没到这行的开始时间，不显示星星
      if (frame < lineStartFrame) {
        return null;
      }

      // 星星水平位置
      const starX = interpolate(normalizedProgress, [0, 1], [line.startX, line.endX]);

      // 统一基于字符高度的相对计算
      const charHeight = Math.max(line.chars[0]?.height || 48, 48);
      
      // 星星垂直位置（波浪效果）- 全部基于字符高度的相对比例
      const baseY = line.y - charHeight / 2 - charHeight * 0.12; // 星星基准位置：字符高度的12%
      const waveAmplitude = charHeight * 0.15; // 波浪幅度：字符高度的15%
      const waveFrequency = Math.max(line.chars.length * 1.2, 2);
      const wavePhase = normalizedProgress * waveFrequency * Math.PI * 2;
      const waveY = Math.sin(wavePhase) * waveAmplitude;
      const starY = baseY + waveY;

      // 星星旋转 - 使用行特定的时长
      const starRotation = (adjustedFrame * 360 / lineDuration * 2) % 360;

      // 找到最接近的字符（仅此行）
      let nearestCharIndex = -1;
      let minDistance = Infinity;
      line.chars.forEach((char) => {
        const distance = Math.abs(starX - char.x);
        if (distance < minDistance) {
          minDistance = distance;
          nearestCharIndex = char.index;
        }
      });

      return (
        <React.Fragment key={lineIndex}>
          {/* 主星星 */}
          <div
            style={{
              position: 'absolute',
              left: `${starX}px`,
              top: `${starY}px`,
              width: `${starSize}px`,
              height: `${starSize}px`,
              transform: 'translate(-50%, -50%)',
              zIndex: 10,
              filter: `drop-shadow(0 0 ${charHeight * 0.08}px ${style.color || '#fff'})`, // 发光大小：字符高度的8%
            }}
          >
            <svg
              width={starSize}
              height={starSize}
              viewBox="0 0 24 24"
              fill={style.color || '#fff'}
              style={{
                transform: `rotate(${starRotation}deg)`,
              }}
            >
              <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z" />
            </svg>
          </div>

          {/* 星星轨迹效果 */}
          <div
            style={{
              position: 'absolute',
              left: `${starX}px`,
              top: `${starY + charHeight * 0.05}px`, // 轨迹偏移：字符高度的5%
              width: `${starSize * 0.6}px`,
              height: `${starSize * 0.6}px`,
              transform: 'translate(-50%, -50%)',
              opacity: 0.3,
              filter: `blur(${charHeight * 0.02}px)`, // 模糊程度：字符高度的2%
              zIndex: 9,
            }}
          >
            <svg
              width={starSize * 0.6}
              height={starSize * 0.6}
              viewBox="0 0 24 24"
              fill={style.color || '#fff'}
              style={{
                transform: `rotate(${starRotation * 0.5}deg)`,
              }}
            >
              <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z" />
            </svg>
          </div>
        </React.Fragment>
      );
    });
  };

  // 计算字符压缩效果
  const getCharScale = (charIndex: number) => {
    // 找到这个字符属于哪一行
    const charLine = lines.find(line => 
      line.chars.some(char => char.index === charIndex)
    );
    
    if (!charLine) return 1;

    const lineIndex = lines.indexOf(charLine);
    const lineStartFrame = lineIndex * lineDelay;
    const adjustedFrame = Math.max(0, frame - lineStartFrame);

    if (frame < lineStartFrame) return 1;

    // 使用行特定的持续时间
    const lineCharCount = charLine.chars.length;
    const baseDurationPerChar = durationFrames / Math.max(...lines.map(l => l.chars.length));
    const lineDuration = Math.max(baseDurationPerChar * lineCharCount, durationFrames * 0.5);

    const cycleProgress = adjustedFrame % lineDuration;
    const normalizedProgress = cycleProgress / lineDuration;

    // 计算这行星星的位置
    const starX = interpolate(normalizedProgress, [0, 1], [charLine.startX, charLine.endX]);
    
    // 找到字符位置
    const char = charPositions[charIndex];
    if (!char) return 1;

    const distance = Math.abs(starX - char.x);
    // 星星影响范围基于字符高度计算，保持一致的相对比例
    const charHeight = Math.max(char.height, 48);
    const maxDistance = charHeight * 0.8; // 影响范围：字符高度的80%

    if (distance < maxDistance) {
      const proximityRatio = 1 - (distance / maxDistance);
      return interpolate(proximityRatio, [0, 1], [1, compressionRatio]);
    }

    return 1;
  };

  return (
    <span
      style={{
        display: 'inline-flex',
        alignItems: 'center',
        justifyContent: 'center',
        height: '100%',
        position: 'relative',
        overflow: 'hidden',
      }}
    >
      {/* 渲染所有行的星星 */}
      {renderStarsForLines()}

      {/* 文字容器 */}
      <span
        ref={containerRef}
        style={{
          display: 'inline-flex',
          alignItems: 'center',
          justifyContent: 'center',
          flexWrap: 'wrap',
          position: 'relative',
        }}
      >
        {characters.map((char, index) => (
          <span
            key={index}
            className="char-element"
            style={{
              display: 'inline-block',
              transform: `scaleY(${getCharScale(index)})`,
              transformOrigin: 'bottom center',
              transition: 'transform 0.1s ease-out',
              marginRight: typeof char === 'string' && char === ' ' ? '0.5em' : undefined,
              ...style,
            }}
          >
            {char}
          </span>
        ))}
      </span>
    </span>
  );
}

StarJumpEffect.key = 'StarJumpEffect';
StarJumpEffect.description = 'star jumping and compressing text effect with multi-line support';
StarJumpEffect.showName = '星星跳跃'; 