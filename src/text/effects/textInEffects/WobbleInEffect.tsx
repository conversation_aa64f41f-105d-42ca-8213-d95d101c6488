import React from 'react';
import { interpolate, useCurrentFrame } from 'remotion';

interface WobbleInEffectProps {
  text: string | React.ReactNode[];
  style: React.CSSProperties;
  durationFrames?: number;
}

export default function WobbleInEffect({
  text = '展示文本',
  style = {},
  durationFrames = 80,
}: WobbleInEffectProps) {
  const frame = useCurrentFrame();

  // 摇摆效果
  const wobbleX = interpolate(
    frame,
    [0, 20, 40, 60, 80],
    [0, -25, 20, -15, 0],
    {
      extrapolateLeft: 'clamp',
      extrapolateRight: 'clamp',
    }
  );

  const wobbleRotate = interpolate(
    frame,
    [0, 20, 40, 60, 80],
    [0, -5, 3, -1, 0],
    {
      extrapolateLeft: 'clamp',
      extrapolateRight: 'clamp',
    }
  );

  // 透明度
  const opacity = interpolate(
    frame,
    [0, 20],
    [0, 1],
    {
      extrapolateLeft: 'clamp',
      extrapolateRight: 'clamp',
    }
  );

  return (
    <span
      style={{
        display: 'inline-flex',
        alignItems: 'center',
        justifyContent: 'center',
        height: '100%',
      }}
    >
      <span
        style={{
          opacity: opacity,
          transform: `translateX(${wobbleX}px) rotate(${wobbleRotate}deg)`,
          color: '#ffffff',
          textAlign: 'center',
          textShadow: '0 0 8px #ffffff',
          ...style,
        }}
      >
        {text}
      </span>
    </span>
  );
}

WobbleInEffect.key = 'WobbleInEffect';
WobbleInEffect.description = 'wobble text effect';
WobbleInEffect.showName = '摇摆'; 