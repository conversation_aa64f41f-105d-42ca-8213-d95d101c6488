import React from 'react';
import { interpolate, useCurrentFrame } from 'remotion';

interface SnowfallInEffectProps {
  text: string | React.ReactNode[];
  style: React.CSSProperties;
  durationFrames?: number;
}

export default function SnowfallInEffect({
  text = '展示文本',
  style = {},
  durationFrames = 80,
}: SnowfallInEffectProps) {
  const frame = useCurrentFrame();

  // 文字淡入
  const opacity = interpolate(
    frame,
    [0, 30],
    [0, 1],
    {
      extrapolateLeft: 'clamp',
      extrapolateRight: 'clamp',
    }
  );

  // 雪花飘落
  const snowflakes = Array.from({ length: 8 }, (_, i) => {
    const snowFrame = frame - i * 6;
    if (snowFrame < 0) return null;
    
    const snowOpacity = interpolate(
      snowFrame,
      [0, 20, 80],
      [0, 1, 0],
      { extrapolateLeft: 'clamp', extrapolateRight: 'clamp' }
    );
    
    const x = 20 + i * 10 + Math.sin(snowFrame * 0.05 + i) * 15;
    const y = snowFrame * 1.2;
    
    return (
      <div
        key={i}
        style={{
          position: 'absolute',
          left: `${x}%`,
          top: `${y % 120}%`,
          fontSize: '16px',
          opacity: snowOpacity,
          transform: 'translate(-50%, -50%)',
        }}
      >
        ❄️
      </div>
    );
  }).filter(Boolean);

  return (
    <span
      style={{
        display: 'inline-flex',
        alignItems: 'center',
        justifyContent: 'center',
        height: '100%',
        position: 'relative',
      }}
    >
      <span
        style={{
          opacity: opacity,
          color: '#ffffff',
          textAlign: 'center',
          textShadow: '0 0 12px #ffffff',
          filter: `brightness(${1 + Math.sin(frame * 0.1) * 0.2})`,
          ...style,
        }}
      >
        {text}
      </span>
      {snowflakes}
    </span>
  );
}

SnowfallInEffect.key = 'SnowfallInEffect';
SnowfallInEffect.description = 'snowfall text effect with floating snowflakes';
SnowfallInEffect.showName = '雪花飘落'; 