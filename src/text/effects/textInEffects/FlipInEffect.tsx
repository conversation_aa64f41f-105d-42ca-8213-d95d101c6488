import React from 'react';
import { interpolate, useCurrentFrame } from 'remotion';

interface FlipInEffectProps {
  text: string | React.ReactNode[];
  style: React.CSSProperties;
  durationFrames?: number;
}

export default function FlipInEffect({
  text = '展示文本',
  style = {},
  durationFrames = 50,
}: FlipInEffectProps) {
  const frame = useCurrentFrame();

  // 翻转角度
  const rotateY = interpolate(
    frame,
    [0, durationFrames],
    [90, 0],
    {
      extrapolateLeft: 'clamp',
      extrapolateRight: 'clamp',
    }
  );

  // 文字透明度
  const opacity = interpolate(
    frame,
    [0, 15],
    [0, 1],
    {
      extrapolateLeft: 'clamp',
      extrapolateRight: 'clamp',
    }
  );

  return (
    <span
      style={{
        display: 'inline-flex',
        alignItems: 'center',
        justifyContent: 'center',
        height: '100%',
        perspective: '1000px',
      }}
    >
      <span
        style={{
          opacity: opacity,
          transform: `rotateY(${rotateY}deg)`,
          color: '#ffffff',
          textAlign: 'center',
          textShadow: '0 0 8px #ffffff',
          ...style,
        }}
      >
        {text}
      </span>
    </span>
  );
}

FlipInEffect.key = 'FlipInEffect';
FlipInEffect.description = '3D flip text effect';
FlipInEffect.showName = '翻转'; 