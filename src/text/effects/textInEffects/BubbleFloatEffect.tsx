import React from 'react';
import { useCurrentFrame, interpolate, useVideoConfig } from 'remotion';

interface BubbleFloatEffectProps {
  text: string;
  color?: string;
  fontSize?: number;
  style?: React.CSSProperties;
}

const BubbleFloatEffect: React.FC<BubbleFloatEffectProps> & { showName: string } = ({
  text = "ABCD123",
  color = "#FFFFFF",
  fontSize = 60,
  style = {}
}) => {
  const frame = useCurrentFrame();
  const { fps } = useVideoConfig();
  
  const duration = 1.5 * fps; // 1.5秒入场动画
  const progress = Math.min(1, frame / duration);
  
  // 上浮速度80px/s
  const floatSpeed = 80;
  
  return (
    <div style={{
      display: 'inline-flex',
      alignItems: 'center',
      justifyContent: 'center',
      position: 'relative',
      ...style
    }}>
      {text.split('').map((char, index) => {
        const charStartTime = index * 0.1;
        const charProgress = Math.max(0, Math.min(1, (progress - charStartTime) / 0.8));
        
        // 入场阶段：从底部浮现到正常位置
        const entranceY = interpolate(charProgress, [0, 1], [100, 0]);
        
        // 入场完成后的循环效果
        const isEntranceComplete = progress >= 1;
        const loopPhase = isEntranceComplete ? (frame % (fps * 2)) / fps : 0;
        const loopY = Math.sin(loopPhase * Math.PI + index * 0.5) * 5;
        
        const finalY = isEntranceComplete ? loopY : entranceY;
        
        return (
          <div
            key={index}
            style={{
              position: 'relative',
              display: 'inline-block',
              margin: '0 2px'
            }}
          >
            <span
              style={{
                fontSize,
                color,
                display: 'inline-block',
                transform: `translateY(${finalY}px)`,
                opacity: interpolate(charProgress, [0, 0.3, 1], [0, 0.7, 1]),
                filter: 'blur(0.5px)',
                textShadow: `
                  0 0 3px rgba(255,255,255,0.5),
                  0 0 8px rgba(255,255,255,0.3)
                `
              }}
            >
              {char}
            </span>
          </div>
        );
      })}
    </div>
  );
};

BubbleFloatEffect.showName = "气泡上浮";

export default BubbleFloatEffect; 