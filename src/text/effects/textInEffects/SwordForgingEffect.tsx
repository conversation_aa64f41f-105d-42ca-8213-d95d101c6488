import React from 'react';
import { useCurrentFrame, interpolate, useVideoConfig } from 'remotion';

interface SwordForgingEffectProps {
  text: string;
  color?: string;
  fontSize?: number;
  style?: React.CSSProperties;
}

const SwordForgingEffect: React.FC<SwordForgingEffectProps> & { showName: string } = ({
  text = "ABCD123",
  color = "#FFFFFF",
  fontSize = 60,
  style = {}
}) => {
  const frame = useCurrentFrame();
  const { fps } = useVideoConfig();
  
  const chars = text.split('');
  const duration = 1.3 * fps; // 1.3秒
  const halfLength = Math.ceil(chars.length / 2);
  const topChars = chars.slice(0, halfLength);
  const bottomChars = chars.slice(halfLength);
  
  const hammerStrikes = 3; // 锻打次数
  
  return (
    <div style={{
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      position: 'relative',
      backgroundColor: 'transparent',
      ...style
    }}>
      {/* 上半文字 - 剑刃烧红下落 */}
      <div style={{
        display: 'flex',
        position: 'relative',
        marginBottom: '2px'
      }}>
        {topChars.map((char, index) => {
          const charDelay = index * 3;
          const charProgress = Math.max(0, Math.min(1, (frame - charDelay) / (duration * 0.6)));
          
          // 烧红效果
          const heatIntensity = interpolate(charProgress, [0, 0.3, 0.8, 1], [0, 1, 0.7, 0.3]);
          const fallY = interpolate(charProgress, [0, 0.8, 1], [-fontSize * 1.5, 0, 0]);
          
          return (
            <div key={`top-${index}`} style={{ position: 'relative' }}>
              {/* 火星飞溅 */}
              {Array.from({ length: 6 }, (_, i) => (
                <div
                  key={`spark-${i}`}
                  style={{
                    position: 'absolute',
                    left: '50%',
                    top: fallY - 10,
                    width: '2px',
                    height: '4px',
                    background: `linear-gradient(180deg, 
                      rgba(255,165,0,${heatIntensity}) 0%, 
                      rgba(255,69,0,${heatIntensity * 0.5}) 100%)`,
                    transform: `translateX(-50%) 
                      translate(${(Math.random() - 0.5) * 20}px, ${Math.random() * -15}px)`,
                    opacity: heatIntensity,
                    pointerEvents: 'none'
                  }}
                />
              ))}
              
              {/* 烧红光晕 */}
              <div style={{
                position: 'absolute',
                left: '50%',
                top: fallY,
                transform: 'translateX(-50%)',
                fontSize,
                color: `rgba(255,69,0,${heatIntensity})`,
                filter: `blur(${heatIntensity * 2}px)`,
                pointerEvents: 'none'
              }}>
                {char}
              </div>
              
              <span style={{
                fontSize,
                color: heatIntensity > 0.5 ? 
                  `rgb(255,${255 - heatIntensity * 100},${255 - heatIntensity * 200})` : color,
                transform: `translateY(${fallY}px)`,
                display: 'inline-block',
                margin: '0 2px',
                opacity: charProgress > 0 ? 1 : 0
              }}>
                {char}
              </span>
            </div>
          );
        })}
      </div>
      
      {/* 下半文字 - 剑胚上顶 */}
      <div style={{
        display: 'flex',
        position: 'relative'
      }}>
        {bottomChars.map((char, index) => {
          const charDelay = index * 3;
          const charProgress = Math.max(0, Math.min(1, (frame - charDelay) / (duration * 0.6)));
          
          const riseY = interpolate(charProgress, [0, 0.8, 1], [fontSize * 1.5, 0, 0]);
          
          return (
            <div key={`bottom-${index}`} style={{ position: 'relative' }}>
              <span style={{
                fontSize,
                color,
                transform: `translateY(${riseY}px)`,
                display: 'inline-block',
                margin: '0 2px',
                opacity: charProgress > 0 ? 1 : 0,
                filter: `drop-shadow(0 0 2px rgba(100,100,100,0.5))`
              }}>
                {char}
              </span>
            </div>
          );
        })}
      </div>
      
      {/* 锻打效果 */}
      {frame > duration * 0.5 && (
        <div style={{
          position: 'absolute',
          left: '50%',
          top: '50%',
          transform: 'translate(-50%, -50%)',
          pointerEvents: 'none'
        }}>
          {Array.from({ length: hammerStrikes }, (_, strikeIndex) => {
            const strikeFrame = duration * 0.5 + strikeIndex * 8;
            const isStriking = frame >= strikeFrame && frame < strikeFrame + 5;
            
            if (!isStriking) return null;
            
            return (
              <div key={`strike-${strikeIndex}`}>
                {/* 铁锤冲击波 */}
                <div style={{
                  position: 'absolute',
                  left: -25,
                  top: -25,
                  width: '50px',
                  height: '50px',
                  border: '3px solid rgba(255,255,255,0.6)',
                  borderRadius: '50%',
                  transform: `scale(${interpolate(frame - strikeFrame, [0, 5], [0.5, 2])})`,
                  opacity: interpolate(frame - strikeFrame, [0, 5], [0.8, 0])
                }} />
                
                {/* 锻打火花 */}
                {Array.from({ length: 12 }, (_, i) => {
                  const angle = (i * 30) * Math.PI / 180;
                  const distance = 20;
                  return (
                    <div
                      key={`hammer-spark-${i}`}
                      style={{
                        position: 'absolute',
                        left: Math.cos(angle) * distance,
                        top: Math.sin(angle) * distance,
                        width: '3px',
                        height: '6px',
                        backgroundColor: '#FFD700',
                        opacity: interpolate(frame - strikeFrame, [0, 3], [1, 0])
                      }}
                    />
                  );
                })}
              </div>
            );
          })}
        </div>
      )}
      
      {/* 淬火蒸汽爆发 */}
      {frame > duration * 0.8 && frame < duration * 0.95 && (
        <div style={{
          position: 'absolute',
          left: '50%',
          top: '50%',
          transform: 'translate(-50%, -50%)',
          pointerEvents: 'none'
        }}>
          {Array.from({ length: 8 }, (_, i) => (
            <div
              key={`steam-${i}`}
              style={{
                position: 'absolute',
                left: (i - 4) * 8,
                top: 0,
                width: '6px',
                height: '20px',
                background: `linear-gradient(180deg, 
                  rgba(255,255,255,0.8) 0%, 
                  transparent 100%)`,
                transform: `translateY(${-i * 5}px)`,
                opacity: interpolate(frame, [duration * 0.8, duration * 0.95], [0, 1]),
                borderRadius: '3px'
              }}
            />
          ))}
        </div>
      )}
      
      {/* 剑纹光效流转 */}
      {frame > duration * 0.9 && (
        <div style={{
          position: 'absolute',
          left: '10%',
          top: '50%',
          width: '80%',
          height: '2px',
          background: `linear-gradient(90deg, 
            transparent 0%, 
            rgba(0,255,255,${interpolate(frame, [duration * 0.9, duration], [0, 0.8])}) 50%, 
            transparent 100%)`,
          transform: 'translateY(-50%)',
          pointerEvents: 'none'
        }}>
          <div style={{
            position: 'absolute',
            left: `${interpolate(frame, [duration * 0.9, duration], [0, 100])}%`,
            top: -2,
            width: '6px',
            height: '6px',
            backgroundColor: '#00FFFF',
            borderRadius: '50%',
            filter: 'blur(1px)'
          }} />
        </div>
      )}
    </div>
  );
};

SwordForgingEffect.showName = "铸剑合刃";

export default SwordForgingEffect; 