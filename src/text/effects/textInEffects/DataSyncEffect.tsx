import React from 'react';
import { useCurrentFrame, interpolate, useVideoConfig } from 'remotion';

interface DataSyncEffectProps {
  text: string;
  color?: string;
  fontSize?: number;
  style?: React.CSSProperties;
}

const DataSyncEffect: React.FC<DataSyncEffectProps> & { showName: string } = ({
  text = "ABCD123",
  color = "#FFFFFF",
  fontSize = 60,
  style = {}
}) => {
  const frame = useCurrentFrame();
  const { fps } = useVideoConfig();
  
  const chars = text.split('');
  const duration = 1.7 * fps; // 1.7秒
  const halfLength = Math.ceil(chars.length / 2);
  const topChars = chars.slice(0, halfLength);
  const bottomChars = chars.slice(halfLength);
  
  const transferRate = 1; // 1Gbps传输速率
  
  return (
    <div style={{
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      position: 'relative',
      backgroundColor: 'transparent',
      ...style
    }}>
      {/* 下半文字 - 二进制地基 */}
      <div style={{
        display: 'flex',
        position: 'relative',
        marginBottom: '4px'
      }}>
        {bottomChars.map((char, index) => {
          const charDelay = index * 4;
          const charProgress = Math.max(0, Math.min(1, (frame - charDelay) / (duration * 0.5)));
          
          return (
            <div key={`bottom-${index}`} style={{ position: 'relative' }}>
              {/* 二进制码流 */}
              {Array.from({ length: 8 }, (_, i) => (
                <div
                  key={`binary-${i}`}
                  style={{
                    position: 'absolute',
                    left: '50%',
                    top: fontSize + 5 + i * 8,
                    transform: 'translateX(-50%)',
                    fontSize: '8px',
                    color: `rgba(0,255,0,${interpolate(charProgress, [0, 0.5, 1], [0, 0.8, 0.4])})`,
                    fontFamily: 'monospace',
                    pointerEvents: 'none'
                  }}
                >
                  {Math.random() > 0.5 ? '1' : '0'}
                </div>
              ))}
              
              {/* 光缆脉冲 */}
              <div style={{
                position: 'absolute',
                left: '50%',
                top: fontSize + 10,
                width: '2px',
                height: '40px',
                background: `linear-gradient(180deg, 
                  rgba(0,100,255,${Math.sin(frame * 0.5 + index) * 0.5 + 0.5}) 0%, 
                  transparent 100%)`,
                transform: 'translateX(-50%)',
                opacity: charProgress > 0 ? 1 : 0,
                pointerEvents: 'none'
              }} />
              
              <span style={{
                fontSize,
                color,
                display: 'inline-block',
                margin: '0 2px',
                opacity: charProgress > 0 ? 1 : 0,
                filter: `drop-shadow(0 0 2px rgba(0,255,0,0.3))`
              }}>
                {char}
              </span>
            </div>
          );
        })}
      </div>
      
      {/* 上半文字 - 全息构件下传 */}
      <div style={{
        display: 'flex',
        position: 'relative'
      }}>
        {topChars.map((char, index) => {
          const charDelay = duration * 0.3 + index * 6;
          const charProgress = Math.max(0, Math.min(1, (frame - charDelay) / (duration * 0.6)));
          
          // 全息下传效果
          const transmitY = interpolate(charProgress, [0, 0.8, 1], [-fontSize * 1.5, 0, 0]);
          
          return (
            <div key={`top-${index}`} style={{ position: 'relative' }}>
              {/* 全息投影线 */}
              {Array.from({ length: 5 }, (_, i) => (
                <div
                  key={`hologram-${i}`}
                  style={{
                    position: 'absolute',
                    left: `${20 + i * 15}%`,
                    top: transmitY - fontSize * 0.5,
                    width: '1px',
                    height: fontSize * 0.5,
                    background: `linear-gradient(180deg, 
                      rgba(0,255,255,0.6) 0%, 
                      transparent 100%)`,
                    opacity: charProgress > 0 ? interpolate(i, [0, 4], [1, 0.3]) : 0,
                    pointerEvents: 'none'
                  }}
                />
              ))}
              
              {/* 进度条扫描 */}
              {charProgress > 0.2 && charProgress < 0.9 && (
                <div style={{
                  position: 'absolute',
                  left: 0,
                  top: transmitY - 5,
                  width: '100%',
                  height: '2px',
                  background: `linear-gradient(90deg, 
                    transparent 0%, 
                    rgba(255,255,0,0.8) ${charProgress * 100}%, 
                    transparent ${charProgress * 100 + 10}%)`,
                  pointerEvents: 'none'
                }} />
              )}
              
              {/* 错误校验闪光 */}
              {charProgress > 0.5 && charProgress < 0.8 && Math.random() > 0.8 && (
                <div style={{
                  position: 'absolute',
                  left: '50%',
                  top: transmitY,
                  transform: 'translateX(-50%)',
                  width: '20px',
                  height: '20px',
                  background: `radial-gradient(circle, 
                    rgba(255,0,0,0.8) 0%, 
                    transparent 100%)`,
                  borderRadius: '50%',
                  pointerEvents: 'none'
                }} />
              )}
              
              <span style={{
                fontSize,
                color,
                transform: `translateY(${transmitY}px)`,
                display: 'inline-block',
                margin: '0 2px',
                opacity: charProgress > 0 ? interpolate(charProgress, [0, 0.3, 1], [0.3, 0.8, 1]) : 0,
                filter: `drop-shadow(0 0 3px rgba(0,255,255,0.4))`
              }}>
                {char}
              </span>
            </div>
          );
        })}
      </div>
      
      {/* 校验完成绿光贯穿 */}
      {frame > duration * 0.85 && (
        <div style={{
          position: 'absolute',
          left: '10%',
          top: '50%',
          width: '80%',
          height: '4px',
          background: `linear-gradient(90deg, 
            transparent 0%, 
            rgba(0,255,0,${interpolate(frame, [duration * 0.85, duration], [0, 1])}) 50%, 
            transparent 100%)`,
          transform: 'translateY(-50%)',
          pointerEvents: 'none',
          borderRadius: '2px'
        }}>
          {/* 绿光粒子 */}
          {Array.from({ length: 6 }, (_, i) => (
            <div
              key={`particle-${i}`}
              style={{
                position: 'absolute',
                left: `${interpolate(frame, [duration * 0.85, duration], [0, 100])}%`,
                top: -2,
                width: '8px',
                height: '8px',
                backgroundColor: '#00FF00',
                borderRadius: '50%',
                transform: `translateX(-50%) translateY(${Math.sin(frame * 0.3 + i) * 2}px)`,
                opacity: 0.8
              }}
            />
          ))}
        </div>
      )}
      

    </div>
  );
};

DataSyncEffect.showName = "数据同步";

export default DataSyncEffect; 