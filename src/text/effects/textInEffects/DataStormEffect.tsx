import React from 'react';
import { useCurrentFrame, interpolate, useVideoConfig } from 'remotion';

interface DataStormEffectProps {
  text: string;
  color?: string;
  fontSize?: number;
  style?: React.CSSProperties;
}

const DataStormEffect: React.FC<DataStormEffectProps> & { showName: string } = ({
  text = "ABCD123",
  color = "#FFFFFF",
  fontSize = 60,
  style = {}
}) => {
  const frame = useCurrentFrame();
  const { fps } = useVideoConfig();
  
  const duration = 1.5 * fps; // 1.5秒
  const progress = Math.min(1, frame / duration);
  
  // 乱码字符集
  const glitchChars = '01ABCDEFGHIJKLMNOPQRSTUVWXYZ!@#$%^&*()_+-=[]{}|;:,.<>?`~';
  
  // 随机函数
  const random = (seed: number) => {
    const x = Math.sin(seed * 12.9898) * 43758.5453;
    return x - Math.floor(x);
  };
  
  // 刷新频率30Hz
  const refreshCycle = Math.floor(frame / (fps / 30));
  
  return (
    <div style={{
      display: 'inline-flex',
      alignItems: 'center',
      justifyContent: 'center',
      position: 'relative',
      ...style
    }}>
      {text.split('').map((char, index) => {
        const charStartTime = index * 0.05; // 更快的开始时间
        const charProgress = Math.max(0, Math.min(1, (progress - charStartTime) / 0.3)); // 更快的转码时间
        
        let displayChar = char;
        let shouldShow = true;
        
        if (charProgress < 1) {
          // 字符瀑布流刷新阶段
          const glitchIntensity = 1 - charProgress;
          if (random(refreshCycle + index) < glitchIntensity) {
            const glitchIndex = Math.floor(random(refreshCycle + index + 100) * glitchChars.length);
            displayChar = glitchChars[glitchIndex];
          } else {
            // 没转码的文字先不要显示
            shouldShow = charProgress > 0.2;
          }
        }
        
        // 正确字符逐步锁定 - 锁定时间提前
        const isLocked = charProgress > 0.6;
        
        // 入场动画完成后继续循环播放
        const loopProgress = progress > 1 ? (frame % (fps * 0.5)) / (fps * 0.5) : 0;
        const loopFlicker = loopProgress > 0 && frame % 60 < 5;
        
        return (
          <span
            key={index}
            style={{
              fontSize,
              color: isLocked ? '#00FF00' : color,
              display: 'inline-block',
              margin: '0 2px',
              fontFamily: 'monospace',
              opacity: shouldShow ? 1 : 0,
              textShadow: isLocked ? 
                '0 0 10px #00FF00, 0 0 20px #00FF00' : 
                `0 0 5px ${color}`,
              filter: isLocked ? 
                ((frame % 20 < 5) || loopFlicker ? 'brightness(2.5)' : 'brightness(1.5)') : 
                'none',
              // 解码完成绿光确认闪烁 + 循环闪烁 - 只闪烁文字
              background: 'transparent',
              transition: 'none'
            }}
          >
            {displayChar}
          </span>
        );
      })}
    </div>
  );
};

DataStormEffect.showName = "数据风暴";

export default DataStormEffect; 