import React from 'react';
import { interpolate, useCurrentFrame } from 'remotion';

interface GlowPulseInEffectProps {
  text: string | React.ReactNode[];
  style: React.CSSProperties;
  durationFrames?: number;
}

export default function GlowPulseInEffect({
  text = '展示文本',
  style = {},
  durationFrames = 60,
}: GlowPulseInEffectProps) {
  const frame = useCurrentFrame();

  // 文字淡入
  const opacity = interpolate(
    frame,
    [0, 20],
    [0, 1],
    {
      extrapolateLeft: 'clamp',
      extrapolateRight: 'clamp',
    }
  );

  // 发光脉冲效果
  const glowIntensity = Math.sin(frame * 0.2) * 0.5 + 1;
  const shadowBlur = 10 + Math.sin(frame * 0.15) * 8;

  return (
    <span
      style={{
        display: 'inline-flex',
        alignItems: 'center',
        justifyContent: 'center',
        height: '100%',
        position: 'relative',
      }}
    >
      <span
        style={{
          opacity: opacity,
          color: '#ffffff',
          textAlign: 'center',
          textShadow: `0 0 ${shadowBlur}px rgba(255, 255, 255, ${glowIntensity})`,
          filter: `brightness(${glowIntensity})`,
          ...style,
        }}
      >
        {text}
      </span>
    </span>
  );
}

GlowPulseInEffect.key = 'GlowPulseInEffect';
GlowPulseInEffect.description = 'glow pulse text effect';
GlowPulseInEffect.showName = '发光脉冲'; 