import React from 'react';
import { useCurrentFrame, interpolate, useVideoConfig } from 'remotion';

interface ParticleReformEffectProps {
  text: string;
  color?: string;
  fontSize?: number;
  style?: React.CSSProperties;
}

const ParticleReformEffect: React.FC<ParticleReformEffectProps> & { showName: string } = ({
  text = "ABCD123",
  color = "#FFFFFF",
  fontSize = 60,
  style = {}
}) => {
  const frame = useCurrentFrame();
  const { fps } = useVideoConfig();
  
  const duration = 1.2 * fps; // 1.2秒
  const chars = text.split('');
  
  // 生成粒子噪声函数
  const noise = (x: number, y: number, seed: number) => {
    const n = Math.sin(x * 12.9898 + y * 78.233 + seed) * 43758.5453;
    return n - Math.floor(n);
  };
  
  return (
    <div style={{
      display: 'inline-flex',
      alignItems: 'center',
      justifyContent: 'center',
      position: 'relative',
      ...style
    }}>
      {chars.map((char, charIndex) => {
        const charWidth = fontSize * 0.9;
        const charHeight = fontSize;
        const particleCount = 15; // 每个字符的粒子数
        
        return (
          <div
            key={charIndex}
            style={{
              position: 'relative',
              width: charWidth,
              height: charHeight,
              margin: '0 5px',
              display: 'inline-block'
            }}
          >
            {/* 粒子系统 */}
            {frame < duration - 15 && Array.from({ length: particleCount }, (_, particleIndex) => {
              const particleDelay = particleIndex * 2; // 不同延迟时间
              const particleStartFrame = particleDelay;
              const particleEndFrame = duration - 15;
              
              if (frame < particleStartFrame || frame > particleEndFrame) return null;
              
              const progress = Math.min(1, (frame - particleStartFrame) / (particleEndFrame - particleStartFrame));
              
              // 粒子在字符内的目标位置
              const targetX = (particleIndex % 4) * (charWidth / 4) + charWidth / 8;
              const targetY = Math.floor(particleIndex / 4) * (charHeight / 4) + charHeight / 8;
              
              // 粒子的起始位置（屏幕外随机位置）
              const startX = noise(particleIndex, charIndex, 1) * 400 - 200;
              const startY = noise(particleIndex, charIndex, 2) * 300 - 150;
              
              // 使用噪声生成曲线路径
              const pathProgress = interpolate(progress, [0, 1], [0, 1]);
              const noiseX = noise(pathProgress * 10, charIndex, particleIndex) * 50;
              const noiseY = noise(pathProgress * 15, charIndex, particleIndex + 10) * 30;
              
              // 当前位置
              const currentX = interpolate(
                pathProgress,
                [0, 1],
                [startX, targetX]
              ) + noiseX * (1 - pathProgress);
              
              const currentY = interpolate(
                pathProgress,
                [0, 1],
                [startY, targetY]
              ) + noiseY * (1 - pathProgress);
              
              // 粒子属性
              const opacity = interpolate(progress, [0, 0.2, 0.8, 1], [0, 1, 1, 1]);
              const scale = interpolate(progress, [0, 0.3, 1], [0.5, 0.8, 1]);
              
              // 碰撞光效
              const glowIntensity = progress > 0.9 ? 
                interpolate(progress, [0.9, 1], [0, 1]) : 0;
              
              return (
                <div
                  key={particleIndex}
                  style={{
                    position: 'absolute',
                    left: currentX,
                    top: currentY,
                    width: 3,
                    height: 3,
                    borderRadius: '50%',
                    backgroundColor: color,
                    opacity,
                    transform: `scale(${scale})`,
                    boxShadow: glowIntensity > 0 ? 
                      `0 0 ${glowIntensity * 10}px ${color}` : 'none',
                    transition: 'box-shadow 0.1s ease'
                  }}
                />
              );
            })}
            
            {/* 最终字符显示 */}
            {frame > duration - 15 && (
              <span
                style={{
                  position: 'absolute',
                  left: 0,
                  top: 0,
                  fontSize,
                  color,
                  opacity: interpolate(frame, [duration - 15, duration], [0, 1]),
                  display: 'inline-block',
                  width: '100%',
                  textAlign: 'center'
                }}
              >
                {char}
              </span>
            )}
          </div>
        );
      })}
    </div>
  );
};

ParticleReformEffect.showName = "粒子重组";

export default ParticleReformEffect; 