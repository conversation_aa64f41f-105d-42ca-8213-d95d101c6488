import React from 'react';
import { interpolate, useCurrentFrame } from 'remotion';

interface SparkleInEffectProps {
  text: string | React.ReactNode[];
  style: React.CSSProperties;
  durationFrames?: number;
}

export default function SparkleInEffect({
  text = '展示文本',
  style = {},
  durationFrames = 80,
}: SparkleInEffectProps) {
  const frame = useCurrentFrame();

  // 文字淡入
  const opacity = interpolate(
    frame,
    [0, 25],
    [0, 1],
    {
      extrapolateLeft: 'clamp',
      extrapolateRight: 'clamp',
    }
  );

  // 星星闪烁
  const sparkles = Array.from({ length: 8 }, (_, i) => {
    const sparkleFrame = frame - i * 8;
    if (sparkleFrame < 0) return null;
    
    const sparkleOpacity = Math.sin(sparkleFrame * 0.3) * 0.5 + 0.5;
    
    const angle = (i / 8) * Math.PI * 2;
    const radius = 60 + Math.sin(sparkleFrame * 0.1) * 20;
    const x = 50 + Math.cos(angle) * radius;
    const y = 50 + Math.sin(angle) * radius;
    
    return (
      <div
        key={i}
        style={{
          position: 'absolute',
          left: `${x}%`,
          top: `${y}%`,
          fontSize: '18px',
          opacity: sparkleOpacity,
          transform: 'translate(-50%, -50%)',
        }}
      >
        ✨
      </div>
    );
  }).filter(Boolean);

  return (
    <span
      style={{
        display: 'inline-flex',
        alignItems: 'center',
        justifyContent: 'center',
        height: '100%',
        position: 'relative',
      }}
    >
      <span
        style={{
          opacity: opacity,
          color: '#ffffff',
          textAlign: 'center',
          textShadow: '0 0 10px #ffffff',
          ...style,
        }}
      >
        {text}
      </span>
      {sparkles}
    </span>
  );
}

SparkleInEffect.key = 'SparkleInEffect';
SparkleInEffect.description = 'sparkle text effect with stars';
SparkleInEffect.showName = '星星闪烁'; 