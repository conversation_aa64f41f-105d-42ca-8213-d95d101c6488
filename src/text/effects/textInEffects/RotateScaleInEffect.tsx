import React, { useRef, useEffect, useState } from 'react';
import { interpolate, useCurrentFrame } from 'remotion';

interface RotateScaleInEffectProps {
  text: string | React.ReactNode[];
  style: React.CSSProperties;
  durationFrames: number;
}

export default function RotateScaleInEffect({
  text = '展示文本',
  style = {},
  durationFrames = 50, // 入场动画持续帧数
}: RotateScaleInEffectProps) {
  const frame = useCurrentFrame();
  const textRef = useRef<HTMLDivElement>(null);
  const [elementSize, setElementSize] = useState({ width: 96, height: 48 }); // 默认值基于fontSize 48px

  useEffect(() => {
    if (textRef.current) {
      const rect = textRef.current.getBoundingClientRect();
      setElementSize({ width: rect.width, height: rect.height });
    }
  }, [text, style.fontSize]); // 只监听text和fontSize，避免无限循环

  // 入场动画进度
  const animateInProgress = interpolate(
    frame,
    [0, durationFrames],
    [0, 1],
    {
      extrapolateLeft: 'clamp',
      extrapolateRight: 'clamp',
    }
  );

  // 旋转动画 - 入场时从旋转到正常
  const rotation = 360 * (1 - animateInProgress);

  // 缩放动画 - 入场时从小放大
  const scale = interpolate(animateInProgress, [0, 1], [0, 1]);

  // 透明度动画
  const opacity = interpolate(
    frame,
    [0, durationFrames * 0.6],
    [0, 1],
    {
      extrapolateLeft: 'clamp',
      extrapolateRight: 'clamp',
    }
  );

  // 添加发光效果，强度随动画进度变化
  const glowIntensity = interpolate(
    animateInProgress,
    [0, 1],
    [20, 5]
  );

  // 添加背景光圈效果
  const ringScale = interpolate(
    animateInProgress,
    [0, 1],
    [2, 0.5]
  );

  const ringOpacity = interpolate(
    animateInProgress,
    [0, 0.5, 1],
    [0, 0.8, 0]
  );

  // 使用元素实际大小计算光圈尺寸
  const ringSize = Math.max(elementSize.width, elementSize.height) * 1.5;

  return (
    <span
      style={{
        display: 'inline-flex',
        alignItems: 'center',
        justifyContent: 'center',
        height: '100%',
        position: 'relative',
      }}
    >
      {/* 背景光圈效果 */}
      <div
        style={{
          position: 'absolute',
          width: `${ringSize}px`,
          height: `${ringSize}px`,
          borderRadius: '50%',
          border: `2px solid ${style.color}`,
          opacity: ringOpacity,
          transform: `scale(${ringScale})`,
          boxShadow: `0 0 20px ${style.color}`,
        }}
      />

      {/* 主文字 */}
      <span
        ref={textRef}
        style={{
          color: style.color,
          opacity: opacity,
          transform: `rotate(${rotation}deg) scale(${scale})`,
          textShadow: `0 0 ${glowIntensity}px ${style.color}`,
          fontWeight: 'bold',
          textAlign: 'center',
          transformOrigin: 'center',
          transition: 'all 0.1s ease',
          ...style,
        }}
      >
        {text}
      </span>

      {/* 添加粒子效果 */}
      {animateInProgress > 0.3 && (
        <>
          {Array.from({ length: 8 }, (_, i) => {
            const angle = (i * 45) + rotation;
            const distance = Math.max(elementSize.width, elementSize.height) * scale * 0.8;
            const x = Math.cos((angle * Math.PI) / 180) * distance;
            const y = Math.sin((angle * Math.PI) / 180) * distance;
            
            return (
              <div
                key={i}
                style={{
                  position: 'absolute',
                  left: '50%',
                  top: '50%',
                  width: '4px',
                  height: '4px',
                  backgroundColor: style.color,
                  borderRadius: '50%',
                  transform: `translate(-50%, -50%) translate(${x}px, ${y}px)`,
                  opacity: opacity * 0.6,
                  boxShadow: `0 0 8px ${style.color}`,
                }}
              />
            );
          })}
        </>
      )}
    </span>
  );
}

RotateScaleInEffect.key = 'RotateScaleInEffect';
RotateScaleInEffect.description = 'rotate and scale in text effect with particle animation';
RotateScaleInEffect.showName = '旋转缩放入场'; 