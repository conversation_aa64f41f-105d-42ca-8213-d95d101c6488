import React from 'react';
import { interpolate, useCurrentFrame } from 'remotion';

interface TadaInEffectProps {
  text: string | React.ReactNode[];
  style: React.CSSProperties;
  durationFrames?: number;
}

export default function TadaInEffect({
  text = '展示文本',
  style = {},
  durationFrames = 100,
}: TadaInEffectProps) {
  const frame = useCurrentFrame();

  // 华丽亮相效果
  const scale = interpolate(
    frame,
    [0, 10, 20, 30, 40, 50, 60, 70, 80, 90, 100],
    [1, 0.9, 0.9, 1.1, 0.9, 1.1, 1.1, 0.95, 1.02, 0.98, 1],
    {
      extrapolateLeft: 'clamp',
      extrapolateRight: 'clamp',
    }
  );

  const rotate = interpolate(
    frame,
    [0, 10, 20, 30, 40, 50, 60, 70, 80, 90, 100],
    [0, -3, -3, 3, -1, 1, 1, -1, 0.5, -0.5, 0],
    {
      extrapolateLeft: 'clamp',
      extrapolateRight: 'clamp',
    }
  );

  // 透明度
  const opacity = interpolate(
    frame,
    [0, 10],
    [0, 1],
    {
      extrapolateLeft: 'clamp',
      extrapolateRight: 'clamp',
    }
  );

  return (
    <span
      style={{
        display: 'inline-flex',
        alignItems: 'center',
        justifyContent: 'center',
        height: '100%',
      }}
    >
      <span
        style={{
          opacity: opacity,
          transform: `scale(${scale}) rotate(${rotate}deg)`,
          color: '#ffffff',
          textAlign: 'center',
          textShadow: '0 0 12px #ffffff',
          ...style,
        }}
      >
        {text}
      </span>
    </span>
  );
}

TadaInEffect.key = 'TadaInEffect';
TadaInEffect.description = 'tada text effect';
TadaInEffect.showName = '华丽亮相'; 