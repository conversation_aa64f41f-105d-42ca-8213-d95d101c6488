import React from 'react';
import { interpolate, useCurrentFrame } from 'remotion';

interface RotateInEffectProps {
  text: string | React.ReactNode[];
  style: React.CSSProperties;
  durationFrames?: number;
}

export default function RotateInEffect({
  text = '展示文本',
  style = {},
  durationFrames = 60,
}: RotateInEffectProps) {
  const frame = useCurrentFrame();

  // 旋转动画
  const rotation = interpolate(
    frame,
    [0, durationFrames],
    [360, 0],
    {
      extrapolateLeft: 'clamp',
      extrapolateRight: 'clamp',
    }
  );

  // 透明度
  const opacity = interpolate(
    frame,
    [0, 20],
    [0, 1],
    {
      extrapolateLeft: 'clamp',
      extrapolateRight: 'clamp',
    }
  );

  return (
    <span
      style={{
        display: 'inline-flex',
        alignItems: 'center',
        justifyContent: 'center',
        height: '100%',
      }}
    >
      <span
        style={{
          opacity: opacity,
          transform: `rotate(${rotation}deg)`,
          color: '#ffffff',
          textAlign: 'center',
          textShadow: '0 0 8px #ffffff',
          ...style,
        }}
      >
        {text}
      </span>
    </span>
  );
}

RotateInEffect.key = 'RotateInEffect';
RotateInEffect.description = 'rotate text effect';
RotateInEffect.showName = '旋转'; 