import React from 'react';
import { interpolate, useCurrentFrame } from 'remotion';

interface TypewriterInEffectProps {
  text: string | React.ReactNode[];
  style: React.CSSProperties;
  durationFrames?: number;
}

export default function TypewriterInEffect({
  text = '展示文本',
  style = {},
  durationFrames = 90,
}: TypewriterInEffectProps) {
  const frame = useCurrentFrame();

  const textString = typeof text === 'string' ? text : text.join('');
  const charsToShow = interpolate(
    frame,
    [0, durationFrames],
    [0, textString.length],
    {
      extrapolateLeft: 'clamp',
      extrapolateRight: 'clamp',
    }
  );

  const showCursor = Math.floor(frame / 20) % 2 === 0;

  return (
    <span
      style={{
        display: 'inline-flex',
        alignItems: 'center',
        justifyContent: 'center',
        height: '100%',
        position: 'relative',
      }}
    >
      <span
        style={{
          color: '#ffffff',
          textAlign: 'center',
          ...style,
        }}
      >
        {textString.slice(0, Math.floor(charsToShow))}
        {frame < durationFrames && showCursor && '|'}
      </span>
    </span>
  );
}

TypewriterInEffect.key = 'TypewriterInEffect';
TypewriterInEffect.description = 'typewriter text effect';
TypewriterInEffect.showName = '打字机'; 