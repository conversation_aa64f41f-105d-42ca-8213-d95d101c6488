import React from 'react';
import { interpolate, useCurrentFrame } from 'remotion';

interface FloatDriftInEffectProps {
  text: string | React.ReactNode[];
  style: React.CSSProperties;
  durationFrames?: number;
}

export default function FloatDriftInEffect({
  text = '展示文本',
  style = {},
  durationFrames = 80,
}: FloatDriftInEffectProps) {
  const frame = useCurrentFrame();

  // 文字淡入
  const opacity = interpolate(
    frame,
    [0, 30],
    [0, 1],
    {
      extrapolateLeft: 'clamp',
      extrapolateRight: 'clamp',
    }
  );

  // 漂浮动画
  const floatY = Math.sin(frame * 0.08) * 3;
  const floatX = Math.sin(frame * 0.06) * 2;

  return (
    <span
      style={{
        display: 'inline-flex',
        alignItems: 'center',
        justifyContent: 'center',
        height: '100%',
        position: 'relative',
      }}
    >
      <span
        style={{
          opacity: opacity,
          transform: `translate(${floatX}px, ${floatY}px)`,
          color: '#ffffff',
          textAlign: 'center',
          textShadow: '0 0 6px #ffffff',
          ...style,
        }}
      >
        {text}
      </span>
    </span>
  );
}

FloatDriftInEffect.key = 'FloatDriftInEffect';
FloatDriftInEffect.description = 'floating drift text effect';
FloatDriftInEffect.showName = '漂浮'; 