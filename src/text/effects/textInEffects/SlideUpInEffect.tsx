import React from 'react';
import { interpolate, useCurrentFrame } from 'remotion';

interface SlideUpInEffectProps {
  text: string | React.ReactNode[];
  style: React.CSSProperties;
  durationFrames?: number;
}

export default function SlideUpInEffect({
  text = '展示文本',
  style = {},
  durationFrames = 40,
}: SlideUpInEffectProps) {
  const frame = useCurrentFrame();

  // 上滑动画
  const translateY = interpolate(
    frame,
    [0, durationFrames],
    [50, 0],
    {
      extrapolateLeft: 'clamp',
      extrapolateRight: 'clamp',
    }
  );

  // 透明度
  const opacity = interpolate(
    frame,
    [0, 20],
    [0, 1],
    {
      extrapolateLeft: 'clamp',
      extrapolateRight: 'clamp',
    }
  );

  return (
    <span
      style={{
        display: 'inline-flex',
        alignItems: 'center',
        justifyContent: 'center',
        height: '100%',
      }}
    >
      <span
        style={{
          opacity: opacity,
          transform: `translateY(${translateY}px)`,
          color: '#ffffff',
          textAlign: 'center',
          textShadow: '0 0 8px #ffffff',
          ...style,
        }}
      >
        {text}
      </span>
    </span>
  );
}

SlideUpInEffect.key = 'SlideUpInEffect';
SlideUpInEffect.description = 'slide up text effect';
SlideUpInEffect.showName = '上滑'; 