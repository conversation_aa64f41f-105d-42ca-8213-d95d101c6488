import React from 'react';
import { interpolate, useCurrentFrame } from 'remotion';

interface SlideInEffectProps {
  text: string | React.ReactNode[];
  style: React.CSSProperties;
  durationFrames: number;
  slideDirection: 'left' | 'right' | 'top' | 'bottom';
}

export default function SlideInEffect({
  text = '展示文本',
  style = {},
  durationFrames = 40, // 滑入持续帧数
  slideDirection = 'left', // 滑动方向
}: SlideInEffectProps) {
  const frame = useCurrentFrame();

  // 滑入动画进度
  const slideInProgress = interpolate(
    frame,
    [0, durationFrames],
    [0, 1],
    {
      extrapolateLeft: 'clamp',
      extrapolateRight: 'clamp',
    }
  );

  // 根据方向计算位移
  const getTransform = () => {
    let x = 0;
    let y = 0;

    // 入场位移
    const inDistance = 200 * (1 - slideInProgress); // 固定滑动距离为200px
    switch (slideDirection) {
      case 'left':
        x = -inDistance;
        break;
      case 'right':
        x = inDistance;
        break;
      case 'top':
        y = -inDistance;
        break;
      case 'bottom':
        y = inDistance;
        break;
    }

    return `translate(${x}px, ${y}px)`;
  };

  // 透明度动画
  const opacity = interpolate(
    frame,
    [0, durationFrames * 0.7],
    [0, 1],
    {
      extrapolateLeft: 'clamp',
      extrapolateRight: 'clamp',
    }
  );

  // 添加运动模糊效果的模拟
  const blurAmount = Math.abs(slideInProgress - 0.5) * 4;

  return (
    <span
      style={{
        display: 'inline-flex',
        alignItems: 'center',
        justifyContent: 'center',
        height: '100%',
        position: 'relative',
        overflow: 'hidden', // 防止文字滑出可视区域时显示
      }}
    >
      <span
        style={{
          opacity: opacity,
          transform: getTransform(),
          filter: `blur(${blurAmount}px)`,
          fontWeight: 'bold',
          textAlign: 'center',
          textShadow: `0 0 10px ${style.color}`,
          transition: 'filter 0.1s ease',
          ...style,
        }}
      >
        {text}
      </span>

      {/* 添加运动轨迹效果 */}
      <span
        style={{
          position: 'absolute',
          opacity: opacity * 0.3,
          transform: getTransform(),
          fontWeight: 'bold',
          textAlign: 'center',
          filter: 'blur(2px)',
          zIndex: -1,
          ...style,
        }}
      >
        {text}
      </span>
    </span>
  );
}

SlideInEffect.key = 'SlideInEffect';
SlideInEffect.description = 'slide in text effect with directional motion';
SlideInEffect.showName = '滑入'; 