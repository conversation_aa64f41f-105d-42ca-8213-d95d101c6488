import React from 'react';
import { interpolate, useCurrentFrame } from 'remotion';

interface HeartBeatInEffectProps {
  text: string | React.ReactNode[];
  style: React.CSSProperties;
  durationFrames?: number;
}

export default function HeartBeatInEffect({
  text = '展示文本',
  style = {},
  durationFrames = 60,
}: HeartBeatInEffectProps) {
  const frame = useCurrentFrame();

  // 文字淡入
  const opacity = interpolate(
    frame,
    [0, 20],
    [0, 1],
    {
      extrapolateLeft: 'clamp',
      extrapolateRight: 'clamp',
    }
  );

  // 心跳缩放效果
  const heartBeat = Math.sin(frame * 0.3) * 0.1 + 1;
  
  // 爱心飘散
  const hearts = Array.from({ length: 6 }, (_, i) => {
    const heartFrame = frame - i * 8;
    if (heartFrame < 0) return null;
    
    const heartOpacity = interpolate(
      heartFrame,
      [0, 30, 60],
      [0, 1, 0],
      { extrapolateLeft: 'clamp', extrapolateRight: 'clamp' }
    );
    
    const x = Math.sin(heartFrame * 0.1 + i) * 50 + 50;
    const y = 50 - heartFrame * 0.8;
    
    return (
      <div
        key={i}
        style={{
          position: 'absolute',
          left: `${x}%`,
          top: `${y}%`,
          fontSize: '20px',
          opacity: heartOpacity,
          transform: 'translate(-50%, -50%)',
        }}
      >
        ❤️
      </div>
    );
  }).filter(Boolean);

  return (
    <span
      style={{
        display: 'inline-flex',
        alignItems: 'center',
        justifyContent: 'center',
        height: '100%',
        position: 'relative',
      }}
    >
      <span
        style={{
          opacity: opacity,
          transform: `scale(${heartBeat})`,
          color: '#ffffff',
          textAlign: 'center',
          textShadow: '0 0 10px #ffffff',
          ...style,
        }}
      >
        {text}
      </span>
      {hearts}
    </span>
  );
}

HeartBeatInEffect.key = 'HeartBeatInEffect';
HeartBeatInEffect.description = 'heartbeat text effect with floating hearts';
HeartBeatInEffect.showName = '心跳'; 