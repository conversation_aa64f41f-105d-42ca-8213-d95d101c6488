import React from 'react';
import { useCurrentFrame, interpolate, useVideoConfig } from 'remotion';

interface MagneticAttractionEffectProps {
  text: string;
  style?: React.CSSProperties;
}

const MagneticAttractionEffect: React.FC<MagneticAttractionEffectProps> & { showName: string } = ({
  text,
  style
}) => {
  const frame = useCurrentFrame();
  const { fps } = useVideoConfig();
  
  const chars = text.split('');
  const duration = 0.9 * fps; // 0.9秒
  const magneticAcceleration = 15; // 磁力加速度 15px/frame²
  
  return (
    <div style={{
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      position: 'relative',
      backgroundColor: 'transparent',
      ...style
    }}>
      {/* 磁场线背景 */}
      <div style={{
        position: 'absolute',
        left: '50%',
        top: '50%',
        transform: 'translate(-50%, -50%)',
        width: '300px',
        height: '120px',
        pointerEvents: 'none'
      }}>
        {Array.from({ length: 6 }, (_, i) => {
          const distortion = interpolate(frame, [0, duration * 0.7, duration], [0, 20, 5]);
          return (
            <div
              key={`fieldline-${i}`}
              style={{
                position: 'absolute',
                left: '50%',
                top: `${(i + 1) * 16}%`,
                width: '90%',
                height: '1px',
                background: `linear-gradient(90deg, 
                  rgba(0,255,255,0.4) 0%, 
                  rgba(255,0,255,0.4) 100%)`,
                transform: `translateX(-50%) 
                  scaleX(${1 + Math.sin(frame * 0.2 + i) * 0.15}) 
                  rotate(${Math.sin(frame * 0.1 + i) * distortion * 0.1}deg)`,
                opacity: interpolate(frame, [0, duration * 0.3, duration * 0.8], [0.3, 0.8, 0.2])
              }}
            />
          );
        })}
      </div>
      
      {/* 文字在一行显示 */}
      {chars.map((char, index) => {
        const charDelay = index * 1.5;
        const charProgress = Math.max(0, Math.min(1, (frame - charDelay) / (duration * 0.8)));
        
        // 随机初始位置（上下左右）
        const initialDirection = index % 4;
        let startX = 0, startY = 0;
        switch (initialDirection) {
          case 0: startY = -80; break; // 上方
          case 1: startX = 80; break;  // 右方
          case 2: startY = 80; break;  // 下方
          case 3: startX = -80; break; // 左方
        }
        
        // 磁力吸引运动（加速度递增）
        const attractionProgress = Math.pow(charProgress, 1.5); // 加速运动
        const currentX = interpolate(attractionProgress, [0, 1], [startX, 0]);
        const currentY = interpolate(attractionProgress, [0, 1], [startY, 0]);
        
        // 碰撞震动效果
        const collisionFrame = duration * 0.7;
        const vibration = frame > collisionFrame + charDelay && frame < collisionFrame + charDelay + 10
          ? Math.sin((frame - collisionFrame - charDelay) * 3) * 
            interpolate(frame - collisionFrame - charDelay, [0, 10], [3, 0])
          : 0;
        
        // 磁极标识
        const poleColor = index % 2 === 0 ? '#FF4444' : '#4444FF';
        const poleLabel = index % 2 === 0 ? 'S' : 'N';
        
        return (
          <div key={index} style={{
            position: 'relative',
            display: 'inline-block',
            margin: '0 2px',
            transform: `translate(${currentX + vibration}px, ${currentY + vibration}px)`
          }}>
            {/* 磁极标识 */}
            {charProgress > 0 && charProgress < 0.8 && (
              <div style={{
                position: 'absolute',
                left: '50%',
                top: startY > 0 ? '100%' : '-20px',
                transform: 'translateX(-50%)',
                fontSize: '10px',
                color: poleColor,
                fontWeight: 'bold',
                opacity: interpolate(charProgress, [0, 0.3, 0.8], [0, 0.8, 0])
              }}>
                {poleLabel}
              </div>
            )}
            
            {/* 磁力线轨迹 */}
            {charProgress > 0 && charProgress < 0.9 && (
              <>
                {Array.from({ length: 4 }, (_, i) => {
                  const trailProgress = Math.max(0, charProgress - i * 0.1);
                  const trailX = interpolate(trailProgress, [0, 1], [startX, 0]);
                  const trailY = interpolate(trailProgress, [0, 1], [startY, 0]);
                  
                  return (
                    <div
                      key={`trail-${i}`}
                      style={{
                        position: 'absolute',
                        left: '50%',
                        top: '50%',
                        width: '2px',
                        height: '2px',
                        backgroundColor: poleColor,
                        borderRadius: '50%',
                        transform: `translate(-50%, -50%) translate(${trailX}px, ${trailY}px)`,
                        opacity: interpolate(trailProgress, [0, 0.5, 1], [0, 0.6, 0]) * (1 - i * 0.2),
                        boxShadow: `0 0 4px ${poleColor}`
                      }}
                    />
                  );
                })}
              </>
            )}
            
            {/* 碰撞火花 */}
            {frame > collisionFrame + charDelay && frame < collisionFrame + charDelay + 15 && (
              <>
                {Array.from({ length: 8 }, (_, sparkIndex) => {
                  const angle = (sparkIndex / 8) * Math.PI * 2;
                  const distance = magneticAcceleration * 
                    interpolate(frame - collisionFrame - charDelay, [0, 15], [1, 0]);
                  const sparkX = Math.cos(angle) * distance;
                  const sparkY = Math.sin(angle) * distance;
                  
                  return (
                    <div
                      key={sparkIndex}
                      style={{
                        position: 'absolute',
                        left: '50%',
                        top: '50%',
                        width: '2px',
                        height: '6px',
                        background: `linear-gradient(180deg, 
                          rgba(255,255,0,0.8) 0%, 
                          rgba(255,100,0,0.4) 100%)`,
                        transform: `translate(-50%, -50%) translate(${sparkX}px, ${sparkY}px) rotate(${angle * 180 / Math.PI + 90}deg)`,
                        opacity: interpolate(frame - collisionFrame - charDelay, [0, 15], [1, 0]),
                        borderRadius: '1px'
                      }}
                    />
                  );
                })}
              </>
            )}
            
            {/* 主文字 */}
            <span style={{
              fontSize: '60px',
              fontWeight: 'bold',
              color: 'white',
              display: 'inline-block',
              opacity: charProgress > 0 ? 1 : 0,
              filter: `drop-shadow(0 0 4px ${poleColor}) brightness(${1 + vibration * 0.1})`,
              textShadow: `0 0 10px rgba(255,255,255,0.5)`
            }}>
              {char}
            </span>
          </div>
        );
      })}
    </div>
  );
};

MagneticAttractionEffect.showName = "磁极吸附测试效果";

export default MagneticAttractionEffect; 