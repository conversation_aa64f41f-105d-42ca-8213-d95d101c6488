import React from 'react';
import { interpolate, useCurrentFrame } from 'remotion';

interface WaveRippleInEffectProps {
  text: string | React.ReactNode[];
  style: React.CSSProperties;
  durationFrames?: number;
}

export default function WaveRippleInEffect({
  text = '展示文本',
  style = {},
  durationFrames = 60,
}: WaveRippleInEffectProps) {
  const frame = useCurrentFrame();

  // 文字淡入
  const opacity = interpolate(
    frame,
    [0, 20],
    [0, 1],
    {
      extrapolateLeft: 'clamp',
      extrapolateRight: 'clamp',
    }
  );

  // 波纹效果
  const waveScale = 1 + Math.sin(frame * 0.3) * 0.1;

  // 背景波纹
  const ripples = Array.from({ length: 3 }, (_, i) => {
    const rippleFrame = frame - i * 10;
    if (rippleFrame < 0) return null;
    
    const rippleOpacity = interpolate(
      rippleFrame,
      [0, 30],
      [0.3, 0],
      { extrapolateLeft: 'clamp', extrapolateRight: 'clamp' }
    );
    
    const rippleScale = rippleFrame * 2;
    
    return (
      <div
        key={i}
        style={{
          position: 'absolute',
          left: '50%',
          top: '50%',
          width: `${rippleScale}px`,
          height: `${rippleScale}px`,
          borderRadius: '50%',
          border: '2px solid rgba(255, 255, 255, 0.3)',
          opacity: rippleOpacity,
          transform: 'translate(-50%, -50%)',
          pointerEvents: 'none',
        }}
      />
    );
  }).filter(Boolean);

  return (
    <span
      style={{
        display: 'inline-flex',
        alignItems: 'center',
        justifyContent: 'center',
        height: '100%',
        position: 'relative',
      }}
    >
      {ripples}
      <span
        style={{
          opacity: opacity,
          transform: `scale(${waveScale})`,
          color: '#ffffff',
          textAlign: 'center',
          textShadow: '0 0 8px #ffffff',
          ...style,
        }}
      >
        {text}
      </span>
    </span>
  );
}

WaveRippleInEffect.key = 'WaveRippleInEffect';
WaveRippleInEffect.description = 'wave ripple text effect';
WaveRippleInEffect.showName = '波纹'; 