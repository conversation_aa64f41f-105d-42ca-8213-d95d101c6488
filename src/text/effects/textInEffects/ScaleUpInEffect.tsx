import React from 'react';
import { interpolate, useCurrentFrame } from 'remotion';

interface ScaleUpInEffectProps {
  text: string | React.ReactNode[];
  style: React.CSSProperties;
  durationFrames?: number;
}

export default function ScaleUpInEffect({
  text = '展示文本',
  style = {},
  durationFrames = 45,
}: ScaleUpInEffectProps) {
  const frame = useCurrentFrame();

  // 缩放动画
  const scale = interpolate(
    frame,
    [0, durationFrames],
    [0.3, 1],
    {
      extrapolateLeft: 'clamp',
      extrapolateRight: 'clamp',
    }
  );

  // 透明度
  const opacity = interpolate(
    frame,
    [0, 15],
    [0, 1],
    {
      extrapolateLeft: 'clamp',
      extrapolateRight: 'clamp',
    }
  );

  return (
    <span
      style={{
        display: 'inline-flex',
        alignItems: 'center',
        justifyContent: 'center',
        height: '100%',
      }}
    >
      <span
        style={{
          opacity: opacity,
          transform: `scale(${scale})`,
          color: '#ffffff',
          textAlign: 'center',
          textShadow: '0 0 8px #ffffff',
          ...style,
        }}
      >
        {text}
      </span>
    </span>
  );
}

ScaleUpInEffect.key = 'ScaleUpInEffect';
ScaleUpInEffect.description = 'scale up text effect';
ScaleUpInEffect.showName = '放大'; 