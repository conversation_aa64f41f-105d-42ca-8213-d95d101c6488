import React from 'react';
import { interpolate, useCurrentFrame } from 'remotion';

interface RubberBandInEffectProps {
  text: string | React.ReactNode[];
  style: React.CSSProperties;
  durationFrames?: number;
}

export default function RubberBandInEffect({
  text = '展示文本',
  style = {},
  durationFrames = 90,
}: RubberBandInEffectProps) {
  const frame = useCurrentFrame();

  // 橡皮筋效果
  const scaleX = interpolate(
    frame,
    [0, 15, 30, 45, 60, 75, 90],
    [1, 1.25, 0.75, 1.15, 0.95, 1.05, 1],
    {
      extrapolateLeft: 'clamp',
      extrapolateRight: 'clamp',
    }
  );

  const scaleY = interpolate(
    frame,
    [0, 15, 30, 45, 60, 75, 90],
    [1, 0.75, 1.25, 0.85, 1.05, 0.95, 1],
    {
      extrapolateLeft: 'clamp',
      extrapolateRight: 'clamp',
    }
  );

  // 透明度
  const opacity = interpolate(
    frame,
    [0, 15],
    [0, 1],
    {
      extrapolateLeft: 'clamp',
      extrapolateRight: 'clamp',
    }
  );

  return (
    <span
      style={{
        display: 'inline-flex',
        alignItems: 'center',
        justifyContent: 'center',
        height: '100%',
      }}
    >
      <span
        style={{
          opacity: opacity,
          transform: `scaleX(${scaleX}) scaleY(${scaleY})`,
          color: '#ffffff',
          textAlign: 'center',
          textShadow: '0 0 8px #ffffff',
          ...style,
        }}
      >
        {text}
      </span>
    </span>
  );
}

RubberBandInEffect.key = 'RubberBandInEffect';
RubberBandInEffect.description = 'rubber band text effect';
RubberBandInEffect.showName = '橡皮筋'; 