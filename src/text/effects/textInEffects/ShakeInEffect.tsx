import React from 'react';
import { interpolate, useCurrentFrame } from 'remotion';

interface ShakeInEffectProps {
  text: string | React.ReactNode[];
  style: React.CSSProperties;
  durationFrames?: number;
}

export default function ShakeInEffect({
  text = '展示文本',
  style = {},
  durationFrames = 45,
}: ShakeInEffectProps) {
  const frame = useCurrentFrame();

  // 文字淡入
  const opacity = interpolate(
    frame,
    [0, 15],
    [0, 1],
    {
      extrapolateLeft: 'clamp',
      extrapolateRight: 'clamp',
    }
  );

  // 抖动强度逐渐减弱
  const shakeIntensity = interpolate(
    frame,
    [0, durationFrames],
    [10, 0],
    {
      extrapolateLeft: 'clamp',
      extrapolateRight: 'clamp',
    }
  );

  // 随机抖动偏移
  const shakeX = (Math.random() - 0.5) * shakeIntensity;
  const shakeY = (Math.random() - 0.5) * shakeIntensity;

  return (
    <span
      style={{
        display: 'inline-flex',
        alignItems: 'center',
        justifyContent: 'center',
        height: '100%',
        position: 'relative',
      }}
    >
      <span
        style={{
          opacity: opacity,
          transform: `translate(${shakeX}px, ${shakeY}px)`,
          color: '#ffffff',
          textAlign: 'center',
          textShadow: '0 0 8px #ffffff',
          ...style,
        }}
      >
        {text}
      </span>
    </span>
  );
}

ShakeInEffect.key = 'ShakeInEffect';
ShakeInEffect.description = 'shake text effect with decreasing intensity';
ShakeInEffect.showName = '抖动'; 