import React from 'react';
import { useCurrentFrame, interpolate, useVideoConfig } from 'remotion';

interface CrystalGrowthEffectProps {
  text: string;
  color?: string;
  fontSize?: number;
  style?: React.CSSProperties;
}

const CrystalGrowthEffect: React.FC<CrystalGrowthEffectProps> & { showName: string } = ({
  text = "ABCD123",
  color = "#FFFFFF",
  fontSize = 60,
  style = {}
}) => {
  const frame = useCurrentFrame();
  const { fps } = useVideoConfig();
  
  const duration = 2.0 * fps; // 2秒
  const progress = Math.min(1, frame / duration);
  
  // 生长速度50px/s
  const growthSpeed = 50;
  const growthDistance = (frame * growthSpeed) / fps;
  
  return (
    <div style={{
      display: 'inline-flex',
      alignItems: 'center',
      justifyContent: 'center',
      position: 'relative',
      ...style
    }}>
      {text.split('').map((char, index) => {
        const charProgress = Math.max(0, Math.min(1, (progress - index * 0.1) / 0.8));
        
        return (
          <div
            key={index}
            style={{
              position: 'relative',
              display: 'inline-block',
              width: fontSize * 0.9,
              height: fontSize,
              margin: '0 5px'
            }}
          >
            {/* 文字轮廓发光路径 */}
            <span
              style={{
                position: 'absolute',
                left: '50%',
                top: '50%',
                transform: 'translate(-50%, -50%)',
                fontSize,
                color: 'transparent',
                display: 'inline-block',
                // 发光路径描边
                textShadow: charProgress > 0 ? `
                  0 0 0 2px rgba(100, 200, 255, ${charProgress * 0.8}),
                  0 0 10px rgba(100, 200, 255, ${charProgress * 0.6}),
                  0 0 20px rgba(100, 200, 255, ${charProgress * 0.4})
                ` : 'none',
                WebkitTextStroke: charProgress > 0 ? `1px rgba(100, 200, 255, ${charProgress})` : 'none'
              }}
            >
              {char}
            </span>
            
            {/* 晶体生长主体 */}
            {charProgress > 0.2 && (
              <span
                style={{
                  position: 'absolute',
                  left: '50%',
                  top: '50%',
                  transform: 'translate(-50%, -50%)',
                  fontSize,
                  color: '#87CEEB',
                  display: 'inline-block',
                  opacity: interpolate(charProgress, [0.2, 0.8], [0, 1]),
                  // 晶体质感
                  textShadow: `
                    0 0 5px #87CEEB,
                    0 0 10px #B0E0E6,
                    inset 0 0 15px rgba(255, 255, 255, 0.3)
                  `,
                  filter: 'drop-shadow(0 0 8px rgba(135, 206, 235, 0.6))'
                }}
              >
                {char}
              </span>
            )}
            
            {/* 生长尖端高亮 */}
            {charProgress > 0.1 && charProgress < 0.9 && Array.from({ length: 8 }, (_, tipIndex) => {
              const angle = (tipIndex / 8) * Math.PI * 2;
              const radius = fontSize * 0.3;
              const tipX = Math.cos(angle) * radius;
              const tipY = Math.sin(angle) * radius;
              
              return (
                <div
                  key={tipIndex}
                  style={{
                    position: 'absolute',
                    left: '50%',
                    top: '50%',
                    transform: `translate(${tipX}px, ${tipY}px)`,
                    width: 4,
                    height: 4,
                    backgroundColor: '#FFFFFF',
                    borderRadius: '50%',
                    opacity: Math.sin(frame * 0.3 + tipIndex) * 0.5 + 0.5,
                    boxShadow: '0 0 8px #FFFFFF',
                    animation: `pulse 0.5s ease-in-out infinite alternate`
                  }}
                />
              );
            })}
            
            {/* 分枝处折射光斑 */}
            {charProgress > 0.3 && Array.from({ length: 4 }, (_, branchIndex) => {
              const branchAngle = (branchIndex / 4) * Math.PI * 2 + frame * 0.02;
              const branchRadius = fontSize * 0.2;
              const branchX = Math.cos(branchAngle) * branchRadius;
              const branchY = Math.sin(branchAngle) * branchRadius;
              
              return (
                <div
                  key={`branch-${branchIndex}`}
                  style={{
                    position: 'absolute',
                    left: '50%',
                    top: '50%',
                    transform: `translate(${branchX}px, ${branchY}px)`,
                    width: 8,
                    height: 8,
                    background: `radial-gradient(circle, 
                      rgba(255, 255, 255, 0.8) 0%, 
                      rgba(135, 206, 235, 0.6) 50%, 
                      transparent 100%)`,
                    borderRadius: '50%',
                    opacity: Math.sin(frame * 0.2 + branchIndex * 1.5) * 0.3 + 0.4
                  }}
                />
              );
            })}
            
            {/* 完全成形后闪烁 */}
            {charProgress > 0.9 && (
              <div
                style={{
                  position: 'absolute',
                  left: '50%',
                  top: '50%',
                  transform: 'translate(-50%, -50%)',
                  width: fontSize * 0.8,
                  height: fontSize * 1.2,
                  background: `radial-gradient(ellipse, 
                    rgba(255, 255, 255, ${frame % 60 < 30 ? 0.3 : 0.1}) 0%, 
                    transparent 70%)`,
                  borderRadius: '50%',
                  pointerEvents: 'none'
                }}
              />
            )}
            
            {/* 晶体内部光线 */}
            {charProgress > 0.5 && (
              <div
                style={{
                  position: 'absolute',
                  left: '50%',
                  top: '50%',
                  width: fontSize * 0.4,
                  height: 2,
                  background: `linear-gradient(90deg, 
                    transparent, 
                    rgba(255, 255, 255, 0.8), 
                    transparent)`,
                  borderRadius: '1px',
                  opacity: Math.sin(frame * 0.1) * 0.5 + 0.5,
                  transform: `translate(-50%, -50%) rotate(${frame * 2}deg)`
                }}
              />
            )}
          </div>
        );
      })}
    </div>
  );
};

CrystalGrowthEffect.showName = "水晶生长";

export default CrystalGrowthEffect; 