import React from 'react';
import { interpolate, useCurrentFrame } from 'remotion';

interface JelloInEffectProps {
  text: string | React.ReactNode[];
  style: React.CSSProperties;
  durationFrames?: number;
}

export default function JelloInEffect({
  text = '展示文本',
  style = {},
  durationFrames = 100,
}: JelloInEffectProps) {
  const frame = useCurrentFrame();

  // 果冻效果
  const skewX = interpolate(
    frame,
    [0, 11.1, 22.2, 33.3, 44.4, 55.5, 66.6, 77.7, 88.8, 100],
    [0, -12.5, 6.25, -3.125, 1.5625, -0.78125, 0.390625, -0.1953125, 0.09765625, 0],
    {
      extrapolateLeft: 'clamp',
      extrapolateRight: 'clamp',
    }
  );

  const skewY = interpolate(
    frame,
    [0, 11.1, 22.2, 33.3, 44.4, 55.5, 66.6, 77.7, 88.8, 100],
    [0, -12.5, 6.25, -3.125, 1.5625, -0.78125, 0.390625, -0.1953125, 0.09765625, 0],
    {
      extrapolateLeft: 'clamp',
      extrapolateRight: 'clamp',
    }
  );

  // 透明度
  const opacity = interpolate(
    frame,
    [0, 11.1],
    [0, 1],
    {
      extrapolateLeft: 'clamp',
      extrapolateRight: 'clamp',
    }
  );

  return (
    <span
      style={{
        display: 'inline-flex',
        alignItems: 'center',
        justifyContent: 'center',
        height: '100%',
      }}
    >
      <span
        style={{
          opacity: opacity,
          transform: `skewX(${skewX}deg) skewY(${skewY}deg)`,
          color: '#ffffff',
          textAlign: 'center',
          textShadow: '0 0 8px #ffffff',
          ...style,
        }}
      >
        {text}
      </span>
    </span>
  );
}

JelloInEffect.key = 'JelloInEffect';
JelloInEffect.description = 'jello text effect';
JelloInEffect.showName = '果冻'; 