import React from 'react';
import { useCurrentFrame, interpolate, useVideoConfig } from 'remotion';

interface Print3DEffectProps {
  text: string;
  style?: React.CSSProperties;
}

const Print3DEffect: React.FC<Print3DEffectProps> & { showName: string } = ({
  text,
  style
}) => {
  const frame = useCurrentFrame();
  const { fps } = useVideoConfig();
  
  const chars = text.split('');
  const duration = 2.2 * fps; // 2.2秒
  const layerThickness = 0.8; // 层厚0.8px
  const layers = 10; // 打印层数
  
  return (
    <div style={{
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      position: 'relative',
      backgroundColor: 'transparent',
      ...style
    }}>

      
      {/* 文字在一行显示 */}
      {chars.map((char, index) => {
        const charDelay = index * 6;
        const charProgress = Math.max(0, Math.min(1, (frame - charDelay) / (duration * 0.8)));
        
        // 打印进度（逐层打印）
        const printProgress = charProgress * layers;
        const currentLayer = Math.floor(printProgress);
        const layerProgress = printProgress - currentLayer;
        
        // 挤出机头位置
        const extruderActive = charProgress > 0 && charProgress < 0.9;
        const extruderX = interpolate(charProgress, [0, 0.8], [-20, 40]);
        
        return (
          <div key={index} style={{
            position: 'relative',
            display: 'inline-block',
            margin: '0 2px'
          }}>

            
            {/* 逐层打印的文字 */}
            {Array.from({ length: layers }, (_, layerIndex) => {
              const layerVisible = currentLayer >= layerIndex;
              const isCurrentLayer = currentLayer === layerIndex;
              
              if (!layerVisible) return null;
              
              // 当前层的打印进度
              const currentLayerOpacity = isCurrentLayer 
                ? layerProgress 
                : 1;
              
              // 冷却收缩效果
              const shrinkage = isCurrentLayer 
                ? interpolate(layerProgress, [0, 0.5, 1], [1.1, 1.05, 1])
                : 1;
              
              return (
                <div
                  key={`layer-${layerIndex}`}
                  style={{
                    position: 'absolute',
                    left: 0,
                    top: -layerIndex * layerThickness,
                    fontSize: '60px',
                    fontWeight: 'bold',
                    color: layerIndex === layers - 1 ? 'white' : `rgba(255,255,255,${0.3 + layerIndex * 0.07})`,
                    opacity: currentLayerOpacity,
                    transform: `scale(${shrinkage})`,
                    transformOrigin: 'bottom center',
                    pointerEvents: 'none',
                    textShadow: `0 0 ${2 + layerIndex}px rgba(255,255,255,0.3)`
                  }}
                >
                  {char}
                </div>
              );
            })}
            
            {/* 支撑结构 */}
            {charProgress > 0.3 && charProgress < 0.8 && (
              <>
                <div style={{
                  position: 'absolute',
                  left: '25%',
                  top: -30,
                  width: '1px',
                  height: 35,
                  background: `linear-gradient(180deg, 
                    rgba(200,200,200,0.6) 0%, 
                    rgba(150,150,150,0.3) 100%)`,
                  opacity: interpolate(charProgress, [0.3, 0.6, 0.8], [0, 0.8, 0])
                }} />
                <div style={{
                  position: 'absolute',
                  left: '75%',
                  top: -25,
                  width: '1px',
                  height: 30,
                  background: `linear-gradient(180deg, 
                    rgba(200,200,200,0.6) 0%, 
                    rgba(150,150,150,0.3) 100%)`,
                  opacity: interpolate(charProgress, [0.3, 0.6, 0.8], [0, 0.8, 0])
                }} />
              </>
            )}
            
            {/* 基础透明文字（占位） */}
            <span style={{
              fontSize: '60px',
              fontWeight: 'bold',
              color: 'transparent',
              display: 'inline-block'
            }}>
              {char}
            </span>
          </div>
        );
      })}
      

      

    </div>
  );
};

Print3DEffect.showName = "3D打印";

export default Print3DEffect;