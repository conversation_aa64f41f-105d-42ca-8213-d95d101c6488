import React from 'react';
import { interpolate, useCurrentFrame } from 'remotion';

interface BounceInEffectProps {
  text: string | React.ReactNode[];
  style: React.CSSProperties;
  durationFrames?: number;
}

export default function BounceInEffect({
  text = '展示文本',
  style = {},
  durationFrames = 60,
}: BounceInEffectProps) {
  const frame = useCurrentFrame();

  // 弹跳动画
  const bounceY = interpolate(
    frame,
    [0, 15, 30, 45, 60],
    [30, -10, 5, -2, 0],
    {
      extrapolateLeft: 'clamp',
      extrapolateRight: 'clamp',
    }
  );

  // 透明度
  const opacity = interpolate(
    frame,
    [0, 15],
    [0, 1],
    {
      extrapolateLeft: 'clamp',
      extrapolateRight: 'clamp',
    }
  );

  return (
    <span
      style={{
        display: 'inline-flex',
        alignItems: 'center',
        justifyContent: 'center',
        height: '100%',
      }}
    >
      <span
        style={{
          opacity: opacity,
          transform: `translateY(${bounceY}px)`,
          color: '#ffffff',
          textAlign: 'center',
          textShadow: '0 0 8px #ffffff',
          ...style,
        }}
      >
        {text}
      </span>
    </span>
  );
}

BounceInEffect.key = 'BounceInEffect';
BounceInEffect.description = 'bounce text effect';
BounceInEffect.showName = '弹跳'; 