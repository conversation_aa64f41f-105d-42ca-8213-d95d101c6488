import React from 'react';
import { useCurrentFrame, interpolate, useVideoConfig } from 'remotion';
import { TextInEffectProps } from './type';

export default function SwordForgingEffect({
  text = "ABCD123+铁剑锻造",
  style = {},
  durationFrames = 78,
}: TextInEffectProps) {
  const frame = useCurrentFrame();
  const { fps } = useVideoConfig();
  
  const duration = durationFrames;
  
  // 计算文字长度
  const textLength = Array.from(text).length;
  
  // 从style中获取字体大小和颜色，提供合理的默认值
  const fontSize = style?.fontSize ? parseInt(String(style.fontSize)) : 60;
  const color = style?.color || '#FFFFFF';
  
  const halfLength = Math.ceil(textLength / 2);
  const hammerStrikes = 3; // 锻打次数
  
  return (
    <span
      style={{
        position: 'relative',
      }}
    >
      {Array.from(text).map((char, index) => {
        // 根据文字长度和动画时长计算每个字符的延迟
        const maxStartDelay = duration * 0.6;
        const charDelay = textLength > 1 ? (maxStartDelay * index) / (textLength - 1) : 0;
        
        // 每个字符的入场动画时长至少占动画时长的40%
        const charAnimationDuration = Math.max(duration * 0.4, duration - charDelay);
        
        // 计算当前字符的入场进度
        const charStartFrame = charDelay;
        const charCurrentFrame = Math.max(0, frame - charStartFrame);
        const charProgress = Math.min(1, charCurrentFrame / charAnimationDuration);
        
        // 判断是上半还是下半部分
        const isTopHalf = index < halfLength;
        
        // 锻造效果
        const heatIntensity = isTopHalf 
          ? interpolate(charProgress, [0, 0.3, 0.8, 1], [0, 1, 0.7, 0.3])
          : 0;
        const moveY = isTopHalf 
          ? interpolate(charProgress, [0, 0.8, 1], [-fontSize * 0.75, 0, 0])
          : interpolate(charProgress, [0, 0.8, 1], [fontSize * 0.75, 0, 0]);
        
        return (
          <span 
            key={index} 
            style={{ 
              position: 'relative',
              display: 'inline-block',
            }}
          >
            {/* 透明占位文字，提供自然尺寸 */}
            <span
              style={{
                opacity: 0,
                ...style
              }}
            >
              {char}
            </span>
            
            {/* 火星飞溅 - 仅对上半部分 */}
            {isTopHalf && Array.from({ length: 6 }, (_, i) => (
              <span
                key={`spark-${i}`}
                style={{
                  position: 'absolute',
                  left: '50%',
                  top: `calc(50% + ${moveY - 10}px)`,
                  transform: `translate(-50%, -50%) translate(${(Math.random() - 0.5) * 20}px, ${Math.random() * -15}px)`,
                  width: '2px',
                  height: '4px',
                  background: `linear-gradient(180deg, 
                    rgba(255,165,0,${heatIntensity}) 0%, 
                    rgba(255,69,0,${heatIntensity * 0.5}) 100%)`,
                  opacity: heatIntensity,
                  pointerEvents: 'none'
                }}
              />
            ))}
            
            {/* 烧红光晕 - 仅对上半部分 */}
            {isTopHalf && (
              <span style={{
                position: 'absolute',
                left: '50%',
                top: `calc(50% + ${moveY}px)`,
                transform: 'translate(-50%, -50%)',
                fontSize: fontSize,
                color: `rgba(255,69,0,${heatIntensity})`,
                filter: `blur(${heatIntensity * 2}px)`,
                pointerEvents: 'none',
                ...style
              }}>
                {char}
              </span>
            )}
            
            <span style={{
              position: 'absolute',
              left: '50%',
              top: '50%',
              transform: `translate(-50%, calc(-50% + ${moveY}px))`,
              fontSize: fontSize,
              color: (isTopHalf && heatIntensity > 0.5) ? 
                `rgb(255,${255 - heatIntensity * 100},${255 - heatIntensity * 200})` : color,
              display: 'inline-block',
              opacity: charProgress > 0 ? 1 : 0,
              filter: isTopHalf ? 'none' : `drop-shadow(0 0 2px rgba(100,100,100,0.5))`,
              ...style
            }}>
              {char}
            </span>
          </span>
        );
      })}
      
      {/* 锻打效果 */}
      {frame > duration * 0.5 && (
        <span style={{
          position: 'absolute',
          left: '50%',
          top: '50%',
          transform: 'translate(-50%, -50%)',
          pointerEvents: 'none'
        }}>
          {Array.from({ length: hammerStrikes }, (_, strikeIndex) => {
            const strikeFrame = duration * 0.5 + strikeIndex * 8;
            const isStriking = frame >= strikeFrame && frame < strikeFrame + 5;
            
            if (!isStriking) return null;
            
            return (
              <span key={`strike-${strikeIndex}`}>
                {/* 铁锤冲击波 */}
                <span style={{
                  position: 'absolute',
                  left: -25,
                  top: -25,
                  width: '50px',
                  height: '50px',
                  border: '3px solid rgba(255,255,255,0.6)',
                  borderRadius: '50%',
                  transform: `scale(${interpolate(frame - strikeFrame, [0, 5], [0.5, 2])})`,
                  opacity: interpolate(frame - strikeFrame, [0, 5], [0.8, 0])
                }} />
                
                {/* 锻打火花 */}
                {Array.from({ length: 12 }, (_, i) => {
                  const angle = (i * 30) * Math.PI / 180;
                  const distance = 20;
                  return (
                    <span
                      key={`hammer-spark-${i}`}
                      style={{
                        position: 'absolute',
                        left: Math.cos(angle) * distance,
                        top: Math.sin(angle) * distance,
                        width: '3px',
                        height: '6px',
                        backgroundColor: '#FFD700',
                        opacity: interpolate(frame - strikeFrame, [0, 3], [1, 0])
                      }}
                    />
                  );
                })}
              </span>
            );
          })}
        </span>
      )}
      
      {/* 淬火蒸汽爆发 */}
      {frame > duration * 0.8 && frame < duration * 0.95 && (
        <span style={{
          position: 'absolute',
          left: '50%',
          top: '50%',
          transform: 'translate(-50%, -50%)',
          pointerEvents: 'none'
        }}>
          {Array.from({ length: 8 }, (_, i) => (
            <span
              key={`steam-${i}`}
              style={{
                position: 'absolute',
                left: (i - 4) * 8,
                top: -10 - Math.random() * 20,
                width: '4px',
                height: '12px',
                background: `linear-gradient(180deg, 
                  rgba(255,255,255,0.6) 0%, 
                  transparent 100%)`,
                opacity: interpolate(frame, [duration * 0.8, duration * 0.95], [0.8, 0]),
                transform: `translateY(${interpolate(frame, [duration * 0.8, duration * 0.95], [0, -30])}px)`
              }}
            />
          ))}
        </span>
      )}
    </span>
  );
}

SwordForgingEffect.key = 'SwordForgingEffect';
SwordForgingEffect.description = 'sword forging heat treatment effect';
SwordForgingEffect.showName = "铁剑锻造"; 