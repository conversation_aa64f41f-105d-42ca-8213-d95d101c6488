import React from 'react';
import { useCurrentFrame, interpolate, useVideoConfig } from 'remotion';
import { TextInEffectProps } from './type';

export default function CalligraphyEffect({
  text = "ABCD123+书法笔触",
  style = {},
  durationFrames = 90,
}: TextInEffectProps) {
  const frame = useCurrentFrame();
  
  const duration = durationFrames;
  
  // 计算文字长度
  const textLength = Array.from(text).length;
  
  // 生成噪声函数用于飞白效果
  const noise = (x: number, y: number) => {
    const n = Math.sin(x * 12.9898 + y * 78.233) * 43758.5453;
    return n - Math.floor(n);
  };
  
  return (
    <span
      style={{        
        position: 'relative',
      }}
    >
      {Array.from(text).map((char, index) => {
        // 根据文字长度和动画时长计算每个字符的延迟
        const maxStartDelay = duration * 0.6;
        const charDelay = textLength > 1 ? (maxStartDelay * index) / (textLength - 1) : 0;
        
        // 每个字符的入场动画时长至少占动画时长的40%
        const charAnimationDuration = Math.max(duration * 0.4, duration - charDelay);
        
        // 计算当前字符的入场进度
        const charStartFrame = charDelay;
        const charCurrentFrame = Math.max(0, frame - charStartFrame);
        const charProgress = Math.min(1, charCurrentFrame / charAnimationDuration);
        
        // 笔画绘制进度
        const strokeProgress = charProgress;
        
        // 墨迹扩散效果
        const inkSpread = interpolate(charProgress, [0.8, 1], [0, 1]);
        
        // 飞白效果强度
        const flyWhiteIntensity = Math.sin((frame - charStartFrame) * 0.3 + index) * 0.1 + 0.1;
        
        return (
          <span
            key={index}
            style={{
              position: 'relative',
              display: 'inline-block'
            }}
          >
            {/* 透明占位文字，提供自然尺寸 */}
            <span
              style={{
                opacity: 0,
                ...style
              }}
            >
              {char}
            </span>
            
            {/* 主要笔画 */}
            <span
              style={{
                position: 'absolute',
                left: '50%',
                top: '50%',
                transform: 'translate(-50%, -50%)',
                display: 'inline-block',
                opacity: strokeProgress,
                filter: `
                  drop-shadow(0 0 ${inkSpread * 2}px rgba(0,0,0,0.3))
                  contrast(${1 + flyWhiteIntensity})
                `,
                ...style
              }}
            >
              {char}
            </span>
            
            {/* 笔尖位置指示器 */}
            {strokeProgress > 0 && strokeProgress < 1 && (
              <span
                style={{
                  position: 'absolute',
                  left: `${strokeProgress * 100}%`,
                  top: `${50 + Math.sin(strokeProgress * Math.PI * 2) * 20}%`,
                  width: 4,
                  height: 4,
                  borderRadius: '50%',
                  backgroundColor: style.color || '#000000',
                  opacity: 0.6,
                  transform: 'translate(-50%, -50%)'
                }}
              />
            )}
            
            {/* 墨迹扩散粒子 */}
            {inkSpread > 0 && Array.from({ length: 8 }, (_, i) => {
              const angle = (i / 8) * Math.PI * 2;
              const distance = inkSpread * 20;
              const particleX = Math.cos(angle) * distance;
              const particleY = Math.sin(angle) * distance;
              const particleSize = (1 - i / 8) * 3;
              
              return (
                <span
                  key={`ink-${i}`}
                  style={{
                    position: 'absolute',
                    left: '50%',
                    top: '50%',
                    width: particleSize,
                    height: particleSize,
                    borderRadius: '50%',
                    backgroundColor: style.color || '#000000',
                    opacity: (1 - inkSpread) * 0.5,
                    transform: `translate(${particleX}px, ${particleY}px)`,
                    pointerEvents: 'none'
                  }}
                />
              );
            })}
          </span>
        );
      })}
    </span>
  );
}

CalligraphyEffect.key = 'CalligraphyEffect';
CalligraphyEffect.description = 'calligraphy brush stroke effect';
CalligraphyEffect.showName = "书法笔触"; 