import React from 'react';
import { useCurrentFrame, interpolate, useVideoConfig } from 'remotion';
import { TextInEffectProps } from './type';

export default function NeonTubeEffect({
  text = "ABCD123+霓虹灯管",
  style = {},
  durationFrames = 60,
}: TextInEffectProps) {
  const frame = useCurrentFrame();
  const { fps } = useVideoConfig();
  
  const duration = durationFrames;
  
  // 计算文字长度
  const textLength = Array.from(text).length;
  
  // 从style中获取颜色和字体大小，提供合理的默认值
  const fontSize = style?.fontSize ? parseInt(String(style.fontSize)) : 60;
  const color = style?.color || '#00FFFF';
  
  return (
    <span
      style={{
        position: 'relative',
      }}
    >
      {Array.from(text).map((char, index) => {
        // 根据文字长度和动画时长计算每个字符的延迟
        const maxStartDelay = duration * 0.6;
        const charDelay = textLength > 1 ? (maxStartDelay * index) / (textLength - 1) : 0;
        
        // 每个字符的入场动画时长至少占动画时长的40%
        const charAnimationDuration = Math.max(duration * 0.4, duration - charDelay);
        
        // 计算当前字符的入场进度
        const charStartFrame = charDelay;
        const charCurrentFrame = Math.max(0, frame - charStartFrame);
        const charProgress = Math.min(1, charCurrentFrame / charAnimationDuration);
        
        // 点亮进度
        const isLit = charProgress > 0.3;
        
        // 完全点亮后的闪烁效果
        const blinkPhase = charProgress > 0.8 ? (frame - charStartFrame - charAnimationDuration * 0.8) : 0;
        const blink1 = blinkPhase > 0 && blinkPhase < 3 ? Math.abs(Math.sin(blinkPhase * 2)) : 1;
        const blink2 = blinkPhase > 10 && blinkPhase < 13 ? Math.abs(Math.sin(blinkPhase * 2)) : 1;
        const lightIntensity = charProgress > 0.8 ? Math.min(blink1, blink2) : interpolate(charProgress, [0.3, 0.8], [0, 1]);
        
        return (
          <span
            key={index}
            style={{
              position: 'relative',
              display: 'inline-block',
            }}
          >
            {/* 透明占位文字，提供自然尺寸 */}
            <span
              style={{
                opacity: 0,
                ...style
              }}
            >
              {char}
            </span>
            
            {/* 暗色轮廓 - 仅在未点亮时显示 */}
            {!isLit && (
              <span
                style={{
                  position: 'absolute',
                  left: '50%',
                  top: '50%',
                  transform: 'translate(-50%, -50%)',
                  fontSize: fontSize,
                  fontWeight: 'bold',
                  color: '#333',
                  display: 'inline-block',
                  opacity: 0.3,
                  ...style
                }}
              >
                {char}
              </span>
            )}
            
            {/* 霓虹发光文字 */}
            {isLit && (
              <span
                style={{
                  position: 'absolute',
                  left: '50%',
                  top: '50%',
                  transform: 'translate(-50%, -50%)',
                  fontSize: fontSize,
                  fontWeight: 'bold',
                  color: color,
                  display: 'inline-block',
                  // @ts-ignore
                  textShadow: `
                    0 0 5px ${color},
                    0 0 10px ${color},
                    0 0 15px ${color},
                    0 0 20px ${color},
                    0 0 25px ${color}
                  `,
                  opacity: lightIntensity,
                  filter: `brightness(${1 + lightIntensity * 0.5})`,
                  ...style
                }}
              >
                {char}
              </span>
            )}
            
            {/* 连接点电火花 - 随机闪烁 */}
            {isLit && index < textLength - 1 && charProgress > 0.5 && (frame - charStartFrame) % 6 < 2 && (
              <span
                style={{
                  position: 'absolute',
                  right: -2,
                  top: '50%',
                  width: 4,
                  height: 1,
                  background: color,
                  opacity: 0.8,
                  boxShadow: `0 0 6px ${color}`,
                  transform: 'translateY(-50%)',
                  borderRadius: '1px'
                }}
              />
            )}
            
            {/* 背景光晕扩散 */}
            {charProgress > 0.8 && (
              <span
                style={{
                  position: 'absolute',
                  left: '50%',
                  top: '50%',
                  transform: 'translate(-50%, -50%)',
                  width: fontSize * 1.5,
                  height: fontSize * 1.5,
                                                        background: `radial-gradient(ellipse, rgba(0, 255, 255, 0.1) 0%, transparent 70%)`,
                  pointerEvents: 'none',
                  borderRadius: '50%'
                }}
              />
            )}
          </span>
        );
      })}
    </span>
  );
}

NeonTubeEffect.key = 'NeonTubeEffect';
NeonTubeEffect.description = 'neon tube lighting effect';
NeonTubeEffect.showName = "霓虹灯管"; 