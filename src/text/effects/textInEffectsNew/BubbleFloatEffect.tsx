import React from 'react';
import { useCurrentFrame, interpolate, useVideoConfig } from 'remotion';
import { TextInEffectProps } from './type';

export default function BubbleFloatEffect({
  text = "ABCD123+气泡上浮",
  style = {},
  durationFrames = 90,
}: TextInEffectProps) {
  const frame = useCurrentFrame();
  
  const duration = durationFrames;
  
  // 计算文字长度
  const textLength = Array.from(text).length;
  
  return (
    <span
      style={{        
        position: 'relative',
      }}
    >
      {Array.from(text).map((char, index) => {
        // 根据文字长度和动画时长计算每个字符的延迟
        const maxStartDelay = duration * 0.6;
        const charDelay = textLength > 1 ? (maxStartDelay * index) / (textLength - 1) : 0;
        
        // 每个字符的入场动画时长至少占动画时长的40%
        const charAnimationDuration = Math.max(duration * 0.4, duration - charDelay);
        
        // 计算当前字符的入场进度
        const charStartFrame = charDelay;
        const charCurrentFrame = Math.max(0, frame - charStartFrame);
        const charProgress = Math.min(1, charCurrentFrame / charAnimationDuration);
        
        // 从底部上浮到正常位置
        const translateY = interpolate(charProgress, [0, 1], [100, 0]);
        
        // 透明度和特效强度
        const opacity = interpolate(charProgress, [0, 0.3, 1], [0, 0.7, 1]);
        const shadowIntensity = charProgress;
        
        return (
          <span
            key={index}
            style={{
              position: 'relative',
              display: 'inline-block',              
            }}
          >
            <span
              style={{                                
                display: 'inline-block',
                transform: `translateY(${translateY}px)`,
                opacity,
                filter: `blur(${0.5 * shadowIntensity}px)`,
                textShadow: shadowIntensity > 0 ? `
                  0 0 ${3 * shadowIntensity}px rgba(255,255,255,${0.5 * shadowIntensity}),
                  0 0 ${8 * shadowIntensity}px rgba(255,255,255,${0.3 * shadowIntensity})
                ` : 'none',
                ...style
              }}
            >
              {char}
            </span>
          </span>
        );
      })}
    </span>
  );
}

BubbleFloatEffect.key = 'BubbleFloatEffect';
BubbleFloatEffect.description = 'bubble float effect with natural ending';
BubbleFloatEffect.showName = "气泡上浮"; 