import React from 'react';
import { useCurrentFrame, interpolate, useVideoConfig } from 'remotion';
import { TextInEffectProps } from './type';

export default function CrystalGrowthEffect({
  text = "ABCD123+水晶生长",
  style = {},
  durationFrames = 120,
}: TextInEffectProps) {
  const frame = useCurrentFrame();
  const { fps } = useVideoConfig();
  
  // 从style中获取字体大小，默认60px
  const fontSize = style?.fontSize ? parseInt(String(style.fontSize)) : 60;
  const color = style?.color || '#FFFFFF';
  
  const duration = durationFrames;
  
  // 计算文字长度
  const textLength = Array.from(text).length;
  
  return (
    <span
      style={{
        position: 'relative',
      }}
    >
      {Array.from(text).map((char, index) => {
        // 根据文字长度和动画时长计算每个字符的延迟
        const maxStartDelay = duration * 0.6;
        const charDelay = textLength > 1 ? (maxStartDelay * index) / (textLength - 1) : 0;
        
        // 每个字符的入场动画时长至少占动画时长的40%
        const charAnimationDuration = Math.max(duration * 0.4, duration - charDelay);
        
        // 计算当前字符的入场进度
        const charStartFrame = charDelay;
        const charCurrentFrame = Math.max(0, frame - charStartFrame);
        const charProgress = Math.min(1, charCurrentFrame / charAnimationDuration);
        
        return (
          <span
            key={index}
            style={{
              position: 'relative',
              display: 'inline-block',
            }}
          >
            {/* 透明占位文字，提供自然尺寸 */}
            <span
              style={{
                opacity: 0,
                ...style
              }}
            >
              {char}
            </span>
            
            {/* 文字轮廓发光路径 */}
            <span
              style={{
                position: 'absolute',
                left: '50%',
                top: '50%',
                transform: 'translate(-50%, -50%)',
                fontSize: fontSize,
                fontWeight: 'bold',
                color: 'transparent',
                display: 'inline-block',
                // 发光路径描边
                textShadow: charProgress > 0 ? `
                  0 0 0 ${fontSize * 0.033}px rgba(100, 200, 255, ${charProgress * 0.8}),
                  0 0 ${fontSize * 0.17}px rgba(100, 200, 255, ${charProgress * 0.6}),
                  0 0 ${fontSize * 0.33}px rgba(100, 200, 255, ${charProgress * 0.4})
                ` : 'none',
                WebkitTextStroke: charProgress > 0 ? `${fontSize * 0.017}px rgba(100, 200, 255, ${charProgress})` : 'none',
                ...style
              }}
            >
              {char}
            </span>
            
            {/* 晶体生长主体 */}
            {charProgress > 0.2 && (
              <span
                style={{
                  position: 'absolute',
                  left: '50%',
                  top: '50%',
                  transform: 'translate(-50%, -50%)',
                  fontSize: fontSize,
                  fontWeight: 'bold',
                  color: color,
                  display: 'inline-block',
                  opacity: interpolate(charProgress, [0.2, 0.8], [0, 1]),
                  // 晶体质感
                  textShadow: `
                    0 0 ${fontSize * 0.08}px ${color},
                    0 0 ${fontSize * 0.17}px rgba(176, 224, 230, 0.8),
                    inset 0 0 ${fontSize * 0.25}px rgba(255, 255, 255, 0.3)
                  `,
                  filter: `drop-shadow(0 0 ${fontSize * 0.13}px rgba(135, 206, 235, 0.6))`,
                  ...style
                }}
              >
                {char}
              </span>
            )}
            
            {/* 生长尖端高亮 */}
            {charProgress > 0.1 && charProgress < 0.9 && Array.from({ length: 8 }, (_, tipIndex) => {
              const angle = (tipIndex / 8) * Math.PI * 2;
              const radius = fontSize * 0.3;
              const tipX = Math.cos(angle) * radius;
              const tipY = Math.sin(angle) * radius;
              
              return (
                <span
                  key={tipIndex}
                  style={{
                    position: 'absolute',
                    left: '50%',
                    top: '50%',
                    transform: `translate(${tipX}px, ${tipY}px)`,
                    width: fontSize * 0.067,
                    height: fontSize * 0.067,
                    backgroundColor: '#FFFFFF',
                    borderRadius: '50%',
                    opacity: Math.sin((frame - charStartFrame) * 0.3 + tipIndex) * 0.5 + 0.5,
                    boxShadow: `0 0 ${fontSize * 0.13}px #FFFFFF`
                  }}
                />
              );
            })}
            
            {/* 分枝处折射光斑 */}
            {charProgress > 0.3 && Array.from({ length: 4 }, (_, branchIndex) => {
              const branchAngle = (branchIndex / 4) * Math.PI * 2 + (frame - charStartFrame) * 0.02;
              const branchRadius = fontSize * 0.2;
              const branchX = Math.cos(branchAngle) * branchRadius;
              const branchY = Math.sin(branchAngle) * branchRadius;
              
              return (
                <span
                  key={`branch-${branchIndex}`}
                  style={{
                    position: 'absolute',
                    left: '50%',
                    top: '50%',
                    transform: `translate(${branchX}px, ${branchY}px)`,
                    width: fontSize * 0.13,
                    height: fontSize * 0.13,
                    background: `radial-gradient(circle, 
                      rgba(255, 255, 255, 0.8) 0%, 
                      rgba(135, 206, 235, 0.6) 50%, 
                      transparent 100%)`,
                    borderRadius: '50%',
                    opacity: Math.sin((frame - charStartFrame) * 0.2 + branchIndex * 1.5) * 0.3 + 0.4
                  }}
                />
              );
            })}
            
            {/* 完全成形后闪烁 */}
            {charProgress > 0.9 && (
              <span
                style={{
                  position: 'absolute',
                  left: '50%',
                  top: '50%',
                  transform: 'translate(-50%, -50%)',
                  width: fontSize * 0.8,
                  height: fontSize * 1.2,
                  background: `radial-gradient(ellipse, 
                    rgba(255, 255, 255, ${((frame - charStartFrame) % 60 < 30) ? 0.3 : 0.1}) 0%, 
                    transparent 70%)`,
                  borderRadius: '50%',
                  pointerEvents: 'none'
                }}
              />
            )}
            
            {/* 晶体内部光线 */}
            {charProgress > 0.5 && (
              <span
                style={{
                  position: 'absolute',
                  left: '50%',
                  top: '50%',
                  width: fontSize * 0.4,
                  height: fontSize * 0.033,
                  background: `linear-gradient(90deg, 
                    transparent, 
                    rgba(255, 255, 255, 0.8), 
                    transparent)`,
                  borderRadius: fontSize * 0.017,
                  opacity: Math.sin((frame - charStartFrame) * 0.1) * 0.5 + 0.5,
                  transform: `translate(-50%, -50%) rotate(${(frame - charStartFrame) * 2}deg)`
                }}
              />
            )}
          </span>
        );
      })}
    </span>
  );
}

CrystalGrowthEffect.key = 'CrystalGrowthEffect';
CrystalGrowthEffect.description = 'crystal growth formation effect';
CrystalGrowthEffect.showName = "水晶生长"; 