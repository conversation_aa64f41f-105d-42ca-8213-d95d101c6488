## 文字入场特效核心要求

### 1. **参数标准化**
- 统一使用 `TextInEffectProps` 类型接口
- 只包含三个核心参数：文本内容、样式对象、动画时长

### 2. **布局一致性**
- 容器不使用flex布局，使用简单的相对定位
- 字符之间不添加额外的间距或边距
- 保证特效前后文字的排列和间距完全一致
- 每个字符使用行内块元素包装

### 3. **时间分配智能化**
- 根据文字长度自动调整每个字符的入场延迟
- 确保所有字符都能在规定的动画时长内完成入场
- 支持任意动画时长和任意文字长度的组合

### 4. **样式继承原则**
- 继承外部传入的样式对象
- 特效相关的属性（颜色、阴影等）从样式对象中获取
- 提供合理的默认值作为fallback
- 与文字本身无关的样式可自定义，如文字修饰特效
- 需要兼容不同字体大小的字

### 5. **动画生命周期**
- 纯粹的入场动画，不包含循环或退场阶段
- 动画完成后保持在正常的最终状态
- 不出现突然的跳变或闪烁

### 6. **性能和兼容性**
- 避免过度复杂的计算和渲染
- 使用标准的CSS属性和Remotion的interpolate函数
- 确保在移动端设备上的流畅运行
- 考虑低版本浏览器的兼容性