import React from 'react';
import { useCurrentFrame, interpolate, useVideoConfig } from 'remotion';
import { TextInEffectProps } from './type';

export default function StarConstellationEffect({
  text = "ABCD123+星空浮现",
  style = {},
  durationFrames = 108,
}: TextInEffectProps) {
  const frame = useCurrentFrame();
  const { fps } = useVideoConfig();
  
  const duration = durationFrames;
  
  // 计算文字长度
  const textLength = Array.from(text).length;
  
  // 从style中获取字体大小和颜色，提供合理的默认值
  const fontSize = style?.fontSize ? parseInt(String(style.fontSize)) : 60;
  const color = style?.color || '#FFFFFF';
  
  return (
    <span
      style={{
        position: 'relative',
      }}
    >
      {Array.from(text).map((char, index) => {
        // 根据文字长度和动画时长计算每个字符的延迟
        const maxStartDelay = duration * 0.6;
        const charDelay = textLength > 1 ? (maxStartDelay * index) / (textLength - 1) : 0;
        
        // 每个字符的入场动画时长至少占动画时长的40%
        const charAnimationDuration = Math.max(duration * 0.4, duration - charDelay);
        
        // 计算当前字符的入场进度
        const charStartFrame = charDelay;
        const charCurrentFrame = Math.max(0, frame - charStartFrame);
        const charProgress = Math.min(1, charCurrentFrame / charAnimationDuration);
        
        // 星座连线形成进度
        const constellationProgress = charProgress;
        
        // 恒星闪烁
        const twinklePhase = ((frame - charStartFrame) + index * 10) % 60;
        const twinkle = Math.sin(twinklePhase * 0.2) * 0.3 + 0.7;
        
        // 星云色彩填充
        const nebulaStartDelay = Math.max(0, 0.4 - index * 0.05);
        const nebulaProgress = Math.max(0, (charProgress - nebulaStartDelay) / (1 - nebulaStartDelay));
        
        return (
          <span
            key={index}
            style={{
              position: 'relative',
              display: 'inline-block',
            }}
          >
            {/* 透明占位文字，提供自然尺寸 */}
            <span
              style={{
                opacity: 0,
                ...style
              }}
            >
              {char}
            </span>
            
            {/* 主文字 - 星座形成 */}
            <span
              style={{
                position: 'absolute',
                left: '50%',
                top: '50%',
                transform: 'translate(-50%, -50%)',
                fontSize: fontSize,
                color: 'transparent',
                display: 'inline-block',
                opacity: constellationProgress,
                WebkitTextStroke: `2px #87CEEB`,
                textShadow: `
                  0 0 10px #87CEEB,
                  0 0 20px rgba(135, 206, 235, 0.5)
                `,
                ...style
              }}
            >
              {char}
            </span>
            
            {/* 星云色彩填充 */}
            {nebulaProgress > 0 && (
              <span
                style={{
                  position: 'absolute',
                  left: '50%',
                  top: '50%',
                  transform: 'translate(-50%, -50%)',
                  fontSize: fontSize,
                  color: '#DDA0DD',
                  display: 'inline-block',
                  opacity: nebulaProgress * 0.6,
                  clipPath: `inset(${100 - nebulaProgress * 100}% 0 0 0)`,
                  textShadow: `
                    0 0 15px #DDA0DD,
                    0 0 30px rgba(221, 160, 221, 0.4)
                  `,
                  ...style
                }}
              >
                {char}
              </span>
            )}
            
            {/* 星点 - 形成文字轮廓 */}
            {constellationProgress > 0.2 && Array.from({ length: 6 }, (_, starIndex) => {
              const starProgress = Math.max(0, Math.min(1, 
                (constellationProgress - 0.2 - starIndex * 0.05) / 0.3));
              
              if (starProgress === 0) return null;
              
              const angle = (starIndex * 60) * (Math.PI / 180);
              const radius = fontSize * 0.4;
              const starX = Math.cos(angle) * radius;
              const starY = Math.sin(angle) * radius;
              
              return (
                <span
                  key={`star-${starIndex}`}
                  style={{
                    position: 'absolute',
                    left: '50%',
                    top: '50%',
                    transform: `translate(calc(-50% + ${starX}px), calc(-50% + ${starY}px)) scale(${0.5 + starProgress * 0.5})`,
                    width: 3,
                    height: 3,
                    backgroundColor: '#FFFFFF',
                    borderRadius: '50%',
                    opacity: starProgress * twinkle,
                    boxShadow: `0 0 ${twinkle * 8}px #FFFFFF`
                  }}
                />
              );
            })}
            
            {/* 星座连线 */}
            {constellationProgress > 0.4 && Array.from({ length: 3 }, (_, lineIndex) => {
              const lineProgress = Math.max(0, Math.min(1, 
                (constellationProgress - 0.4 - lineIndex * 0.1) / 0.2));
              
              if (lineProgress === 0) return null;
              
              const lineLength = fontSize * 0.6 * lineProgress;
              const lineAngle = lineIndex * 45;
              
              return (
                <span
                  key={`line-${lineIndex}`}
                  style={{
                    position: 'absolute',
                    left: '50%',
                    top: '50%',
                    width: lineLength,
                    height: 1,
                    backgroundColor: '#87CEEB',
                    opacity: 0.6,
                    transform: `translate(-50%, -50%) rotate(${lineAngle}deg)`,
                    transformOrigin: '0 50%'
                  }}
                />
              );
            })}
          </span>
        );
      })}
    </span>
  );
}

StarConstellationEffect.key = 'StarConstellationEffect';
StarConstellationEffect.description = 'star constellation formation effect';
StarConstellationEffect.showName = "星空浮现"; 