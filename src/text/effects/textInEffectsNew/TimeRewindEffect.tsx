import React from 'react';
import { useCurrentFrame, interpolate, useVideoConfig } from 'remotion';
import { TextInEffectProps } from './type';

export default function TimeRewindEffect({
  text = "ABCD123+时光倒流",
  style = {},
  durationFrames = 78,
}: TextInEffectProps) {
  const frame = useCurrentFrame();
  const { fps } = useVideoConfig();
  
  const duration = durationFrames;
  
  // 计算文字长度
  const textLength = Array.from(text).length;
  
  // 从style中获取字体大小和颜色，提供合理的默认值
  const fontSize = style?.fontSize ? parseInt(String(style.fontSize)) : 60;
  const color = style?.color || '#FFFFFF';
  
  // 倒流速度1.2x
  const rewindSpeed = 1.2;
  
  return (
    <span
      style={{
        position: 'relative',
      }}
    >
      {Array.from(text).map((char, index) => {
        // 根据文字长度和动画时长计算每个字符的延迟
        const maxStartDelay = duration * 0.6;
        const charDelay = textLength > 1 ? (maxStartDelay * index) / (textLength - 1) : 0;
        
        // 每个字符的入场动画时长至少占动画时长的40%
        const charAnimationDuration = Math.max(duration * 0.4, duration - charDelay);
        
        // 计算当前字符的入场进度
        const charStartFrame = charDelay;
        const charCurrentFrame = Math.max(0, frame - charStartFrame);
        const charProgress = Math.min(1, charCurrentFrame / charAnimationDuration);
        
        // 时光倒流的逆向进度（从分解到完整）
        const rewindProgress = charProgress * rewindSpeed;
        
        // 建筑拆除效果的逆向 - 从散落到聚合
        const assemblyPhase = Math.min(1, rewindProgress / 0.6);
        const materialPhase = Math.max(0, (rewindProgress - 0.6) / 0.4);
        
        // 粒子倒流轨迹
        const particleCount = 8;
        const particleRadius = fontSize * (1 - assemblyPhase) * 2;
        
        // 时间扭曲视觉效果
        const timeWarp = Math.sin((frame - charStartFrame) * 0.3 + index) * (1 - materialPhase) * 3;
        
        return (
          <span
            key={index}
            style={{
              position: 'relative',
              display: 'inline-block',
            }}
          >
            {/* 透明占位文字，提供自然尺寸 */}
            <span
              style={{
                opacity: 0,
                ...style
              }}
            >
              {char}
            </span>
            
            {/* 主文字 - 从原始材料重构 */}
            <span
              style={{
                position: 'absolute',
                left: '50%',
                top: '50%',
                transform: `translate(-50%, -50%) translateX(${timeWarp}px) scale(${interpolate(materialPhase, [0, 0.5, 1], [0.6, 1.2, 1])})`,
                fontSize: fontSize,
                color: materialPhase > 0.5 ? color : '#8B4513', // 原始材料色 -> 最终颜色
                display: 'inline-block',
                opacity: interpolate(materialPhase, [0, 0.3, 1], [0.4, 0.7, 1]),
                filter: materialPhase < 0.8 ? 
                  `blur(${(1 - materialPhase) * 4}px) sepia(${(1 - materialPhase) * 0.8})` : 
                  'none',
                textShadow: materialPhase > 0.6 ? `0 0 10px ${color}` : 'none',
                ...style
              }}
            >
              {char}
            </span>
            
            {/* 粒子倒流轨迹 */}
            {assemblyPhase < 1 && Array.from({ length: particleCount }, (_, particleIndex) => {
              const angle = (particleIndex * 45) * (Math.PI / 180);
              const particleX = Math.cos(angle) * particleRadius;
              const particleY = Math.sin(angle) * particleRadius;
              
              const particleProgress = Math.max(0, Math.min(1, 
                (assemblyPhase - particleIndex * 0.05) / 0.3));
              
              if (particleProgress === 0) return null;
              
              return (
                <span
                  key={`particle-${particleIndex}`}
                  style={{
                    position: 'absolute',
                    left: '50%',
                    top: '50%',
                    transform: `translate(calc(-50% + ${particleX}px), calc(-50% + ${particleY}px)) scale(${1 - particleProgress * 0.5})`,
                    width: 3,
                    height: 3,
                    backgroundColor: '#8B4513',
                    borderRadius: '50%',
                    opacity: (1 - particleProgress) * 0.8,
                    boxShadow: `0 0 ${(1 - particleProgress) * 8}px #8B4513`
                  }}
                />
              );
            })}
            
            {/* 建筑拆除的逆向效果 - 结构重组 */}
            {assemblyPhase > 0.3 && assemblyPhase < 0.9 && Array.from({ length: 3 }, (_, structIndex) => {
              const structProgress = Math.max(0, Math.min(1, 
                (assemblyPhase - 0.3 - structIndex * 0.1) / 0.3));
              
              if (structProgress === 0) return null;
              
              const structY = [fontSize * 0.2, fontSize * 0.5, fontSize * 0.8][structIndex];
              
              return (
                <span
                  key={`struct-${structIndex}`}
                  style={{
                    position: 'absolute',
                    left: '10%',
                    top: `calc(50% - ${fontSize * 0.5}px + ${structY}px)`,
                    width: `${structProgress * 80}%`,
                    height: 2,
                    backgroundColor: '#696969',
                    opacity: structProgress * 0.6,
                    transform: `scaleX(${structProgress})`
                  }}
                />
              );
            })}
            
            {/* 时间涟漪效果 */}
            {materialPhase > 0.8 && (
              <span
                style={{
                  position: 'absolute',
                  left: '50%',
                  top: '50%',
                  width: fontSize * 1.5,
                  height: fontSize * 1.5,
                  border: `1px solid ${color}`,
                  borderRadius: '50%',
                  opacity: 0.3,
                  transform: `translate(-50%, -50%) scale(${Math.sin((frame - charStartFrame) * 0.2) * 0.2 + 1})`,
                  pointerEvents: 'none'
                }}
              />
            )}
          </span>
        );
      })}
    </span>
  );
}

TimeRewindEffect.key = 'TimeRewindEffect';
TimeRewindEffect.description = 'time rewind reconstruction effect';
TimeRewindEffect.showName = "时光倒流"; 