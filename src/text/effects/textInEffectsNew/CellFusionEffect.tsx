import React from 'react';
import { useCurrentFrame, interpolate, useVideoConfig } from 'remotion';
import { TextInEffectProps } from './type';

export default function CellFusionEffect({
  text = "ABCD123+细胞融合",
  style = {},
  durationFrames = 90,
}: TextInEffectProps) {
  const frame = useCurrentFrame();
  
  const duration = durationFrames;
  
  // 计算文字长度
  const textLength = Array.from(text).length;
  
  // 从样式中获取颜色和字体大小，提供合理的默认值
  const textColor = style.color || '#FFFFFF';
  const fontSize = style?.fontSize ? parseInt(String(style.fontSize)) : 48;
  
  return (
    <span
      style={{        
        position: 'relative',
      }}
    >
      {Array.from(text).map((char, index) => {
        // 根据文字长度和动画时长计算每个字符的延迟
        const maxStartDelay = duration * 0.6;
        const charDelay = textLength > 1 ? (maxStartDelay * index) / (textLength - 1) : 0;
        
        // 每个字符的入场动画时长至少占动画时长的40%
        const charAnimationDuration = Math.max(duration * 0.4, duration - charDelay);
        
        // 计算当前字符的入场进度
        const charStartFrame = charDelay;
        const charCurrentFrame = Math.max(0, frame - charStartFrame);
        const charProgress = Math.min(1, charCurrentFrame / charAnimationDuration);
        
        // 判断是上半还是下半部分
        const isTopHalf = index < Math.ceil(textLength / 2);
        
        // 细胞膜收缩动画 - 从分离位置移动到正常位置
        const membraneY = isTopHalf 
          ? interpolate(charProgress, [0, 0.8, 1], [-fontSize * 0.6, 0, 0])
          : interpolate(charProgress, [0, 0.8, 1], [fontSize * 0.6, 0, 0]);
        
        // 透明度和特效强度
        const opacity = interpolate(charProgress, [0, 0.3, 1], [0, 0.8, 1]);
        
        // 伪足伸缩动画
        const pseudopodExtension = Math.sin((frame - charStartFrame) * 0.3 + index) * 5 * charProgress;
        
        return (
          <span
            key={index}
            style={{
              position: 'relative',
              display: 'inline-block',              
            }}
          >
            {/* 透明占位文字，提供自然尺寸 */}
            <span
              style={{
                opacity: 0,
                ...style
              }}
            >
              {char}
            </span>
            
            {/* 细胞膜轮廓 */}
            <span style={{
              position: 'absolute',
              left: '50%',
              top: `calc(50% + ${membraneY - 10}px)`,
              transform: 'translate(-50%, -50%)',
              width: fontSize * 0.4,
              height: fontSize * 0.4,
              border: `2px solid rgba(${isTopHalf ? '0,255,128' : '128,0,255'},${0.4 * charProgress})`,
              borderRadius: '50%',
              opacity: charProgress > 0 ? 0.6 : 0,
              pointerEvents: 'none',
              zIndex: -1
            }} />
            
            {/* 伪足 */}
            {Array.from({ length: 4 }, (_, i) => (
              <span
                key={`pseudopod-${i}`}
                style={{
                  position: 'absolute',
                  left: '50%',
                  top: `calc(50% + ${membraneY + fontSize * 0.15}px)`,
                  width: '3px',
                  height: 8 + pseudopodExtension,
                  background: `linear-gradient(180deg, 
                    rgba(${isTopHalf ? '0,255,128' : '128,0,255'},${0.6 * charProgress}) 0%, 
                    transparent 100%)`,
                  transform: `translate(-50%, -50%) rotate(${(i - 1.5) * 20}deg)`,
                  transformOrigin: 'top center',
                  opacity: charProgress > 0.3 ? 1 : 0,
                  pointerEvents: 'none',
                  zIndex: -1
                }}
              />
            ))}
            
            {/* 细胞核 */}
            <span style={{
              position: 'absolute',
              left: '50%',
              top: `calc(50% + ${membraneY - 5}px)`,
              transform: 'translate(-50%, -50%)',
              width: '4px',
              height: '4px',
              backgroundColor: `rgba(${isTopHalf ? '255,100,100' : '100,100,255'},${0.8 * charProgress})`,
              borderRadius: '50%',
              opacity: charProgress > 0 ? 1 : 0,
              pointerEvents: 'none',
              zIndex: -1
            }} />
            
            <span
              style={{
                position: 'absolute',
                left: '50%',
                top: '50%',
                transform: `translate(-50%, calc(-50% + ${membraneY}px))`,
                display: 'inline-block',
                opacity,
                filter: `drop-shadow(0 0 3px rgba(${isTopHalf ? '0,255,128' : '128,0,255'},${0.3 * charProgress}))`,
                ...style
              }}
            >
              {char}
            </span>
          </span>
        );
      })}
      
      {/* 细胞膜融合效果 - 全局装饰 */}
      {frame > duration * 0.6 && frame < duration * 0.9 && (
        <span style={{
          position: 'absolute',
          left: '50%',
          top: '50%',
          transform: 'translate(-50%, -50%)',
          pointerEvents: 'none',
          zIndex: -1
        }}>
          {/* 脂质双分子层波动 */}
          {Array.from({ length: 6 }, (_, i) => (
            <span
              key={`lipid-${i}`}
              style={{
                position: 'absolute',
                left: (i - 2.5) * 8,
                top: 0,
                width: '4px',
                height: '2px',
                background: `linear-gradient(90deg, 
                  rgba(255,255,0,0.6) 0%, 
                  rgba(255,128,0,0.6) 100%)`,
                transform: `translateY(${Math.sin(frame * 0.5 + i) * 3}px)`,
                borderRadius: '1px'
              }}
            />
          ))}
        </span>
      )}
    </span>
  );
}

CellFusionEffect.key = 'CellFusionEffect';
CellFusionEffect.description = 'cell fusion biological effect with natural ending';
CellFusionEffect.showName = "细胞融合"; 