import React from 'react';
import { useCurrentFrame, interpolate, useVideoConfig } from 'remotion';
import { TextInEffectProps } from './type';

export default function DataSyncEffect({
  text = "ABCD123+数据同步",
  style = {},
  durationFrames = 102,
}: TextInEffectProps) {
  const frame = useCurrentFrame();
  const { fps } = useVideoConfig();
  
  const duration = durationFrames;
  
  // 计算文字长度
  const textLength = Array.from(text).length;
  
  // 从style中获取字体大小和颜色，提供合理的默认值
  const fontSize = style?.fontSize ? parseInt(String(style.fontSize)) : 60;
  const color = style?.color || '#FFFFFF';
  
  const halfLength = Math.ceil(textLength / 2);
  const transferRate = 1; // 1Gbps传输速率
  
  return (
    <span
      style={{
        position: 'relative',
      }}
    >
      {Array.from(text).map((char, index) => {
        // 根据文字长度和动画时长计算每个字符的延迟
        const maxStartDelay = duration * 0.6;
        const charDelay = textLength > 1 ? (maxStartDelay * index) / (textLength - 1) : 0;
        
        // 每个字符的入场动画时长至少占动画时长的40%
        const charAnimationDuration = Math.max(duration * 0.4, duration - charDelay);
        
        // 计算当前字符的入场进度
        const charStartFrame = charDelay;
        const charCurrentFrame = Math.max(0, frame - charStartFrame);
        const charProgress = Math.min(1, charCurrentFrame / charAnimationDuration);
        
        // 判断是上半还是下半部分
        const isTopHalf = index < halfLength;
        
        return (
          <span 
            key={index} 
            style={{ 
              position: 'relative',
              display: 'inline-block',
            }}
          >
            {/* 透明占位文字，提供自然尺寸 */}
            <span
              style={{
                opacity: 0,
                ...style
              }}
            >
              {char}
            </span>
            
            {/* 二进制码流 */}
            {Array.from({ length: 4 }, (_, i) => (
              <span
                key={`binary-${i}`}
                style={{
                  position: 'absolute',
                  left: '50%',
                  top: `calc(${isTopHalf ? '0%' : '100%'} + ${(isTopHalf ? -20 : 5) + i * 8}px)`,
                  transform: 'translateX(-50%)',
                  fontSize: '8px',
                  color: `rgba(0,255,0,${interpolate(charProgress, [0, 0.5, 1], [0, 0.8, 0.4])})`,
                  fontFamily: 'monospace',
                  pointerEvents: 'none'
                }}
              >
                {Math.random() > 0.5 ? '1' : '0'}
              </span>
            ))}
            
            {/* 光缆脉冲 */}
            <span style={{
              position: 'absolute',
              left: '50%',
              top: `calc(${isTopHalf ? '0%' : '100%'} + ${isTopHalf ? -10 : 10}px)`,
              width: '2px',
              height: '20px',
              background: `linear-gradient(180deg, 
                rgba(0,100,255,${Math.sin((frame - charStartFrame) * 0.5 + index) * 0.5 + 0.5}) 0%, 
                transparent 100%)`,
              transform: 'translateX(-50%)',
              opacity: charProgress > 0 ? 1 : 0,
              pointerEvents: 'none'
            }} />
            
            {/* 进度条扫描 */}
            {charProgress > 0.2 && charProgress < 0.9 && (
              <span style={{
                position: 'absolute',
                left: 0,
                top: `calc(${isTopHalf ? '0%' : '100%'} + ${isTopHalf ? -5 : -5}px)`,
                width: '100%',
                height: '2px',
                background: `linear-gradient(90deg, 
                  transparent 0%, 
                  rgba(255,255,0,0.8) ${charProgress * 100}%, 
                  transparent ${charProgress * 100 + 10}%)`,
                pointerEvents: 'none'
              }} />
            )}
            
            <span style={{
              position: 'absolute',
              left: '50%',
              top: '50%',
              transform: 'translate(-50%, -50%)',
              fontSize: fontSize,
              color: color,
              display: 'inline-block',
              opacity: charProgress > 0 ? 1 : 0,
              filter: `drop-shadow(0 0 2px rgba(0,255,${isTopHalf ? '255' : '0'},0.3))`,
              ...style
            }}>
              {char}
            </span>
          </span>
        );
      })}
      
      {/* 校验完成绿光贯穿 */}
      {frame > duration * 0.85 && (
        <span style={{
          position: 'absolute',
          left: '10%',
          top: '50%',
          width: '80%',
          height: '4px',
          background: `linear-gradient(90deg, 
            transparent 0%, 
            rgba(0,255,0,${interpolate(frame, [duration * 0.85, duration], [0, 1])}) 50%, 
            transparent 100%)`,
          transform: 'translateY(-50%)',
          pointerEvents: 'none',
          borderRadius: '2px'
        }}>
          {/* 绿光粒子 */}
          {Array.from({ length: 6 }, (_, i) => (
            <span
              key={`particle-${i}`}
              style={{
                position: 'absolute',
                left: `${i * 16}%`,
                top: '50%',
                width: '3px',
                height: '3px',
                backgroundColor: '#00FF00',
                borderRadius: '50%',
                transform: 'translateY(-50%)',
                opacity: interpolate(frame, [duration * 0.85, duration], [0, 1])
              }}
            />
          ))}
        </span>
      )}
    </span>
  );
}

DataSyncEffect.key = 'DataSyncEffect';
DataSyncEffect.description = 'data synchronization transfer effect';
DataSyncEffect.showName = "数据同步"; 