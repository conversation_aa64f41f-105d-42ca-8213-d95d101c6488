import React from 'react';
import { useCurrentFrame, interpolate, useVideoConfig } from 'remotion';
import { TextInEffectProps } from './type';

export default function DataStormEffect({
  text = "ABCD123+数据风暴",
  style = {},
  durationFrames = 90,
}: TextInEffectProps) {
  const frame = useCurrentFrame();
  const { fps } = useVideoConfig();
  
  const duration = durationFrames;
  
  // 计算文字长度
  const textLength = Array.from(text).length;
  
  // 从样式中获取颜色和字体大小，提供合理的默认值
  const textColor = style.color || '#FFFFFF';
  
  // 乱码字符集
  const glitchChars = '01ABCDEFGHIJKLMNOPQRSTUVWXYZ!@#$%^&*()_+-=[]{}|;:,.<>?`~';
  
  // 随机函数
  const random = (seed: number) => {
    const x = Math.sin(seed * 12.9898) * 43758.5453;
    return x - Math.floor(x);
  };
  
  // 刷新频率30Hz
  const refreshCycle = Math.floor(frame / (fps / 30));
  
  return (
    <span
      style={{        
        position: 'relative',
      }}
    >
      {Array.from(text).map((char, index) => {
        // 根据文字长度和动画时长计算每个字符的延迟
        const maxStartDelay = duration * 0.6;
        const charDelay = textLength > 1 ? (maxStartDelay * index) / (textLength - 1) : 0;
        
        // 每个字符的入场动画时长至少占动画时长的40%
        const charAnimationDuration = Math.max(duration * 0.4, duration - charDelay);
        
        // 计算当前字符的入场进度
        const charStartFrame = charDelay;
        const charCurrentFrame = Math.max(0, frame - charStartFrame);
        const charProgress = Math.min(1, charCurrentFrame / charAnimationDuration);
        
        let displayChar = char;
        let shouldShow = true;
        
        if (charProgress < 1) {
          // 字符瀑布流刷新阶段
          const glitchIntensity = 1 - charProgress;
          if (random(refreshCycle + index) < glitchIntensity) {
            const glitchIndex = Math.floor(random(refreshCycle + index + 100) * glitchChars.length);
            displayChar = glitchChars[glitchIndex];
          } else {
            // 没转码的文字先不要显示
            shouldShow = charProgress > 0.2;
          }
        }
        
        // 正确字符逐步锁定 - 锁定时间提前
        const isLocked = charProgress > 0.6;
        
        // 透明度和特效强度
        const opacity = interpolate(charProgress, [0, 0.3, 1], [0, 0.8, 1]);
        
        // 入场动画完成后继续循环播放
        const loopProgress = charProgress >= 1 ? (frame % (duration * 0.3)) / (duration * 0.3) : 0;
        const loopFlicker = loopProgress > 0 && frame % 60 < 5;
        
        return (
          <span
            key={index}
            style={{
              position: 'relative',
              display: 'inline-block',              
            }}
          >
            <span
              style={{                                
                display: 'inline-block',
                color: isLocked ? '#00FF00' : textColor,
                fontFamily: 'monospace',
                opacity: shouldShow ? opacity : 0,
                textShadow: isLocked ? 
                  '0 0 10px #00FF00, 0 0 20px #00FF00' : 
                  `0 0 5px ${textColor}`,
                filter: isLocked ? 
                  ((frame % 20 < 5) || loopFlicker ? 'brightness(2.5)' : 'brightness(1.5)') : 
                  'none',
                // 解码完成绿光确认闪烁 + 循环闪烁 - 只闪烁文字
                background: 'transparent',
                transition: 'none',
                ...style
              }}
            >
              {displayChar}
            </span>
          </span>
        );
      })}
    </span>
  );
}

DataStormEffect.key = 'DataStormEffect';
DataStormEffect.description = 'data storm decoding glitch effect with natural ending';
DataStormEffect.showName = "数据风暴"; 