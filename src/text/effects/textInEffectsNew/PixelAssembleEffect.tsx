import React from 'react';
import { useCurrentFrame, interpolate, useVideoConfig } from 'remotion';
import { TextInEffectProps } from './type';

export default function PixelAssembleEffect({
  text = "ABCD123+像素组装",
  style = {},
  durationFrames = 60,
}: TextInEffectProps) {
  const frame = useCurrentFrame();
  const { fps } = useVideoConfig();
  
  const duration = durationFrames;
  
  // 计算文字长度
  const textLength = Array.from(text).length;
  
  // 从style中获取字体大小和颜色，提供合理的默认值
  const fontSize = style?.fontSize ? parseInt(String(style.fontSize)) : 60;
  const color = style?.color || '#FFFFFF';
  
  return (
    <span
      style={{
        position: 'relative',
      }}
    >
      {Array.from(text).map((char, index) => {
        // 根据文字长度和动画时长计算每个字符的延迟
        const maxStartDelay = duration * 0.6;
        const charDelay = textLength > 1 ? (maxStartDelay * index) / (textLength - 1) : 0;
        
        // 每个字符的入场动画时长至少占动画时长的40%
        const charAnimationDuration = Math.max(duration * 0.4, duration - charDelay);
        
        // 计算当前字符的入场进度
        const charStartFrame = charDelay;
        const charCurrentFrame = Math.max(0, frame - charStartFrame);
        const charProgress = Math.min(1, charCurrentFrame / charAnimationDuration);
        
        // 3个组装阶梯：噪点 -> 4bit -> 8bit -> 16bit (正常)
        const getPixelSize = () => {
          if (charProgress < 0.25) return 12; // 噪点
          if (charProgress < 0.5) return 8;   // 4bit
          if (charProgress < 0.75) return 4;  // 8bit
          return 0; // 正常清晰度
        };
        
        const pixelSize = getPixelSize();
        
        return (
          <span
            key={index}
            style={{
              position: 'relative',
              display: 'inline-block',
            }}
          >
            {/* 透明占位文字，提供自然尺寸 */}
            <span
              style={{
                opacity: 0,
                ...style
              }}
            >
              {char}
            </span>
            
            <span
              style={{
                position: 'absolute',
                left: '50%',
                top: '50%',
                transform: `translate(-50%, -50%) scale(${interpolate(charProgress, [0, 0.5, 1], [0.8, 1.1, 1])}) rotate(${Math.sin((frame - charStartFrame) * 0.5) * charProgress * 2}deg)`,
                fontSize: fontSize,
                color: color,
                display: 'inline-block',
                opacity: interpolate(charProgress, [0, 0.2, 1], [0, 0.5, 1]),
                filter: pixelSize > 0 ? 
                  `blur(${pixelSize * 0.3}px) contrast(${1 + pixelSize * 0.1}) saturate(${Math.max(0, 1 - pixelSize * 0.1)})` : 
                  'none',
                textShadow: pixelSize > 0 ? `
                  ${Math.sin((frame - charStartFrame) * 0.3) * pixelSize * 0.5}px 
                  ${Math.cos((frame - charStartFrame) * 0.3) * pixelSize * 0.5}px 
                  0 ${color}
                ` : 'none',
                ...style
              }}
            >
              {char}
            </span>
          </span>
        );
      })}
    </span>
  );
}

PixelAssembleEffect.key = 'PixelAssembleEffect';
PixelAssembleEffect.description = 'pixel assembly resolution effect';
PixelAssembleEffect.showName = "像素组装"; 