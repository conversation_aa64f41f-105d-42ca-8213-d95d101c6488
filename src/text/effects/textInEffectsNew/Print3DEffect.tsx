import React from 'react';
import { useCurrentFrame, interpolate, useVideoConfig } from 'remotion';
import { TextInEffectProps } from './type';

export default function Print3DEffect({
  text = "ABCD123+3D打印",
  style = {},
  durationFrames = 132,
}: TextInEffectProps) {
  const frame = useCurrentFrame();
  const { fps } = useVideoConfig();
  
  const duration = durationFrames;
  
  // 计算文字长度
  const textLength = Array.from(text).length;
  
  // 从style中获取字体大小，提供合理的默认值
  const fontSize = style?.fontSize ? parseInt(String(style.fontSize)) : 60;
  
  // 打印参数
  const layerThickness = 0.8;
  const layers = 10;
  
  return (
    <span
      style={{
        position: 'relative',
      }}
    >
      {Array.from(text).map((char, index) => {
        // 根据文字长度和动画时长计算每个字符的延迟
        const maxStartDelay = duration * 0.6;
        const charDelay = textLength > 1 ? (maxStartDelay * index) / (textLength - 1) : 0;
        
        // 每个字符的入场动画时长至少占动画时长的40%
        const charAnimationDuration = Math.max(duration * 0.4, duration - charDelay);
        
        // 计算当前字符的入场进度
        const charStartFrame = charDelay;
        const charCurrentFrame = Math.max(0, frame - charStartFrame);
        const charProgress = Math.min(1, charCurrentFrame / charAnimationDuration);
        
        // 打印进度（逐层打印）
        const printProgress = charProgress * layers;
        const currentLayer = Math.floor(printProgress);
        const layerProgress = printProgress - currentLayer;
        
        return (
          <span 
            key={index} 
            style={{
              position: 'relative',
              display: 'inline-block',
            }}
          >
            {/* 透明占位文字，提供自然尺寸 */}
            <span
              style={{
                opacity: 0,
                ...style
              }}
            >
              {char}
            </span>
            
            {/* 逐层打印的文字 */}
            {Array.from({ length: layers }, (_, layerIndex) => {
              const layerVisible = currentLayer >= layerIndex;
              const isCurrentLayer = currentLayer === layerIndex;
              
              if (!layerVisible) return null;
              
              // 当前层的打印进度
              const currentLayerOpacity = isCurrentLayer 
                ? layerProgress 
                : 1;
              
              // 冷却收缩效果
              const shrinkage = isCurrentLayer 
                ? interpolate(layerProgress, [0, 0.5, 1], [1.1, 1.05, 1])
                : 1;
              
              return (
                <span
                  key={`layer-${layerIndex}`}
                  style={{
                    position: 'absolute',
                    left: '50%',
                    top: `calc(50% - ${layerIndex * layerThickness}px)`,
                    transform: `translate(-50%, -50%) scale(${shrinkage})`,
                    fontSize: fontSize,
                    fontWeight: 'bold',
                    color: layerIndex === layers - 1 ? (style.color || 'white') : `rgba(255,255,255,${0.3 + layerIndex * 0.07})`,
                    opacity: currentLayerOpacity,
                    transformOrigin: 'bottom center',
                    pointerEvents: 'none',
                    textShadow: `0 0 ${2 + layerIndex}px rgba(255,255,255,0.3)`,
                    ...style
                  }}
                >
                  {char}
                </span>
              );
            })}
            
            {/* 支撑结构 */}
            {charProgress > 0.3 && charProgress < 0.8 && (
              <>
                <span style={{
                  position: 'absolute',
                  left: '25%',
                  top: 'calc(50% - 30px)',
                  transform: 'translateX(-50%)',
                  width: '1px',
                  height: 35,
                  background: `linear-gradient(180deg, 
                    rgba(200,200,200,0.6) 0%, 
                    rgba(150,150,150,0.3) 100%)`,
                  opacity: interpolate(charProgress, [0.3, 0.6, 0.8], [0, 0.8, 0])
                }} />
                <span style={{
                  position: 'absolute',
                  left: '75%',
                  top: 'calc(50% - 25px)',
                  transform: 'translateX(-50%)',
                  width: '1px',
                  height: 30,
                  background: `linear-gradient(180deg, 
                    rgba(200,200,200,0.6) 0%, 
                    rgba(150,150,150,0.3) 100%)`,
                  opacity: interpolate(charProgress, [0.3, 0.6, 0.8], [0, 0.8, 0])
                }} />
              </>
            )}
          </span>
        );
      })}
    </span>
  );
}

Print3DEffect.key = 'Print3DEffect';
Print3DEffect.description = '3D printing layer by layer effect';
Print3DEffect.showName = "3D打印";