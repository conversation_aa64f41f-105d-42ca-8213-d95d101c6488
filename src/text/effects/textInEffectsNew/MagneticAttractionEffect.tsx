import React from 'react';
import { useCurrentFrame, interpolate, useVideoConfig } from 'remotion';
import { TextInEffectProps } from './type';

export default function MagneticAttractionEffect({
  text = "ABCD123+磁极吸附",
  style = {},
  durationFrames = 54,
}: TextInEffectProps) {
  const frame = useCurrentFrame();
  const { fps } = useVideoConfig();
  
  const duration = durationFrames;
  
  // 计算文字长度
  const textLength = Array.from(text).length;
  
  // 从style中获取字体大小和颜色，提供合理的默认值
  const fontSize = style?.fontSize ? parseInt(String(style.fontSize)) : 60;
  const color = style?.color || '#FFFFFF';
  
  const magneticAcceleration = 15;
  
  return (
    <span
      style={{
        position: 'relative',
      }}
    >
      {/* 磁场线背景 */}
      <span style={{
        position: 'absolute',
        left: '50%',
        top: '50%',
        transform: 'translate(-50%, -50%)',
        width: '300px',
        height: '120px',
        pointerEvents: 'none'
      }}>
        {Array.from({ length: 6 }, (_, i) => {
          const distortion = interpolate(frame, [0, duration * 0.7, duration], [0, 20, 5]);
          return (
            <span
              key={`fieldline-${i}`}
              style={{
                position: 'absolute',
                left: '50%',
                top: `${(i + 1) * 16}%`,
                width: '90%',
                height: '1px',
                background: `linear-gradient(90deg, 
                  rgba(0,255,255,0.4) 0%, 
                  rgba(255,0,255,0.4) 100%)`,
                transform: `translateX(-50%) 
                  scaleX(${1 + Math.sin(frame * 0.2 + i) * 0.15}) 
                  rotate(${Math.sin(frame * 0.1 + i) * distortion * 0.1}deg)`,
                opacity: interpolate(frame, [0, duration * 0.3, duration * 0.8], [0.3, 0.8, 0.2])
              }}
            />
          );
        })}
      </span>
      
      {Array.from(text).map((char, index) => {
        // 根据文字长度和动画时长计算每个字符的延迟
        const maxStartDelay = duration * 0.6;
        const charDelay = textLength > 1 ? (maxStartDelay * index) / (textLength - 1) : 0;
        
        // 每个字符的入场动画时长至少占动画时长的40%
        const charAnimationDuration = Math.max(duration * 0.4, duration - charDelay);
        
        // 计算当前字符的入场进度
        const charStartFrame = charDelay;
        const charCurrentFrame = Math.max(0, frame - charStartFrame);
        const charProgress = Math.min(1, charCurrentFrame / charAnimationDuration);
        
        // 随机初始位置（上下左右）
        const initialDirection = index % 4;
        let startX = 0, startY = 0;
        switch (initialDirection) {
          case 0: startY = -80; break; // 上方
          case 1: startX = 80; break;  // 右方
          case 2: startY = 80; break;  // 下方
          case 3: startX = -80; break; // 左方
        }
        
        // 磁力吸引运动（加速度递增）
        const attractionProgress = Math.pow(charProgress, 1.5); // 加速运动
        const currentX = interpolate(attractionProgress, [0, 1], [startX, 0]);
        const currentY = interpolate(attractionProgress, [0, 1], [startY, 0]);
        
        // 碰撞震动效果
        const collisionFrame = duration * 0.7;
        const vibration = (frame - charStartFrame) > collisionFrame && (frame - charStartFrame) < collisionFrame + 10
          ? Math.sin(((frame - charStartFrame) - collisionFrame) * 3) * 
            interpolate((frame - charStartFrame) - collisionFrame, [0, 10], [3, 0])
          : 0;
        
        // 磁极标识
        const poleColor = index % 2 === 0 ? '#FF4444' : '#4444FF';
        const poleLabel = index % 2 === 0 ? 'S' : 'N';
        
        return (
          <span 
            key={index} 
            style={{
              position: 'relative',
              display: 'inline-block',
              transform: `translate(${currentX + vibration}px, ${currentY + vibration}px)`
            }}
          >
            {/* 透明占位文字，提供自然尺寸 */}
            <span
              style={{
                opacity: 0,
                ...style
              }}
            >
              {char}
            </span>
            
            {/* 磁极标识 */}
            {charProgress > 0 && charProgress < 0.8 && (
              <span style={{
                position: 'absolute',
                left: '50%',
                top: startY > 0 ? '100%' : '-20px',
                transform: 'translateX(-50%)',
                fontSize: '10px',
                color: poleColor,
                fontWeight: 'bold',
                opacity: interpolate(charProgress, [0, 0.3, 0.8], [0, 0.8, 0])
              }}>
                {poleLabel}
              </span>
            )}
            
            {/* 磁力线轨迹 */}
            {charProgress > 0 && charProgress < 0.9 && (
              <>
                {Array.from({ length: 4 }, (_, i) => {
                  const trailProgress = Math.max(0, charProgress - i * 0.1);
                  const trailX = interpolate(trailProgress, [0, 1], [startX, 0]);
                  const trailY = interpolate(trailProgress, [0, 1], [startY, 0]);
                  
                  return (
                    <span
                      key={`trail-${i}`}
                      style={{
                        position: 'absolute',
                        left: '50%',
                        top: '50%',
                        width: '2px',
                        height: '2px',
                        backgroundColor: poleColor,
                        borderRadius: '50%',
                        transform: `translate(-50%, -50%) translate(${trailX}px, ${trailY}px)`,
                        opacity: interpolate(trailProgress, [0, 0.5, 1], [0, 0.6, 0]) * (1 - i * 0.2),
                        boxShadow: `0 0 4px ${poleColor}`
                      }}
                    />
                  );
                })}
              </>
            )}
            
            {/* 碰撞火花 */}
            {(frame - charStartFrame) > collisionFrame && (frame - charStartFrame) < collisionFrame + 15 && (
              <>
                {Array.from({ length: 8 }, (_, sparkIndex) => {
                  const angle = (sparkIndex / 8) * Math.PI * 2;
                  const distance = magneticAcceleration * 
                    interpolate((frame - charStartFrame) - collisionFrame, [0, 15], [1, 0]);
                  const sparkX = Math.cos(angle) * distance;
                  const sparkY = Math.sin(angle) * distance;
                  
                  return (
                    <span
                      key={sparkIndex}
                      style={{
                        position: 'absolute',
                        left: '50%',
                        top: '50%',
                        width: '2px',
                        height: '6px',
                        background: `linear-gradient(180deg, 
                          rgba(255,255,0,0.8) 0%, 
                          rgba(255,100,0,0.4) 100%)`,
                        transform: `translate(-50%, -50%) translate(${sparkX}px, ${sparkY}px) rotate(${angle * 180 / Math.PI + 90}deg)`,
                        opacity: interpolate((frame - charStartFrame) - collisionFrame, [0, 15], [1, 0]),
                        borderRadius: '1px'
                      }}
                    />
                  );
                })}
              </>
            )}
            
            {/* 主文字 */}
            <span style={{
              position: 'absolute',
              left: '50%',
              top: '50%',
              transform: 'translate(-50%, -50%)',
              fontSize: fontSize,
              fontWeight: 'bold',
              color: color,
              display: 'inline-block',
              opacity: charProgress > 0 ? 1 : 0,
              filter: `drop-shadow(0 0 4px ${poleColor}) brightness(${1 + vibration * 0.1})`,
              textShadow: `0 0 10px rgba(255,255,255,0.5)`,
              ...style
            }}>
              {char}
            </span>
          </span>
        );
      })}
    </span>
  );
}

MagneticAttractionEffect.key = 'MagneticAttractionEffect';
MagneticAttractionEffect.description = 'magnetic attraction collision effect';
MagneticAttractionEffect.showName = "磁极吸附"; 