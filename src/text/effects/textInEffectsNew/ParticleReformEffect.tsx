import React from 'react';
import { useCurrentFrame, interpolate, useVideoConfig } from 'remotion';
import { TextInEffectProps } from './type';

export default function ParticleReformEffect({
  text = "ABCD123+粒子重组",
  style = {},
  durationFrames = 72,
}: TextInEffectProps) {
  const frame = useCurrentFrame();
  const { fps } = useVideoConfig();
  
  const duration = durationFrames;
  
  // 计算文字长度
  const textLength = Array.from(text).length;
  
  // 从style中获取字体大小和颜色，提供合理的默认值
  const fontSize = style?.fontSize ? parseInt(String(style.fontSize)) : 60;
  const color = style?.color || '#FFFFFF';
  
  // 生成粒子噪声函数
  const noise = (x: number, y: number, seed: number) => {
    const n = Math.sin(x * 12.9898 + y * 78.233 + seed) * 43758.5453;
    return n - Math.floor(n);
  };
  
  return (
    <span
      style={{
        position: 'relative',
      }}
    >
      {Array.from(text).map((char, charIndex) => {
        // 根据文字长度和动画时长计算每个字符的延迟
        const maxStartDelay = duration * 0.6;
        const charDelay = textLength > 1 ? (maxStartDelay * charIndex) / (textLength - 1) : 0;
        
        // 每个字符的入场动画时长至少占动画时长的40%
        const charAnimationDuration = Math.max(duration * 0.4, duration - charDelay);
        
        // 计算当前字符的入场进度
        const charStartFrame = charDelay;
        const charCurrentFrame = Math.max(0, frame - charStartFrame);
        const charProgress = Math.min(1, charCurrentFrame / charAnimationDuration);
        
        const charWidth = fontSize * 0.9;
        const charHeight = fontSize;
        const particleCount = 15; // 每个字符的粒子数
        
        return (
          <span
            key={charIndex}
            style={{
              position: 'relative',
              display: 'inline-block'
            }}
          >
            {/* 透明占位文字，提供自然尺寸 */}
            <span
              style={{
                opacity: 0,
                ...style
              }}
            >
              {char}
            </span>
            
            {/* 粒子系统 */}
            {charProgress < 0.9 && Array.from({ length: particleCount }, (_, particleIndex) => {
              const particleDelay = particleIndex * 0.05; // 不同延迟时间
              const particleStartProgress = particleDelay;
              const particleEndProgress = 0.9;
              
              if (charProgress < particleStartProgress || charProgress > particleEndProgress) return null;
              
              const progress = Math.min(1, (charProgress - particleStartProgress) / (particleEndProgress - particleStartProgress));
              
              // 粒子在字符内的目标位置
              const targetX = (particleIndex % 4) * (charWidth / 4) + charWidth / 8;
              const targetY = Math.floor(particleIndex / 4) * (charHeight / 4) + charHeight / 8;
              
              // 粒子的起始位置（屏幕外随机位置）
              const startX = noise(particleIndex, charIndex, 1) * 400 - 200;
              const startY = noise(particleIndex, charIndex, 2) * 300 - 150;
              
              // 使用噪声生成曲线路径
              const pathProgress = interpolate(progress, [0, 1], [0, 1]);
              const noiseX = noise(pathProgress * 10, charIndex, particleIndex) * 50;
              const noiseY = noise(pathProgress * 15, charIndex, particleIndex + 10) * 30;
              
              // 当前位置
              const currentX = interpolate(
                pathProgress,
                [0, 1],
                [startX, targetX - charWidth / 2]
              ) + noiseX * (1 - pathProgress);
              
              const currentY = interpolate(
                pathProgress,
                [0, 1],
                [startY, targetY - charHeight / 2]
              ) + noiseY * (1 - pathProgress);
              
              // 粒子属性
              const opacity = interpolate(progress, [0, 0.2, 0.8, 1], [0, 1, 1, 1]);
              const scale = interpolate(progress, [0, 0.3, 1], [0.5, 0.8, 1]);
              
              // 碰撞光效
              const glowIntensity = progress > 0.9 ? 
                interpolate(progress, [0.9, 1], [0, 1]) : 0;
              
              return (
                <span
                  key={particleIndex}
                  style={{
                    position: 'absolute',
                    left: '50%',
                    top: '50%',
                    transform: `translate(calc(-50% + ${currentX}px), calc(-50% + ${currentY}px)) scale(${scale})`,
                    width: 3,
                    height: 3,
                    borderRadius: '50%',
                    backgroundColor: color,
                    opacity,
                    boxShadow: glowIntensity > 0 ? 
                      `0 0 ${glowIntensity * 10}px ${color}` : 'none',
                    transition: 'box-shadow 0.1s ease'
                  }}
                />
              );
            })}
            
            {/* 最终字符显示 */}
            {charProgress > 0.85 && (
              <span
                style={{
                  position: 'absolute',
                  left: '50%',
                  top: '50%',
                  transform: 'translate(-50%, -50%)',
                  fontSize: fontSize,
                  color: color,
                  opacity: interpolate(charProgress, [0.85, 1], [0, 1]),
                  display: 'inline-block',
                  ...style
                }}
              >
                {char}
              </span>
            )}
          </span>
        );
      })}
    </span>
  );
}

ParticleReformEffect.key = 'ParticleReformEffect';
ParticleReformEffect.description = 'particle reformation assembly effect';
ParticleReformEffect.showName = "粒子重组"; 