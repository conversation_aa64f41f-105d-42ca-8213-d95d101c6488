import React from 'react';
import { useCurrentFrame, interpolate } from 'remotion';
import { TextOutEffectProps } from './type';

const InkDiffusionEffect = ({
  text,
  style,
  durationFrames,
  startFrame
}: TextOutEffectProps) => {
  const frame = useCurrentFrame();
  
  const chars = Array.from(text);
  const totalChars = chars.length;
  const diffusionRange = 1.5; // 扩散范围150%
  
  // 相对于startFrame的当前帧数
  const relativeFrame = frame - (startFrame || 0);
  
  // 单个字符动画持续时间
  const charAnimationDuration = Math.max(durationFrames * 0.6, durationFrames / totalChars);
  
  // 计算字符间延迟，确保最后一个字符在durationFrames - 1帧时完成消失
  const lastCharStartDelay = durationFrames - 1 - charAnimationDuration;
  const delayInterval = totalChars > 1 ? lastCharStartDelay / (totalChars - 1) : 0;
  
  return (
    <div style={{
      position: 'relative',
      backgroundColor: 'transparent'
    }}>
      {chars.map((char, index) => {
        // 计算每个字符的开始延迟
        const charDelay = index * delayInterval;
        
        // 字符动画进度（基于相对帧数）
        const charProgress = Math.max(0, Math.min(1, (relativeFrame - charDelay) / charAnimationDuration));
        
        // 模糊程度
        const blurAmount = interpolate(charProgress, [0, 0.3, 1], [0, 2, 15]);
        
        // 扩散尺寸
        const diffusionScale = interpolate(charProgress, [0, 1], [1, diffusionRange]);
        
        // 文字透明度 - 出场动画，从1到0
        const opacity = interpolate(charProgress, [0, 0.4, 1], [1, 0.6, 0]);
        
        // 墨迹浓度
        const inkDensity = interpolate(charProgress, [0, 0.5, 1], [1, 0.4, 0.1]);
        
        return (
          <div key={index} style={{
            position: 'relative',
            display: 'inline-block'
          }}>
            {/* 毛刺边缘效果 */}
            {charProgress > 0.2 && opacity > 0 && (
              <>
                {Array.from({ length: 12 }, (_, i) => {
                  const angle = (i / 12) * Math.PI * 2;
                  const distance = interpolate(charProgress, [0.2, 1], [0, 25]);
                  const bristleX = Math.cos(angle) * distance;
                  const bristleY = Math.sin(angle) * distance;
                  const bristleOpacity = interpolate(charProgress, [0.2, 0.6, 1], [0, 0.8, 0]);
                  
                  return (
                    <div
                      key={`bristle-${i}`}
                      style={{
                        position: 'absolute',
                        left: '50%',
                        top: '50%',
                        width: '2px',
                        height: `${8 + Math.random() * 12}px`,
                        background: `linear-gradient(${angle}rad, 
                          rgba(255,255,255,${bristleOpacity}) 0%, 
                          transparent 100%)`,
                        transform: `translate(-50%, -50%) translate(${bristleX}px, ${bristleY}px) rotate(${angle}rad)`,
                        pointerEvents: 'none'
                      }}
                    />
                  );
                })}
              </>
            )}
            
            {/* 墨滴飞溅 */}
            {charProgress > 0.3 && opacity > 0 && (
              <>
                {Array.from({ length: 8 }, (_, i) => {
                  const splashAngle = (Math.random() - 0.5) * Math.PI;
                  const splashDistance = interpolate(charProgress, [0.3, 1], [0, 40 + Math.random() * 20]);
                  const splashX = Math.cos(splashAngle) * splashDistance;
                  const splashY = Math.sin(splashAngle) * splashDistance;
                  const splashOpacity = interpolate(charProgress, [0.3, 0.7, 1], [0, 0.6, 0]);
                  
                  return (
                    <div
                      key={`splash-${i}`}
                      style={{
                        position: 'absolute',
                        left: '50%',
                        top: '50%',
                        width: `${3 + Math.random() * 4}px`,
                        height: `${3 + Math.random() * 4}px`,
                        backgroundColor: `rgba(255,255,255,${splashOpacity})`,
                        borderRadius: '50%',
                        transform: `translate(-50%, -50%) translate(${splashX}px, ${splashY}px)`,
                        filter: `blur(${Math.random() * 2}px)`,
                        pointerEvents: 'none'
                      }}
                    />
                  );
                })}
              </>
            )}
            
            {/* 主文字 */}
            <span style={{
              display: 'inline-block',
              opacity: opacity,
              color: 'white',
              filter: `blur(${blurAmount}px)`,
              transform: `scale(${diffusionScale})`,
              textShadow: `0 0 ${blurAmount * 2}px rgba(255,255,255,${opacity * 0.5})`,
              ...style
            }}>
              {char}
            </span>
          </div>
        );
      })}
    </div>
  );
};

InkDiffusionEffect.showName = "墨水扩散";
InkDiffusionEffect.description = "ink diffusion effect with bristles and splashes";
InkDiffusionEffect.key = "InkDiffusionEffect";

export default InkDiffusionEffect; 