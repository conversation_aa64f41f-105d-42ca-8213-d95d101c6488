import React from 'react';
import { useCurrentFrame, interpolate, useVideoConfig } from 'remotion';
import { TextOutEffectProps } from './type';

const BubbleBurstEffect = ({
  text,
  style,
  durationFrames,
  startFrame
}: TextOutEffectProps) => {
  const frame = useCurrentFrame();
  
  const chars = Array.from(text);
  const totalChars = chars.length;
  const bubbleCount = 8; // 泡沫数量
  
  // 相对于startFrame的当前帧数
  const relativeFrame = frame - (startFrame || 0);
  
  // 单个字符动画持续时间（根据总时长和字符数量动态计算）
  // 保证有足够的动画时间，但也要留出延迟分布空间
  const charAnimationDuration = Math.max(durationFrames * 0.3, durationFrames / totalChars);
  
  // 计算字符间延迟，确保最后一个字符在durationFrames-1帧时charProgress达到1
  // 最后一个字符开始时间 + 动画时长 = durationFrames - 1
  const lastCharStartDelay = (durationFrames - 1) - charAnimationDuration;
  const delayInterval = totalChars > 1 ? lastCharStartDelay / (totalChars - 1) : 0;
  
  return (
    <div style={{
      // display: 'inline-flex',
      // alignItems: 'center',
      // justifyContent: 'center',
      position: 'relative',
      backgroundColor: 'transparent',      
    }}>
      {chars.map((char, index) => {
        // 计算每个字符的开始延迟
        const charDelay = index * delayInterval;
        
        // 字符动画进度（基于相对帧数）
        const charProgress = Math.max(0, Math.min(1, (relativeFrame - charDelay) / charAnimationDuration));
        
        // 泡沫阶段
        const bubblePhase = interpolate(charProgress, [0, 0.4, 0.8, 1], [0, 1, 2, 3]);
        
        // 文字透明度 - 出场动画，从1到0
        const opacity = interpolate(charProgress, [0, 0.7, 1], [1, 0.3, 0]);
        
        return (
          <div key={index} style={{
            position: 'relative',
            display: 'inline-block',
            margin: '0 2px'
          }}>
            {/* 虹彩波动效果 */}
            {bubblePhase < 2 && opacity > 0 && (
              <div style={{
                position: 'absolute',
                left: -3,
                top: -3,
                right: -3,
                bottom: -3,
                background: `conic-gradient(
                  from ${frame * 3}deg,
                  rgba(255,0,255,${interpolate(bubblePhase, [0, 1, 2], [0, 0.3, 0])}) 0deg,
                  rgba(0,255,255,${interpolate(bubblePhase, [0, 1, 2], [0, 0.3, 0])}) 60deg,
                  rgba(255,255,0,${interpolate(bubblePhase, [0, 1, 2], [0, 0.3, 0])}) 120deg,
                  rgba(255,0,0,${interpolate(bubblePhase, [0, 1, 2], [0, 0.3, 0])}) 180deg,
                  rgba(0,255,0,${interpolate(bubblePhase, [0, 1, 2], [0, 0.3, 0])}) 240deg,
                  rgba(0,0,255,${interpolate(bubblePhase, [0, 1, 2], [0, 0.3, 0])}) 300deg,
                  rgba(255,0,255,${interpolate(bubblePhase, [0, 1, 2], [0, 0.3, 0])}) 360deg
                )`,
                borderRadius: '50%',
                filter: 'blur(1px)',
                pointerEvents: 'none'
              }} />
            )}
            
            {/* 液膜颤抖效果 */}
            {bubblePhase >= 0.5 && bubblePhase < 2 && opacity > 0 && (
              <div style={{
                position: 'absolute',
                left: -2,
                top: -2,
                right: -2,
                bottom: -2,
                background: `radial-gradient(circle, 
                  transparent 60%, 
                  rgba(255,255,255,${interpolate(bubblePhase, [0.5, 1.5, 2], [0, 0.4, 0])}) 70%, 
                  transparent 80%)`,
                borderRadius: '50%',
                transform: `scale(${1 + Math.sin(frame * 0.5) * 0.05})`,
                pointerEvents: 'none'
              }} />
            )}
            
            {/* 泡沫破裂瞬间 */}
            {bubblePhase >= 1.8 && bubblePhase < 2.5 && opacity > 0 && (
              <>
                {Array.from({ length: bubbleCount }, (_, i) => {
                  const burstAngle = (i / bubbleCount) * Math.PI * 2;
                  const burstDistance = interpolate(bubblePhase, [1.8, 2.5], [0, 25]);
                  const burstX = Math.cos(burstAngle) * burstDistance;
                  const burstY = Math.sin(burstAngle) * burstDistance;
                  const burstOpacity = interpolate(bubblePhase, [1.8, 2.2, 2.5], [0, 1, 0]);
                  
                  return (
                    <div
                      key={`burst-${i}`}
                      style={{
                        position: 'absolute',
                        left: '50%',
                        top: '50%',
                        width: '4px',
                        height: '4px',
                        background: `radial-gradient(circle, 
                          rgba(255,255,255,${burstOpacity}) 0%, 
                          rgba(200,230,255,${burstOpacity * 0.8}) 50%, 
                          transparent 100%)`,
                        borderRadius: '50%',
                        transform: `translate(-50%, -50%) translate(${burstX}px, ${burstY}px)`,
                        pointerEvents: 'none'
                      }}
                    />
                  );
                })}
              </>
            )}
            
            {/* 水雾效果 - 只在文字还可见时显示 */}
            {bubblePhase >= 2 && opacity > 0 && (
              <>
                {Array.from({ length: 12 }, (_, i) => {
                  const mistProgress = Math.max(0, (bubblePhase - 2) / 1);
                  const mistAngle = (Math.random() - 0.5) * Math.PI * 2;
                  const mistDistance = interpolate(mistProgress, [0, 1], [0, 30 + Math.random() * 20]);
                  const mistX = Math.cos(mistAngle) * mistDistance;
                  const mistY = Math.sin(mistAngle) * mistDistance - mistProgress * 15;
                  const mistOpacity = interpolate(mistProgress, [0, 0.5, 1], [0, 0.6, 0]) * opacity;
                  const mistSize = 3 + Math.random() * 5;
                  
                  return (
                    <div
                      key={`mist-${i}`}
                      style={{
                        position: 'absolute',
                        left: '50%',
                        top: '50%',
                        width: `${mistSize}px`,
                        height: `${mistSize}px`,
                        background: `radial-gradient(circle, 
                          rgba(220,240,255,${mistOpacity}) 0%, 
                          transparent 100%)`,
                        borderRadius: '50%',
                        transform: `translate(-50%, -50%) translate(${mistX}px, ${mistY}px)`,
                        filter: 'blur(1px)',
                        pointerEvents: 'none'
                      }}
                    />
                  );
                })}
              </>
            )}
            
            {/* 残留水粒 - 静止状态 */}
            {bubblePhase >= 2.5 && opacity > 0 && (
              <>
                {Array.from({ length: 6 }, (_, i) => {
                  const dropletAngle = (i / 6) * Math.PI * 2;
                  const dropletDistance = 15 + Math.random() * 10;
                  const dropletX = Math.cos(dropletAngle) * dropletDistance;
                  const dropletY = Math.sin(dropletAngle) * dropletDistance;
                  const dropletOpacity = interpolate(bubblePhase, [2.5, 3], [0.8, 0.4]) * opacity;
                  const dropletSize = 1 + Math.random() * 2;
                  
                  return (
                    <div
                      key={`droplet-${i}`}
                      style={{
                        position: 'absolute',
                        left: '50%',
                        top: '50%',
                        width: `${dropletSize}px`,
                        height: `${dropletSize * 1.5}px`,
                        background: `rgba(180,220,255,${dropletOpacity})`,
                        borderRadius: '50% 50% 50% 50% / 60% 60% 40% 40%',
                        transform: `translate(-50%, -50%) translate(${dropletX}px, ${dropletY}px)`,
                        pointerEvents: 'none'
                      }}
                    />
                  );
                })}
              </>
            )}
            
            {/* 主文字 */}
            <span style={{
              fontSize: '60px',
              fontWeight: 'bold',
              color: 'white',
              display: 'inline-block',
              opacity: opacity,
              textShadow: bubblePhase < 1.5 
                ? `0 0 8px rgba(255,255,255,0.6)` 
                : 'none',
              filter: bubblePhase >= 1.5 
                ? `blur(${interpolate(bubblePhase, [1.5, 2.5], [0, 2])}px)` 
                : 'none',
                ...style
            }}>
              {char}
            </span>
          </div>
        );
      })}
    </div>
  );
};

BubbleBurstEffect.showName = "泡沫破裂";
BubbleBurstEffect.description = "bubble burst explosion effect";
BubbleBurstEffect.key = "BubbleBurstEffect";

export default BubbleBurstEffect; 