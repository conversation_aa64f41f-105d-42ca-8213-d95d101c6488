import React from 'react';
import { useCurrentFrame, interpolate } from 'remotion';
import { TextOutEffectProps } from './type';

const MagneticRepelEffect = ({
  text,
  style,
  durationFrames,
  startFrame
}: TextOutEffectProps) => {
  const frame = useCurrentFrame();
  
  const chars = Array.from(text);
  const totalChars = chars.length;
  const repelForce = 1.8; // 排斥力1.8
  
  // 相对于startFrame的当前帧数
  const relativeFrame = frame - (startFrame || 0);
  
  // 单个字符动画持续时间
  const charAnimationDuration = Math.max(durationFrames * 0.6, durationFrames / totalChars);
  
  // 计算字符间延迟，确保最后一个字符在durationFrames - 1帧时完成消失
  const lastCharStartDelay = durationFrames - 1 - charAnimationDuration;
  const delayInterval = totalChars > 1 ? lastCharStartDelay / (totalChars - 1) : 0;
  
  return (
    <div style={{
      position: 'relative',
      backgroundColor: 'transparent'
    }}>
      {chars.map((char, index) => {
        // 计算每个字符的开始延迟
        const charDelay = index * delayInterval;
        
        // 字符动画进度（基于相对帧数）
        const charProgress = Math.max(0, Math.min(1, (relativeFrame - charDelay) / charAnimationDuration));
        
        // 磁力阶段
        const magneticPhase = interpolate(charProgress, [0, 0.3, 0.7, 1], [0, 1, 2, 3]);
        
        // 文字透明度 - 出场动画，从1到0
        const opacity = interpolate(charProgress, [0, 0.6, 1], [1, 0.3, 0]);
        
        return (
          <div key={index} style={{
            position: 'relative',
            display: 'inline-block'
          }}>
            {/* 磁场线可见效果 */}
            {magneticPhase >= 0.2 && magneticPhase < 2.5 && opacity > 0 && (
              <>
                {Array.from({ length: 8 }, (_, i) => {
                  const fieldAngle = (i / 8) * Math.PI * 2;
                  const fieldRadius = interpolate(magneticPhase, [0.2, 1.5], [10, 40]);
                  const fieldOpacity = interpolate(magneticPhase, [0.2, 1, 2, 2.5], [0, 0.6, 0.4, 0]);
                  
                  return (
                    <div
                      key={`field-${i}`}
                      style={{
                        position: 'absolute',
                        left: '50%',
                        top: '50%',
                        width: `${fieldRadius * 2}px`,
                        height: '1px',
                        background: `linear-gradient(90deg, 
                          transparent 0%, 
                          rgba(255,100,100,${fieldOpacity}) 20%, 
                          rgba(100,100,255,${fieldOpacity}) 80%, 
                          transparent 100%)`,
                        transform: `translate(-50%, -50%) rotate(${fieldAngle}rad)`,
                        transformOrigin: '50% 50%',
                        pointerEvents: 'none'
                      }}
                    />
                  );
                })}
              </>
            )}
            
            {/* 文字分裂碎片 */}
            {magneticPhase >= 0.8 && opacity > 0 && (
              <>
                {Array.from({ length: 16 }, (_, i) => {
                  const fragmentProgress = Math.max(0, (magneticPhase - 0.8) / 2.2);
                  const fragmentAngle = (i / 16) * Math.PI * 2 + (Math.random() - 0.5) * 0.5;
                  
                  // 磁力排斥加速度计算
                  const acceleration = repelForce * fragmentProgress * 15; // 15px/frame²
                  const fragmentDistance = acceleration * fragmentProgress;
                  const fragmentX = Math.cos(fragmentAngle) * fragmentDistance;
                  const fragmentY = Math.sin(fragmentAngle) * fragmentDistance;
                  
                  // 碎片旋转加速
                  const fragmentRotation = interpolate(fragmentProgress, [0, 1], [0, 360 + Math.random() * 720]);
                  const fragmentOpacity = interpolate(fragmentProgress, [0, 0.5, 1], [1, 0.8, 0]);
                  
                  const fragmentSize = 3 + Math.random() * 5;
                  const isPositive = i % 2 === 0;
                  
                  return (
                    <div
                      key={`fragment-${i}`}
                      style={{
                        position: 'absolute',
                        left: '50%',
                        top: '50%',
                        width: `${fragmentSize}px`,
                        height: `${fragmentSize}px`,
                        background: isPositive 
                          ? `radial-gradient(circle, 
                              rgba(255,100,100,${fragmentOpacity}) 0%, 
                              rgba(255,150,150,${fragmentOpacity * 0.8}) 50%, 
                              transparent 100%)`
                          : `radial-gradient(circle, 
                              rgba(100,100,255,${fragmentOpacity}) 0%, 
                              rgba(150,150,255,${fragmentOpacity * 0.8}) 50%, 
                              transparent 100%)`,
                        border: `1px solid ${isPositive ? `rgba(255,100,100,${fragmentOpacity * 0.5})` : `rgba(100,100,255,${fragmentOpacity * 0.5})`}`,
                        borderRadius: '2px',
                        transform: `translate(-50%, -50%) translate(${fragmentX}px, ${fragmentY}px) rotate(${fragmentRotation}deg)`,
                        pointerEvents: 'none'
                      }}
                    />
                  );
                })}
              </>
            )}
            
            {/* 撞击边缘闪光 */}
            {magneticPhase >= 2 && opacity > 0 && (
              <>
                {Array.from({ length: 6 }, (_, i) => {
                  const sparkProgress = Math.max(0, (magneticPhase - 2 - i * 0.05) / 0.3);
                  if (sparkProgress <= 0) return null;
                  
                  const sparkAngle = (i / 6) * Math.PI * 2;
                  const sparkDistance = 35 + Math.random() * 15;
                  const sparkX = Math.cos(sparkAngle) * sparkDistance;
                  const sparkY = Math.sin(sparkAngle) * sparkDistance;
                  const sparkOpacity = interpolate(sparkProgress, [0, 0.3, 1], [0, 1, 0]);
                  
                  return (
                    <div
                      key={`spark-${i}`}
                      style={{
                        position: 'absolute',
                        left: '50%',
                        top: '50%',
                        width: '8px',
                        height: '2px',
                        background: `linear-gradient(90deg, 
                          transparent 0%, 
                          rgba(255,255,255,${sparkOpacity}) 50%, 
                          transparent 100%)`,
                        transform: `translate(-50%, -50%) translate(${sparkX}px, ${sparkY}px) rotate(${sparkAngle}rad)`,
                        pointerEvents: 'none',
                        boxShadow: `0 0 4px rgba(255,255,255,${sparkOpacity * 0.8})`
                      }}
                    />
                  );
                })}
              </>
            )}
            
            {/* 磁极指示器 */}
            {magneticPhase >= 0.5 && magneticPhase < 2 && opacity > 0 && (
              <>
                {/* 正极 */}
                <div style={{
                  position: 'absolute',
                  left: '25%',
                  top: '25%',
                  width: '8px',
                  height: '8px',
                  background: `rgba(255,100,100,${interpolate(magneticPhase, [0.5, 1.5, 2], [0, 0.8, 0])})`,
                  border: `1px solid rgba(255,100,100,${interpolate(magneticPhase, [0.5, 1.5, 2], [0, 1, 0])})`,
                  borderRadius: '50%',
                  transform: 'translate(-50%, -50%)',
                  pointerEvents: 'none'
                }} />
                {/* 负极 */}
                <div style={{
                  position: 'absolute',
                  right: '25%',
                  bottom: '25%',
                  width: '8px',
                  height: '8px',
                  background: `rgba(100,100,255,${interpolate(magneticPhase, [0.5, 1.5, 2], [0, 0.8, 0])})`,
                  border: `1px solid rgba(100,100,255,${interpolate(magneticPhase, [0.5, 1.5, 2], [0, 1, 0])})`,
                  borderRadius: '50%',
                  transform: 'translate(50%, 50%)',
                  pointerEvents: 'none'
                }} />
              </>
            )}
            
            {/* 磁力波动 */}
            {magneticPhase >= 1 && magneticPhase < 2.5 && opacity > 0 && (
              <div style={{
                position: 'absolute',
                left: '50%',
                top: '50%',
                width: '60px',
                height: '60px',
                background: `radial-gradient(circle, 
                  transparent 40%, 
                  rgba(200,100,200,${interpolate(magneticPhase, [1, 2, 2.5], [0, 0.3, 0])}) 50%, 
                  transparent 60%)`,
                borderRadius: '50%',
                transform: `translate(-50%, -50%) scale(${1 + Math.sin(frame * 0.3) * 0.1})`,
                pointerEvents: 'none'
              }} />
            )}
            
            {/* 主文字 */}
            <span style={{
              display: 'inline-block',
              opacity: opacity,
              color: 'white',
              textShadow: magneticPhase >= 0.5 
                ? `0 0 8px rgba(200,100,200,${opacity * 0.8})` 
                : '0 0 8px rgba(255,255,255,0.6)',
              filter: magneticPhase >= 1.5 
                ? `blur(${interpolate(magneticPhase, [1.5, 3], [0, 2])}px)` 
                : 'none',
              ...style
            }}>
              {char}
            </span>
          </div>
        );
      })}
    </div>
  );
};

MagneticRepelEffect.showName = "磁力排斥";
MagneticRepelEffect.description = "magnetic repel effect with field lines and fragments";
MagneticRepelEffect.key = "MagneticRepelEffect";

export default MagneticRepelEffect; 