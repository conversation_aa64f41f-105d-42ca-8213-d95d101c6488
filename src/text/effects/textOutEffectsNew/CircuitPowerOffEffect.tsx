import React from 'react';
import { useCurrentFrame, interpolate } from 'remotion';
import { TextOutEffectProps } from './type';

const CircuitPowerOffEffect = ({
  text,
  style,
  durationFrames,
  startFrame
}: TextOutEffectProps) => {
  const frame = useCurrentFrame();
  
  const chars = Array.from(text);
  const totalChars = chars.length;
  const circuitComplexity = 8; // 电路复杂度
  
  // 相对于startFrame的当前帧数
  const relativeFrame = frame - (startFrame || 0);
  
  // 单个字符动画持续时间
  const charAnimationDuration = Math.max(durationFrames * 0.6, durationFrames / totalChars);
  
  // 计算字符间延迟，确保最后一个字符在durationFrames - 1帧时完成消失
  const lastCharStartDelay = durationFrames - 1 - charAnimationDuration;
  const delayInterval = totalChars > 1 ? lastCharStartDelay / (totalChars - 1) : 0;
  
  return (
    <div style={{
      position: 'relative',
      backgroundColor: 'transparent'
    }}>
      {chars.map((char, index) => {
        // 计算每个字符的开始延迟
        const charDelay = index * delayInterval;
        
        // 字符动画进度（基于相对帧数）
        const charProgress = Math.max(0, Math.min(1, (relativeFrame - charDelay) / charAnimationDuration));
        
        // 断电阶段
        const powerOffPhase = interpolate(charProgress, [0, 0.3, 0.7, 1], [0, 1, 2, 3]);
        
        // 文字透明度 - 出场动画，从1到0
        const opacity = interpolate(charProgress, [0, 0.6, 1], [1, 0.3, 0]);
        const electricIntensity = interpolate(powerOffPhase, [0, 1, 2, 3], [1, 0.5, 0.2, 0]);
        
        return (
          <div key={index} style={{
            position: 'relative',
            display: 'inline-block'
          }}>
            {/* 电路线条 */}
            {powerOffPhase < 2.5 && opacity > 0 && (
              <>
                {Array.from({ length: circuitComplexity }, (_, i) => {
                  const lineAngle = (i / circuitComplexity) * Math.PI * 2;
                  const lineLength = 25 + i * 3;
                  const lineOpacity = interpolate(powerOffPhase, [0, 1, 2.5], [electricIntensity, electricIntensity * 0.7, 0]);
                  const flickerIntensity = Math.sin(frame * 0.3 + i) * 0.3 + 0.7;
                  
                  return (
                    <div
                      key={`circuit-${i}`}
                      style={{
                        position: 'absolute',
                        left: '50%',
                        top: '50%',
                        width: `${lineLength}px`,
                        height: '2px',
                        background: `linear-gradient(90deg, 
                          rgba(0, 255, 255, ${lineOpacity * flickerIntensity}) 0%, 
                          rgba(0, 150, 255, ${lineOpacity * flickerIntensity * 0.6}) 100%)`,
                        borderRadius: '1px',
                        transform: `translate(-50%, -50%) rotate(${lineAngle}rad)`,
                        transformOrigin: '0 50%',
                        pointerEvents: 'none',
                        boxShadow: `0 0 4px rgba(0, 255, 255, ${lineOpacity * flickerIntensity * 0.5})`
                      }}
                    />
                  );
                })}
              </>
            )}
            
            {/* 电路节点 */}
            {powerOffPhase < 2.5 && opacity > 0 && (
              <>
                {Array.from({ length: 6 }, (_, i) => {
                  const nodeAngle = (i / 6) * Math.PI * 2;
                  const nodeDistance = 20 + i * 5;
                  const nodeX = Math.cos(nodeAngle) * nodeDistance;
                  const nodeY = Math.sin(nodeAngle) * nodeDistance;
                  const nodeOpacity = interpolate(powerOffPhase, [0, 1, 2.5], [electricIntensity, electricIntensity * 0.8, 0]);
                  const nodePulse = Math.sin(frame * 0.2 + i) * 0.5 + 0.5;
                  
                  return (
                    <div
                      key={`node-${i}`}
                      style={{
                        position: 'absolute',
                        left: '50%',
                        top: '50%',
                        width: '4px',
                        height: '4px',
                        background: `rgba(0, 255, 255, ${nodeOpacity * nodePulse})`,
                        borderRadius: '50%',
                        transform: `translate(-50%, -50%) translate(${nodeX}px, ${nodeY}px)`,
                        pointerEvents: 'none',
                        boxShadow: `0 0 6px rgba(0, 255, 255, ${nodeOpacity * nodePulse * 0.8})`
                      }}
                    />
                  );
                })}
              </>
            )}
            
            {/* 火花效果 */}
            {powerOffPhase >= 1.5 && powerOffPhase < 2.2 && opacity > 0 && (
              <>
                {Array.from({ length: 8 }, (_, i) => {
                  const sparkAngle = Math.random() * Math.PI * 2;
                  const sparkDistance = Math.random() * 30;
                  const sparkX = Math.cos(sparkAngle) * sparkDistance;
                  const sparkY = Math.sin(sparkAngle) * sparkDistance;
                  const sparkOpacity = interpolate(powerOffPhase, [1.5, 2, 2.2], [0, 1, 0]) * Math.random();
                  
                  return (
                    <div
                      key={`spark-${i}`}
                      style={{
                        position: 'absolute',
                        left: '50%',
                        top: '50%',
                        width: '2px',
                        height: '2px',
                        background: `rgba(255, 255, 0, ${sparkOpacity})`,
                        borderRadius: '50%',
                        transform: `translate(-50%, -50%) translate(${sparkX}px, ${sparkY}px)`,
                        pointerEvents: 'none',
                        boxShadow: `0 0 4px rgba(255, 255, 0, ${sparkOpacity})`
                      }}
                    />
                  );
                })}
              </>
            )}
            
            {/* 主文字 */}
            <span style={{
              display: 'inline-block',
              opacity: opacity,
              color: `rgba(${Math.floor(100 + electricIntensity * 155)}, ${Math.floor(200 + electricIntensity * 55)}, 255, 1)`,
              textShadow: electricIntensity > 0.3 
                ? `0 0 ${5 + electricIntensity * 15}px rgba(0, 255, 255, ${electricIntensity * 0.8})` 
                : 'none',
              filter: `brightness(${0.5 + electricIntensity * 0.8}) contrast(${1 + electricIntensity * 0.5})`,
              ...style
            }}>
              {char}
            </span>
          </div>
        );
      })}
    </div>
  );
};

CircuitPowerOffEffect.showName = "电路断电";
CircuitPowerOffEffect.description = "circuit power off shutdown effect";
CircuitPowerOffEffect.key = "CircuitPowerOffEffect";

export default CircuitPowerOffEffect; 