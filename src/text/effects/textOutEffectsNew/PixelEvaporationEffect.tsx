import React from 'react';
import { useCurrentFrame, interpolate } from 'remotion';
import { TextOutEffectProps } from './type';

const PixelEvaporationEffect = ({
  text,
  style,
  durationFrames,
  startFrame
}: TextOutEffectProps) => {
  const frame = useCurrentFrame();
  
  const chars = Array.from(text);
  const totalChars = chars.length;
  const pixelSize = 3;
  
  // 相对于startFrame的当前帧数
  const relativeFrame = frame - (startFrame || 0);
  
  // 单个字符动画持续时间
  const charAnimationDuration = Math.max(durationFrames * 0.6, durationFrames / totalChars);
  
  // 计算字符间延迟，确保最后一个字符在durationFrames - 1帧时完成消失
  const lastCharStartDelay = durationFrames - 1 - charAnimationDuration;
  const delayInterval = totalChars > 1 ? lastCharStartDelay / (totalChars - 1) : 0;
  
  return (
    <div style={{
      position: 'relative',
      backgroundColor: 'transparent'
    }}>
      {chars.map((char, index) => {
        // 计算每个字符的开始延迟
        const charDelay = index * delayInterval;
        
        // 字符动画进度（基于相对帧数）
        const charProgress = Math.max(0, Math.min(1, (relativeFrame - charDelay) / charAnimationDuration));
        
        // 蒸发阶段
        const evaporationPhase = interpolate(charProgress, [0, 0.4, 0.8, 1], [0, 1, 2, 3]);
        
        // 文字透明度 - 出场动画，从1到0
        const opacity = interpolate(charProgress, [0, 0.6, 1], [1, 0.4, 0]);
        
        return (
          <div key={index} style={{
            position: 'relative',
            display: 'inline-block'
          }}>
            {/* 像素分解效果 */}
            {evaporationPhase >= 0.5 && opacity > 0 && (
              <>
                {Array.from({ length: 40 }, (_, i) => {
                  const pixelX = (i % 8 - 4) * pixelSize + Math.random() * pixelSize;
                  const pixelY = (Math.floor(i / 8) - 2.5) * pixelSize + Math.random() * pixelSize;
                  
                  // 像素上升速度
                  const riseSpeed = interpolate(evaporationPhase, [0.5, 3], [0, 60 + Math.random() * 40]);
                  const pixelFinalY = pixelY - riseSpeed;
                  
                  // 像素透明度
                  const pixelOpacity = interpolate(evaporationPhase, [0.5, 1.5, 3], [0.8, 0.6, 0]) * opacity;
                  
                  // 像素颜色随高度变化
                  const colorIntensity = Math.max(0, 1 - riseSpeed / 100);
                  const pixelColor = `rgba(${Math.floor(255 * colorIntensity)}, ${Math.floor(255 * colorIntensity)}, 255, ${pixelOpacity})`;
                  
                  return (
                    <div
                      key={`pixel-${i}`}
                      style={{
                        position: 'absolute',
                        left: '50%',
                        top: '50%',
                        width: `${pixelSize}px`,
                        height: `${pixelSize}px`,
                        backgroundColor: pixelColor,
                        transform: `translate(-50%, -50%) translate(${pixelX}px, ${pixelFinalY}px)`,
                        pointerEvents: 'none',
                        opacity: pixelOpacity > 0.1 ? 1 : 0
                      }}
                    />
                  );
                })}
              </>
            )}
            
            {/* 蒸汽效果 */}
            {evaporationPhase >= 1 && opacity > 0 && (
              <>
                {Array.from({ length: 8 }, (_, i) => {
                  const steamProgress = Math.max(0, (evaporationPhase - 1) / 2);
                  const steamX = Math.sin(frame * 0.1 + i) * 10;
                  const steamY = -steamProgress * 40 - 10;
                  const steamOpacity = interpolate(steamProgress, [0, 0.5, 1], [0, 0.4, 0]) * opacity;
                  const steamSize = 4 + steamProgress * 8;
                  
                  return (
                    <div
                      key={`steam-${i}`}
                      style={{
                        position: 'absolute',
                        left: '50%',
                        top: '50%',
                        width: `${steamSize}px`,
                        height: `${steamSize}px`,
                        background: `radial-gradient(circle, 
                          rgba(200, 220, 255, ${steamOpacity}) 0%, 
                          rgba(255, 255, 255, ${steamOpacity * 0.5}) 50%, 
                          transparent 100%)`,
                        borderRadius: '50%',
                        transform: `translate(-50%, -50%) translate(${steamX}px, ${steamY}px)`,
                        pointerEvents: 'none',
                        filter: 'blur(2px)'
                      }}
                    />
                  );
                })}
              </>
            )}
            
            {/* 网格化效果 */}
            {evaporationPhase >= 0.3 && evaporationPhase < 1.5 && opacity > 0 && (
              <div style={{
                position: 'absolute',
                left: '50%',
                top: '50%',
                width: '60px',
                height: '80px',
                background: `repeating-linear-gradient(
                  0deg,
                  transparent 0px,
                  transparent ${pixelSize - 1}px,
                  rgba(100, 150, 255, ${interpolate(evaporationPhase, [0.3, 1.5], [0, 0.3]) * opacity}) ${pixelSize - 1}px,
                  rgba(100, 150, 255, ${interpolate(evaporationPhase, [0.3, 1.5], [0, 0.3]) * opacity}) ${pixelSize}px
                ), repeating-linear-gradient(
                  90deg,
                  transparent 0px,
                  transparent ${pixelSize - 1}px,
                  rgba(100, 150, 255, ${interpolate(evaporationPhase, [0.3, 1.5], [0, 0.3]) * opacity}) ${pixelSize - 1}px,
                  rgba(100, 150, 255, ${interpolate(evaporationPhase, [0.3, 1.5], [0, 0.3]) * opacity}) ${pixelSize}px
                )`,
                transform: 'translate(-50%, -50%)',
                pointerEvents: 'none'
              }} />
            )}
            
            {/* 主文字 */}
            <span style={{
              display: 'inline-block',
              opacity: opacity,
              color: 'white',
              textShadow: evaporationPhase < 1 
                ? `0 0 10px rgba(100, 150, 255, ${evaporationPhase * 0.8})` 
                : 'none',
              filter: evaporationPhase >= 0.5 
                ? `blur(${interpolate(evaporationPhase, [0.5, 2], [0, 1])}px)` 
                : 'none',
              ...style
            }}>
              {char}
            </span>
          </div>
        );
      })}
    </div>
  );
};

PixelEvaporationEffect.showName = "像素蒸发";
PixelEvaporationEffect.description = "pixel evaporation dissolution effect";
PixelEvaporationEffect.key = "PixelEvaporationEffect";

export default PixelEvaporationEffect; 