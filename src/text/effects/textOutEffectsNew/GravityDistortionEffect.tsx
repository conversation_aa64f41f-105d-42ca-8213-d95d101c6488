import React from 'react';
import { useCurrentFrame, interpolate } from 'remotion';
import { TextOutEffectProps } from './type';

const GravityDistortionEffect = ({
  text,
  style,
  durationFrames,
  startFrame
}: TextOutEffectProps) => {
  const frame = useCurrentFrame();
  
  const chars = Array.from(text);
  const totalChars = chars.length;
  const distortionStrength = 1;
  
  // 相对于startFrame的当前帧数
  const relativeFrame = frame - (startFrame || 0);
  
  // 单个字符动画持续时间
  const charAnimationDuration = Math.max(durationFrames * 0.6, durationFrames / totalChars);
  
  // 计算字符间延迟，确保最后一个字符在durationFrames - 1帧时完成消失
  const lastCharStartDelay = durationFrames - 1 - charAnimationDuration;
  const delayInterval = totalChars > 1 ? lastCharStartDelay / (totalChars - 1) : 0;
  
  return (
    <div style={{
      position: 'relative',
      backgroundColor: 'transparent'
    }}>
      {chars.map((char, index) => {
        // 计算每个字符的开始延迟
        const charDelay = index * delayInterval;
        
        // 字符动画进度（基于相对帧数）
        const charProgress = Math.max(0, Math.min(1, (relativeFrame - charDelay) / charAnimationDuration));
        
        // 重力扭曲阶段
        const distortionPhase = interpolate(charProgress, [0, 0.4, 0.8, 1], [0, 1, 2, 3]);
        
        // 文字透明度和扭曲 - 出场动画，从1到0
        const opacity = interpolate(charProgress, [0, 0.7, 1], [1, 0.4, 0]);
        const distortion = interpolate(distortionPhase, [0, 2, 3], [0, distortionStrength * 20, distortionStrength * 50]);
        
        return (
          <div key={index} style={{
            position: 'relative',
            display: 'inline-block'
          }}>
            {/* 重力波环 */}
            {distortionPhase >= 0.5 && distortionPhase < 2.5 && opacity > 0 && (
              <>
                {Array.from({ length: 4 }, (_, i) => {
                  const waveRadius = 20 + i * 15;
                  const waveOpacity = interpolate(distortionPhase, [0.5, 1.5, 2.5], [0, 0.6 - i * 0.1, 0]);
                  const waveScale = 1 + Math.sin(frame * 0.1 + i) * 0.1;
                  
                  return (
                    <div
                      key={`wave-${i}`}
                      style={{
                        position: 'absolute',
                        left: '50%',
                        top: '50%',
                        width: `${waveRadius * waveScale}px`,
                        height: `${waveRadius * waveScale}px`,
                        border: `2px solid rgba(100, 0, 200, ${waveOpacity})`,
                        borderRadius: '50%',
                        transform: 'translate(-50%, -50%)',
                        pointerEvents: 'none'
                      }}
                    />
                  );
                })}
              </>
            )}
            
            {/* 主文字 */}
            <span style={{
              display: 'inline-block',
              opacity: opacity,
              color: 'white',
              transform: `
                perspective(200px) 
                rotateX(${Math.sin(frame * 0.1 + index) * distortion * 0.3}deg) 
                rotateY(${Math.cos(frame * 0.08 + index) * distortion * 0.2}deg)
                scale(${1 - distortion * 0.01})
              `,
              textShadow: distortionPhase < 2 
                ? `0 0 10px rgba(100, 0, 200, 0.8)` 
                : 'none',
              filter: `blur(${distortion * 0.1}px) brightness(${1 - distortion * 0.02})`,
              ...style
            }}>
              {char}
            </span>
          </div>
        );
      })}
    </div>
  );
};

GravityDistortionEffect.showName = "重力扭曲";
GravityDistortionEffect.description = "gravity distortion collapse effect";
GravityDistortionEffect.key = "GravityDistortionEffect";

export default GravityDistortionEffect; 