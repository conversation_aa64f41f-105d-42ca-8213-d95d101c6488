import React from 'react';
import { useCurrentFrame, interpolate } from 'remotion';
import { TextOutEffectProps } from './type';

const SlideDownOutEffect = ({
  text,
  style,
  durationFrames,
  startFrame
}: TextOutEffectProps) => {
  const frame = useCurrentFrame();
  
  const chars = Array.isArray(text) ? text : text.split('');
  
  // 相对于startFrame的当前帧数
  const relativeFrame = frame - (startFrame || 0);
  
  // 整体动画进度
  const progress = Math.max(0, Math.min(1, relativeFrame / durationFrames));
  
  // 向下移动动画 - 整体从0到200px，ease-in缓动
  const translateY = interpolate(progress, [0, 1], [0, 200], {
    extrapolateLeft: 'clamp',
    extrapolateRight: 'clamp',
    easing: (t) => t * t // ease-in
  });
  
  // 透明度动画 - 整体从1到0，ease-in缓动
  const opacity = interpolate(progress, [0, 1], [1, 0], {
    extrapolateLeft: 'clamp',
    extrapolateRight: 'clamp',
    easing: (t) => t * t // ease-in
  });
  
  return (
    <div style={{
      position: 'relative',
      backgroundColor: 'transparent',
      transform: `translateY(${translateY}px)`,
      opacity: opacity,
      ...style      
    }}>
      {chars.map((char, index) => (
        <span key={index} style={{
          display: 'inline-block'
        }}>
          {char}
        </span>
      ))}
    </div>
  );
};

SlideDownOutEffect.showName = "下滑飞出";
SlideDownOutEffect.description = "slide down and fade out effect";
SlideDownOutEffect.key = "SlideDownOutEffect";

export default SlideDownOutEffect; 