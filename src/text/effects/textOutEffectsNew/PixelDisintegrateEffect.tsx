import React from 'react';
import { useCurrentFrame, interpolate } from 'remotion';
import { TextOutEffectProps } from './type';

const PixelDisintegrateEffect = ({
  text,
  style,
  durationFrames,
  startFrame
}: TextOutEffectProps) => {
  const frame = useCurrentFrame();
  
  const chars = Array.from(text);
  const totalChars = chars.length;
  
  // 相对于startFrame的当前帧数
  const relativeFrame = frame - (startFrame || 0);
  
  // 单个字符动画持续时间
  const charAnimationDuration = Math.max(durationFrames * 0.6, durationFrames / totalChars);
  
  // 计算字符间延迟，确保最后一个字符在durationFrames - 1帧时完成消失
  const lastCharStartDelay = durationFrames - 1 - charAnimationDuration;
  const delayInterval = totalChars > 1 ? lastCharStartDelay / (totalChars - 1) : 0;
  
  // 随机函数
  const random = (seed: number) => {
    const x = Math.sin(seed * 12.9898) * 43758.5453;
    return x - Math.floor(x);
  };
  
  // 像素块大小16x16
  const pixelSize = 16;
  
  return (
    <div style={{
      position: 'relative',
      backgroundColor: 'transparent'
    }}>
      {chars.map((char, index) => {
        // 计算每个字符的开始延迟
        const charDelay = index * delayInterval;
        
        // 字符动画进度（基于相对帧数）
        const charProgress = Math.max(0, Math.min(1, (relativeFrame - charDelay) / charAnimationDuration));
        
        // 计算字符区域的像素块数量
        const charWidth = 60 * 0.6;
        const charHeight = 60;
        const pixelsX = Math.ceil(charWidth / pixelSize);
        const pixelsY = Math.ceil(charHeight / pixelSize);
        
        // 文字透明度 - 出场动画，从1到0
        const opacity = interpolate(charProgress, [0, 0.3, 1], [1, 0.8, 0]);
        
        return (
          <div
            key={index}
            style={{
              position: 'relative',
              display: 'inline-block',
              width: charWidth,
              height: charHeight
            }}
          >
            {/* 原始文字 - 在像素化开始前显示 */}
            {charProgress < 0.1 && (
              <span
                style={{
                  position: 'absolute',
                  left: 0,
                  top: 0,
                  color: 'white',
                  display: 'inline-block',
                  ...style
                }}
              >
                {char}
              </span>
            )}
            
            {/* 生成像素块，向外溅射 */}
            {charProgress >= 0.1 && opacity > 0 && Array.from({ length: pixelsX * pixelsY }, (_, pixelIndex) => {
              const pixelX = pixelIndex % pixelsX;
              const pixelY = Math.floor(pixelIndex / pixelsX);
              
              // 每个像素块的随机参数
              const pixelSeed = index * 1000 + pixelIndex;
              const splashAngle = random(pixelSeed) * Math.PI * 2;
              const splashDistance = 200 * (0.5 + random(pixelSeed + 100) * 0.5);
              const rotationSpeed = (random(pixelSeed + 200) - 0.5) * 720; // 旋转角度
              
              // 溅射动画（向外飞出）
              const splashProgress = Math.max(0, (charProgress - 0.1) / 0.9);
              const splashX = Math.cos(splashAngle) * splashDistance * splashProgress;
              const splashY = Math.sin(splashAngle) * splashDistance * splashProgress;
              const rotation = rotationSpeed * splashProgress;
              
              // 像素块透明度
              const pixelOpacity = interpolate(splashProgress, [0, 0.3, 1], [1, 0.8, 0]);
              
              return (
                <div
                  key={pixelIndex}
                  style={{
                    position: 'absolute',
                    left: pixelX * pixelSize,
                    top: pixelY * pixelSize,
                    width: pixelSize,
                    height: pixelSize,
                    backgroundColor: 'white',
                    transform: `translate(${splashX}px, ${splashY}px) rotate(${rotation}deg)`,
                    opacity: pixelOpacity,
                    // 拖尾效果
                    boxShadow: splashProgress > 0.2 ? `
                      ${-splashX * 0.3}px ${-splashY * 0.3}px 0px rgba(255,255,255, ${pixelOpacity * 0.5}),
                      ${-splashX * 0.6}px ${-splashY * 0.6}px 0px rgba(255,255,255, ${pixelOpacity * 0.3})
                    ` : 'none'
                  }}
                />
              );
            })}
          </div>
        );
      })}
    </div>
  );
};

PixelDisintegrateEffect.showName = "像素解体";
PixelDisintegrateEffect.description = "pixel disintegrate effect with splashing fragments";
PixelDisintegrateEffect.key = "PixelDisintegrateEffect";

export default PixelDisintegrateEffect; 