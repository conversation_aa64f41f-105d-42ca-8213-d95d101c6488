import React from 'react';
import { useCurrentFrame, interpolate } from 'remotion';
import { TextOutEffectProps } from './type';

const CharacterByCharacterOutEffect = ({
  text,
  style,
  durationFrames,
  startFrame
}: TextOutEffectProps) => {
  const frame = useCurrentFrame();
  
  const chars = Array.isArray(text) ? text : text.split('');
  const totalChars = chars.length;
  
  // 相对于startFrame的当前帧数
  const relativeFrame = frame - (startFrame || 0);
  
  // 每个字符的动画持续时间（较短，使字符快速消失）
  const charAnimationDuration = Math.max(10, durationFrames * 0.2);
  
  // 计算字符间延迟，确保最后一个字符在durationFrames-1帧时完成消失
  const lastCharStartDelay = (durationFrames - 1) - charAnimationDuration;
  const delayInterval = totalChars > 1 ? lastCharStartDelay / (totalChars - 1) : 0;
  
  return (
    <div style={{
      position: 'relative',
      backgroundColor: 'transparent',      
    }}>
      {chars.map((char, index) => {
        // 计算每个字符的开始延迟
        const charDelay = index * delayInterval;
        
        // 字符动画进度（基于相对帧数）
        const charProgress = Math.max(0, Math.min(1, (relativeFrame - charDelay) / charAnimationDuration));
        
        // 字符透明度 - 快速淡出
        const opacity = interpolate(charProgress, [0, 1], [1, 0], {
          extrapolateLeft: 'clamp',
          extrapolateRight: 'clamp'
        });
        
        return (
          <span key={index} style={{
            display: 'inline-block',
            opacity: opacity,
            transition: 'none', // 确保没有CSS过渡干扰
            ...style
          }}>
            {char}
          </span>
        );
      })}
    </div>
  );
};

CharacterByCharacterOutEffect.showName = "逐字消失";
CharacterByCharacterOutEffect.description = "character by character fade out effect";
CharacterByCharacterOutEffect.key = "CharacterByCharacterOutEffect";

export default CharacterByCharacterOutEffect; 