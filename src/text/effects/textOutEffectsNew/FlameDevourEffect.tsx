import React from 'react';
import { useCurrentFrame, interpolate } from 'remotion';
import { TextOutEffectProps } from './type';

const FlameDevourEffect = ({
  text,
  style,
  durationFrames,
  startFrame
}: TextOutEffectProps) => {
  const frame = useCurrentFrame();
  
  const chars = Array.from(text);
  const totalChars = chars.length;
  const flameIntensity = 1;
  
  // 相对于startFrame的当前帧数
  const relativeFrame = frame - (startFrame || 0);
  
  // 单个字符动画持续时间
  const charAnimationDuration = Math.max(durationFrames * 0.6, durationFrames / totalChars);
  
  // 计算字符间延迟，确保最后一个字符在durationFrames - 1帧时完成消失
  const lastCharStartDelay = durationFrames - 1 - charAnimationDuration;
  const delayInterval = totalChars > 1 ? lastCharStartDelay / (totalChars - 1) : 0;
  
  return (
    <div style={{
      position: 'relative',
      backgroundColor: 'transparent'
    }}>
      {chars.map((char, index) => {
        // 计算每个字符的开始延迟
        const charDelay = index * delayInterval;
        
        // 字符动画进度（基于相对帧数）
        const charProgress = Math.max(0, Math.min(1, (relativeFrame - charDelay) / charAnimationDuration));
        
        // 火焰吞噬阶段
        const devourPhase = interpolate(charProgress, [0, 0.3, 0.7, 1], [0, 1, 2, 3]);
        
        // 文字透明度 - 出场动画，从1到0
        const opacity = interpolate(charProgress, [0, 0.5, 1], [1, 0.3, 0]);
        
        return (
          <div key={index} style={{
            position: 'relative',
            display: 'inline-block'
          }}>
            {/* 火焰从底部开始燃烧 */}
            {devourPhase >= 0.5 && devourPhase < 2.5 && opacity > 0 && (
              <>
                {Array.from({ length: 8 }, (_, i) => {
                  const flameHeight = interpolate(devourPhase, [0.5, 2], [0, 80]);
                  const flameX = (i - 4) * 6 + Math.sin(frame * 0.3 + i) * 3;
                  const flameY = 30 - (flameHeight * i / 8);
                  const flameWidth = Math.max(1, 6 - i * 0.5);
                  const flameOpacity = interpolate(devourPhase, [0.5, 1.5, 2.5], [0, flameIntensity, 0]) * (0.8 - i * 0.1);
                  
                  // 火焰颜色从橙红到黄白
                  const flameColorIntensity = i / 8;
                  const red = 255;
                  const green = Math.floor(100 + flameColorIntensity * 155);
                  const blue = Math.floor(flameColorIntensity * 100);
                  
                  return (
                    <div
                      key={`flame-${i}`}
                      style={{
                        position: 'absolute',
                        left: '50%',
                        bottom: '0%',
                        width: `${flameWidth}px`,
                        height: `${flameHeight * (0.8 + i * 0.1)}px`,
                        background: `linear-gradient(0deg, 
                          rgba(${red}, ${green}, ${blue}, ${flameOpacity}) 0%,
                          rgba(255, 200, 0, ${flameOpacity * 0.8}) 50%,
                          rgba(255, 255, 100, ${flameOpacity * 0.4}) 80%,
                          transparent 100%)`,
                        borderRadius: '50% 50% 50% 50% / 100% 100% 20% 20%',
                        transform: `translateX(-50%) translateX(${flameX}px) translateY(${flameY}px) scale(${1 + Math.sin(frame * 0.2 + i) * 0.1})`,
                        pointerEvents: 'none',
                        filter: 'blur(1px)'
                      }}
                    />
                  );
                })}
              </>
            )}
            
            {/* 火星飞溅 */}
            {devourPhase >= 1 && devourPhase < 2 && opacity > 0 && (
              <>
                {Array.from({ length: 12 }, (_, i) => {
                  const sparkAngle = (i / 12) * Math.PI * 2 + Math.sin(frame * 0.1) * 0.5;
                  const sparkDistance = interpolate(devourPhase, [1, 2], [0, 30 + Math.random() * 20]);
                  const sparkX = Math.cos(sparkAngle) * sparkDistance;
                  const sparkY = Math.sin(sparkAngle) * sparkDistance + 20;
                  const sparkOpacity = interpolate(devourPhase, [1, 1.5, 2], [0, 1, 0]) * Math.random();
                  const sparkSize = 1 + Math.random() * 2;
                  
                  return (
                    <div
                      key={`spark-${i}`}
                      style={{
                        position: 'absolute',
                        left: '50%',
                        top: '50%',
                        width: `${sparkSize}px`,
                        height: `${sparkSize}px`,
                        background: `rgba(255, ${Math.floor(150 + Math.random() * 105)}, 0, ${sparkOpacity})`,
                        borderRadius: '50%',
                        transform: `translate(-50%, -50%) translate(${sparkX}px, ${sparkY}px)`,
                        pointerEvents: 'none',
                        boxShadow: `0 0 ${sparkSize * 3}px rgba(255, 165, 0, ${sparkOpacity * 0.6})`
                      }}
                    />
                  );
                })}
              </>
            )}
            
            {/* 烟雾效果 */}
            {devourPhase >= 1.5 && opacity > 0 && (
              <>
                {Array.from({ length: 6 }, (_, i) => {
                  const smokeProgress = Math.max(0, (devourPhase - 1.5) / 1.5);
                  const smokeX = Math.sin(frame * 0.05 + i) * 15;
                  const smokeY = -smokeProgress * 50 - 10;
                  const smokeOpacity = interpolate(smokeProgress, [0, 0.5, 1], [0, 0.4, 0]) * opacity;
                  const smokeSize = 8 + smokeProgress * 12;
                  
                  return (
                    <div
                      key={`smoke-${i}`}
                      style={{
                        position: 'absolute',
                        left: '50%',
                        top: '50%',
                        width: `${smokeSize}px`,
                        height: `${smokeSize}px`,
                        background: `radial-gradient(circle, 
                          rgba(100, 100, 100, ${smokeOpacity}) 0%, 
                          rgba(150, 150, 150, ${smokeOpacity * 0.5}) 50%, 
                          transparent 100%)`,
                        borderRadius: '50%',
                        transform: `translate(-50%, -50%) translate(${smokeX}px, ${smokeY}px)`,
                        pointerEvents: 'none',
                        filter: 'blur(2px)'
                      }}
                    />
                  );
                })}
              </>
            )}
            
            {/* 灰烬残留 */}
            {devourPhase >= 2.5 && opacity > 0 && (
              <>
                {Array.from({ length: 8 }, (_, i) => {
                  const ashX = (Math.random() - 0.5) * 40;
                  const ashY = 20 + Math.random() * 20;
                  const ashOpacity = interpolate(devourPhase, [2.5, 3], [0.6, 0.2]) * opacity;
                  const ashSize = 1 + Math.random() * 2;
                  
                  return (
                    <div
                      key={`ash-${i}`}
                      style={{
                        position: 'absolute',
                        left: '50%',
                        top: '50%',
                        width: `${ashSize}px`,
                        height: `${ashSize}px`,
                        background: `rgba(80, 80, 80, ${ashOpacity})`,
                        borderRadius: '50%',
                        transform: `translate(-50%, -50%) translate(${ashX}px, ${ashY}px)`,
                        pointerEvents: 'none'
                      }}
                    />
                  );
                })}
              </>
            )}
            
            {/* 主文字 */}
            <span style={{
              display: 'inline-block',
              opacity: opacity,
              color: devourPhase < 1.5 ? 'white' : `rgba(100, 100, 100, ${opacity})`,
              textShadow: devourPhase < 1.5 
                ? `0 0 10px rgba(255, 100, 0, ${Math.min(devourPhase, 1) * 0.8})` 
                : 'none',
              filter: devourPhase >= 1.5 
                ? `blur(${interpolate(devourPhase, [1.5, 3], [0, 2])}px) brightness(${interpolate(devourPhase, [1.5, 3], [1, 0.3])})` 
                : `brightness(${1 + Math.min(devourPhase, 1) * 0.3})`,
              ...style
            }}>
              {char}
            </span>
          </div>
        );
      })}
    </div>
  );
};

FlameDevourEffect.showName = "火焰吞噬";
FlameDevourEffect.description = "flame devour effect with fire and smoke";
FlameDevourEffect.key = "FlameDevourEffect";

export default FlameDevourEffect; 