import React from 'react';
import { useCurrentFrame, interpolate } from 'remotion';
import { TextOutEffectProps } from './type';

const StarAnnihilationEffect = ({
  text,
  style,
  durationFrames,
  startFrame
}: TextOutEffectProps) => {
  const frame = useCurrentFrame();
  
  const chars = Array.from(text);
  const totalChars = chars.length;
  const starCount = 12;
  
  // 相对于startFrame的当前帧数
  const relativeFrame = frame - (startFrame || 0);
  
  // 单个字符动画持续时间
  const charAnimationDuration = Math.max(durationFrames * 0.6, durationFrames / totalChars);
  
  // 计算字符间延迟，确保最后一个字符在durationFrames - 1帧时完成消失
  const lastCharStartDelay = durationFrames - 1 - charAnimationDuration;
  const delayInterval = totalChars > 1 ? lastCharStartDelay / (totalChars - 1) : 0;
  
  return (
    <div style={{
      position: 'relative',
      backgroundColor: 'transparent'
    }}>
      {chars.map((char, index) => {
        // 计算每个字符的开始延迟
        const charDelay = index * delayInterval;
        
        // 字符动画进度（基于相对帧数）
        const charProgress = Math.max(0, Math.min(1, (relativeFrame - charDelay) / charAnimationDuration));
        
        // 湮灭阶段
        const annihilationPhase = interpolate(charProgress, [0, 0.3, 0.7, 1], [0, 1, 2, 3]);
        
        // 文字透明度 - 出场动画，从1到0
        const opacity = interpolate(charProgress, [0, 0.5, 1], [1, 0.5, 0]);
        
        return (
          <div key={index} style={{
            position: 'relative',
            display: 'inline-block'
          }}>
            {/* 星光闪烁 */}
            {annihilationPhase < 2 && opacity > 0 && (
              <>
                {Array.from({ length: starCount }, (_, i) => {
                  const starAngle = ((i / starCount) * Math.PI * 2) + (frame * 0.02);
                  const starDistance = 25 + Math.sin(frame * 0.1 + i) * 10;
                  const starX = Math.cos(starAngle) * starDistance;
                  const starY = Math.sin(starAngle) * starDistance;
                  const starOpacity = interpolate(annihilationPhase, [0, 1, 2], [0, 1, 0]) * (0.7 + Math.sin(frame * 0.2 + i) * 0.3);
                  const starSize = 2 + Math.sin(frame * 0.15 + i) * 1;
                  
                  return (
                    <div
                      key={`star-${i}`}
                      style={{
                        position: 'absolute',
                        left: '50%',
                        top: '50%',
                        width: `${starSize}px`,
                        height: `${starSize}px`,
                        background: `radial-gradient(circle, 
                          rgba(255,255,255,${starOpacity}) 0%, 
                          rgba(200,220,255,${starOpacity * 0.8}) 50%, 
                          transparent 100%)`,
                        borderRadius: '50%',
                        transform: `translate(-50%, -50%) translate(${starX}px, ${starY}px)`,
                        pointerEvents: 'none',
                        boxShadow: `0 0 ${starSize * 2}px rgba(255,255,255,${starOpacity * 0.5})`
                      }}
                    />
                  );
                })}
              </>
            )}
            
            {/* 湮灭爆发 */}
            {annihilationPhase >= 1.5 && annihilationPhase < 2.5 && opacity > 0 && (
              <div style={{
                position: 'absolute',
                left: '50%',
                top: '50%',
                width: `${interpolate(annihilationPhase, [1.5, 2.5], [0, 80])}px`,
                height: `${interpolate(annihilationPhase, [1.5, 2.5], [0, 80])}px`,
                background: `radial-gradient(circle, 
                  rgba(255,255,255,${interpolate(annihilationPhase, [1.5, 2, 2.5], [0, 1, 0])}) 0%, 
                  rgba(100,150,255,${interpolate(annihilationPhase, [1.5, 2, 2.5], [0, 0.8, 0])}) 30%, 
                  transparent 100%)`,
                borderRadius: '50%',
                transform: 'translate(-50%, -50%)',
                pointerEvents: 'none',
                filter: 'blur(2px)'
              }} />
            )}
            
            {/* 能量粒子飞散 */}
            {annihilationPhase >= 2 && opacity > 0 && (
              <>
                {Array.from({ length: 16 }, (_, i) => {
                  const particleAngle = (i / 16) * Math.PI * 2;
                  const particleProgress = Math.max(0, (annihilationPhase - 2) / 1);
                  const particleDistance = interpolate(particleProgress, [0, 1], [0, 60 + Math.random() * 40]);
                  const particleX = Math.cos(particleAngle) * particleDistance;
                  const particleY = Math.sin(particleAngle) * particleDistance;
                  const particleOpacity = interpolate(particleProgress, [0, 0.3, 1], [1, 0.6, 0]);
                  const particleSize = 1 + Math.random() * 2;
                  
                  return (
                    <div
                      key={`particle-${i}`}
                      style={{
                        position: 'absolute',
                        left: '50%',
                        top: '50%',
                        width: `${particleSize}px`,
                        height: `${particleSize}px`,
                        background: `rgba(100, 150, 255, ${particleOpacity})`,
                        borderRadius: '50%',
                        transform: `translate(-50%, -50%) translate(${particleX}px, ${particleY}px)`,
                        pointerEvents: 'none',
                        boxShadow: `0 0 ${particleSize * 2}px rgba(100,150,255,${particleOpacity * 0.5})`
                      }}
                    />
                  );
                })}
              </>
            )}
            
            {/* 主文字 */}
            <span style={{
              display: 'inline-block',
              opacity: opacity,
              color: 'white',
              textShadow: annihilationPhase < 1.5 
                ? `0 0 10px rgba(255,255,255,0.8), 0 0 20px rgba(100,150,255,0.6)` 
                : 'none',
              filter: annihilationPhase >= 1.5 
                ? `blur(${interpolate(annihilationPhase, [1.5, 2.5], [0, 3])}px) brightness(${interpolate(annihilationPhase, [1.5, 2], [1, 3])})` 
                : 'none',
              ...style
            }}>
              {char}
            </span>
          </div>
        );
      })}
    </div>
  );
};

StarAnnihilationEffect.showName = "星空湮灭";
StarAnnihilationEffect.description = "star annihilation destruction effect";
StarAnnihilationEffect.key = "StarAnnihilationEffect";

export default StarAnnihilationEffect; 