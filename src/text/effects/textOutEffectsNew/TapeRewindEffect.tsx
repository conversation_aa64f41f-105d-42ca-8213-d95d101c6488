import React from 'react';
import { useCurrentFrame, interpolate } from 'remotion';
import { TextOutEffectProps } from './type';

const TapeRewindEffect = ({
  text,
  style,
  durationFrames,
  startFrame
}: TextOutEffectProps) => {
  const frame = useCurrentFrame();
  
  const chars = Array.from(text);
  const totalChars = chars.length;
  const rewindSpeed = 120; // 卷带速度120px/s
  
  // 相对于startFrame的当前帧数
  const relativeFrame = frame - (startFrame || 0);
  
  // 单个字符动画持续时间
  const charAnimationDuration = Math.max(durationFrames * 0.6, durationFrames / totalChars);
  
  // 计算字符间延迟，确保最后一个字符在durationFrames - 1帧时完成消失
  const lastCharStartDelay = durationFrames - 1 - charAnimationDuration;
  const delayInterval = totalChars > 1 ? lastCharStartDelay / (totalChars - 1) : 0;
  
  return (
    <div style={{
      position: 'relative',
      backgroundColor: 'transparent'
    }}>
      {chars.map((char, index) => {
        // 计算每个字符的开始延迟
        const charDelay = index * delayInterval;
        
        // 字符动画进度（基于相对帧数）
        const charProgress = Math.max(0, Math.min(1, (relativeFrame - charDelay) / charAnimationDuration));
        
        // 倒卷阶段
        const rewindPhase = interpolate(charProgress, [0, 0.2, 0.6, 1], [0, 1, 2, 3]);
        
        // 文字透明度 - 出场动画，从1到0
        const opacity = interpolate(charProgress, [0, 0.8, 1], [1, 0.2, 0]);
        
        return (
          <div key={index} style={{
            position: 'relative',
            display: 'inline-block'
          }}>
            {/* 播放器反向旋转 */}
            {rewindPhase >= 0.5 && rewindPhase < 2.8 && opacity > 0 && (
              <>
                {/* 左卷轴 */}
                <div style={{
                  position: 'absolute',
                  left: -50,
                  top: '50%',
                  width: '20px',
                  height: '20px',
                  background: `radial-gradient(circle, 
                    rgba(150,150,150,${interpolate(rewindPhase, [0.5, 1.5, 2.8], [0, 0.8, 0]) * opacity}) 0%, 
                    rgba(100,100,100,${interpolate(rewindPhase, [0.5, 1.5, 2.8], [0, 0.6, 0]) * opacity}) 100%)`,
                  borderRadius: '50%',
                  border: `2px solid rgba(120,120,120,${interpolate(rewindPhase, [0.5, 1.5, 2.8], [0, 0.8, 0]) * opacity})`,
                  transform: `translate(-50%, -50%) rotate(${-frame * 8}deg)`,
                  pointerEvents: 'none'
                }} />
                
                {/* 右卷轴 */}
                <div style={{
                  position: 'absolute',
                  right: -50,
                  top: '50%',
                  width: `${interpolate(rewindPhase, [0.5, 2.5], [20, 8])}px`,
                  height: `${interpolate(rewindPhase, [0.5, 2.5], [20, 8])}px`,
                  background: `radial-gradient(circle, 
                    rgba(150,150,150,${interpolate(rewindPhase, [0.5, 1.5, 2.8], [0, 0.8, 0]) * opacity}) 0%, 
                    rgba(100,100,100,${interpolate(rewindPhase, [0.5, 1.5, 2.8], [0, 0.6, 0]) * opacity}) 100%)`,
                  borderRadius: '50%',
                  border: `2px solid rgba(120,120,120,${interpolate(rewindPhase, [0.5, 1.5, 2.8], [0, 0.8, 0]) * opacity})`,
                  transform: `translate(50%, -50%) rotate(${-frame * 12}deg)`,
                  pointerEvents: 'none'
                }} />
              </>
            )}
            
            {/* 磁带卷入效果 */}
            {rewindPhase >= 1 && opacity > 0 && (
              <>
                {Array.from({ length: 8 }, (_, i) => {
                  const tapeProgress = Math.max(0, (rewindPhase - 1 - i * 0.1) / 1.5);
                  
                  if (tapeProgress <= 0) return null;
                  
                  const tapeX = interpolate(tapeProgress, [0, 1], [0, -40 - i * 5]);
                  const tapeOpacity = interpolate(tapeProgress, [0, 0.5, 1], [0.6, 0.8, 0]) * opacity;
                  const tapeWidth = interpolate(tapeProgress, [0, 1], [60, 10]);
                  
                  return (
                    <div
                      key={`tape-${i}`}
                      style={{
                        position: 'absolute',
                        left: '50%',
                        top: '50%',
                        width: `${tapeWidth}px`,
                        height: '4px',
                        background: `linear-gradient(90deg, 
                          rgba(100,80,60,${tapeOpacity}) 0%, 
                          rgba(80,60,40,${tapeOpacity * 0.8}) 100%)`,
                        transform: `translate(-50%, -50%) translateX(${tapeX}px)`,
                        pointerEvents: 'none'
                      }}
                    />
                  );
                })}
              </>
            )}
            
            {/* 音轨波纹消失 */}
            {rewindPhase >= 0.8 && rewindPhase < 2.2 && opacity > 0 && (
              <>
                {Array.from({ length: 6 }, (_, i) => {
                  const waveProgress = Math.max(0, (rewindPhase - 0.8 - i * 0.1) / 1.2);
                  const waveOpacity = interpolate(waveProgress, [0, 0.5, 1], [0.8, 0.6, 0]) * opacity;
                  const waveHeight = 3 + Math.sin(frame * 0.2 + i) * 2;
                  const waveX = -30 + i * 10;
                  
                  return (
                    <div
                      key={`wave-${i}`}
                      style={{
                        position: 'absolute',
                        left: '50%',
                        top: '50%',
                        width: '2px',
                        height: `${waveHeight}px`,
                        background: `rgba(200,180,160,${waveOpacity})`,
                        transform: `translate(-50%, -50%) translateX(${waveX}px)`,
                        pointerEvents: 'none'
                      }}
                    />
                  );
                })}
              </>
            )}
            
            {/* 磁带卷入卡匣 */}
            {rewindPhase >= 2.2 && rewindPhase < 2.8 && opacity > 0 && (
              <div style={{
                position: 'absolute',
                left: -60,
                top: -20,
                width: '120px',
                height: '40px',
                background: `linear-gradient(135deg, 
                  rgba(60,60,60,${interpolate(rewindPhase, [2.2, 2.8], [0, 0.8]) * opacity}) 0%, 
                  rgba(80,80,80,${interpolate(rewindPhase, [2.2, 2.8], [0, 0.6]) * opacity}) 100%)`,
                borderRadius: '4px',
                border: `1px solid rgba(100,100,100,${interpolate(rewindPhase, [2.2, 2.8], [0, 0.8]) * opacity})`,
                pointerEvents: 'none'
              }} />
            )}
            
            {/* 插头拔出 */}
            {rewindPhase >= 2.8 && opacity > 0 && (
              <div style={{
                position: 'absolute',
                right: -80,
                top: '50%',
                width: '15px',
                height: '8px',
                background: `linear-gradient(90deg, 
                  rgba(200,200,200,${interpolate(rewindPhase, [2.8, 3], [0.8, 0]) * opacity}) 0%, 
                  rgba(150,150,150,${interpolate(rewindPhase, [2.8, 3], [0.6, 0]) * opacity}) 100%)`,
                borderRadius: '2px',
                transform: `translate(0, -50%) translateX(${interpolate(rewindPhase, [2.8, 3], [0, 30])}px)`,
                pointerEvents: 'none'
              }} />
            )}
            
            {/* 播放指示灯 */}
            {rewindPhase >= 0.5 && rewindPhase < 2.5 && opacity > 0 && (
              <div style={{
                position: 'absolute',
                left: -30,
                top: -25,
                width: '6px',
                height: '6px',
                background: `rgba(255,100,100,${interpolate(rewindPhase, [0.5, 1.5, 2.5], [0, 0.8, 0]) * opacity})`,
                borderRadius: '50%',
                pointerEvents: 'none',
                boxShadow: `0 0 8px rgba(255,100,100,${interpolate(rewindPhase, [0.5, 1.5, 2.5], [0, 0.6, 0]) * opacity})`
              }} />
            )}
            
            {/* 速度指示条 */}
            {rewindPhase >= 1.2 && rewindPhase < 2.5 && opacity > 0 && (
              <>
                {Array.from({ length: 4 }, (_, i) => {
                  const speedBarHeight = interpolate(rewindPhase, [1.2, 2], [2, 8 + i * 2]);
                  const speedBarOpacity = interpolate(rewindPhase, [1.2, 2, 2.5], [0, 0.8, 0]) * opacity;
                  
                  return (
                    <div
                      key={`speedbar-${i}`}
                      style={{
                        position: 'absolute',
                        left: -40 + i * 3,
                        bottom: -30,
                        width: '2px',
                        height: `${speedBarHeight}px`,
                        background: `rgba(100,255,100,${speedBarOpacity})`,
                        pointerEvents: 'none'
                      }}
                    />
                  );
                })}
              </>
            )}
            
            {/* 主文字 */}
            <span style={{
              display: 'inline-block',
              opacity: opacity,
              color: 'white',
              textShadow: `0 0 8px rgba(200,180,160,${opacity * 0.8})`,
              filter: rewindPhase >= 1.5 
                ? `blur(${interpolate(rewindPhase, [1.5, 3], [0, 2])}px)` 
                : 'none',
              ...style
            }}>
              {char}
            </span>
          </div>
        );
      })}
    </div>
  );
};

TapeRewindEffect.showName = "磁带倒卷";
TapeRewindEffect.description = "tape rewind effect with cassette and spools";
TapeRewindEffect.key = "TapeRewindEffect";

export default TapeRewindEffect; 