import React from 'react';
import { useCurrentFrame, interpolate } from 'remotion';
import { TextOutEffectProps } from './type';

const DataClearEffect = ({
  text,
  style,
  durationFrames,
  startFrame
}: TextOutEffectProps) => {
  const frame = useCurrentFrame();
  
  const chars = Array.from(text);
  const totalChars = chars.length;
  
  // 相对于startFrame的当前帧数
  const relativeFrame = frame - (startFrame|| 0);
  
  // 单个字符动画持续时间
  const charAnimationDuration = Math.max(durationFrames * 0.6, durationFrames / totalChars);
  
  // 计算字符间延迟，确保最后一个字符在durationFrames - 1帧时完成消失
  const lastCharStartDelay = durationFrames - 1 - charAnimationDuration;
  const delayInterval = totalChars > 1 ? lastCharStartDelay / (totalChars - 1) : 0;
  
  return (
    <div style={{
      position: 'relative',
      backgroundColor: 'transparent',
      fontFamily: 'monospace'
    }}>
      {chars.map((char, index) => {
        // 计算每个字符的开始延迟
        const charDelay = index * delayInterval;
        
        // 字符动画进度（基于相对帧数）
        const charProgress = Math.max(0, Math.min(1, (relativeFrame - charDelay) / charAnimationDuration));
        
        // 数据清除阶段
        const clearPhase = interpolate(charProgress, [0, 0.3, 0.7, 1], [0, 1, 2, 3]);
        
        // 文字透明度 - 出场动画，从1到0
        const opacity = interpolate(charProgress, [0, 0.6, 1], [1, 0.3, 0]);
        
        // 随机字符替换
        const randomChar = Math.random().toString(36)[2] || '0';
        const isScrambled = clearPhase > 1 && clearPhase < 2 && Math.random() < 0.7;
        const displayChar = isScrambled ? randomChar : char;
        
        return (
          <div key={index} style={{
            position: 'relative',
            display: 'inline-block'
          }}>
            {/* 数据流效果 */}
            {clearPhase >= 0.5 && clearPhase < 2.5 && opacity > 0 && (
              <>
                {Array.from({ length: 6 }, (_, i) => {
                  const streamY = ((frame * 2 + i * 10) % 80) - 10;
                  const streamOpacity = interpolate(clearPhase, [0.5, 1.5, 2.5], [0, 0.6, 0]);
                  const streamChar = Math.random().toString(36)[2] || '1';
                  
                  return (
                    <span
                      key={`stream-${i}`}
                      style={{
                        position: 'absolute',
                        left: '50%',
                        top: `${streamY}px`,
                        fontSize: '12px',
                        color: `rgba(0, 255, 0, ${streamOpacity})`,
                        transform: 'translateX(-50%)',
                        pointerEvents: 'none',
                        textShadow: `0 0 5px rgba(0, 255, 0, ${streamOpacity * 0.5})`
                      }}
                    >
                      {streamChar}
                    </span>
                  );
                })}
              </>
            )}
            
            {/* 删除进度条 */}
            {clearPhase >= 1.5 && clearPhase < 2.5 && opacity > 0 && (
              <span style={{
                position: 'absolute',
                left: '0%',
                bottom: '-5px',
                width: `${interpolate(clearPhase, [1.5, 2.5], [0, 100])}%`,
                height: '2px',
                background: `linear-gradient(90deg, 
                  rgba(255, 0, 0, 0.8) 0%, 
                  rgba(255, 100, 0, 0.6) 100%)`,
                pointerEvents: 'none'
              }} />
            )}
            
            {/* 主文字 */}
            <span style={{
              display: 'inline-block',
              opacity: opacity,
              color: clearPhase < 1 ? 'white' : 
                    clearPhase < 2 ? '#00FF00' : 
                    `rgba(255, 0, 0, 1)`,
              textShadow: clearPhase >= 1 && clearPhase < 2 
                ? `0 0 10px rgba(0, 255, 0, 0.8)` 
                : clearPhase >= 2 
                ? `0 0 10px rgba(255, 0, 0, ${opacity * 0.8})` 
                : 'none',
              filter: clearPhase >= 1.5 
                ? `blur(${interpolate(clearPhase, [1.5, 3], [0, 1])}px)` 
                : 'none',
              transform: clearPhase >= 2 ? `scale(${1 - (clearPhase - 2) * 0.2})` : 'none',
              ...style
            }}>
              {displayChar}
            </span>
          </div>
        );
      })}
    </div>
  );
};

DataClearEffect.showName = "数据清除";
DataClearEffect.description = "data clearing deletion effect";
DataClearEffect.key = "DataClearEffect";

export default DataClearEffect; 