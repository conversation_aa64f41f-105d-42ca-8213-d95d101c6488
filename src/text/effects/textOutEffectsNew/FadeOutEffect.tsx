import React from 'react';
import { interpolate, useCurrentFrame } from 'remotion';
import { TextOutEffectProps } from './type';

const FadeOutEffect = ({
  text,
  style,
  durationFrames,
  startFrame
}: TextOutEffectProps) => {
  const frame = useCurrentFrame();
  
  const chars = Array.from(text);
  const totalChars = chars.length;
  
  // 相对于startFrame的当前帧数
  const relativeFrame = frame - (startFrame || 0);
  
  // 单个字符动画持续时间
  const charAnimationDuration = Math.max(durationFrames * 0.6, durationFrames / totalChars);
  
  // 计算字符间延迟，确保最后一个字符在durationFrames - 1帧时完成消失
  const lastCharStartDelay = durationFrames - 1 - charAnimationDuration;
  const delayInterval = totalChars > 1 ? lastCharStartDelay / (totalChars - 1) : 0;

  return (
    <div style={{
      position: 'relative',
      backgroundColor: 'transparent'
    }}>
      {chars.map((char, index) => {
        // 计算每个字符的开始延迟
        const charDelay = index * delayInterval;
        
        // 字符动画进度（基于相对帧数）
        const charProgress = Math.max(0, Math.min(1, (relativeFrame - charDelay) / charAnimationDuration));
        
        // 文字透明度 - 出场动画，从1到0
        const opacity = interpolate(charProgress, [0, 1], [1, 0]);

        return (
          <span
            key={index}
            style={{
              display: 'inline-block',
              opacity: opacity,
              color: '#ffffff',
              textShadow: '0 0 8px #ffffff',
              ...style
            }}
          >
            {char}
          </span>
        );
      })}
    </div>
  );
};

FadeOutEffect.showName = "淡出";
FadeOutEffect.description = "fade out text effect";
FadeOutEffect.key = "FadeOutEffect";

export default FadeOutEffect; 