import React from 'react';
import { useCurrentFrame, interpolate } from 'remotion';
import { TextOutEffectProps } from './type';

const HologramShutdownEffect = ({
  text,
  style,
  durationFrames,
  startFrame
}: TextOutEffectProps) => {
  const frame = useCurrentFrame();
  
  const chars = Array.from(text);
  const totalChars = chars.length;
  
  // 相对于startFrame的当前帧数
  const relativeFrame = frame - (startFrame || 0);
  
  // 单个字符动画持续时间
  const charAnimationDuration = Math.max(durationFrames * 0.6, durationFrames / totalChars);
  
  // 计算字符间延迟，确保最后一个字符在durationFrames - 1帧时完成消失
  const lastCharStartDelay = durationFrames - 1 - charAnimationDuration;
  const delayInterval = totalChars > 1 ? lastCharStartDelay / (totalChars - 1) : 0;
  
  return (
    <div style={{
      position: 'relative',
      backgroundColor: 'transparent'
    }}>
      {chars.map((char, index) => {
        // 计算每个字符的开始延迟
        const charDelay = index * delayInterval;
        
        // 字符动画进度（基于相对帧数）
        const charProgress = Math.max(0, Math.min(1, (relativeFrame - charDelay) / charAnimationDuration));
        
        // 关闭阶段
        const shutdownPhase = interpolate(charProgress, [0, 0.3, 0.7, 1], [0, 1, 2, 3]);
        
        // 文字透明度 - 出场动画，从1到0
        const opacity = interpolate(charProgress, [0, 0.8, 1], [1, 0.2, 0]);
        
        return (
          <div key={index} style={{
            position: 'relative',
            display: 'inline-block'
          }}>
            {/* 投影射线 */}
            {shutdownPhase < 2 && opacity > 0 && (
              <>
                {Array.from({ length: 8 }, (_, i) => {
                  const rayAngle = (i * 45) * Math.PI / 180;
                  const rayLength = interpolate(shutdownPhase, [0, 1, 2], [40, 40, 0]);
                  const rayOpacity = interpolate(shutdownPhase, [0, 0.5, 1, 2], [0, 1, 1, 0]);
                  
                  return (
                    <div
                      key={`ray-${i}`}
                      style={{
                        position: 'absolute',
                        left: '50%',
                        top: '50%',
                        width: '2px',
                        height: `${rayLength}px`,
                        background: `linear-gradient(180deg, 
                          rgba(0,255,255,${rayOpacity}) 0%, 
                          rgba(0,150,255,${rayOpacity * 0.5}) 50%, 
                          transparent 100%)`,
                        transform: `translate(-50%, -50%) rotate(${rayAngle}rad)`,
                        transformOrigin: '50% 0%',
                        pointerEvents: 'none'
                      }}
                    />
                  );
                })}
              </>
            )}
            
            {/* 扫描线反向运动 */}
            {shutdownPhase >= 1.2 && shutdownPhase < 2.5 && opacity > 0 && (
              <>
                {Array.from({ length: 3 }, (_, i) => {
                  const scanProgress = (shutdownPhase - 1.2) / 1.3;
                  const scanY = interpolate(scanProgress, [0, 1], [60, -20]) - i * 15;
                  const scanOpacity = interpolate(scanProgress, [0, 0.3, 0.7, 1], [0, 1, 1, 0]);
                  
                  return (
                    <div
                      key={`scan-${i}`}
                      style={{
                        position: 'absolute',
                        left: -10,
                        right: -10,
                        top: '50%',
                        height: '2px',
                        background: `linear-gradient(90deg, 
                          transparent 0%, 
                          rgba(0,255,255,${scanOpacity}) 50%, 
                          transparent 100%)`,
                        transform: `translateY(${scanY}px)`,
                        pointerEvents: 'none'
                      }}
                    />
                  );
                })}
              </>
            )}
            
            {/* 像素点离散 */}
            {shutdownPhase >= 2 && opacity > 0 && (
              <>
                {Array.from({ length: 16 }, (_, i) => {
                  const pixelProgress = Math.max(0, (shutdownPhase - 2) / 1);
                  const pixelAngle = (i / 16) * Math.PI * 2;
                  const pixelDistance = interpolate(pixelProgress, [0, 1], [0, 30]);
                  const pixelX = Math.cos(pixelAngle) * pixelDistance;
                  const pixelY = Math.sin(pixelAngle) * pixelDistance;
                  const pixelOpacity = interpolate(pixelProgress, [0, 0.5, 1], [1, 0.6, 0]);
                  const pixelSize = 2 + Math.random() * 3;
                  
                  return (
                    <div
                      key={`pixel-${i}`}
                      style={{
                        position: 'absolute',
                        left: '50%',
                        top: '50%',
                        width: `${pixelSize}px`,
                        height: `${pixelSize}px`,
                        backgroundColor: `rgba(0,255,255,${pixelOpacity})`,
                        borderRadius: '50%',
                        transform: `translate(-50%, -50%) translate(${pixelX}px, ${pixelY}px)`,
                        boxShadow: `0 0 4px rgba(0,255,255,${pixelOpacity})`,
                        pointerEvents: 'none'
                      }}
                    />
                  );
                })}
              </>
            )}
            
            {/* 主文字 */}
            <span style={{
              display: 'inline-block',
              opacity: opacity,
              color: 'rgba(0,255,255,1)',
              textShadow: `0 0 10px rgba(0,255,255,${opacity * 0.8})`,
              filter: shutdownPhase >= 1.5 
                ? `blur(${interpolate(shutdownPhase, [1.5, 3], [0, 2])}px)` 
                : 'none',
              ...style
            }}>
              {char}
            </span>
          </div>
        );
      })}
    </div>
  );
};

HologramShutdownEffect.showName = "全息关闭";
HologramShutdownEffect.description = "hologram shutdown effect with rays and scan lines";
HologramShutdownEffect.key = "HologramShutdownEffect";

export default HologramShutdownEffect; 