import React from 'react';
import { useCurrentFrame, interpolate, spring } from 'remotion';
import { TextInEffectProps } from './type';

export default function SlideInEffect({
  text = "滑动飞入",
  style = {},
  durationFrames = 30,
}: TextInEffectProps) {
  const frame = useCurrentFrame();
  
  const duration = durationFrames;
  
  // 计算文字长度
  const textLength = Array.from(text).length;
  
  return (
    <span
      style={{        
        position: 'relative',
      }}
    >
      {Array.from(text).map((char: any, index: number) => {
        // 根据文字长度和动画时长计算每个字符的延迟
        const maxStartDelay = duration * 0.6;
        const charDelay = textLength > 1 ? (maxStartDelay * index) / (textLength - 1) : 0;
        
        // 每个字符的入场动画时长至少占动画时长的40%
        const charAnimationDuration = Math.max(duration * 0.4, duration - charDelay);
        
        // 计算当前字符的入场进度
        const charStartFrame = charDelay;
        const charCurrentFrame = Math.max(0, frame - charStartFrame);
        
        // 使用 spring 物理动画
        const springProgress = spring({
          frame: charCurrentFrame,
          fps: 30,
          config: {
            damping: 10,
            mass: 0.8,
            stiffness: 100,
          },
        });
        
        // 水平位移：从左侧屏幕外 (-200px) 到原始位置 (0px)
        const translateX = interpolate(springProgress, [0, 1], [-200, 0]);
        
        // 透明度：只有开始飞入后才显示
        const opacity = springProgress > 0 ? interpolate(springProgress, [0, 0.2, 1], [0, 0.8, 1]) : 0;
        
        return (
          <span
            key={index}
            style={{
              position: 'relative',
              display: 'inline-block',              
            }}
          >
            <span
              style={{                                
                display: 'inline-block',
                transform: `translateX(${translateX}px)`,
                opacity,
                ...style
              }}
            >
              {char}
            </span>
          </span>
        );
      })}
    </span>
  );
}

SlideInEffect.key = 'SlideInEffect';
SlideInEffect.description = 'horizontal slide in effect with spring physics';
SlideInEffect.showName = "水平飞入"; 