import React from 'react';
import { useCurrentFrame, interpolate, spring } from 'remotion';
import { TextInEffectProps } from './type';

export default function ScaleInEffect({
  text = "放大进入",
  style = {},
  durationFrames = 90,
}: TextInEffectProps) {
  const frame = useCurrentFrame();
  
  const duration = durationFrames;
  
  // 计算文字长度
  const textLength = Array.from(text).length;
  
  return (
    <span
      style={{        
        position: 'relative',
      }}
    >
      {Array.from(text).map((char, index) => {
        // 根据文字长度和动画时长计算每个字符的延迟
        const maxStartDelay = duration * 0.4; // 减少延迟，给spring更多时间
        const charDelay = textLength > 1 ? (maxStartDelay * index) / (textLength - 1) : 0;
        
        // 计算当前字符的入场进度
        const charStartFrame = charDelay;
        const charCurrentFrame = Math.max(0, frame - charStartFrame);
        
        // 使用 spring 物理动画，优化配置确保自然衰减
        const springProgress = spring({
          frame: charCurrentFrame,
          fps: 30,
          config: {
            damping: 12,    // 降低阻尼，允许更多震荡
            mass: 1.2,      // 增加质量，延长动画时间
            stiffness: 100, // 降低刚度，让动画更柔和
            overshootClamping: false, // 启用回弹效果
          },
        });
        
        // 尺寸变化：从30%缩小到100%原始尺寸，增加变化幅度
        const scale = interpolate(springProgress, [0, 1], [0.3, 1]);
        
        // 添加轻微的透明度变化
        const opacity = interpolate(springProgress, [0, 0.2, 1], [0, 0.8, 1]);
        
        return (
          <span
            key={index}
            style={{
              position: 'relative',
              display: 'inline-block',              
            }}
          >
            <span
              style={{                                
                display: 'inline-block',
                transform: `scale(${scale})`,
                transformOrigin: 'center center',
                opacity,
                // 添加轻微的发光效果在动画过程中
                filter: springProgress < 0.9 ? `brightness(${1 + 0.3 * (1 - springProgress)})` : 'none',
                ...style
              }}
            >
              {char}
            </span>
          </span>
        );
      })}
    </span>
  );
}

ScaleInEffect.key = 'ScaleInEffect';
ScaleInEffect.description = 'scale in effect with extended spring bounce';
ScaleInEffect.showName = "放大进入"; 