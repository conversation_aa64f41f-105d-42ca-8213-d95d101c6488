import React from 'react';
import { useCurrentFrame, interpolate, spring } from 'remotion';
import { TextInEffectProps } from './type';

export default function RotateInEffect({
  text = "旋转进入",
  style = {},
  durationFrames = 40,
}: TextInEffectProps) {
  const frame = useCurrentFrame();
  
  const duration = durationFrames;
  
  // 计算文字长度
  const textLength = Array.from(text).length;
  
  return (
    <span
      style={{        
        position: 'relative',
      }}
    >
      {Array.from(text).map((char, index) => {
        // 根据文字长度和动画时长计算每个字符的延迟
        const maxStartDelay = duration * 0.6;
        const charDelay = textLength > 1 ? (maxStartDelay * index) / (textLength - 1) : 0;
        
        // 每个字符的入场动画时长至少占动画时长的40%
        const charAnimationDuration = Math.max(duration * 0.4, duration - charDelay);
        
        // 计算当前字符的入场进度
        const charStartFrame = charDelay;
        const charCurrentFrame = Math.max(0, frame - charStartFrame);
        
        // 使用 spring 物理动画
        const springProgress = spring({
          frame: charCurrentFrame,
          fps: 30,
          config: {
            damping: 12,
            mass: 1,
            stiffness: 150,
          },
        });
        
        // 旋转角度：从90度到0度（顺时针方向）
        const rotate = interpolate(springProgress, [0, 1], [90, 0]);
        
        // 透明度：只有开始旋转后才显示
        const opacity = springProgress > 0 ? interpolate(springProgress, [0, 0.3, 1], [0, 0.7, 1]) : 0;
        
        return (
          <span
            key={index}
            style={{
              position: 'relative',
              display: 'inline-block',              
            }}
          >
            <span
              style={{                                
                display: 'inline-block',
                transform: `rotate(${rotate}deg)`,
                transformOrigin: 'center center',
                opacity,
                ...style
              }}
            >
              {char}
            </span>
          </span>
        );
      })}
    </span>
  );
}

RotateInEffect.key = 'RotateInEffect';
RotateInEffect.description = 'rotate in effect with spring animation';
RotateInEffect.showName = "旋转进入"; 