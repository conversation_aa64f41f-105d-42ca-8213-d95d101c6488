import React from 'react';
import { useCurrentFrame, interpolate } from 'remotion';
import { TextInEffectProps } from './type';

export default function BlurRevealEffect({
  text = "模糊显现",
  style = {},
  durationFrames = 25,
}: TextInEffectProps) {
  const frame = useCurrentFrame();
  
  const duration = durationFrames;
  
  // 计算文字长度
  const textLength = Array.from(text).length;
  
  return (
    <span
      style={{        
        position: 'relative',
      }}
    >
      {Array.from(text).map((char, index) => {
        // 根据文字长度和动画时长计算每个字符的延迟
        const maxStartDelay = duration * 0.6;
        const charDelay = textLength > 1 ? (maxStartDelay * index) / (textLength - 1) : 0;
        
        // 每个字符的入场动画时长至少占动画时长的40%
        const charAnimationDuration = Math.max(duration * 0.4, duration - charDelay);
        
        // 计算当前字符的入场进度
        const charStartFrame = charDelay;
        const charCurrentFrame = Math.max(0, frame - charStartFrame);
        const charProgress = Math.min(1, charCurrentFrame / charAnimationDuration);
        
        // easeOutCubic 缓动曲线
        const easedProgress = 1 - Math.pow(1 - charProgress, 3);
        
        // 模糊度：从20px到0px
        const blurAmount = interpolate(easedProgress, [0, 1], [20, 0]);
        
        // 透明度变化：从0.8到1.0
        const opacity = interpolate(easedProgress, [0, 1], [0.8, 1.0]);
        
        // 色相微调：+10度
        const hueRotate = interpolate(easedProgress, [0, 1], [10, 0]);
        
        return (
          <span
            key={index}
            style={{
              position: 'relative',
              display: 'inline-block',              
            }}
          >
            <span
              style={{                                
                display: 'inline-block',
                filter: `blur(${blurAmount}px) hue-rotate(${hueRotate}deg)`,
                opacity,
                ...style
              }}
            >
              {char}
            </span>
          </span>
        );
      })}
    </span>
  );
}

BlurRevealEffect.key = 'BlurRevealEffect';
BlurRevealEffect.description = 'blur reveal effect with hue adjustment';
BlurRevealEffect.showName = "模糊显现"; 