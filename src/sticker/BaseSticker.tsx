import React, { useMemo } from "react";
import { useCurrentFrame, useVideoConfig, Img, Audio } from "remotion";
import { StickerProps } from "./type";

export const BaseSticker = ({
  imageSequenceUrlTemplate = "https://static.qiaoxuesi.com/remotion/sticker/{index}.png",
  urls,
  totalFrames,
  startIndex = 0,
  style = {},
  audioUrl,
  audioVolume = 0.2,
  duration = 1,
  crossOrigin = "anonymous",
  enableAudio = true,
}: StickerProps) => {
  const frame = useCurrentFrame();
  const { fps } = useVideoConfig();
  
  // 确定实际的帧数和图片URL列表
  const { actualTotalFrames, imageUrls } = useMemo(() => {
    if (urls && urls.length > 0) {
      // 使用urls模式
      return {
        actualTotalFrames: urls.length,
        imageUrls: urls
      };
    } else {
      // 使用imageSequenceUrlTemplate模式
      const frames = totalFrames || 24;
      const urlList = [];
      for (let i = 0; i < frames; i++) {
        const index = startIndex + i;
        urlList.push(imageSequenceUrlTemplate.replace("{index}", index.toString()));
      }
      return {
        actualTotalFrames: frames,
        imageUrls: urlList
      };
    }
  }, [urls, imageSequenceUrlTemplate, totalFrames, startIndex]);
  
  // 计算当前应该显示的图片帧索引
  const currentFrameIndex = useMemo(() => {
    // 计算在duration秒内播完actualTotalFrames的播放速度
    const framesPerSecond = actualTotalFrames / duration;
    // 根据当前帧和fps计算经过的时间
    const elapsedTime = frame / fps;
    // 计算当前应该播放的序列帧索引
    const sequenceFrame = Math.floor(elapsedTime * framesPerSecond) % actualTotalFrames;
    
    return sequenceFrame;
  }, [frame, fps, actualTotalFrames, duration]);

  // 获取当前帧对应的图片URL
  const currentImageUrl = useMemo(() => {
    return imageUrls[currentFrameIndex] || imageUrls[0] || "";
  }, [imageUrls, currentFrameIndex]);

  return (
    <>
      {/* 图片序列帧 */}
      <Img 
        src={currentImageUrl}
        style={{
          width: '100%',
          height: '100%',
        //   objectFit: 'contain',
          ...(style || {})
        }}
        crossOrigin={crossOrigin}
      />
      
      {/* 音频（如果提供了音频URL并且启用了音频） */}
      {/* {audioUrl && enableAudio && (
        <Audio 
          src={audioUrl}
          volume={audioVolume}
        />
      )} */}
    </>
  );
};