import React, { useRef, useEffect, useMemo, useState, useCallback } from "react";
import { useCurrentFrame, useVideoConfig, Audio } from "remotion";
import { GifStickerProps } from "./type";

export const SmartGifSticker = ({
  gifUrl = 'https://img.qiaoxuesi.com/upfiles/420_wao.gif',
  totalFrames = 24,
  duration = 1,
  style,
  audioUrl,
  audioVolume = 0.5,
  crossOrigin = "anonymous",
  enableAudio = true,
}: GifStickerProps) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const imgRef = useRef<HTMLImageElement>(null);
  const [gifLoaded, setGifLoaded] = useState(false);
  const [gifDimensions, setGifDimensions] = useState({ width: 0, height: 0 });
  
  const frame = useCurrentFrame();
  const { fps } = useVideoConfig();
  
  // 计算当前应该显示的gif帧索引
  const currentFrameIndex = useMemo(() => {
    const framesPerSecond = totalFrames / duration;
    const elapsedTime = frame / fps;
    const sequenceFrame = Math.floor(elapsedTime * framesPerSecond) % totalFrames;
    return sequenceFrame;
  }, [frame, fps, totalFrames, duration]);

  // 计算当前帧在gif中的时间位置（用于重置gif播放位置）
  const gifTimePosition = useMemo(() => {
    return (currentFrameIndex / totalFrames) * duration;
  }, [currentFrameIndex, totalFrames, duration]);

  // 加载gif图片
  useEffect(() => {
    if (!imgRef.current) return;
    
    const img = imgRef.current;
    img.crossOrigin = crossOrigin;
    img.src = gifUrl;
    
    const handleLoad = () => {
      setGifDimensions({ width: img.naturalWidth, height: img.naturalHeight });
      setGifLoaded(true);
    };
    
    const handleError = () => {
      console.error('Failed to load GIF:', gifUrl);
    };
    
    img.addEventListener('load', handleLoad);
    img.addEventListener('error', handleError);
    
    return () => {
      img.removeEventListener('load', handleLoad);
      img.removeEventListener('error', handleError);
    };
  }, [gifUrl, crossOrigin]);

  // 设置canvas尺寸
  useEffect(() => {
    if (!canvasRef.current || !gifLoaded) return;
    
    const canvas = canvasRef.current;
    canvas.width = gifDimensions.width;
    canvas.height = gifDimensions.height;
  }, [gifLoaded, gifDimensions]);

  // 在canvas上绘制gif的当前帧
  const drawGifFrame = useCallback(() => {
    if (!canvasRef.current || !imgRef.current || !gifLoaded) return;
    
    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');
    const img = imgRef.current;
    
    if (!ctx) return;
    
    // 清除canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    
    // 绘制gif图片
    ctx.drawImage(img, 0, 0, canvas.width, canvas.height);
    
    // 为了模拟帧控制，我们可以通过重新加载gif来"重置"播放位置
    // 这是一个权衡方案，因为浏览器不支持真正的gif帧控制
    
  }, [gifLoaded]);

  // 每帧更新绘制
  useEffect(() => {
    drawGifFrame();
  }, [drawGifFrame, currentFrameIndex]);

  // 当需要"重置"gif播放位置时，重新加载图片
  useEffect(() => {
    if (!imgRef.current || !gifLoaded) return;
    
    // 每完成一个循环就重新加载gif来同步播放
    const cycleNumber = Math.floor(frame / (fps * duration));
    const img = imgRef.current;
    
    // 通过添加时间戳参数来强制重新加载
    const newSrc = `${gifUrl}?cycle=${cycleNumber}`;
    if (img.src !== newSrc) {
      img.src = newSrc;
    }
  }, [gifUrl, frame, fps, duration, gifLoaded]);

  return (
    <>
      {/* 隐藏的gif图片元素，用于加载 */}
      <img
        ref={imgRef}
        style={{ display: 'none' }}
        alt="gif-sticker"
      />
      
      {/* Canvas用于显示gif */}
      <canvas
        ref={canvasRef}
        style={{
          width: '100%',
          height: '100%',
          opacity: gifLoaded ? 1 : 0,
          transition: 'opacity 0.3s ease',
          ...(style || {})
        }}
      />
      
      {/* 加载指示器 */}
      {!gifLoaded && (
        <div style={{
          position: 'absolute',
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
          background: 'rgba(0,0,0,0.7)',
          color: 'white',
          padding: '8px 12px',
          borderRadius: '4px',
          fontSize: '12px',
          fontFamily: 'monospace'
        }}>
          🎬 Loading GIF...
        </div>
      )}
      
      {/* 调试信息（开发时可见） */}
      {process.env.NODE_ENV === 'development' && gifLoaded && (
        <div style={{
          position: 'absolute',
          top: '5px',
          left: '5px',
          background: 'rgba(0,0,0,0.8)',
          color: 'white',
          padding: '4px 8px',
          borderRadius: '3px',
          fontSize: '10px',
          fontFamily: 'monospace',
          lineHeight: '1.2'
        }}>
          Frame: {currentFrameIndex}/{totalFrames}<br/>
          Time: {gifTimePosition.toFixed(2)}s
        </div>
      )}
      
      {/* 音频（如果提供了音频URL并且启用了音频） */}
      {audioUrl && enableAudio && (
        <Audio 
          src={audioUrl}
          volume={audioVolume}
        />
      )}
    </>
  );
}; 