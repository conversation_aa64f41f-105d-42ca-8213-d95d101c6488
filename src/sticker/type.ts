export type StickerProps = {
    /** 图片序列URL模板，使用{index}作为占位符 */
    imageSequenceUrlTemplate?: string;
    /** 图片地址列表，与imageSequenceUrlTemplate二选一 */
    urls?: string[];
    /** 图片序列总帧数 */
    totalFrames?: number;
    /** 图片序列起始索引，默认0 */
    startIndex?: number;
    /** 贴纸样式 */
    style?: React.CSSProperties;
    /** 音频文件URL */
    audioUrl?: string;
    /** 音频音量，范围0-1，默认0.5 */
    audioVolume?: number;
    /** 播完一次序列的时长（秒），默认1秒 */
    duration?: number;
    /** 图片加载跨域设置 */
    crossOrigin?: "anonymous" | "use-credentials";
    /** 是否启用音频，默认true */
    enableAudio?: boolean;
}

export type GifStickerProps = {
    /** gif图片URL */
    gifUrl?: string;
    /** gif总帧数 */
    totalFrames?: number;
    /** 播完一次序列的时长（秒），默认1秒 */
    duration?: number;
    /** 贴纸样式 */
    style?: React.CSSProperties;
    /** 音频文件URL */
    audioUrl?: string;
    /** 音频音量，范围0-1，默认0.5 */
    audioVolume?: number;
    /** 图片加载跨域设置 */
    crossOrigin?: "anonymous" | "use-credentials";
    /** 是否启用音频，默认true */
    enableAudio?: boolean;
}