import React, { useRef, useEffect, useMemo, useState, useCallback } from "react";
import { useCurrentFrame, useVideoConfig, Audio } from "remotion";
import { GifStickerProps } from "./type";

// 简化的GIF帧解析器接口
interface GifFrame {
  imageData: ImageData;
  delay: number;
  disposal: number;
}

export const AdvancedGifSticker = ({
  gifUrl = 'https://img.qiaoxuesi.com/upfiles/420_wao.gif',
  totalFrames = 24,
  duration = 1,
  style,
  audioUrl,
  audioVolume = 0.5,
  crossOrigin = "anonymous",
  enableAudio = true,
}: GifStickerProps) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [gifFrames, setGifFrames] = useState<GifFrame[]>([]);
  const [gifLoaded, setGifLoaded] = useState(false);
  const [loading, setLoading] = useState(false);
  
  const frame = useCurrentFrame();
  const { fps } = useVideoConfig();
  
  // 计算当前应该显示的gif帧索引
  const currentFrameIndex = useMemo(() => {
    // 计算在duration秒内播完totalFrames的播放速度
    const framesPerSecond = totalFrames / duration;
    // 根据当前帧和fps计算经过的时间
    const elapsedTime = frame / fps;
    // 计算当前应该播放的序列帧索引
    const sequenceFrame = Math.floor(elapsedTime * framesPerSecond) % totalFrames;
    
    return sequenceFrame;
  }, [frame, fps, totalFrames, duration]);

  // 使用gif-frames替代方案：基于Canvas的GIF解析
  const parseGifFrames = useCallback(async (url: string) => {
    try {
      setLoading(true);
      
      // 创建一个离屏Canvas来解析GIF
      const img = new Image();
      img.crossOrigin = crossOrigin;
      
      return new Promise<GifFrame[]>((resolve, reject) => {
        img.onload = () => {
          try {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            
            if (!ctx) {
              reject(new Error('Cannot get canvas context'));
              return;
            }
            
            canvas.width = img.width;
            canvas.height = img.height;
            
            // 注意：这是一个简化版本，真正的GIF解析需要更复杂的逻辑
            // 这里我们创建单个帧作为示例
            ctx.drawImage(img, 0, 0);
            const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
            
            // 创建虚拟的多帧数据（实际应用中需要真正的GIF解析库）
            const frames: GifFrame[] = [];
            for (let i = 0; i < totalFrames; i++) {
              frames.push({
                imageData: imageData,
                delay: Math.floor(1000 / (totalFrames / duration)), // 毫秒
                disposal: 0
              });
            }
            
            resolve(frames);
          } catch (error) {
            reject(error);
          }
        };
        
        img.onerror = () => {
          reject(new Error('Failed to load GIF'));
        };
        
        img.src = url;
      });
    } catch (error) {
      console.error('Error parsing GIF:', error);
      return [];
    } finally {
      setLoading(false);
    }
  }, [crossOrigin, totalFrames, duration]);

  // 加载和解析GIF
  useEffect(() => {
    const loadGif = async () => {
      const frames = await parseGifFrames(gifUrl);
      setGifFrames(frames);
      setGifLoaded(frames.length > 0);
    };
    
    loadGif();
  }, [gifUrl, parseGifFrames]);

  // 在Canvas上绘制当前帧
  const drawCurrentFrame = useCallback(() => {
    if (!canvasRef.current || !gifLoaded || gifFrames.length === 0) return;
    
    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');
    
    if (!ctx) return;
    
    // 获取当前帧数据
    const frameIndex = Math.min(currentFrameIndex, gifFrames.length - 1);
    const currentFrame = gifFrames[frameIndex];
    
    if (currentFrame && currentFrame.imageData) {
      // 清除canvas
      ctx.clearRect(0, 0, canvas.width, canvas.height);
      // 绘制当前帧
      ctx.putImageData(currentFrame.imageData, 0, 0);
    }
  }, [gifLoaded, gifFrames, currentFrameIndex]);

  // 每帧更新绘制
  useEffect(() => {
    drawCurrentFrame();
  }, [drawCurrentFrame]);

  // 设置canvas尺寸
  useEffect(() => {
    if (!canvasRef.current || gifFrames.length === 0) return;
    
    const canvas = canvasRef.current;
    const firstFrame = gifFrames[0];
    
    if (firstFrame && firstFrame.imageData) {
      canvas.width = firstFrame.imageData.width;
      canvas.height = firstFrame.imageData.height;
    }
  }, [gifFrames]);

  return (
    <>
      {/* Canvas用于显示控制的gif帧 */}
      <canvas
        ref={canvasRef}
        style={{
          width: '100%',
          height: '100%',
          opacity: loading ? 0.5 : 1,
          ...(style || {})
        }}
      />
      
      {/* 加载状态 */}
      {loading && (
        <div style={{
          position: 'absolute',
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
          background: 'rgba(0,0,0,0.7)',
          color: 'white',
          padding: '10px',
          borderRadius: '5px',
          fontSize: '12px'
        }}>
          Loading GIF...
        </div>
      )}
      
      {/* 音频（如果提供了音频URL并且启用了音频） */}
      {audioUrl && enableAudio && (
        <Audio 
          src={audioUrl}
          volume={audioVolume}
        />
      )}
    </>
  );
}; 