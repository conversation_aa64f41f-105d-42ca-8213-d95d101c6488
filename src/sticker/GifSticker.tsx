import React, { useMemo } from "react";
import { useCurrentFrame, useVideoConfig, Img, Audio } from "remotion";
import { GifStickerProps } from "./type";

export const GifSticker = ({
  gifUrl = 'https://img.qiaoxuesi.com/upfiles/420_wao.gif',
  totalFrames = 24,
  duration = 1,
  style,
  audioUrl,
  audioVolume = 0.5,
  crossOrigin = "anonymous",
  enableAudio = true,
}: GifStickerProps) => {
  const frame = useCurrentFrame();
  const { fps } = useVideoConfig();
  
  // 计算当前应该显示的gif帧索引
  const currentFrameIndex = useMemo(() => {
    // 计算在duration秒内播完totalFrames的播放速度
    const framesPerSecond = totalFrames / duration;
    // 根据当前帧和fps计算经过的时间
    const elapsedTime = frame / fps;
    // 计算当前应该播放的序列帧索引
    const sequenceFrame = Math.floor(elapsedTime * framesPerSecond) % totalFrames;
    
    return sequenceFrame;
  }, [frame, fps, totalFrames, duration]);

  // 生成带时间戳的gif URL来"刷新"gif播放状态
  // 这是一个技巧：通过改变URL来重置gif的播放位置
  const currentGifUrl = useMemo(() => {
    // 每个循环周期重新加载gif
    const cycleIndex = Math.floor(frame / (fps * duration));
    return `${gifUrl}?t=${cycleIndex}&f=${currentFrameIndex}`;
  }, [gifUrl, frame, fps, duration, currentFrameIndex]);

  return (
    <>
      {/* gif图片 - 通过URL变化来控制播放 */}
      <Img 
        src={currentGifUrl}
        style={{
          width: '100%',
          height: '100%',
          ...(style || {})
        }}
        crossOrigin={crossOrigin}
      />
      
      {/* 音频（如果提供了音频URL并且启用了音频） */}
      {audioUrl && enableAudio && (
        <Audio 
          src={audioUrl}
          volume={audioVolume}
        />
      )}
    </>
  );
}; 