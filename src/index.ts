import * as AdvancedText from './text/flowers/advanced-text';
// 装饰性文本
import * as DecorativeText from './text/flowers/decorative-text';
// 花朵组件
import * as FlowersSvg from './text/flowers/svg';
import * as FlowersCss from './text/flowers/css';

// 特效
import * as TextLoopEffects from './text/effects/textLoopEffectsNew';
import * as TextInEffects from './text/effects/textInEffectsNew';
import * as TextInEffects2 from './text/effects/textInEffects2';
import * as TextOutEffects from './text/effects/textOutEffectsNew';
// 图片组件
import * as Images from './image';
// 视频组件
import * as Videos from './video';
// 个人卡片组件
import * as PersonCards from './person-card';
// 视频特效
import * as VideoEffects from './global-effects';
// 入场场景特效
import * as SceneEffects from './sence-effects/in';
// 出场场景特效
import * as SceneEffectsOut from './sence-effects/out';
// 动态文本特效
import * as DynamicSectionTextEffects from './text/dynamic/sections';
// 贴纸
import StickerData from './sticker/data';

// 文本组件
export const FlowerTextComponents = [
  // ...Object.values(FlowersSvg),
  ...Object.values(FlowersCss),
  ...Object.values(FlowersSvg),
  // 高级文本
  // ...Object.values(AdvancedText),
  // // 装饰性文本
  // ...Object.values(DecorativeText),
];

// 文本特效组件
export const TextLoopEffectsComponents = [
  // 基础特效
  ...Object.values(TextLoopEffects),
];
export const TextInEffectsComponents = [
  ...Object.values(TextInEffects),
  ...Object.values(TextInEffects2),
];
export const TextOutEffectsComponents = [
  ...Object.values(TextOutEffects),
];

// 动态文本特效组件
export const DynamicSectionTextEffectsComponents = [
  ...Object.values(DynamicSectionTextEffects),
];

// 贴纸组件
// export const StickerComponents = [
//   ...Object.values(Stickers),
// ];

// 场景特效组件
export const SceneEffectsComponents = [
  ...Object.values(SceneEffects),
];

export const SceneEffectsOutComponents = [
  ...Object.values(SceneEffectsOut),
];
// 未来扩展使用的组件
export const ImageComponents = [...Object.values(Images)];
export const VideoComponents = [...Object.values(Videos)];
export const PersonCardComponents = [...Object.values(PersonCards)];

export { BaseText } from './text';
export { BaseVideo } from './video/BaseVideo';
export { BaseImage } from './image';
// export { Base as BasePersonCard } from './person-card';
export { BaseSticker } from './sticker/BaseSticker';

export {StickerData}

// 未来扩展使用的特效
export const ImageEffectsComponents = [];
export const VideoEffectsComponents = [...Object.values(VideoEffects)];

console.log({VideoEffectsComponents})