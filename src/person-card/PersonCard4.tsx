import React, { useMemo, useRef, useEffect, useCallback, useState, memo } from 'react';
import { useCurrentScale } from 'remotion';

const defaultStyle = {
  size: {
    width: 0.6,
    height: 0.2
  }
}

// 防抖函数
const debounce = <T extends (...args: any[]) => void>(func: T, wait: number): T => {
  let timeout: NodeJS.Timeout;
  return ((...args: any[]) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  }) as T;
};

const defaultTitleStyle = {
  color: 'orange',
  fontSize: '80px',
  textShadow: '2px 2px 2px black',
  height: '40%',
  fontStyle: 'italic'
}
const defaultSubTitleStyle = {
  color: 'white',
  fontSize: '60px',
  textShadow: '2px 2px 2px black',
  height: '56%'
}

export const PersonCard4: any = memo(({title, titleStyle, style, subTitle, subTitleStyle, width = 1080, height = 1920, debug = false}: {title: React.ReactNode, titleStyle?: React.CSSProperties, style?: React.CSSProperties, subTitle?: React.ReactNode, subTitleStyle: React.CSSProperties, width?: number, height?: number, debug?: boolean}) => {
  const currentScale = useCurrentScale()
  // 基于1080为基准的缩放比例
  const scale = width / 1080;
  
  // 状态管理自适应字体大小
  const [adaptiveTitleFontSize, setAdaptiveTitleFontSize] = useState<number>(80);
  const [adaptiveSubTitleFontSize, setAdaptiveSubTitleFontSize] = useState<number>(60);
  const [isCalculating, setIsCalculating] = useState<boolean>(false);

  const finalTitleStyle = useMemo(() => {
    return {
      ...defaultTitleStyle,
      ...(titleStyle || {}),
      fontSize: `${adaptiveTitleFontSize * scale}px`
    }
  }, [titleStyle, adaptiveTitleFontSize, scale])

  const finalSubTitleStyle = useMemo(() => {
    return {
      ...defaultSubTitleStyle,
      ...(subTitleStyle || {}),
      fontSize: `${adaptiveSubTitleFontSize * scale}px`
    }
  }, [subTitleStyle, adaptiveSubTitleFontSize, scale])

  const middleWidth = scale * 8
  const gapWidth = 20
  const restWidth = defaultStyle.size.width * width - middleWidth - gapWidth * 2;
  const leftWidth = restWidth * 1;
  const rightWidth = restWidth * 1;
  const marginTop = 50 * scale;
  const maxHeight = defaultStyle.size.height * height - marginTop;

  const virtualTitleRef = useRef<HTMLDivElement>(null);
  const virtualSubTitleRef = useRef<HTMLDivElement>(null);

  // 计算文本在指定容器中的最大字体大小
  const calculateMaxFontSize = useCallback((
    element: HTMLDivElement | null,
    content: React.ReactNode,
    containerWidth: number,
    containerHeight: number,
    baseStyle: React.CSSProperties,
    textAlign: 'left' | 'right' = 'left',
    minFontSize: number = 8,
    maxFontSize: number = 200
  ): number => {
    if (!element || !content) {
      if (debug) console.log('calculateMaxFontSize: 元素或内容为空');
      return minFontSize;
    }

    // 确保element在DOM中
    if (!element.offsetParent && element.style.position !== 'absolute') {
      if (debug) console.log('calculateMaxFontSize: 元素不在DOM中');
      return minFontSize;
    }

    try {
      // 二分查找最佳字体大小
      let low = minFontSize;
      let high = maxFontSize;
      let bestSize = minFontSize;
      let attempts = 0;
      const maxAttempts = 25;

      // 使用更精确的容错判断
      const getToleranceForSize = (size: number) => {
        // 基于容器大小的相对容错：容器越大，容错越大
        const relativeTolerance = Math.max(containerWidth, containerHeight) * 0.01; // 1%
        // 但不能太小，最小10px，最大40px
        return Math.max(10 * scale, Math.min(40 * scale, relativeTolerance * scale));
      };

      if (debug) {
        console.log(`开始计算字体大小: 容器尺寸 ${containerWidth}x${containerHeight}, 内容: ${content}`);
      }

      let count = 0
      while (low <= high && attempts < maxAttempts) {
        attempts++;
        const mid = Math.floor((low + high) / 2);
        
        // 设置测试字体大小
        element.style.fontSize = `${mid * scale}px`;
        
        // 强制重新计算布局
        element.offsetHeight;
        
        // 使用更精确的测量方法
        const rect = element.getBoundingClientRect();
        const actualWidth = Math.ceil(rect.width / currentScale);
        const actualHeight = Math.ceil(rect.height / currentScale);

        // 动态计算容错范围
        const widthTolerance = getToleranceForSize(containerWidth);
        const heightTolerance = getToleranceForSize(containerHeight);

        if (debug && attempts <= 3) {
          console.log(`尝试 ${attempts}: 字体大小 ${mid}px, 实际尺寸 ${actualWidth}x${actualHeight}, 容器 ${containerWidth}x${containerHeight}, 容错 ${widthTolerance}x${heightTolerance}`);
        }

        // 核心逻辑：文本必须适合容器，但不能过小
        // 允许超过容器2px的误差（处理渲染精度问题）
        const renderTolerance = 2;
        const fitsWidthUpper = actualWidth <= containerWidth + renderTolerance; // 允许超过2px
        const fitsWidthLower = actualWidth >= containerWidth - widthTolerance; // 不能过小
        const fitsHeightUpper = actualHeight <= containerHeight + renderTolerance; // 允许超过2px
        const fitsHeightLower = actualHeight >= containerHeight - heightTolerance; // 不能过小
        
        const fitsWidth = fitsWidthUpper && fitsWidthLower;
        const fitsHeight = fitsHeightUpper && fitsHeightLower;
        const fits = fitsWidth && fitsHeight;

        if (fits) {
          // 在理想范围内，继续寻找更大的字体
          bestSize = mid;
          low = mid + 1;
        } else if (actualWidth > containerWidth + renderTolerance || actualHeight > containerHeight + renderTolerance) {
          // 明显超过容器+误差，字体太大了，往小的方向找
          high = mid - 1;
        } else {
          // 文本在容器内但不在理想范围，判断是太大还是太小
          const tooSmallWidth = actualWidth < containerWidth - widthTolerance;
          const tooSmallHeight = actualHeight < containerHeight - heightTolerance;
          const tooSmall = tooSmallWidth || tooSmallHeight;
          
          if (tooSmall) {
            // 文本太小，往大的方向找
            if (mid > bestSize) {
              bestSize = mid; // 记录当前作为候选
            }
            low = mid + 1;
          } else {
            // 文本不算太小，但也不在理想范围，往小的方向找
            if (mid > bestSize) {
              bestSize = mid; // 记录当前作为候选
            }
            high = mid - 1;
          }
        }
        count++
      }

      // 最终验证：确保字体大小适合容器（允许2px误差）
      if (bestSize > minFontSize) {
        element.style.fontSize = `${bestSize * scale}px`;
        element.offsetHeight;
        
        const finalRect = element.getBoundingClientRect();
        const finalWidth = Math.ceil(finalRect.width / currentScale);
        const finalHeight = Math.ceil(finalRect.height / currentScale);

        const renderTolerance = 2;
        // 最终检查：不能明显超过容器+误差
        if (finalWidth > containerWidth + renderTolerance || finalHeight > containerHeight + renderTolerance) {
          // 明显超过了，需要调整
          bestSize = Math.max(minFontSize, bestSize - 1);
          if (debug) console.log(`最终验证: 尺寸超出 ${finalWidth}x${finalHeight} > ${containerWidth + renderTolerance}x${containerHeight + renderTolerance}, 调整字体大小至: ${bestSize}`);
        }
      }

      if (debug) {
        console.log(`计算完成: 最终字体大小 ${bestSize}px, 尝试次数 ${attempts}`);
      }
      console.log({count, bestSize})

      return bestSize;
    } catch (error) {
      if (debug) console.error('字体大小计算错误:', error);
      return minFontSize;
    }
  }, [scale, debug, currentScale]);

  // 防抖的重新计算函数
  const debouncedRecalculate = useCallback(
    debounce(() => {
      setIsCalculating(true);
      
      // 使用requestAnimationFrame确保DOM更新完成
      requestAnimationFrame(() => {
        try {
          if (virtualTitleRef.current && title) {
            const mergedTitleStyle = {
              ...defaultTitleStyle,
              ...(titleStyle || {}),
            }
            const maxSize = calculateMaxFontSize(
              virtualTitleRef.current,
              title,
              leftWidth,
              maxHeight,
              mergedTitleStyle,
              'right'
            );
            setAdaptiveTitleFontSize(Math.min(maxSize, parseFloat((mergedTitleStyle.fontSize) as string)));
          }

          if (virtualSubTitleRef.current && subTitle) {
            const mergedSubTitleStyle = {
              ...defaultSubTitleStyle,
              ...(subTitleStyle || {}),
            }
            const maxSize = calculateMaxFontSize(
              virtualSubTitleRef.current,
              subTitle,
              rightWidth,
              maxHeight,
              mergedSubTitleStyle,
              'left'
            );
            // console.log({maxSize, subTitle})
            setAdaptiveSubTitleFontSize(Math.max(maxSize, parseFloat((mergedSubTitleStyle.fontSize) as string)));
          }
        } finally {
          setIsCalculating(false);
        }
      });
    }, 100),
    [title, subTitle, titleStyle, subTitleStyle, leftWidth, rightWidth, maxHeight, calculateMaxFontSize, defaultTitleStyle, defaultSubTitleStyle]
  );

  // 监听内容变化并重新计算字体大小
  useEffect(() => {
    debouncedRecalculate();
  }, [debouncedRecalculate]);

  return (
      <div className='person-card-2' style={{
        display: 'flex',
        flexDirection: 'column',
        flexWrap: 'nowrap',
        width: `${defaultStyle.size.width * width}px`,
        height: `${defaultStyle.size.height * height}px`,
        paddingLeft: gapWidth * scale,
        gap: `${gapWidth * scale}px`,
        ...(style || {})
      }}>
        <div className='title' style={{
          flex: 'none',
          overflow: 'hidden',
          lineHeight: '1.2',
          wordWrap: 'break-word',
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'flex-end',
          width: `300px`,
          border: '3px solid red',
          fontFamily: 'FZZJ-PANYBJW',
          ...finalTitleStyle
        }}>
          {title}
        </div>

        <div ref={virtualTitleRef} style={{
          textAlign: 'right', 
          flex: 'none',
          width: `${leftWidth}px`,
          marginBottom: `${marginTop}px`,
          overflow: 'hidden',
          position: 'absolute',
          top: '-9999px',
          left: '-9999px',
          visibility: 'hidden',
          zIndex: -1000,
          lineHeight: '1.2',
          wordWrap: 'break-word',
          ...finalTitleStyle
        }}>
          {title}
        </div>


        <div className='sub-title' style={{
          flex: 'none',
          overflow: 'hidden',
          lineHeight: '1.2',
          wordWrap: 'break-word',
          border: '3px solid red',
          position: 'relative',
          paddingLeft: '30px',
          ...finalSubTitleStyle
        }}>
          <div style={{
            position: 'relative'
          }}>
            <span style={{
              position: 'absolute',
              left: '-25px',
              top: '50%',
              transform: 'translateY(-50%)',
              width: '10px',
              height: '10px',
              backgroundColor: finalSubTitleStyle.color,
              borderRadius: '50%',
              display: 'inline-block'
            }}></span>
           {subTitle}
          </div>
        </div>

        <div className='virtual-sub-title' ref={virtualSubTitleRef} style={{
          textAlign: 'left',
          flex: 'none',
          width: `${rightWidth}px`,
          marginTop: `${marginTop}px`,
          overflow: 'hidden',
          position: 'absolute',
          top: '-9999px',
          left: '-9999px',
          visibility: 'hidden',
          zIndex: -1000,
          lineHeight: '1.2',
          wordWrap: 'break-word',
          ...finalSubTitleStyle
        }}>
          {subTitle}
        </div>
      </div>
  );
})

PersonCard4.key = 'PersonCard4';
PersonCard4.displayName = '人物名片';
PersonCard4.description = '人物名片';
PersonCard4.style = defaultStyle;