import React, { useMemo, useRef, useEffect, useCallback, useState, memo } from 'react';
import { useCurrentScale } from 'remotion';
import { calculateMaxFontSize } from './util';

const defaultStyle = {
  size: {
    width: 0.6,
    height: 0.2
  }
}

// 防抖函数
const debounce = <T extends (...args: any[]) => void>(func: T, wait: number): T => {
  let timeout: NodeJS.Timeout;
  return ((...args: any[]) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  }) as T;
};

const defaultTitleStyle = {
  color: 'orange',
  fontSize: '80px',
  textShadow: '2px 2px 2px black',
  fontStyle: 'italic'
}
const defaultSubTitleStyle = {
  color: 'white',
  fontSize: '60px',
  textShadow: '2px 2px 2px black',
  height: '56%'
}

export const PersonCard5: any = memo(({title, titleStyle, style, subTitle, subTitleStyle, width = 1080, height = 1920, debug = false}: {title: React.ReactNode, titleStyle?: React.CSSProperties, style?: React.CSSProperties, subTitle?: React.ReactNode, subTitleStyle: React.CSSProperties, width?: number, height?: number, debug?: boolean}) => {
  const currentScale = useCurrentScale()
  // 基于1080为基准的缩放比例
  const scale = width / 1080;
  
  // 状态管理自适应字体大小
  const [adaptiveTitleFontSize, setAdaptiveTitleFontSize] = useState<number>(80);
  const [adaptiveSubTitleFontSize, setAdaptiveSubTitleFontSize] = useState<number>(60);
  const [isCalculating, setIsCalculating] = useState<boolean>(false);

  const finalTitleStyle = useMemo(() => {
    return {
      ...defaultTitleStyle,
      ...(titleStyle || {}),
      fontSize: `${adaptiveTitleFontSize * scale}px`
    }
  }, [titleStyle, adaptiveTitleFontSize, scale])

  const finalSubTitleStyle = useMemo(() => {
    return {
      ...defaultSubTitleStyle,
      ...(subTitleStyle || {}),
      fontSize: `${adaptiveSubTitleFontSize * scale}px`
    }
  }, [subTitleStyle, adaptiveSubTitleFontSize, scale])

  const middleWidth = scale * 8
  const gapWidth = 20
  const restWidth = defaultStyle.size.width * width - middleWidth - gapWidth * 2;
  const leftWidth = restWidth * 1;
  const rightWidth = restWidth * 1;
  const marginTop = 50 * scale;
  const maxHeight = defaultStyle.size.height * height - marginTop;

  const virtualTitleRef = useRef<HTMLDivElement>(null);
  const virtualSubTitleRef = useRef<HTMLDivElement>(null);


  // 防抖的重新计算函数
  const debouncedRecalculate = useCallback(
    debounce(() => {
      setIsCalculating(true);
      
      // 使用requestAnimationFrame确保DOM更新完成
      requestAnimationFrame(() => {
        try {
          if (virtualTitleRef.current && title) {
            const mergedTitleStyle = {
              ...defaultTitleStyle,
              ...(titleStyle || {}),
            }
            const maxSize = calculateMaxFontSize(
              scale,
              debug,
              currentScale,
              virtualTitleRef.current,
              title,
              leftWidth,
              maxHeight,
              mergedTitleStyle,
              'right'
            );
            if (maxSize > parseFloat((mergedTitleStyle.fontSize) as string)) {
              setAdaptiveTitleFontSize(parseFloat((mergedTitleStyle.fontSize) as string))
            } else {
              setAdaptiveTitleFontSize(maxSize)
            }
          }

          if (virtualSubTitleRef.current && subTitle) {
            const mergedSubTitleStyle = {
              ...defaultSubTitleStyle,
              ...(subTitleStyle || {}),
            }
            const maxSize = calculateMaxFontSize(
              scale,
              debug,
              currentScale,
              virtualSubTitleRef.current,
              subTitle,
              rightWidth,
              maxHeight,
              mergedSubTitleStyle,
              'left'
            );
            // console.log({maxSize, subTitle, finalSubTitleStyle})
            if (maxSize > parseFloat((mergedSubTitleStyle.fontSize) as string)) {
              setAdaptiveSubTitleFontSize(parseFloat((mergedSubTitleStyle.fontSize) as string))
            } else {
              setAdaptiveSubTitleFontSize(maxSize)
            }
          }
        } finally {
          setIsCalculating(false);
        }
      });
    }, 100),
    [title, subTitle, titleStyle, subTitleStyle, leftWidth, rightWidth, maxHeight, scale, debug, currentScale, defaultTitleStyle, defaultSubTitleStyle]
  );

  // 监听内容变化并重新计算字体大小
  useEffect(() => {
    debouncedRecalculate();
  }, [debouncedRecalculate]);

  return (
      <div className='person-card-5' style={{
        display: 'flex',
        flexDirection: 'column',
        flexWrap: 'nowrap',
        width: `${defaultStyle.size.width * width}px`,
        height: `${defaultStyle.size.height * height}px`,
        paddingLeft: gapWidth * scale,
        gap: `${gapWidth * scale}px`,
        ...(style || {})
      }}>
        <div className='title' style={{
          flex: 'none',
          overflow: 'hidden',
          lineHeight: '1.2',
          wordWrap: 'break-word',
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'flex-end',
          width: `300px`,
          fontFamily: 'initial',
          backgroundColor: 'black',
          // color: 'white',
          ...finalTitleStyle
        }}>
          {title}
        </div>

        <div ref={virtualTitleRef} style={{
          textAlign: 'right', 
          flex: 'none',
          width: `${leftWidth}px`,
          marginBottom: `${marginTop}px`,
          overflow: 'hidden',
          position: 'absolute',
          top: '-9999px',
          left: '-9999px',
          visibility: 'hidden',
          zIndex: -1000,
          lineHeight: '1.2',
          wordWrap: 'break-word',
          ...finalTitleStyle
        }}>
          {title}
        </div>


        <div className='sub-title' style={{
          flex: 'none',
          overflow: 'hidden',
          lineHeight: '1.2',
          wordWrap: 'break-word',
          border: '3px solid red',
          position: 'relative',
          paddingLeft: '30px',
          backgroundColor: 'gray',
          // color: 'black',
          ...finalSubTitleStyle
        }}>
          <div style={{
            position: 'relative'
          }}>
            <span style={{
              position: 'absolute',
              left: '-25px',
              top: '50%',
              transform: 'translateY(-50%)',
              width: '10px',
              height: '10px',
              backgroundColor: finalSubTitleStyle.color,
              borderRadius: '50%',
              display: 'inline-block'
            }}></span>
           {subTitle}
          </div>
        </div>

        <div className='virtual-sub-title' ref={virtualSubTitleRef} style={{
          textAlign: 'left',
          flex: 'none',
          width: `${rightWidth}px`,
          marginTop: `${marginTop}px`,
          overflow: 'hidden',
          position: 'absolute',
          top: '-9999px',
          left: '-9999px',
          visibility: 'hidden',
          zIndex: -1000,
          lineHeight: '1.2',
          wordWrap: 'break-word',
          ...finalSubTitleStyle
        }}>
          {subTitle}
        </div>
      </div>
  );
})

PersonCard5.key = 'PersonCard5';
PersonCard5.displayName = '人物名片';
PersonCard5.description = '人物名片';
PersonCard5.style = defaultStyle;