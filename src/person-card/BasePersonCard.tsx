import React, { useMemo } from 'react';

export const BasePersonCard = ({title, titleStyle, style, subTitle, subTitleStyle}: {title: React.ReactNode, titleStyle?: React.CSSProperties, style?: React.CSSProperties, subTitle?: React.ReactNode, subTitleStyle: React.CSSProperties}) => {
  const defaultTitleStyle = {
    color: '#ffffff',
    fontSize: '80px',
  }
  const defaultSubTitleStyle = {
    color: '#ffffff',
    fontSize: '60px',
  }

  const finalTitleStyle = useMemo(() => {
    return {
      ...defaultTitleStyle,
      ...(titleStyle || {})
    }
  }, [titleStyle])

  const finalSubTitleStyle = useMemo(() => {
    return {
      ...defaultSubTitleStyle,
      ...(subTitleStyle || {})
    }
  }, [subTitleStyle])

  return <div className='base-person-card' style={{...(style || {})}}>
    <div style={{textAlign: 'left', ...finalTitleStyle}}>{title}</div>
    <div style={{textAlign: 'left',...finalSubTitleStyle}}>{subTitle}</div>
  </div>;
}

BasePersonCard.key = 'BasePersonCard';
BasePersonCard.displayName = '基础模板';
BasePersonCard.description = '基础模板';
BasePersonCard.style = {
  size: {
    width: 0.4,
    height: 0.3
  }
}