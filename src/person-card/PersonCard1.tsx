import React, { useMemo, useRef, useEffect, useCallback, useState, memo } from 'react';
import { useCurrentScale } from 'remotion';
import { calculateMaxFontSize } from './util';

const defaultStyle = {
  size: {
    width: 0.6,
    height: 0.2
  }
}

// 防抖函数
const debounce = <T extends (...args: any[]) => void>(func: T, wait: number): T => {
  let timeout: NodeJS.Timeout;
  return ((...args: any[]) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  }) as T;
};

const defaultTitleStyle = {
  color: '#dc143c',
  fontSize: '80px',
}
const defaultSubTitleStyle = {
  color: 'blue',
  fontSize: '60px',
}

export const PersonCard1: any = memo(({title, titleStyle, style, subTitle, subTitleStyle, width = 1080, height = 1920, debug = false}: {title: React.ReactNode, titleStyle?: React.CSSProperties, style?: React.CSSProperties, subTitle?: React.ReactNode, subTitleStyle: React.CSSProperties, width?: number, height?: number, debug?: boolean}) => {
  const currentScale = useCurrentScale()
  // 基于1080为基准的缩放比例
  const scale = width / 1080;
  
  // 状态管理自适应字体大小
  const [adaptiveTitleFontSize, setAdaptiveTitleFontSize] = useState<number>(60);
  const [adaptiveSubTitleFontSize, setAdaptiveSubTitleFontSize] = useState<number>(60);
  const [isCalculating, setIsCalculating] = useState<boolean>(false);

  const finalTitleStyle = useMemo(() => {
    return {
      ...defaultTitleStyle,
      ...(titleStyle || {}),
      fontSize: `${adaptiveTitleFontSize * scale}px`
    }
  }, [titleStyle, adaptiveTitleFontSize, scale])

  const finalSubTitleStyle = useMemo(() => {
    return {
      ...defaultSubTitleStyle,
      ...(subTitleStyle || {}),
      fontSize: `${adaptiveSubTitleFontSize * scale}px`
    }
  }, [subTitleStyle, adaptiveSubTitleFontSize, scale])

  const middleWidth = scale * 8
  const gapWidth = 20
  const restWidth = defaultStyle.size.width * width - middleWidth - gapWidth * 2;
  const leftWidth = restWidth * 0.4;
  const rightWidth = restWidth * 0.6;
  const marginTop = 50 * scale;
  const maxHeight = defaultStyle.size.height * height - marginTop;

  const virtualTitleRef = useRef<HTMLDivElement>(null);
  const virtualSubTitleRef = useRef<HTMLDivElement>(null);

  // 防抖的重新计算函数
  const debouncedRecalculate = useCallback(
    debounce(() => {
      setIsCalculating(true);
      
      // 使用requestAnimationFrame确保DOM更新完成
      requestAnimationFrame(() => {
        try {
          if (virtualTitleRef.current && title) {
            const mergedTitleStyle = {
              ...defaultTitleStyle,
              ...(titleStyle || {}),
            }
            const maxSize = calculateMaxFontSize(
              scale,
              debug,
              currentScale,
              virtualTitleRef.current,
              title,
              leftWidth,
              maxHeight,
              mergedTitleStyle,
              'right'
            );
            if (maxSize > parseFloat((mergedTitleStyle.fontSize) as string)) {
              setAdaptiveTitleFontSize(parseFloat((mergedTitleStyle.fontSize) as string))
            } else {
              setAdaptiveTitleFontSize(maxSize)
            }
          }

          if (virtualSubTitleRef.current && subTitle) {
            const mergedSubTitleStyle = {
              ...defaultSubTitleStyle,
              ...(subTitleStyle || {}),
            }
            const maxSize = calculateMaxFontSize(
              scale,
              debug,
              currentScale,
              virtualSubTitleRef.current,
              subTitle,
              rightWidth,
              maxHeight,
              mergedSubTitleStyle,
              'left'
            );
            // console.log({maxSize, subTitle, finalSubTitleStyle})
            if (maxSize > parseFloat((mergedSubTitleStyle.fontSize) as string)) {
              setAdaptiveSubTitleFontSize(parseFloat((mergedSubTitleStyle.fontSize) as string))
            } else {
              setAdaptiveSubTitleFontSize(maxSize)
            }
          }
        } finally {
          setIsCalculating(false);
        }
      });
    }, 100),
    [title, subTitle, titleStyle, subTitleStyle, leftWidth, rightWidth, maxHeight, scale, debug, currentScale, defaultTitleStyle, defaultSubTitleStyle]
  );

  // 监听内容变化并重新计算字体大小
  useEffect(() => {
    debouncedRecalculate();
  }, [debouncedRecalculate]);

// console.log({titleStyle, subTitleStyle, finalTitleStyle, finalSubTitleStyle})
  return (
    <>
      {debug && (
        <div style={{
          position: 'absolute',
          top: '10px',
          left: '10px',
          background: 'rgba(0,0,0,0.8)',
          color: 'white',
          padding: '8px',
          fontSize: '12px',
          zIndex: 1000
        }}>
          <div>标题字体: {adaptiveTitleFontSize}px (缩放后: {adaptiveTitleFontSize * scale}px)</div>
          <div>子标题字体: {adaptiveSubTitleFontSize}px (缩放后: {adaptiveSubTitleFontSize * scale}px)</div>
          <div>容器: {leftWidth.toFixed(1)}x{maxHeight.toFixed(1)} | {rightWidth.toFixed(1)}x{maxHeight.toFixed(1)}</div>
          <div>缩放比例: {scale.toFixed(3)}</div>
          {isCalculating && <div style={{color: 'yellow'}}>计算中...</div>}
        </div>
      )}
      
      <div className='person-card-1' style={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        flexWrap: 'nowrap',
        gap: `${Math.round(8 * scale)}px`,
        width: `${defaultStyle.size.width * width}px`,
        height: `${defaultStyle.size.height * height}px`,
        ...(style || {})
      }}>
        <div className='title' style={{
          textAlign: 'right', 
          flex: 'none',
          width: `${leftWidth}px`,
          marginBottom: `${marginTop}px`,
          overflow: 'hidden',
          lineHeight: '1.2',
          wordWrap: 'break-word',
          ...finalTitleStyle
        }}>
          {title}
        </div>

        <div ref={virtualTitleRef} style={{
          textAlign: 'right', 
          flex: 'none',
          width: `${leftWidth}px`,
          marginBottom: `${marginTop}px`,
          overflow: 'hidden',
          position: 'absolute',
          top: '-9999px',
          left: '-9999px',
          visibility: 'hidden',
          zIndex: -1000,
          lineHeight: '1.2',
          wordWrap: 'break-word',
          ...finalTitleStyle
        }}>
          {title}
        </div>

        <div className='line' style={{
          width: `${middleWidth}px`,
          height: `${Math.round(200 * scale)}px`,
          backgroundColor: finalTitleStyle.color,
          transform: 'rotate(15deg)',
          flex: 'none',
          margin: `0 ${Math.round(gapWidth * scale)}px`
        }}>
        </div>

        <div className='sub-title' style={{
          textAlign: 'left',
          flex: 'none',
          width: `${rightWidth}px`,
          marginTop: `${marginTop}px`,
          overflow: 'hidden',
          lineHeight: '1.2',
          wordWrap: 'break-word',
          ...finalSubTitleStyle
        }}>
          {subTitle}
        </div>

        <div className='virtual-sub-title' ref={virtualSubTitleRef} style={{
          textAlign: 'left',
          flex: 'none',
          width: `${rightWidth}px`,
          marginTop: `${marginTop}px`,
          overflow: 'hidden',
          position: 'absolute',
          top: '200%',
          left: '0',
          // visibility: 'hidden',
          // zIndex: -1000,
          lineHeight: '1.2',
          wordWrap: 'break-word',
          ...finalSubTitleStyle
        }}>
          {subTitle}
        </div>
      </div>
    </>
  );
})

PersonCard1.key = 'PersonCard1';
PersonCard1.displayName = '人物名片';
PersonCard1.description = '人物名片';
PersonCard1.style = defaultStyle;