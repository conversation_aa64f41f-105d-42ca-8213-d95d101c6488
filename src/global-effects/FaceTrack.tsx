import React from 'react';
import { Sequence, AbsoluteFill, interpolate, useCurrentFrame, useVideoConfig } from 'remotion';
import { needSegmentationsData } from 'remotion-wanqiang/utils/segmentationSampling';

// 新的人脸数据格式接口
export interface FaceData {
  _x: number;      // 中心点X坐标
  _y: number;      // 中心点Y坐标
  _width: number;  // 人脸宽度（可选，用于调试显示）
  _height: number; // 人脸高度（可选，用于调试显示）
}

export interface FaceTrackProps {
  /** 人脸坐标数据（从外部传入）- _x和_y就是中心点坐标 */
  faceCoordinates: FaceData | null;
  /** 开始显示时间（秒），默认0 */
  startTime?: number;
  /** 显示时长（秒），默认2 */
  duration?: number;
  /** 当前视频帧（用于合成） */
  currentVideoFrame?: CanvasImageSource;
  /** 动画持续帧数，默认100（向后兼容） */
  durationInFrames?: number;
  /** 遮罩背景色，默认黑色 */
  backgroundColor?: string;
  /** 基础半径大小，默认400px */
  radius?: number;
  /** 圆形遮罩的最小半径，默认会基于radius计算 */
  minRadius?: number;
  /** 圆形遮罩的最大半径，默认会基于radius计算 */
  maxRadius?: number;
  /** 人脸区域Y轴偏移量，默认0（因为已经是圆心了） */
  faceOffsetY?: number;
  /** 默认圆心位置（当没有人脸数据时） */
  fallbackCenter?: { x: string; y: string };
  /** 子组件 */
  children?: React.ReactNode;
}

export const FaceTrack = ({ 
  faceCoordinates,
  startTime = 0,
  duration = 2,
  currentVideoFrame,
  durationInFrames = 100,
  backgroundColor = 'rgba(0, 0, 0, 1)',
  radius = 400,
  minRadius,
  maxRadius,
  faceOffsetY = 0, // 默认0，因为传入的已经是圆心坐标
  fallbackCenter = { x: '50%', y: '30%' },
  children
}: FaceTrackProps) => {
  const { fps, width, height } = useVideoConfig();
  const frame = useCurrentFrame();
  
  // 🎯 计算当前时间和是否应该显示特效
  const currentTime = frame / fps;
  const shouldShowEffect = currentTime >= startTime && currentTime <= (startTime + duration);
  const effectProgress = shouldShowEffect ? 
    Math.min(1, (currentTime - startTime) / duration) : 0;

  // 计算实际的动画关键帧（基于特效进度）
  const startFrame = Math.round(startTime * fps);
  const effectDurationInFrames = Math.round(duration * fps);
  
  // 动画关键帧（相对于特效开始时间）
  const animationKeyframes = [
    startFrame,
    startFrame + Math.round(effectDurationInFrames * 0.1),
    startFrame + Math.round(effectDurationInFrames * 0.85),
    startFrame + effectDurationInFrames
  ];

  // 计算动态半径，基于传入的基础半径
  const actualMinRadius = minRadius || radius * 0.65; // 默认为基础半径的65%
  const actualMaxRadius = maxRadius || radius * 1.5;  // 默认为基础半径的150%

  // 计算当前半径
  const currentRadius = interpolate(
    frame,
    animationKeyframes,
    [actualMaxRadius, actualMinRadius, actualMinRadius, actualMaxRadius]
  );

  console.log('================> 原始人脸数据:', faceCoordinates);
  console.log('当前半径:', currentRadius);
  
  // 直接使用传入的_x和_y作为中心点坐标 - 当_x和_y变化时，这里会自动跟随移动
  const centerX = faceCoordinates ? faceCoordinates._x : width / 2;
  const centerY = faceCoordinates ? faceCoordinates._y + faceOffsetY : height * 0.3;

  console.log('最终圆心位置:', { centerX, centerY });

  // 如果不在特效时间范围内，只显示子组件和视频帧（如果有）
  if (!shouldShowEffect) {
    return (
      <AbsoluteFill style={{ backgroundColor: "transparent" }}>
        {children}
        {currentVideoFrame && (
          <div
            style={{
              position: "absolute",
              top: 0,
              left: 0,
              width: "100%",
              height: "100%",
              backgroundImage: `url(${currentVideoFrame})`,
              backgroundSize: "cover",
              backgroundPosition: "center",
            }}
          />
        )}
      </AbsoluteFill>
    );
  }

  // 计算特效透明度
  const effectOpacity = Math.sin(effectProgress * Math.PI);

  // 计算巨大的box-shadow尺寸，确保覆盖整个屏幕
  const shadowSize = Math.max(width, height) * 3;

  return (
    <AbsoluteFill style={{ backgroundColor: "transparent" }}>
      {children}
      
      {/* 视频背景（如果有） */}
      {currentVideoFrame && (
        <div
          style={{
            position: "absolute",
            top: 0,
            left: 0,
            width: "100%",
            height: "100%",
            backgroundImage: `url(${currentVideoFrame})`,
            backgroundSize: "cover",
            backgroundPosition: "center",
            zIndex: -1,
          }}
        />
      )}
      
      {/* 透明圆形区域 - 根据人脸中心点动态定位 */}
      <div
        style={{
          position: "absolute",
          width: `${currentRadius * 2}px`,
          height: `${currentRadius * 2}px`,
          borderRadius: "50%",
          backgroundColor: "transparent",
          // 使用box-shadow创建周围的黑色遮罩
          boxShadow: `0 0 0 ${shadowSize}px ${backgroundColor}`,
          // 直接使用_x和_y作为中心点位置 - 当_x和_y变化时自动跟随
          transform: `translate(${centerX}px, ${centerY}px)`,
          opacity: effectOpacity,
          zIndex: 1000,
          // 添加过渡动画，让位置变化更平滑
          transition: shouldShowEffect ? 'transform 0.1s ease-out' : 'none',
        }}
      />
    </AbsoluteFill>
  );
};

FaceTrack.key = 'FaceTrack';
FaceTrack.showName = '人脸跟踪';
FaceTrack.description = '人脸跟踪特效';
FaceTrack.ranges = ['main-track']; // 作用范围
FaceTrack.needSegmentationsData = needSegmentationsData;