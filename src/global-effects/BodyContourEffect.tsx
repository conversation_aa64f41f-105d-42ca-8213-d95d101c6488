import React, { useCallback, useRef, useEffect, useState } from "react";
import { useCurrentFrame, useVideoConfig } from "remotion";
import { needSegmentationsData } from "../utils/segmentationSampling";

// 🎯 人体轮廓特效组件配置接口（使用外部数据）
export interface BodyContourEffectProps {
  children?: React.ReactNode;
  /** 人体分割数据（从AI模型获取） */
  segmentations: any[] | null;
  /** 当前视频帧（用于合成） */
  currentVideoFrame?: CanvasImageSource;
  /** 开始显示时间（秒），默认0 */
  startTime?: number;
  /** 显示时长（秒），默认2 */
  duration?: number;
  /** 轮廓颜色，默认红色 */
  contourColor?: string;
  /** 轮廓粗细，默认6 */
  contourThickness?: number;
  /** 放大倍数，默认1.2 */
  scaleFactor?: number;
  /** 是否显示调试信息，默认false */
  showDebugInfo?: boolean;
  /** 是否显示装饰贴片，默认true */
  showDecorations?: boolean;
  /** 装饰贴片数量，默认8 */
  decorationCount?: number;
  /** 装饰贴片大小，默认30 */
  decorationSize?: number;
  /** 插值质量 (1-5)，默认3 */
  interpolationQuality?: number;
  /** 关键帧缓存时间窗口(秒)，默认10 */
  keyframeWindowSize?: number;
  /** 是否显示插值调试信息，默认false */
  showInterpolationDebug?: boolean;
  /** 动画显示帧率，默认为视频帧率 */
  animationFps?: number;
}

// 关键帧数据结构
interface KeyframeData {
  timestamp: number; // 时间戳（秒）
  frame: number;     // 帧号
  contourPoints: {x: number, y: number}[]; // 轮廓点数据
  boundingBox: {
    minX: number, maxX: number, 
    minY: number, maxY: number,
    width: number, height: number
  };
  centerPoint: {x: number, y: number}; // 中心点
}

export const BodyContourEffect = ({
  children,
  segmentations,
  currentVideoFrame,
  startTime = 0,
  duration = 2,
  contourColor = '#FF6B6B',
  contourThickness = 6,
  scaleFactor = 1.2,
  showDebugInfo = false,
  showDecorations = true,
  decorationCount = 8,
  decorationSize = 30,
  interpolationQuality = 3,
  keyframeWindowSize = 10,
  showInterpolationDebug = false,
  animationFps
}: BodyContourEffectProps) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  // 移除离屏缓冲区，直接在主canvas上绘制，通过透明度控制避免闪烁
  
  const { width, height, fps } = useVideoConfig();
  const frame = useCurrentFrame();
  
  // 🎯 动画帧率控制 - 如果未指定则使用视频帧率
  const effectiveAnimationFps = animationFps || fps;
  
  // 🎯 计算动画帧号 - 用于控制动画更新频率
  const animationFrame = Math.floor(frame * effectiveAnimationFps / fps);
  const prevAnimationFrameRef = useRef<number>(-1);
  
  // 判断是否需要更新动画（只在动画帧变化时更新）
  const shouldUpdateAnimation = animationFrame !== prevAnimationFrameRef.current;
  
  // 更新动画帧记录
  useEffect(() => {
    prevAnimationFrameRef.current = animationFrame;
  }, [animationFrame]);
  
  // 关键帧插值系统状态
  const [keyframes, setKeyframes] = useState<KeyframeData[]>([]);
  const [lastInterpolatedFrame, setLastInterpolatedFrame] = useState<number>(-1);
  const interpolatedPointsRef = useRef<{x: number, y: number}[]>([]);
  
  // 🎯 新增：Canvas显示状态控制
  const [canvasReady, setCanvasReady] = useState<boolean>(false);
  const canvasReadyRef = useRef<boolean>(false);
  
  // 上一帧是否成功渲染的标记
  const lastFrameRenderedRef = useRef<boolean>(false);
  
  // 🎯 计算当前时间和是否应该显示特效
  const currentTime = frame / fps;
  const shouldShowEffect = currentTime >= startTime && currentTime <= (startTime + duration);
  const effectProgress = shouldShowEffect ? 
    Math.min(1, (currentTime - startTime) / duration) : 0;

  // 🎯 基于动画帧率的特效进度计算
  const animationTime = animationFrame / effectiveAnimationFps;
  const animationEffectProgress = shouldShowEffect ? 
    Math.min(1, (animationTime - startTime) / duration) : 0;

  const hexToRgb = (hex: string) => {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    return result ? {
      r: parseInt(result[1], 16),
      g: parseInt(result[2], 16),
      b: parseInt(result[3], 16)
    } : {r: 255, g: 107, b: 107};
  }

  // 关键帧管理系统
  const manageKeyframes = useCallback((newKeyframe: KeyframeData) => {
    // 只在特效展示时间范围内缓存关键帧
    if (newKeyframe.timestamp < startTime - keyframeWindowSize || 
        newKeyframe.timestamp > startTime + duration + keyframeWindowSize) {
      return;
    }
    
    setKeyframes(prev => {
      // 添加新关键帧
      const updated = [...prev, newKeyframe];
      
      // 移除过期关键帧
      return updated.filter(kf => 
        Math.abs(kf.timestamp - newKeyframe.timestamp) <= keyframeWindowSize
      ).sort((a, b) => a.timestamp - b.timestamp);
    });
  }, [startTime, duration, keyframeWindowSize]);

  // 从分割数据中提取关键帧数据
  const extractKeyframeData = useCallback((segmentationData: any, frameNumber: number, timeInSec: number): KeyframeData | null => {
    if (!segmentationData || !segmentationData.segmentations || !segmentationData.segmentations[0]) return null;
    
    try {
      // 使用现有的轮廓提取逻辑
      const imageData = segmentationData.segmentations[0]?.mask?.toImageData();
      if (!imageData) return null;
      
      // 提取轮廓点
      const contourPoints = extractContourPointsForScaling(imageData, interpolationQuality);
      if (contourPoints.length < 3) return null;
      
      // 计算原始图像到canvas的缩放系数
      const scaleX = width / imageData.width;
      const scaleY = height / imageData.height;
      
      // 确保轮廓点坐标正确映射到canvas尺寸
      const scaledContourPoints = contourPoints.map(point => ({
        x: point.x * scaleX,
        y: point.y * scaleY
      }));
      
      // 计算包围盒
      const boundingBox = getBoundingBox(scaledContourPoints);
      
      // 计算中心点
      const centerX = scaledContourPoints.reduce((sum, p) => sum + p.x, 0) / scaledContourPoints.length;
      const centerY = scaledContourPoints.reduce((sum, p) => sum + p.y, 0) / scaledContourPoints.length;
      
      return {
        timestamp: timeInSec,
        frame: frameNumber,
        contourPoints: scaledContourPoints,
        boundingBox,
        centerPoint: {x: centerX, y: centerY}
      };
    } catch (error) {
      if (showDebugInfo) console.error('提取关键帧数据失败:', error);
      return null;
    }
  }, [interpolationQuality, showDebugInfo, width, height]);

  // 关键帧插值计算函数
  const interpolateContourPoints = useCallback((
    prevKeyframe: KeyframeData,
    nextKeyframe: KeyframeData,
    currentTime: number
  ): {x: number, y: number}[] => {
    // 计算插值比例 (0到1之间)
    const totalTimeDiff = nextKeyframe.timestamp - prevKeyframe.timestamp;
    if (totalTimeDiff <= 0) return prevKeyframe.contourPoints;
    
    let t = (currentTime - prevKeyframe.timestamp) / totalTimeDiff;
    t = Math.max(0, Math.min(1, t)); // 确保t在0-1范围内
    
    // 使用自适应采样
    // 如果两个关键帧的点数不同，我们需要先对齐
    const prevPoints = prevKeyframe.contourPoints;
    const nextPoints = nextKeyframe.contourPoints;
    
    // 如果点数相差太多，使用重采样
    if (Math.abs(prevPoints.length - nextPoints.length) > prevPoints.length * 0.3) {
      return interpolateResampledContours(prevPoints, nextPoints, t);
    }
    
    // 简单情况：直接插值
    return interpolateAlignedContours(prevPoints, nextPoints, t);
  }, []);

  // 对齐的轮廓点插值
  const interpolateAlignedContours = useCallback((
    prevPoints: {x: number, y: number}[],
    nextPoints: {x: number, y: number}[],
    t: number
  ): {x: number, y: number}[] => {
    // 确保两组点数相同
    const targetLength = Math.min(prevPoints.length, nextPoints.length);
    const sampledPrev = resampleContourPoints(prevPoints, targetLength);
    const sampledNext = resampleContourPoints(nextPoints, targetLength);
    
    // 点对点插值
    return sampledPrev.map((prev, i) => ({
      x: prev.x + (sampledNext[i].x - prev.x) * t,
      y: prev.y + (sampledNext[i].y - prev.y) * t
    }));
  }, []);

  // 重采样轮廓点（等距采样）
  const resampleContourPoints = useCallback((
    points: {x: number, y: number}[],
    targetCount: number
  ): {x: number, y: number}[] => {
    if (points.length <= 3) return points;
    if (points.length === targetCount) return points;
    
    // 计算轮廓总长度
    let totalLength = 0;
    const distances: number[] = [];
    
    for (let i = 0; i < points.length; i++) {
      const p1 = points[i];
      const p2 = points[(i + 1) % points.length];
      const dist = Math.sqrt(Math.pow(p2.x - p1.x, 2) + Math.pow(p2.y - p1.y, 2));
      distances.push(dist);
      totalLength += dist;
    }
    
    // 计算理想采样间距
    const segmentLength = totalLength / targetCount;
    const result: {x: number, y: number}[] = [];
    
    // 第一个点总是保留
    result.push({...points[0]});
    
    let currentDist = 0;
    let currentIndex = 0;
    
    // 等距采样
    for (let i = 1; i < targetCount; i++) {
      const targetDist = i * segmentLength;
      
      // 向前移动直到达到目标距离
      while (currentDist + distances[currentIndex] < targetDist) {
        currentDist += distances[currentIndex];
        currentIndex = (currentIndex + 1) % points.length;
      }
      
      // 计算当前段内的插值位置
      const p1 = points[currentIndex];
      const p2 = points[(currentIndex + 1) % points.length];
      const segmentDist = distances[currentIndex];
      const t = segmentDist > 0 ? (targetDist - currentDist) / segmentDist : 0;
      
      // 线性插值
      result.push({
        x: p1.x + (p2.x - p1.x) * t,
        y: p1.y + (p2.y - p1.y) * t
      });
    }
    
    return result;
  }, []);

  // 处理形状变化较大的情况
  const interpolateResampledContours = useCallback((
    prevPoints: {x: number, y: number}[],
    nextPoints: {x: number, y: number}[],
    t: number
  ): {x: number, y: number}[] => {
    // 目标点数
    const targetCount = Math.floor(prevPoints.length * (1-t) + nextPoints.length * t);
    
    // 重采样两组点
    const sampledPrev = resampleContourPoints(prevPoints, targetCount);
    const sampledNext = resampleContourPoints(nextPoints, targetCount);
    
    // 基于极坐标系插值，处理旋转和形变
    return interpolateWithPolarCoordinates(sampledPrev, sampledNext, t);
  }, [resampleContourPoints]);

  // 使用极坐标系插值，更好地处理旋转
  const interpolateWithPolarCoordinates = useCallback((
    prevPoints: {x: number, y: number}[],
    nextPoints: {x: number, y: number}[],
    t: number
  ): {x: number, y: number}[] => {
    // 如果点数相差太大，先重采样到相同点数
    if (Math.abs(prevPoints.length - nextPoints.length) > prevPoints.length * 0.2) {
      const targetCount = Math.max(Math.min(prevPoints.length, nextPoints.length), 20);
      const sampledPrev = resampleContourPoints(prevPoints, targetCount);
      const sampledNext = resampleContourPoints(nextPoints, targetCount);
      return interpolateWithPolarCoordinates(sampledPrev, sampledNext, t);
    }
    
    // 确保两组点数相同
    const minLength = Math.min(prevPoints.length, nextPoints.length);
    const prevPointsAligned = prevPoints.slice(0, minLength);
    const nextPointsAligned = nextPoints.slice(0, minLength);
    
    // 计算两个轮廓的中心点
    const centerPrev = {
      x: prevPointsAligned.reduce((sum, p) => sum + p.x, 0) / prevPointsAligned.length,
      y: prevPointsAligned.reduce((sum, p) => sum + p.y, 0) / prevPointsAligned.length
    };
    
    const centerNext = {
      x: nextPointsAligned.reduce((sum, p) => sum + p.x, 0) / nextPointsAligned.length,
      y: nextPointsAligned.reduce((sum, p) => sum + p.y, 0) / nextPointsAligned.length
    };
    
    // 中心点插值
    const centerInterp = {
      x: centerPrev.x + (centerNext.x - centerPrev.x) * t,
      y: centerPrev.y + (centerNext.y - centerPrev.y) * t
    };
    
    // 确定统一的参考点顺序 - 按角度排序
    const prevSorted = [...prevPointsAligned].sort((a, b) => {
      const angleA = Math.atan2(a.y - centerPrev.y, a.x - centerPrev.x);
      const angleB = Math.atan2(b.y - centerPrev.y, b.x - centerPrev.x);
      return angleA - angleB;
    });
    
    const nextSorted = [...nextPointsAligned].sort((a, b) => {
      const angleA = Math.atan2(a.y - centerNext.y, a.x - centerNext.x);
      const angleB = Math.atan2(b.y - centerNext.y, b.x - centerNext.x);
      return angleA - angleB;
    });
    
    // 转换为极坐标
    const polarPrev = prevSorted.map(p => ({
      angle: Math.atan2(p.y - centerPrev.y, p.x - centerPrev.x),
      radius: Math.sqrt(Math.pow(p.x - centerPrev.x, 2) + Math.pow(p.y - centerPrev.y, 2))
    }));
    
    const polarNext = nextSorted.map(p => ({
      angle: Math.atan2(p.y - centerNext.y, p.x - centerNext.x),
      radius: Math.sqrt(Math.pow(p.x - centerNext.x, 2) + Math.pow(p.y - centerNext.y, 2))
    }));
    
    // 插值极坐标
    return polarPrev.map((prev, i) => {
      let angleDiff = polarNext[i].angle - prev.angle;
      
      // 处理角度跨越-π和π的情况
      if (angleDiff > Math.PI) angleDiff -= 2 * Math.PI;
      if (angleDiff < -Math.PI) angleDiff += 2 * Math.PI;
      
      const angleInterp = prev.angle + angleDiff * t;
      const radiusInterp = prev.radius + (polarNext[i].radius - prev.radius) * t;
      
      // 转回笛卡尔坐标
      return {
        x: centerInterp.x + Math.cos(angleInterp) * radiusInterp,
        y: centerInterp.y + Math.sin(angleInterp) * radiusInterp
      };
    });
  }, [resampleContourPoints]);

  // 清理过期关键帧的效果
  useEffect(() => {
    const interval = setInterval(() => {
      const currentTime = frame / fps;
      setKeyframes(prev => 
        prev.filter(kf => Math.abs(kf.timestamp - currentTime) <= keyframeWindowSize)
      );
    }, 5000); // 每5秒清理一次
    
    return () => clearInterval(interval);
  }, [frame, fps, keyframeWindowSize]);

  // 直接从现有分割数据中渲染轮廓的辅助函数（用于调试和初始渲染）
  const renderSegmentationDirectly = useCallback(async (
    segmentationData: any, 
    canvas: HTMLCanvasElement,
    context: CanvasRenderingContext2D
  ) => {
    if (!segmentationData || !segmentationData.segmentations || 
        !segmentationData.segmentations[0] || !segmentationData.segmentations[0].mask) {
      return null;
    }
    
    try {
      const mask = segmentationData.segmentations[0].mask;
      const imageData = await mask.toImageData();
      
      if (!imageData) return null;
      
      // 计算图像到canvas的映射比例
      const scaleX = canvas.width / imageData.width;
      const scaleY = canvas.height / imageData.height;
      
      // 提取轮廓点
      const contourPoints = extractContourPointsForScaling(imageData, interpolationQuality);
      
      // 映射轮廓点到canvas坐标系
      const canvasPoints = contourPoints.map(point => ({
        x: point.x * scaleX,
        y: point.y * scaleY
      }));
      
      // 计算中心点
      const centerX = canvasPoints.reduce((sum, p) => sum + p.x, 0) / canvasPoints.length;
      const centerY = canvasPoints.reduce((sum, p) => sum + p.y, 0) / canvasPoints.length;
      
      // 应用缩放
      const scaledPoints = canvasPoints.map(point => ({
        x: centerX + (point.x - centerX) * scaleFactor,
        y: centerY + (point.y - centerY) * scaleFactor
      }));
      
      // 绘制轮廓
      drawSmoothContourPath(scaledPoints, contourColor, contourThickness, context, animationEffectProgress);
      
      if (scaleFactor > 1.0) {
        addScaleEffects(canvasPoints, scaledPoints, contourColor, contourThickness, context, animationEffectProgress);
      }
      
      if (showDecorations) {
        drawDecorations(scaledPoints, context, animationEffectProgress);
      }
      
      return scaledPoints;
    } catch (error) {
      if (showDebugInfo) console.error('直接渲染分割数据失败:', error);
      return null;
    }
  }, [interpolationQuality, scaleFactor, contourColor, contourThickness, animationEffectProgress, showDecorations, showDebugInfo]);

  // 原有的轮廓绘制方法（为了保持向后兼容性）
  const drawScaledContour = async (segmentation: any, scaleFactor: number, thickness: number, color: string, canvas: HTMLCanvasElement) => {
    try {
      if (showDebugInfo) {
        console.log('🎯 使用人体轮廓特效组件的drawScaledContour方法');
        console.log('🔍 参数:', { 
          segmentationExists: !!segmentation, 
          scaleFactor,
          thickness,
          color,
          canvasSize: { width: canvas.width, height: canvas.height }
        });
      }

      if (!segmentation || !segmentation.mask) {
        if (showDebugInfo) console.warn('⚠️ 分割结果无效，跳过轮廓绘制');
        return;
      }

      if (typeof segmentation.mask.toImageData !== 'function') {
        if (showDebugInfo) console.error('❌ mask对象没有toImageData方法');
        return;
      }

      const imageData = await segmentation.mask.toImageData();
      
      if (!imageData) {
        if (showDebugInfo) console.error('❌ toImageData返回null或undefined');
        return;
      }

      const { width: imgWidth, height: imgHeight, data } = imageData;
      
      if (showDebugInfo) {
        console.log(`🔍 ImageData尺寸: ${imgWidth}x${imgHeight}, 数据长度: ${data?.length}`);
      }
      
      if (!imgWidth || !imgHeight || imgWidth <= 0 || imgHeight <= 0) {
        if (showDebugInfo) console.error('❌ ImageData尺寸无效:', { width: imgWidth, height: imgHeight });
        return;
      }

      if (!data || data.length === 0) {
        if (showDebugInfo) console.error('❌ ImageData数据为空');
        return;
      }

      const expectedLength = imgWidth * imgHeight * 4;
      if (data.length !== expectedLength) {
        if (showDebugInfo) {
          console.error('❌ ImageData数据长度不匹配:', { 
            actual: data.length, 
            expected: expectedLength,
            width: imgWidth,
            height: imgHeight 
          });
        }
        return;
      }
      
      const smoothness = 3;
      const contourPoints = extractContourPointsForScaling(imageData, smoothness);
      
      if (contourPoints.length === 0) {
        if (showDebugInfo) console.warn('⚠️ 未检测到轮廓点');
        return;
      }
      
      if (showDebugInfo) console.log(`🎯 检测到 ${contourPoints.length} 个轮廓点`);
      
      // 计算图像到canvas的映射比例
      const scaleX = canvas.width / imgWidth;
      const scaleY = canvas.height / imgHeight;
      
      // 映射轮廓点到canvas坐标系
      const canvasPoints = contourPoints.map(point => ({
        x: point.x * scaleX,
        y: point.y * scaleY
      }));
      
      const centerX = canvasPoints.reduce((sum, p) => sum + p.x, 0) / canvasPoints.length;
      const centerY = canvasPoints.reduce((sum, p) => sum + p.y, 0) / canvasPoints.length;
      
      const scaledPoints = canvasPoints.map(point => ({
        x: centerX + (point.x - centerX) * scaleFactor,
        y: centerY + (point.y - centerY) * scaleFactor
      }));
      
      const context = canvas.getContext('2d', { alpha: true }); // 使用透明背景
      if (context) {
        // 🎯 简化：直接在主canvas上绘制，不使用缓冲区
        // 清除为透明
        context.clearRect(0, 0, canvas.width, canvas.height);
        
        // 如果有视频帧，先绘制视频背景
        if (currentVideoFrame) {
          context.drawImage(currentVideoFrame, 0, 0, canvas.width, canvas.height);
        }
        
        // 直接在主canvas上绘制轮廓
        drawSmoothContourPath(scaledPoints, color, thickness, context, animationEffectProgress);
        
        if (scaleFactor > 1.0) {
          addScaleEffects(canvasPoints, scaledPoints, color, thickness, context, animationEffectProgress);
        }

        // 🎨 绘制装饰贴片
        if (showDecorations) {
          drawDecorations(scaledPoints, context, animationEffectProgress);
        }
      }
      
    } catch (error: any) {
      if (showDebugInfo) {
        console.error('❌ drawScaledContour 失败:', error);
        console.error('❌ 错误详情:', {
          message: error.message,
          stack: error.stack,
          segmentationExists: !!segmentation,
          maskExists: !!segmentation?.mask,
          errorName: error.name
        });
      }
    }
  };

  // 🎯 监听frame变化，渲染轮廓特效
  useEffect(() => {
    const renderFrame = async () => {
      if (!canvasRef.current) return;
      
      const canvas = canvasRef.current;
      const context = canvas.getContext('2d', { alpha: true }); // 确保使用透明背景
      if (!context) return;

      // 🎯 如果动画帧率受限且当前帧不需要更新动画，跳过渲染
      if (effectiveAnimationFps < fps && !shouldUpdateAnimation && lastFrameRenderedRef.current) {
        return;
      }

      try {
        // 🎯 简化：直接在主canvas上绘制，移除离屏缓冲区
        // 清除画布 - 使用透明清除
        context.clearRect(0, 0, canvas.width, canvas.height);
        
        // 如果不在特效时间范围内，保持透明
        if (!shouldShowEffect) {
          lastFrameRenderedRef.current = false;
          return;
        }

        // 尝试直接渲染当前分割数据（如果可用）
        let effectRendered = false;
        
        if (segmentations && segmentations.length > 0) {
          // 直接从当前分割数据渲染到主canvas
          const renderedPoints = await renderSegmentationDirectly(
            {segmentations}, 
            canvas,
            context
          );
          
          if (renderedPoints) {
            effectRendered = true;
            
            // 如果是关键帧时刻，保存关键帧
            if (frame % Math.round(fps / 2) === 0) {
              const keyframeData = {
                timestamp: currentTime,
                frame: frame,
                contourPoints: renderedPoints,
                boundingBox: getBoundingBox(renderedPoints),
                centerPoint: {
                  x: renderedPoints.reduce((sum, p) => sum + p.x, 0) / renderedPoints.length,
                  y: renderedPoints.reduce((sum, p) => sum + p.y, 0) / renderedPoints.length
                }
              };
              
              manageKeyframes(keyframeData);
              
              if (showDebugInfo) {
                console.log('🔑 添加关键帧:', {
                  timestamp: keyframeData.timestamp.toFixed(2),
                  frame: keyframeData.frame,
                  pointsCount: keyframeData.contourPoints.length
                });
              }
            }
          }
        }
        
        // 如果当前帧没有成功渲染特效，尝试使用关键帧插值
        if (!effectRendered) {
          let contourPointsToRender: {x: number, y: number}[] = [];
          
          if (keyframes.length >= 2) {
            // 查找当前时间的前后关键帧
            let prevKeyframe: KeyframeData | null = null;
            let nextKeyframe: KeyframeData | null = null;
            
            for (let i = 0; i < keyframes.length - 1; i++) {
              if (keyframes[i].timestamp <= currentTime && keyframes[i+1].timestamp > currentTime) {
                prevKeyframe = keyframes[i];
                nextKeyframe = keyframes[i+1];
                break;
              }
            }
            
            // 如果找到了合适的关键帧对，进行插值
            if (prevKeyframe && nextKeyframe) {
              contourPointsToRender = interpolateContourPoints(
                prevKeyframe,
                nextKeyframe,
                currentTime
              );
              
              setLastInterpolatedFrame(frame);
              interpolatedPointsRef.current = contourPointsToRender;
              
              if (showInterpolationDebug && frame % 10 === 0) {
                console.log('⚙️ 关键帧插值:', {
                  currentTime: currentTime.toFixed(2),
                  prevFrame: prevKeyframe.frame,
                  prevTime: prevKeyframe.timestamp.toFixed(2),
                  nextFrame: nextKeyframe.frame,
                  nextTime: nextKeyframe.timestamp.toFixed(2),
                  interpPoints: contourPointsToRender.length
                });
              }
            } else if (keyframes.length > 0) {
              // 没有找到合适的关键帧对，使用最近的关键帧
              const nearest = keyframes.reduce((prev, curr) => 
                Math.abs(curr.timestamp - currentTime) < Math.abs(prev.timestamp - currentTime) 
                  ? curr : prev
              );
              
              contourPointsToRender = nearest.contourPoints;
            }
          } else if (keyframes.length === 1) {
            // 只有一个关键帧，直接使用
            contourPointsToRender = keyframes[0].contourPoints;
          } else if (interpolatedPointsRef.current.length > 0) {
            // 使用上一次的插值结果
            contourPointsToRender = interpolatedPointsRef.current;
          }
          
          // 如果有可渲染的轮廓点，直接在主canvas上绘制
          if (contourPointsToRender.length > 0) {
            if (showDebugInfo && frame % 60 === 0) {
              console.log('🎯 使用插值数据绘制人体轮廓...');
            }
            
            // 计算中心点和缩放后的轮廓点
            const centerX = contourPointsToRender.reduce((sum, p) => sum + p.x, 0) / contourPointsToRender.length;
            const centerY = contourPointsToRender.reduce((sum, p) => sum + p.y, 0) / contourPointsToRender.length;
            
            const scaledPoints = contourPointsToRender.map(point => ({
              x: centerX + (point.x - centerX) * scaleFactor,
              y: centerY + (point.y - centerY) * scaleFactor
            }));
            
            // 绘制轮廓到主canvas
            drawSmoothContourPath(scaledPoints, contourColor, contourThickness, context, animationEffectProgress);
            
            if (scaleFactor > 1.0) {
              addScaleEffects(contourPointsToRender, scaledPoints, contourColor, contourThickness, context, animationEffectProgress);
            }
            
            // 绘制装饰贴片
            if (showDecorations) {
              drawDecorations(scaledPoints, context, animationEffectProgress);
            }
            
            effectRendered = true;
          }
        }
        
        // 🎯 设置canvas显示状态
        if (effectRendered && !canvasReadyRef.current) {
          setCanvasReady(true);
          canvasReadyRef.current = true;
        }
        
        lastFrameRenderedRef.current = effectRendered;
        
        if (!effectRendered && showDebugInfo && frame % 60 === 0) {
          console.log('⚠️ 无可用数据渲染特效...');
        }
        
      } catch (error: any) {
        if (showDebugInfo) console.error('❌ 轮廓特效渲染失败:', error);
        // 渲染失败时，清除canvas保持透明
        context.clearRect(0, 0, canvas.width, canvas.height);
      }
    };

    renderFrame();
  }, [
    frame, shouldShowEffect, animationEffectProgress, segmentations, currentVideoFrame, 
    scaleFactor, contourThickness, contourColor, showDebugInfo, currentTime, 
    width, height, keyframes, manageKeyframes, extractKeyframeData, 
    interpolateContourPoints, interpolationQuality, fps, showInterpolationDebug,
    renderSegmentationDirectly, effectiveAnimationFps, shouldUpdateAnimation, animationFrame
  ]);

  // 🎯 新增：特效结束时隐藏canvas
  useEffect(() => {
    if (!shouldShowEffect && canvasReadyRef.current) {
      // 特效结束时，延迟隐藏canvas，避免突然消失
      const timer = setTimeout(() => {
        setCanvasReady(false);
        canvasReadyRef.current = false;
      }, 100);
      
      return () => clearTimeout(timer);
    }
  }, [shouldShowEffect]);

  // 🎯 新增：动态设置canvas透明度的函数
  const updateCanvasOpacity = useCallback(() => {
    if (canvasRef.current) {
      const canvas = canvasRef.current;
      const targetOpacity = canvasReady && shouldShowEffect ? 1 : 0;
      
      // 直接通过JS设置CSS样式，实现平滑过渡
      canvas.style.opacity = targetOpacity.toString();
      canvas.style.transition = 'opacity 0.3s ease-in-out';
      
      if (showDebugInfo && frame % 60 === 0) {
        console.log(`🎨 Canvas透明度: ${targetOpacity} (Ready: ${canvasReady}, ShowEffect: ${shouldShowEffect})`);
      }
    }
  }, [canvasReady, shouldShowEffect, showDebugInfo, frame]);

  // 🎯 新增：监听透明度状态变化并更新canvas样式
  useEffect(() => {
    updateCanvasOpacity();
  }, [updateCanvasOpacity]);

  const extractContourPointsForScaling = (imageData: ImageData, smoothness = 3) => {
    const { width, height, data } = imageData;
    const contourPoints: { x: number; y: number }[] = [];
    
    const smoothnessConfig = {
      1: { stepMultiplier: 0.8, threshold: 130, bgThreshold: 110, minRatio: 0.15, maxRatio: 0.85, edgeStrength: 0.3 },
      2: { stepMultiplier: 1.0, threshold: 140, bgThreshold: 105, minRatio: 0.2, maxRatio: 0.8, edgeStrength: 0.4 },
      3: { stepMultiplier: 1.5, threshold: 150, bgThreshold: 100, minRatio: 0.25, maxRatio: 0.75, edgeStrength: 0.5 },
      4: { stepMultiplier: 2.0, threshold: 160, bgThreshold: 95, minRatio: 0.3, maxRatio: 0.7, edgeStrength: 0.6 },
      5: { stepMultiplier: 2.5, threshold: 170, bgThreshold: 90, minRatio: 0.35, maxRatio: 0.65, edgeStrength: 0.7 }
    };
    
    const config = smoothnessConfig[smoothness as keyof typeof smoothnessConfig] || smoothnessConfig[3];
    
    const baseStep = Math.max(1, Math.floor(Math.min(width, height) / 150));
    const step = Math.max(1, Math.floor(baseStep * config.stepMultiplier));
    
    const candidatePoints: { x: number; y: number; strength: number }[] = [];
    
    for (let y = step; y < height - step; y += step) {
      for (let x = step; x < width - step; x += step) {
        const index = (y * width + x) * 4;
        const alpha = data[index + 3];
        
        if (alpha > config.threshold) {
          const edgeStrength = calculateEdgeStrength(data, x, y, width, height, step);
          
          if (edgeStrength > config.edgeStrength) {
            const backgroundRatio = calculateBackgroundRatio(data, x, y, width, height, step, config.bgThreshold);
            
            if (backgroundRatio > config.minRatio && backgroundRatio < config.maxRatio) {
              candidatePoints.push({ x, y, strength: edgeStrength });
            }
          }
        }
      }
    }
    
    const suppressedPoints = nonMaximumSuppression(candidatePoints, step * 2);
    suppressedPoints.sort((a, b) => b.strength - a.strength);
    
    const maxPoints = Math.min(suppressedPoints.length, Math.floor(Math.min(width, height) / (step * 2)));
    const selectedPoints = suppressedPoints.slice(0, maxPoints).map(p => ({ x: p.x, y: p.y }));
    
    const tolerance = Math.max(2, Math.floor(Math.min(width, height) / (80 - smoothness * 10)));
    return simplifyContourPointsForScaling(selectedPoints, tolerance);
  };

  const calculateEdgeStrength = (
    data: Uint8ClampedArray, 
    x: number, 
    y: number, 
    width: number, 
    height: number, 
    step: number
  ) => {
    const sobelX = [[-1, 0, 1], [-2, 0, 2], [-1, 0, 1]];
    const sobelY = [[-1, -2, -1], [0, 0, 0], [1, 2, 1]];
    
    let gx = 0, gy = 0;
    
    for (let dy = -1; dy <= 1; dy++) {
      for (let dx = -1; dx <= 1; dx++) {
        const nx = x + dx * step;
        const ny = y + dy * step;
        
        if (nx >= 0 && nx < width && ny >= 0 && ny < height) {
          const index = (ny * width + nx) * 4;
          const alpha = data[index + 3];
          
          gx += alpha * sobelX[dy + 1][dx + 1];
          gy += alpha * sobelY[dy + 1][dx + 1];
        }
      }
    }
    
    return Math.sqrt(gx * gx + gy * gy) / 255;
  };

  const calculateBackgroundRatio = (
    data: Uint8ClampedArray,
    x: number,
    y: number,
    width: number,
    height: number,
    step: number,
    bgThreshold: number
  ) => {
    let backgroundCount = 0;
    let totalChecked = 0;
    
    const checkRadius = step * 2;
    for (let dy = -checkRadius; dy <= checkRadius; dy += step) {
      for (let dx = -checkRadius; dx <= checkRadius; dx += step) {
        if (dx === 0 && dy === 0) continue;
        
        const nx = x + dx;
        const ny = y + dy;
        
        if (nx >= 0 && nx < width && ny >= 0 && ny < height) {
          const neighborIndex = (ny * width + nx) * 4;
          const neighborAlpha = data[neighborIndex + 3];
          
          if (neighborAlpha <= bgThreshold) {
            backgroundCount++;
          }
          totalChecked++;
        }
      }
    }
    
    return totalChecked > 0 ? backgroundCount / totalChecked : 0;
  };

  const nonMaximumSuppression = (
    points: { x: number; y: number; strength: number }[], 
    radius: number
  ) => {
    const suppressed: { x: number; y: number; strength: number }[] = [];
    const used = new Set<number>();
    
    const sortedPoints = [...points].sort((a, b) => b.strength - a.strength);
    
    for (let i = 0; i < sortedPoints.length; i++) {
      if (used.has(i)) continue;
      
      const current = sortedPoints[i];
      suppressed.push(current);
      
      for (let j = i + 1; j < sortedPoints.length; j++) {
        if (used.has(j)) continue;
        
        const other = sortedPoints[j];
        const distance = Math.sqrt(
          Math.pow(current.x - other.x, 2) + Math.pow(current.y - other.y, 2)
        );
        
        if (distance < radius) {
          used.add(j);
        }
      }
    }
    
    return suppressed;
  };

  const simplifyContourPointsForScaling = (points: { x: number; y: number }[], tolerance = 5) => {
    if (points.length <= 3) return points;
    
    const centerX = points.reduce((sum, p) => sum + p.x, 0) / points.length;
    const centerY = points.reduce((sum, p) => sum + p.y, 0) / points.length;
    
    points.sort((a, b) => {
      const angleA = Math.atan2(a.y - centerY, a.x - centerX);
      const angleB = Math.atan2(b.y - centerY, b.x - centerX);
      return angleA - angleB;
    });
    
    const boundingBox = getBoundingBox(points);
    const adaptiveTolerance = Math.max(tolerance, Math.min(boundingBox.width, boundingBox.height) / 50);
    
    const distanceFiltered = [points[0]];
    for (let i = 1; i < points.length; i++) {
      const prev = distanceFiltered[distanceFiltered.length - 1];
      const curr = points[i];
      const dist = Math.sqrt(Math.pow(curr.x - prev.x, 2) + Math.pow(curr.y - prev.y, 2));
      
      if (dist > adaptiveTolerance) {
        distanceFiltered.push(curr);
      }
    }
    
    if (distanceFiltered.length > 8) {
      const curvatureFiltered = [distanceFiltered[0]];
      
      for (let i = 2; i < distanceFiltered.length - 2; i++) {
        const p1 = distanceFiltered[i - 2];
        const p2 = distanceFiltered[i - 1];
        const p3 = distanceFiltered[i];
        const p4 = distanceFiltered[i + 1];
        const p5 = distanceFiltered[i + 2];
        
        const curvature = calculateCurvature(p1, p2, p3, p4, p5);
        
        if (curvature > 0.15) {
          curvatureFiltered.push(p3);
        }
      }
      
      if (curvatureFiltered.length < 6) {
        return distanceFiltered;
      }
      
      curvatureFiltered.push(distanceFiltered[distanceFiltered.length - 1]);
      return curvatureFiltered;
    }
    
    if (distanceFiltered.length > 6) {
      const angleSmoothed = [distanceFiltered[0]];
      
      for (let i = 1; i < distanceFiltered.length - 1; i++) {
        const prev = distanceFiltered[i - 1];
        const curr = distanceFiltered[i];
        const next = distanceFiltered[i + 1];
        
        const angle1 = Math.atan2(curr.y - prev.y, curr.x - prev.x);
        const angle2 = Math.atan2(next.y - curr.y, next.x - curr.x);
        let angleDiff = Math.abs(angle2 - angle1);
        
        if (angleDiff > Math.PI) {
          angleDiff = 2 * Math.PI - angleDiff;
        }
        
        if (angleDiff > 0.15) {
          angleSmoothed.push(curr);
        }
      }
      
      angleSmoothed.push(distanceFiltered[distanceFiltered.length - 1]);
      return angleSmoothed.length > 3 ? angleSmoothed : distanceFiltered;
    }
    
    return distanceFiltered;
  };

  const getBoundingBox = (points: { x: number; y: number }[]) => {
    const xs = points.map(p => p.x);
    const ys = points.map(p => p.y);
    return {
      minX: Math.min(...xs),
      maxX: Math.max(...xs),
      minY: Math.min(...ys),
      maxY: Math.max(...ys),
      width: Math.max(...xs) - Math.min(...xs),
      height: Math.max(...ys) - Math.min(...ys)
    };
  };

  const calculateCurvature = (
    p1: { x: number; y: number },
    p2: { x: number; y: number },
    p3: { x: number; y: number },
    p4: { x: number; y: number },
    p5: { x: number; y: number }
  ) => {
    const dx1 = p3.x - p1.x;
    const dy1 = p3.y - p1.y;
    const dx2 = p5.x - p3.x;
    const dy2 = p5.y - p3.y;
    
    const angle1 = Math.atan2(dy1, dx1);
    const angle2 = Math.atan2(dy2, dx2);
    
    let angleDiff = Math.abs(angle2 - angle1);
    if (angleDiff > Math.PI) {
      angleDiff = 2 * Math.PI - angleDiff;
    }
    
    const dist1 = Math.sqrt(dx1 * dx1 + dy1 * dy1);
    const dist2 = Math.sqrt(dx2 * dx2 + dy2 * dy2);
    const avgDist = (dist1 + dist2) / 2;
    
    return avgDist > 0 ? angleDiff / avgDist : 0;
  };

  const drawSmoothContourPath = (
    points: { x: number; y: number }[], 
    color: string, 
    thickness: number, 
    context: CanvasRenderingContext2D,
    progress: number
  ) => {
    if (points.length < 3) return;
    
    // 🎨 根据进度调整透明度和效果强度
    // const alpha = Math.sin(progress * Math.PI); // 淡入淡出效果
    const alpha = 1
    
    context.strokeStyle = color;
    context.lineWidth = thickness * (0.5 + 0.5 * alpha); // 线条粗细动画
    context.lineCap = 'round';
    context.lineJoin = 'round';
    context.shadowColor = color;
    context.shadowBlur = Math.max(3, thickness / 1.5) * alpha;
    context.globalAlpha = alpha;
    
    const enhancedPoints = addIntermediatePoints(points);
    
    context.beginPath();
    
    if (enhancedPoints.length < 4) {
      context.moveTo(enhancedPoints[0].x, enhancedPoints[0].y);
      for (let i = 1; i < enhancedPoints.length; i++) {
        const current = enhancedPoints[i];
        const prev = enhancedPoints[i - 1];
        const cpx = (prev.x + current.x) / 2;
        const cpy = (prev.y + current.y) / 2;
        context.quadraticCurveTo(prev.x, prev.y, cpx, cpy);
      }
      context.closePath();
    } else {
      drawCatmullRomSpline(enhancedPoints, context);
    }
    
    context.stroke();
    context.shadowBlur = 0;
    context.globalAlpha = 1.0;
  };

  const addIntermediatePoints = (points: { x: number; y: number }[]) => {
    if (points.length < 3) return points;
    
    const enhanced: { x: number; y: number }[] = [];
    
    for (let i = 0; i < points.length; i++) {
      const current = points[i];
      const next = points[(i + 1) % points.length];
      
      enhanced.push(current);
      
      const midX = (current.x + next.x) / 2;
      const midY = (current.y + next.y) / 2;
      
      const distance = Math.sqrt(Math.pow(next.x - current.x, 2) + Math.pow(next.y - current.y, 2));
      if (distance > 20) {
        enhanced.push({ x: midX, y: midY });
      }
    }
    
    return enhanced;
  };

  const drawCatmullRomSpline = (points: { x: number; y: number }[], context: CanvasRenderingContext2D) => {
    const tension = 0.5;
    const segments = 20;
    
    const extendedPoints = [
      points[points.length - 1],
      ...points,
      points[0],
      points[1]
    ];
    
    context.moveTo(points[0].x, points[0].y);
    
    for (let i = 1; i < extendedPoints.length - 2; i++) {
      const p0 = extendedPoints[i - 1];
      const p1 = extendedPoints[i];
      const p2 = extendedPoints[i + 1];
      const p3 = extendedPoints[i + 2];
      
      for (let t = 0; t <= segments; t++) {
        const u = t / segments;
        const point = catmullRomPoint(p0, p1, p2, p3, u, tension);
        
        if (t === 0) {
          context.moveTo(point.x, point.y);
        } else {
          context.lineTo(point.x, point.y);
        }
      }
    }
    
    context.closePath();
  };

  const catmullRomPoint = (
    p0: { x: number; y: number },
    p1: { x: number; y: number },
    p2: { x: number; y: number },
    p3: { x: number; y: number },
    t: number,
    tension: number
  ) => {
    const t2 = t * t;
    const t3 = t2 * t;
    
    const x = 0.5 * (
      (2 * p1.x) +
      (-p0.x + p2.x) * t +
      (2 * p0.x - 5 * p1.x + 4 * p2.x - p3.x) * t2 +
      (-p0.x + 3 * p1.x - 3 * p2.x + p3.x) * t3
    );
    
    const y = 0.5 * (
      (2 * p1.y) +
      (-p0.y + p2.y) * t +
      (2 * p0.y - 5 * p1.y + 4 * p2.y - p3.y) * t2 +
      (-p0.y + 3 * p1.y - 3 * p2.y + p3.y) * t3
    );
    
    return { x, y };
  };

  const addScaleEffects = (
    originalPoints: { x: number; y: number }[], 
    scaledPoints: { x: number; y: number }[], 
    color: string, 
    thickness: number, 
    context: CanvasRenderingContext2D,
    progress: number
  ) => {
    const alpha = Math.sin(progress * Math.PI) * 0.3;
    
    context.strokeStyle = color;
    context.lineWidth = Math.max(1, thickness / 3);
    context.globalAlpha = alpha;
    
    const step = Math.max(1, Math.floor(originalPoints.length / 8));
    for (let i = 0; i < originalPoints.length; i += step) {
      context.beginPath();
      context.moveTo(originalPoints[i].x, originalPoints[i].y);
      context.lineTo(scaledPoints[i].x, scaledPoints[i].y);
      context.stroke();
    }
    
    context.globalAlpha = 1.0;
  };

  // 🎨 绘制装饰贴片
  const drawDecorations = (
    contourPoints: { x: number; y: number }[],
    context: CanvasRenderingContext2D,
    progress: number
  ) => {
    if (!showDecorations || contourPoints.length === 0) return;

    // 计算轮廓的边界框
    const boundingBox = getBoundingBox(contourPoints);
    const centerX = (boundingBox.minX + boundingBox.maxX) / 2;
    const centerY = (boundingBox.minY + boundingBox.maxY) / 2;

    // 装饰元素类型
    const decorationTypes = ['star', 'flower', 'heart', 'sparkle', 'diamond'];
    
    // 根据进度计算透明度和动画效果
    const alpha = Math.sin(progress * Math.PI);
    const animationOffset = progress * 20; // 轻微的浮动动画

    context.globalAlpha = alpha * 0.8;

    // 在轮廓周围生成装饰贴片
    for (let i = 0; i < decorationCount; i++) {
      // 使用固定的随机种子确保装饰位置一致
      const seed = i * 137.508; // 黄金角度
      const angle = (seed % 360) * (Math.PI / 180);
      
      // 在轮廓外围一定距离放置装饰
      const distance = Math.min(boundingBox.width, boundingBox.height) * (0.6 + (i % 3) * 0.2);
      const x = centerX + Math.cos(angle) * distance;
      const y = centerY + Math.sin(angle) * distance + Math.sin(progress * Math.PI * 2 + i) * animationOffset;

      // 随机选择装饰类型
      const decorationType = decorationTypes[i % decorationTypes.length];
      
      // 计算装饰大小（带动画效果）
      const size = decorationSize * (0.8 + 0.4 * Math.sin(progress * Math.PI));
      
      // 绘制装饰
      drawDecoration(context, x, y, size, decorationType, progress, i);
    }

    context.globalAlpha = 1.0;
  };

  // 🎨 绘制单个装饰元素
  const drawDecoration = (
    context: CanvasRenderingContext2D,
    x: number,
    y: number,
    size: number,
    type: string,
    progress: number,
    index: number
  ) => {
    context.save();
    context.translate(x, y);
    
    // 添加旋转动画
    const rotation = progress * Math.PI * 2 + index * 0.5;
    context.rotate(rotation);

    // 根据类型绘制不同的装饰
    switch (type) {
      case 'star':
        drawStar(context, size);
        break;
      case 'flower':
        drawFlower(context, size);
        break;
      case 'heart':
        drawHeart(context, size);
        break;
      case 'sparkle':
        drawSparkle(context, size);
        break;
      case 'diamond':
        drawDiamond(context, size);
        break;
    }

    context.restore();
  };

  // ⭐ 绘制星星
  const drawStar = (context: CanvasRenderingContext2D, size: number) => {
    const spikes = 5;
    const outerRadius = size / 2;
    const innerRadius = outerRadius * 0.4;

    context.beginPath();
    for (let i = 0; i < spikes * 2; i++) {
      const radius = i % 2 === 0 ? outerRadius : innerRadius;
      const angle = (i * Math.PI) / spikes;
      const x = Math.cos(angle) * radius;
      const y = Math.sin(angle) * radius;
      
      if (i === 0) {
        context.moveTo(x, y);
      } else {
        context.lineTo(x, y);
      }
    }
    context.closePath();
    
    // 渐变填充
    const gradient = context.createRadialGradient(0, 0, 0, 0, 0, outerRadius);
    gradient.addColorStop(0, '#FFD700');
    gradient.addColorStop(1, '#FFA500');
    context.fillStyle = gradient;
    context.fill();
    
    context.strokeStyle = '#FF8C00';
    context.lineWidth = 2;
    context.stroke();
  };

  // 🌸 绘制花朵
  const drawFlower = (context: CanvasRenderingContext2D, size: number) => {
    const petals = 6;
    const radius = size / 3;

    // 绘制花瓣
    for (let i = 0; i < petals; i++) {
      context.save();
      context.rotate((i * 2 * Math.PI) / petals);
      
      context.beginPath();
      context.ellipse(0, -radius * 0.7, radius * 0.6, radius * 1.2, 0, 0, 2 * Math.PI);
      
      const gradient = context.createRadialGradient(0, -radius * 0.7, 0, 0, -radius * 0.7, radius);
      gradient.addColorStop(0, '#FF69B4');
      gradient.addColorStop(1, '#FF1493');
      context.fillStyle = gradient;
      context.fill();
      
      context.strokeStyle = '#DC143C';
      context.lineWidth = 1;
      context.stroke();
      
      context.restore();
    }

    // 绘制花心
    context.beginPath();
    context.arc(0, 0, radius * 0.3, 0, 2 * Math.PI);
    context.fillStyle = '#FFD700';
    context.fill();
    context.strokeStyle = '#FFA500';
    context.lineWidth = 1;
    context.stroke();
  };

  // 💖 绘制爱心
  const drawHeart = (context: CanvasRenderingContext2D, size: number) => {
    const scale = size / 40;
    
    context.beginPath();
    context.moveTo(0, 5 * scale);
    
    // 左半边
    context.bezierCurveTo(-15 * scale, -10 * scale, -30 * scale, 0, 0, 20 * scale);
    // 右半边
    context.bezierCurveTo(30 * scale, 0, 15 * scale, -10 * scale, 0, 5 * scale);
    
    const gradient = context.createLinearGradient(0, -10 * scale, 0, 20 * scale);
    gradient.addColorStop(0, '#FF69B4');
    gradient.addColorStop(1, '#FF1493');
    context.fillStyle = gradient;
    context.fill();
    
    context.strokeStyle = '#DC143C';
    context.lineWidth = 2;
    context.stroke();
  };

  // ✨ 绘制闪光
  const drawSparkle = (context: CanvasRenderingContext2D, size: number) => {
    const length = size / 2;
    
    context.strokeStyle = '#FFD700';
    context.lineWidth = 3;
    context.lineCap = 'round';
    
    // 绘制十字形闪光
    context.beginPath();
    context.moveTo(-length, 0);
    context.lineTo(length, 0);
    context.moveTo(0, -length);
    context.lineTo(0, length);
    
    // 绘制对角线
    const diagLength = length * 0.7;
    context.moveTo(-diagLength, -diagLength);
    context.lineTo(diagLength, diagLength);
    context.moveTo(-diagLength, diagLength);
    context.lineTo(diagLength, -diagLength);
    
    context.stroke();
    
    // 中心亮点
    context.beginPath();
    context.arc(0, 0, 3, 0, 2 * Math.PI);
    context.fillStyle = '#FFFFFF';
    context.fill();
  };

  // 💎 绘制钻石
  const drawDiamond = (context: CanvasRenderingContext2D, size: number) => {
    const radius = size / 2;
    
    context.beginPath();
    context.moveTo(0, -radius);
    context.lineTo(radius * 0.6, -radius * 0.3);
    context.lineTo(radius * 0.6, radius * 0.3);
    context.lineTo(0, radius);
    context.lineTo(-radius * 0.6, radius * 0.3);
    context.lineTo(-radius * 0.6, -radius * 0.3);
    context.closePath();
    
    const gradient = context.createLinearGradient(-radius, -radius, radius, radius);
    gradient.addColorStop(0, '#E6E6FA');
    gradient.addColorStop(0.5, '#DDA0DD');
    gradient.addColorStop(1, '#DA70D6');
    context.fillStyle = gradient;
    context.fill();
    
    context.strokeStyle = '#9370DB';
    context.lineWidth = 2;
    context.stroke();
    
    // 添加高光效果
    context.beginPath();
    context.moveTo(-radius * 0.2, -radius * 0.6);
    context.lineTo(radius * 0.2, -radius * 0.6);
    context.lineTo(0, -radius * 0.2);
    context.closePath();
    context.fillStyle = 'rgba(255, 255, 255, 0.6)';
    context.fill();
  };

  return (
    <>
      {children}
      {/* 🎯 最终合成Canvas - 设置透明背景 */}
      <canvas 
        ref={canvasRef} 
        width={width} 
        height={height}
        style={{
          width,
          height,
          position: 'absolute',
          top: 0,
          left: 0,
          // zIndex: 1000,
          opacity: 0, // 🎯 初始透明度为0，防止黑色闪烁
          transition: 'opacity 0.3s ease-in-out' // 🎯 添加平滑过渡效果
        }}
      />

      {/* 🎯 调试信息显示 */}
      {showDebugInfo && (
        <div style={{
          position: 'absolute',
          top: 20,
          left: 20,
          background: 'rgba(0,0,0,0.8)',
          color: 'white',
          padding: '15px',
          borderRadius: '8px',
          fontSize: '12px',
          zIndex: 25,
          fontFamily: 'monospace',
          minWidth: '300px'
        }}>
          <div>⏰ 当前时间: {currentTime.toFixed(2)}s</div>
          <div>🎯 特效时间: {startTime}s - {(startTime + duration).toFixed(1)}s</div>
          <div>📊 特效状态: {shouldShowEffect ? `✅ 激活 (${(effectProgress * 100).toFixed(0)}%)` : '⏸️ 未激活'}</div>
          <div>🎬 视频帧率: {fps}fps</div>
          <div>🎨 动画帧率: {effectiveAnimationFps}fps</div>
          <div>🎞️ 当前帧号: {frame} (动画帧: {animationFrame})</div>
          <div>🔄 动画进度: {(animationEffectProgress * 100).toFixed(0)}%</div>
          <div>🎨 轮廓颜色: {contourColor}</div>
          <div>📏 轮廓粗细: {contourThickness}px</div>
          <div>🔍 放大倍数: {scaleFactor}x</div>
          <div>🎯 人体数据: {segmentations && segmentations.length > 0 ? `✅ ${segmentations.length}个分割` : '❌ 无数据'}</div>
          <div>🎬 视频帧: {currentVideoFrame ? '✅ 可用' : '❌ 无帧'}</div>
          <div>🔧 Canvas尺寸: {width}x{height}</div>
          <div>🎨 装饰状态: {showDecorations ? `✅ ${decorationCount}个装饰` : '❌ 已关闭'}</div>
          <div>📊 装饰大小: {decorationSize}px</div>
          <div>🔑 关键帧数: {keyframes.length}</div>
          <div>⚡ 渲染优化: {effectiveAnimationFps < fps ? '✅ 帧率优化已启用' : '❌ 全帧率渲染'}</div>
          <div>👁️ Canvas状态: {canvasReady ? '✅ 可见' : '❌ 隐藏'}</div>
        </div>
      )}

      {/* 关键帧插值调试信息 */}
      {showInterpolationDebug && (
        <div style={{
          position: 'absolute',
          bottom: 50,
          left: 20,
          background: 'rgba(0,0,0,0.8)',
          color: 'white',
          padding: '10px',
          borderRadius: '8px',
          fontSize: '12px',
          zIndex: 25,
          fontFamily: 'monospace',
          maxWidth: '80%',
          overflowX: 'hidden'
        }}>
          <div>📊 插值统计:</div>
          <div>- 关键帧数: {keyframes.length}</div>
          <div>- 插值点数: {interpolatedPointsRef.current.length}</div>
          <div>- 时间窗口: {keyframeWindowSize}秒</div>
          <div>- 插值质量: {interpolationQuality}/5</div>
          <div>
            - 关键帧时间: {keyframes.slice(0, 5).map(k => k.timestamp.toFixed(1)).join(', ')}
            {keyframes.length > 5 ? `... (共${keyframes.length}个)` : ''}
          </div>
        </div>
      )}
    </>
  );
}; 

BodyContourEffect.key = 'BodyContourEffect';
BodyContourEffect.showName = '人体轮廓';
BodyContourEffect.description = '人体轮廓特效（带关键帧插值）';
// BodyContourEffect.ranges = ['main-track', 'overlay', 'image', 'video'] // 作用范围
BodyContourEffect.ranges = ['main-track'] // 作用范围
// 修改静态方法，进一步智能化关键帧采样策略
BodyContourEffect.needSegmentationsData = needSegmentationsData