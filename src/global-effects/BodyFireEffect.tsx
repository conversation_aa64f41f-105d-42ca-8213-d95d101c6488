import React, { useCallback, useRef, useEffect, useState } from "react";
import { useCurrentFrame, useVideoConfig, delayRender, continueRender, Audio } from "remotion";

// 🔥 图片序列火焰特效组件配置接口
export interface BodyFireEffectProps {
  /** 图片序列基础URL模板，使用{index}作为占位符，如：/fire/frame_{index}.png */
  imageSequenceUrlTemplate: string;
  /** 图片序列总帧数 */
  totalFrames: number;
  /** 人体分割数据（从AI模型获取） */
  segmentations: any[] | null;
  /** 当前视频帧（用于人体合成），可选 */
  currentVideoFrame?: CanvasImageSource;
  /** 图片序列帧率，默认30 */
  animationFps?: number;
  /** 开始显示时间（秒），默认0 */
  startTime?: number;
  /** 火焰播放时长（秒），不传默认播放一次完整序列 */
  duration?: number;
  /** 是否显示调试信息，默认false */
  showDebugInfo?: boolean;
  /** 跨域设置 */
  crossOrigin?: "anonymous" | "use-credentials";
  /** 火焰透明度，默认0.8 */
  fireOpacity?: number;
  /** 火焰缩放比例，默认1.0 */
  fireScale?: number;
  /** 火焰显示宽度（像素），优先级高于fireScale */
  fireWidth?: number;
  /** 火焰显示高度（像素），优先级高于fireScale */
  fireHeight?: number;
  /** 火焰X轴位置（像素），如果设置则覆盖AI头部位置 */
  fireX?: number;
  /** 火焰Y轴位置（像素），如果设置则覆盖AI头部位置 */
  fireY?: number;
  /** 是否循环播放图片序列，默认true */
  loop?: boolean;
  /** 图片序列起始索引，默认1 */
  startIndex?: number;
  /** 音效文件URL */
  audioSrc?: string;
  /** 音效音量，默认1.0 (0-1) */
  audioVolume?: number;
  /** 音效是否循环播放，默认false */
  audioLoop?: boolean;
  /** 音效播放延迟（秒），相对于特效开始时间，默认0 */
  audioDelay?: number;
  /** 是否启用音效，默认true（当audioSrc存在时） */
  enableAudio?: boolean;
  children?: React.ReactNode;
}

export const BodyFireEffect = ({
  imageSequenceUrlTemplate = "https://static.qiaoxuesi.com/remotion/body-fire/{index}.png",
  totalFrames = 24,
  segmentations,
  currentVideoFrame,
  animationFps = 24,
  startTime = 0,
  duration,
  showDebugInfo = false,
  crossOrigin = "anonymous",
  fireOpacity = 0.8,
  fireScale = 1.0,
  fireWidth,
  fireHeight,
  fireX = 0,
  fireY = 0,
  loop = true,
  startIndex = 0,
  audioSrc = 'https://img.qiaoxuesi.com/upfiles/995_6319.mp3',
  audioVolume = 0.3,
  audioLoop = false,
  audioDelay = 0,
  enableAudio = true,
  children,
}: BodyFireEffectProps) => {
  const fireCanvasRef = useRef<HTMLCanvasElement>(null);
  const compositeCanvasRef = useRef<HTMLCanvasElement>(null);
  
  // 🎯 人体轮廓数据缓存 - 从props接收后缓存
  const bodySegmentationCache = useRef<{
    headPosition: { x: number; y: number } | null;
    maskImageData: ImageData | null;
    isInitialized: boolean;
  }>({
    headPosition: null,
    maskImageData: null,
    isInitialized: false
  });
  
  const { width, height, fps } = useVideoConfig();
  const frame = useCurrentFrame();
  
  // 🔥 计算实际播放时长：如果没有传firePlayDuration，则为播放一次完整序列的时长
  const actualFireDuration = duration || totalFrames / animationFps;
  
  // 🎯 计算当前时间和是否应该显示特效
  const currentTime = frame / fps;
  const shouldShowEffect = currentTime >= startTime && currentTime <= (startTime + actualFireDuration);
  const effectProgress = shouldShowEffect ? 
    Math.min(1, (currentTime - startTime) / actualFireDuration) : 0;
  
  // 🔥 图片序列相关状态
  const [imagesLoaded, setImagesLoaded] = useState(false);
  const [loadedImages, setLoadedImages] = useState<HTMLImageElement[]>([]);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  
  // 🎯 人体轮廓数据状态
  const [bodyDataReady, setBodyDataReady] = useState(false);
  const [renderHandle] = useState(() => delayRender('等待图片序列和人体轮廓数据加载', { timeoutInMilliseconds: 120000 }));

  // 🎯 初始化人体轮廓数据缓存
  useEffect(() => {
    const initializeBodyData = async () => {
      try {
        if (showDebugInfo) {
          console.log('🎯 初始化人体轮廓数据:', segmentations);
        }

        if (segmentations && segmentations.length > 0) {
          const segmentation = segmentations[0];
          
          // 计算头部位置
          const headPosition = await getHeadPosition(segmentation, width, height);
          bodySegmentationCache.current.headPosition = headPosition;

          // 获取mask数据
          const maskImageData = await segmentation.mask.toImageData();
          bodySegmentationCache.current.maskImageData = maskImageData;
          
          if (showDebugInfo) {
            console.log('✅ 人体轮廓数据初始化完成:', {
              headPosition,
              maskSize: maskImageData ? `${maskImageData.width}x${maskImageData.height}` : 'null'
            });
          }
        } else {
          // 使用默认头部位置
          bodySegmentationCache.current.headPosition = { x: width / 2, y: height * 0.3 };
          bodySegmentationCache.current.maskImageData = null;
          
          if (showDebugInfo) console.log('⚠️ 未提供人体分割数据，使用默认位置');
        }

        bodySegmentationCache.current.isInitialized = true;
        setBodyDataReady(true);

        if (showDebugInfo) {
          console.log('✅ 人体轮廓数据初始化完成:', {
            headPosition: bodySegmentationCache.current.headPosition,
            hasMask: !!bodySegmentationCache.current.maskImageData
          });
        }

      } catch (error) {
        if (showDebugInfo) console.error('❌ 人体轮廓数据初始化失败:', error);
        
        // 使用默认值
        bodySegmentationCache.current.headPosition = { x: width / 2, y: height * 0.3 };
        bodySegmentationCache.current.maskImageData = null;
        bodySegmentationCache.current.isInitialized = true;
        setBodyDataReady(true);
      }
    };

    initializeBodyData();
  }, [segmentations, width, height, showDebugInfo]);

  // 🔥 获取人体头部位置
  const getHeadPosition = async (segmentation: any, canvasWidth: number, canvasHeight: number) => {
    try {
      if (!segmentation || !segmentation.mask) return { x: canvasWidth / 2, y: canvasHeight * 0.3 };

      const imageData = await segmentation.mask.toImageData();
      if (!imageData) return { x: canvasWidth / 2, y: canvasHeight * 0.3 };

      const { width, height, data } = imageData;
      let minY = height;
      let sumX = 0;
      let count = 0;

      // 寻找人体最上方的点（头部区域）
      for (let y = 0; y < height; y++) {
        for (let x = 0; x < width; x++) {
          const index = (y * width + x) * 4;
          const alpha = data[index + 3];
          
          if (alpha > 128) { // 人体像素
            if (y < minY) {
              minY = y;
            }
            if (y < height * 0.4) { // 只考虑上半身
              sumX += x;
              count++;
            }
          }
        }
      }

      const headX = count > 0 ? sumX / count : width / 2;
      const headY = minY < height ? minY : height * 0.3;

      // 转换到canvas坐标
      return {
        x: (headX / width) * canvasWidth,
        y: (headY / height) * canvasHeight
      };
    } catch (error) {
      if (showDebugInfo) console.error('❌ 获取头部位置失败:', error);
      return { x: canvasWidth / 2, y: canvasHeight * 0.3 };
    }
  };

  // 🔥 预加载图片序列
  useEffect(() => {
    const loadImageSequence = async () => {
      try {
        if (showDebugInfo) console.log('🔥 开始加载图片序列...', { totalFrames, imageSequenceUrlTemplate });
        
        const images: HTMLImageElement[] = [];
        const loadPromises: Promise<void>[] = [];
        
        for (let i = 0; i < totalFrames; i++) {
          const imageIndex = startIndex + i;
          const imageUrl = imageSequenceUrlTemplate.replace('{index}', imageIndex.toString());
          
          const loadPromise = new Promise<void>((resolve, reject) => {
            const img = new Image();
            img.crossOrigin = crossOrigin;
            
            img.onload = () => {
              images[i] = img;
              if (showDebugInfo && i % 10 === 0) {
                console.log(`✅ 图片 ${i + 1}/${totalFrames} 加载完成: ${imageUrl}`);
              }
              resolve();
            };
            
            img.onerror = (error) => {
              console.error(`❌ 图片 ${i + 1} 加载失败: ${imageUrl}`, error);
              reject(error);
            };
            
            img.src = imageUrl;
          });
          
          loadPromises.push(loadPromise);
        }
        
        await Promise.all(loadPromises);
        
        setLoadedImages(images);
        setImagesLoaded(true);
        
        if (showDebugInfo) {
          console.log('✅ 所有图片序列加载完成:', {
            totalImages: images.length,
            firstImageSize: images[0] ? { width: images[0].width, height: images[0].height } : null
          });
        }
        
      } catch (error) {
        if (showDebugInfo) console.error('❌ 图片序列加载失败:', error);
        setImagesLoaded(false);
      }
    };

    loadImageSequence();
  }, [imageSequenceUrlTemplate, totalFrames, crossOrigin, showDebugInfo, startIndex]);

  // 🎯 当图片序列和人体数据都准备好时，继续渲染
  useEffect(() => {
    if ((imagesLoaded || !shouldShowEffect) && (bodyDataReady || !shouldShowEffect)) {
      if (showDebugInfo) console.log('🎯 所有条件都满足，继续渲染');
      continueRender(renderHandle);
    }
  }, [imagesLoaded, bodyDataReady, shouldShowEffect, renderHandle, showDebugInfo]);

  // 🔥 计算当前应该显示的图片索引
  useEffect(() => {
    if (!shouldShowEffect || !imagesLoaded || loadedImages.length === 0) return;

    // 计算基于序列帧率的当前帧
    const sequenceCurrentTime = effectProgress * actualFireDuration;
    let frameIndex = Math.floor(sequenceCurrentTime * animationFps);
    
    if (loop) {
      frameIndex = frameIndex % totalFrames;
    } else {
      frameIndex = Math.min(frameIndex, totalFrames - 1);
    }
    
    setCurrentImageIndex(frameIndex);
    
    if (showDebugInfo && frame % 30 === 0) {
      console.log('🔥 图片序列播放:', {
        effectProgress: effectProgress.toFixed(2),
        frameIndex,
        totalFrames,
        sequenceCurrentTime: sequenceCurrentTime.toFixed(2),
        actualFireDuration: actualFireDuration.toFixed(2)
      });
    }
  }, [shouldShowEffect, effectProgress, actualFireDuration, animationFps, totalFrames, loop, imagesLoaded, loadedImages.length, showDebugInfo, frame]);

  // 🔥 绘制火焰效果
  const drawFireEffect = useCallback(async (headPosition: { x: number; y: number }) => {
    if (!fireCanvasRef.current || !shouldShowEffect || !imagesLoaded || loadedImages.length === 0) return;

    try {
      const canvas = fireCanvasRef.current;
      const context = canvas.getContext('2d');
      if (!context) return;

      // 清空画布
      context.clearRect(0, 0, canvas.width, canvas.height);

      // 获取当前要显示的图片
      const currentImage = loadedImages[currentImageIndex];
      if (!currentImage) return;

      // 保存当前状态
      context.save();

      // 计算火焰位置和缩放
      const displayWidth = fireWidth || width * fireScale;
      const displayHeight = fireHeight || height * fireScale;
      
      // 确定火焰位置：优先使用自定义位置，否则使用传入的头部位置
      let finalFireX: number;
      let finalFireY: number;
      
      if (fireX !== undefined && fireY !== undefined) {
        // 使用自定义绝对位置
        finalFireX = fireX;
        finalFireY = fireY;
      } else if (fireX !== undefined) {
        // 只设置了X，Y使用头部位置
        finalFireX = fireX;
        finalFireY = headPosition.y - displayHeight * 0.8;
      } else if (fireY !== undefined) {
        // 只设置了Y，X使用头部位置
        finalFireX = headPosition.x - displayWidth / 2;
        finalFireY = fireY;
      } else {
        // 使用传入的头部位置（修改后的逻辑）
        finalFireX = headPosition.x - displayWidth / 2;
        finalFireY = headPosition.y - displayHeight * 0.8; // 火焰在头部上方
      }

      // 应用缩放和透明度
      context.globalAlpha = fireOpacity * Math.sin(effectProgress * Math.PI);
      
      // 绘制火焰图片
      context.drawImage(currentImage, finalFireX, finalFireY, displayWidth, displayHeight);

      // 恢复状态
      context.restore();
      
      if (showDebugInfo && frame % 30 === 0) {
        console.log('🔥 火焰渲染:', {
          currentImageIndex,
          firePosition: { x: finalFireX, y: finalFireY },
          fireSize: { width: displayWidth, height: displayHeight },
          headPosition,
          customPosition: { fireX, fireY },
          opacity: context.globalAlpha
        });
      }

    } catch (error) {
      if (showDebugInfo) console.error('❌ 火焰绘制失败:', error);
    }
  }, [shouldShowEffect, imagesLoaded, loadedImages, currentImageIndex, fireScale, fireOpacity, effectProgress, showDebugInfo, frame, fireWidth, fireHeight, fireX, fireY]);

  // 🎯 监听frame变化，渲染火焰特效
  useEffect(() => {
    const renderFrame = async () => {
      if (!shouldShowEffect || !bodyDataReady || !imagesLoaded || loadedImages.length === 0 || !bodySegmentationCache.current.isInitialized) {
        // 清空合成canvas
        if (compositeCanvasRef.current) {
          const compositeContext = compositeCanvasRef.current.getContext('2d');
          if (compositeContext) {
            compositeContext.clearRect(0, 0, width, height);
          }
        }
        return;
      }

      try {
        if (showDebugInfo && Math.floor(currentTime) % 1 === 0 && currentTime % 1 < 0.1) {
          console.log('🎬 渲染帧:', {
            currentTime: currentTime.toFixed(2),
            shouldShowEffect,
            effectProgress: effectProgress.toFixed(2),
            bodyDataReady,
            imagesLoaded,
            frame
          });
        }

        // 🎯 使用传入的人体轮廓数据
        const headPosition = bodySegmentationCache.current.headPosition!;
        const maskImageData = bodySegmentationCache.current.maskImageData;
        
        if (showDebugInfo && frame % 60 === 0) {
          console.log('🔥 人体数据和图片序列都可用，开始火焰特效...', { headPosition });
        }
        
        // 1. 绘制火焰效果到火焰canvas
        await drawFireEffect(headPosition);
        
        // 2. 合成最终效果到合成canvas
        if (compositeCanvasRef.current) {
          const compositeContext = compositeCanvasRef.current.getContext('2d');
          if (compositeContext) {
            // 清空合成画布
            compositeContext.clearRect(0, 0, width, height);
            
            // 2.1 先绘制火焰作为背景
            if (fireCanvasRef.current) {
              compositeContext.drawImage(fireCanvasRef.current, 0, 0, width, height);
            }
            
            // 2.2 如果有人体mask，绘制抠像后的人体（在火焰之上）
            if (maskImageData) {
              // 创建人体mask canvas
              const maskCanvas = document.createElement('canvas');
              const maskContext = maskCanvas.getContext('2d');
              if (maskContext) {
                maskCanvas.width = maskImageData.width;
                maskCanvas.height = maskImageData.height;
                maskContext.putImageData(maskImageData, 0, 0);
                
                // 如果有当前视频帧，进行人体抠像合成
                if (currentVideoFrame) {
                  // 创建人体图像canvas
                  const bodyCanvas = document.createElement('canvas');
                  const bodyContext = bodyCanvas.getContext('2d');
                  if (bodyContext) {
                    bodyCanvas.width = width;
                    bodyCanvas.height = height;
                    
                    // 绘制原始视频帧
                    bodyContext.drawImage(currentVideoFrame, 0, 0, width, height);
                    
                    // 使用mask进行合成，只保留人体部分
                    bodyContext.globalCompositeOperation = 'destination-in';
                    bodyContext.drawImage(maskCanvas, 0, 0, width, height);
                    
                    // 将抠像后的人体绘制到合成画布上
                    compositeContext.drawImage(bodyCanvas, 0, 0, width, height);
                  }
                } else {
                  // 没有视频帧时，绘制调试用的人体轮廓
                  if (showDebugInfo) {
                    compositeContext.save();
                    compositeContext.globalAlpha = 0.3;
                    compositeContext.fillStyle = '#00FF00';
                    
                    // 根据mask数据绘制人体轮廓
                    const imageData = compositeContext.createImageData(width, height);
                    const data = imageData.data;
                    
                    // 将mask数据映射到合成canvas上
                    for (let y = 0; y < height; y++) {
                      for (let x = 0; x < width; x++) {
                        const maskX = Math.floor((x / width) * maskImageData.width);
                        const maskY = Math.floor((y / height) * maskImageData.height);
                        const maskIndex = (maskY * maskImageData.width + maskX) * 4;
                        const maskAlpha = maskImageData.data[maskIndex + 3];
                        
                        if (maskAlpha > 128) {
                          const index = (y * width + x) * 4;
                          data[index] = 0;     // R
                          data[index + 1] = 255; // G
                          data[index + 2] = 0;   // B
                          data[index + 3] = 100; // A
                        }
                      }
                    }
                    
                    compositeContext.putImageData(imageData, 0, 0);
                    compositeContext.restore();
                  }
                }
              }
            }
          }
        }
        
      } catch (error: any) {
        if (showDebugInfo) console.error('❌ 火焰特效渲染失败:', error);
      }
    };

    renderFrame();
  }, [frame, shouldShowEffect, bodyDataReady, imagesLoaded, loadedImages.length, bodySegmentationCache.current.isInitialized, currentTime, effectProgress, showDebugInfo, drawFireEffect, width, height]);
  
  return (
    <>
      {children}
      {/* 🎵 Remotion音频组件 */}
      {audioSrc && enableAudio && shouldShowEffect && (
        <Audio 
          src={audioSrc}
          volume={audioVolume}          
          loop={audioLoop}
        />
      )}

      {/* 🔥 隐藏的火焰Canvas（用于图片序列渲染） */}
      <canvas 
        ref={fireCanvasRef} 
        width={width} 
        height={height}
        style={{
          position: 'absolute',
          width,
          height,
          top: 0,
          left: 0,
          opacity: 0,
          pointerEvents: 'none'
        }}
      />

      {/* 🎯 最终合成Canvas */}
      <canvas 
        ref={compositeCanvasRef} 
        width={width} 
        height={height}
        style={{
          width,
          height,
          position: 'absolute',
          top: 0,
          left: 0,
          zIndex: 1000
        }}
      />

      {/* 🎯 调试信息显示 */}
      {showDebugInfo && (
        <div style={{
          position: 'absolute',
          top: 20,
          left: 20,
          background: 'rgba(0,0,0,0.8)',
          color: 'white',
          padding: '15px',
          borderRadius: '8px',
          fontSize: '12px',
          zIndex: 25,
          fontFamily: 'monospace',
          minWidth: '300px'
        }}>
          <div>🔥 图片序列模板: {imageSequenceUrlTemplate.substring(0, 50)}...</div>
          <div>⏰ 当前时间: {currentTime.toFixed(2)}s</div>
          <div>🎯 特效时间: {startTime}s - {(startTime + actualFireDuration).toFixed(1)}s</div>
          <div>⏱️ 火焰播放时长: {actualFireDuration.toFixed(2)}s {duration ? '(自定义)' : '(自动计算)'}</div>
          <div>📊 特效状态: {shouldShowEffect ? `✅ 激活 (${(effectProgress * 100).toFixed(0)}%)` : '⏸️ 未激活'}</div>
          <div>🔥 图片序列状态: {imagesLoaded ? '✅ 已加载' : '⏳ 加载中'}</div>
          <div>🖼️ 当前帧: {currentImageIndex + 1}/{totalFrames}</div>
          <div>🎯 人体数据状态: {bodyDataReady ? '✅ 已就绪' : '⏳ 初始化中'}</div>
          <div>🎯 人体轮廓缓存: {
            bodySegmentationCache.current.isInitialized ? 
            `✅ 已初始化 (头部: ${bodySegmentationCache.current.headPosition?.x.toFixed(0)}, ${bodySegmentationCache.current.headPosition?.y.toFixed(0)})` : 
            '⏳ 未初始化'
          }</div>
          <div>🔧 Canvas尺寸: {width}x{height}</div>
          <div>🔥 火焰透明度: {fireOpacity}</div>
          <div>🔥 火焰缩放: {fireScale}</div>
          <div>📐 火焰尺寸: {fireWidth ? `${fireWidth}px` : '自动'} x {fireHeight ? `${fireHeight}px` : '自动'}</div>
          <div>📍 火焰位置: {
            fireX !== undefined || fireY !== undefined ? 
            `自定义 (${fireX ?? '自动'}, ${fireY ?? '自动'})` : 
            '使用传入头部位置'
          }</div>
          <div>🔄 循环播放: {loop ? '✅' : '❌'}</div>
          <div>📊 传入数据: 
            头部({bodySegmentationCache.current.headPosition?.x.toFixed(0) || '未知'}, {bodySegmentationCache.current.headPosition?.y.toFixed(0) || '未知'}) 
            Mask:{bodySegmentationCache.current.maskImageData ? '✅' : '❌'}
          </div>
          <div>🎬 当前帧号: {frame}</div>
          <div>🎯 合成模式: 火焰背景 + 人体前景</div>
          {audioSrc && enableAudio && (
            <>
              <div>🎵 音频文件: {audioSrc.substring(0, 30)}...</div>
              <div>🎵 音频状态: Remotion Audio</div>
              <div>🎵 播放状态: {shouldShowEffect ? '▶️ 可播放' : '⏸️ 未激活'}</div>
              <div>🔊 音频音量: {audioVolume}</div>
              <div>🔁 音频循环: {audioLoop ? '✅' : '❌'}</div>
              <div>⏰ 音频延迟: {audioDelay}s</div>
            </>
          )}
        </div>
      )}
    </>
  );
}; 

BodyFireEffect.key = 'BodyFireEffect';
BodyFireEffect.showName = '人体火焰';
BodyFireEffect.description = '人体火焰特效（带关键帧插值）';
BodyFireEffect.ranges = ['main-track'] // 作用范围
// 使用封装的智能化关键帧采样策略
BodyFireEffect.needSegmentationsData = (currentTime: number, 
  currentFrame: number, 
  fps: number
) => {
  return currentFrame === 0
};