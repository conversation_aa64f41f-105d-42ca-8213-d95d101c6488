import React, { useEffect } from 'react';
import { useCurrentFrame, spring, interpolate, useVideoConfig, Easing } from 'remotion';

export interface Spiral3Props {
  /** 动效开始帧 */
  startFrame: number;
  /** 动效持续帧数 */
  durationInFrames: number;
  /** 初始缩放比例，默认0.2 */
  startScale?: number;
  /** 螺旋半径，默认80像素 */
  spiralRadius?: number;
  /** 旋转圈数，默认1.5圈 */
  rotationRounds?: number;
  /** 动画缓动函数类型 */
  easing?: 'linear' | 'easeOut' | 'easeInOut' | 'spring';
  /** 是否启用调试模式 */
  debug?: boolean;
  /** 外部元素的ref，样式将应用到此元素上 */
  targetRef?: React.RefObject<HTMLElement>;
}

export const Spiral3 = ({
  startFrame,
  durationInFrames,
  startScale = 0.2,
  spiralRadius = 80,
  rotationRounds = 1.5,
  easing = 'easeOut',
  debug,
  targetRef
}: Spiral3Props) => {
  const frame = useCurrentFrame(); // 这是Sequence内部的相对帧数
  const { fps } = useVideoConfig();

  // 内部维护的 Spring 配置，针对复合螺旋效果优化
  const springConfig = {
    mass: 1,
    damping: 18,
    stiffness: 180,
    overshootClamping: false, // 允许超调，让螺旋更自然
  };

  // 判断是入场动效还是出场动效
  const isEnterEffect = startFrame === 0;
  const isExitEffect = startFrame > 0;

  // 计算动效的实际帧范围和当前帧
  let effectFrame = 0;
  let isInEffectRange = false;
  let isAfterEffect = false;

  if (isEnterEffect) {
    // 入场动效：从帧0开始
    isInEffectRange = frame >= 0 && frame < durationInFrames;
    isAfterEffect = frame >= durationInFrames;
    effectFrame = Math.max(0, Math.min(frame, durationInFrames));
  } else if (isExitEffect) {
    // 出场动效：从startFrame开始
    isInEffectRange = frame >= startFrame && frame < startFrame + durationInFrames;
    isAfterEffect = frame >= startFrame + durationInFrames;
    effectFrame = Math.max(0, Math.min(frame - startFrame, durationInFrames));
  }

  // 使用 spring 或 interpolate 计算动画进度
  let animationProgress = 0;

  if (isInEffectRange || isAfterEffect) {
    if (easing === 'spring') {
      // 使用 spring 动画，确保流畅
      animationProgress = spring({
        frame: effectFrame,
        fps,
        config: springConfig,
        durationInFrames,
      });
    } else {
      // 使用 interpolate 配合优化的 Easing 函数
      const easingFunction = easing === 'linear'
        ? (x: number) => x
        : easing === 'easeOut'
        ? Easing.out(Easing.ease) // 使用更流畅的 ease 函数
        : Easing.inOut(Easing.ease);

      animationProgress = interpolate(
        effectFrame,
        [0, durationInFrames],
        [0, 1],
        {
          easing: easingFunction,
          extrapolateLeft: 'clamp',
          extrapolateRight: 'clamp',
        }
      );
    }

    // 动画完成后保持在最终状态
    if (isAfterEffect) {
      animationProgress = 1;
    }
  }

  // 计算当前缩放比例、旋转角度和螺旋位移 - 使用更流畅的插值
  let currentScale = 1;
  let currentRotation = 0;
  let currentTranslateX = 0;

  if (isInEffectRange || isAfterEffect) {
    if (isEnterEffect) {
      // 入场动效：从小缩放到正常，从外圈螺旋到中心
      currentScale = interpolate(
        animationProgress,
        [0, 1],
        [startScale, 1],
        {
          extrapolateLeft: 'clamp',
          extrapolateRight: 'clamp',
          easing: Easing.out(Easing.ease)
        }
      );

      currentRotation = interpolate(
        animationProgress,
        [0, 1],
        [0, rotationRounds * 360],
        {
          extrapolateLeft: 'clamp',
          extrapolateRight: 'clamp',
          easing: Easing.out(Easing.ease)
        }
      );

      const currentRadius = interpolate(
        animationProgress,
        [0, 1],
        [spiralRadius, 0],
        {
          extrapolateLeft: 'clamp',
          extrapolateRight: 'clamp',
          easing: Easing.out(Easing.ease)
        }
      );

      currentTranslateX = currentRadius * Math.cos((currentRotation * Math.PI) / 180);
    } else if (isExitEffect) {
      // 出场动效：从正常缩放到小，从中心螺旋到外圈
      currentScale = interpolate(
        animationProgress,
        [0, 1],
        [1, startScale],
        {
          extrapolateLeft: 'clamp',
          extrapolateRight: 'clamp',
          easing: Easing.in(Easing.ease)
        }
      );

      currentRotation = interpolate(
        animationProgress,
        [0, 1],
        [0, rotationRounds * 360],
        {
          extrapolateLeft: 'clamp',
          extrapolateRight: 'clamp',
          easing: Easing.in(Easing.ease)
        }
      );

      const currentRadius = interpolate(
        animationProgress,
        [0, 1],
        [0, spiralRadius],
        {
          extrapolateLeft: 'clamp',
          extrapolateRight: 'clamp',
          easing: Easing.in(Easing.ease)
        }
      );

      currentTranslateX = currentRadius * Math.cos((currentRotation * Math.PI) / 180);
    }
  } else {
    // 动画开始前保持初始状态
    if (isEnterEffect) {
      currentScale = startScale;
      currentRotation = 0;
      currentTranslateX = spiralRadius; // 入场前在外圈
    }
  }

  // 当有 targetRef 时，将样式应用到目标元素上
  useEffect(() => {
    if (targetRef?.current) {
      const targetElement = targetRef.current;

      if (debug) {
        console.log('🌊 Spiral3 Debug:', {
          frame,
          startFrame,
          durationInFrames,
          effectFrame,
          isInEffectRange,
          isAfterEffect,
          isEnterEffect,
          isExitEffect,
          animationProgress: animationProgress.toFixed(3),
          currentScale: currentScale.toFixed(3),
          currentRotation: currentRotation.toFixed(3),
          currentTranslateX: currentTranslateX.toFixed(3),
          easing,
          status: isInEffectRange ? 'ANIMATING' : isAfterEffect ? 'COMPLETED' : 'WAITING'
        });
      }

      // 应用动效样式（在动效期间和动效结束后都需要保持状态）
      if (isInEffectRange || isAfterEffect) {
        const existingTransform = targetElement.style.transform || '';
        const spiralTransform = `scale(${currentScale}) rotate(${currentRotation}deg) translateX(${currentTranslateX}px)`;

        // 检查是否存在 scaleX(-1) 并提取保留
        const hasScaleXNegative = existingTransform.includes('scaleX(-1)');
        const scaleXTransform = hasScaleXNegative ? 'scaleX(-1)' : '';

        // 构建最终的transform：螺旋3效果 + scaleX(-1)（如果存在）
        const finalTransform = scaleXTransform
          ? `${spiralTransform} ${scaleXTransform}`
          : spiralTransform;

        Object.assign(targetElement.style, {
          transform: finalTransform,
          transformOrigin: 'center center',
          transition: 'none',
          border: debug ? '2px solid navy' : '',
        });
      }
    }
  }, [targetRef, currentScale, currentRotation, currentTranslateX, debug, frame, startFrame, effectFrame, animationProgress, durationInFrames, isInEffectRange, isAfterEffect, isEnterEffect, isExitEffect, easing]);

  // 如果有 targetRef，样式已经通过 useEffect 应用到目标元素，直接返回 null
  if (targetRef) {
    return null;
  }

  // 没有 targetRef 时，使用包装 div 的原有逻辑（保持向后兼容）
  const fallbackStyle: React.CSSProperties = {
    transform: `scale(${currentScale}) rotate(${currentRotation}deg) translateX(${currentTranslateX}px)`,
    transformOrigin: 'center center',
    transition: 'none', // 禁用CSS过渡，完全由帧控制
    border: debug ? '2px solid navy' : 'none', // 调试时显示海军蓝边框
    width: '100%',
    height: '100%',
    position: 'relative',
  };
  
  return (
    <div style={fallbackStyle}>
    </div>
  );
}; 

Spiral3.key = 'spiral3';
Spiral3.showName = '螺旋3';
Spiral3.description = '螺旋3';