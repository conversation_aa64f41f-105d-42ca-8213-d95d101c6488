import React, { useEffect } from 'react';
import { useCurrentFrame, spring, interpolate, useVideoConfig, Easing } from 'remotion';

export interface SlideLeftProps {
  /** 动效开始帧 */
  startFrame: number;
  /** 动效持续帧数 */
  durationInFrames: number;
  /** 起始位移距离（相对于容器宽度的百分比），默认100% */
  startOffset?: number;
  /** 动画缓动函数类型 */
  easing?: 'linear' | 'easeOut' | 'easeInOut' | 'spring';
  /** 是否启用调试模式 */
  debug?: boolean;
  /** 外部元素的ref，样式将应用到此元素上 */
  targetRef?: React.RefObject<HTMLElement>;
}

export const SlideLeft = ({
  startFrame,
  durationInFrames,
  startOffset = 100,
  easing = 'easeOut',
  debug,
  targetRef
}: SlideLeftProps) => {
  const frame = useCurrentFrame(); // 这是Sequence内部的相对帧数
  const { fps } = useVideoConfig();

  // 内部维护的 Spring 配置，针对滑动效果优化
  const springConfig = {
    mass: 1,
    damping: 20,
    stiffness: 200,
    overshootClamping: true,
  };

  // 计算动效的实际帧范围和当前帧（只处理入场动效）
  const isInEffectRange = frame >= startFrame && frame < startFrame + durationInFrames;
  const isAfterEffect = frame >= startFrame + durationInFrames - 1;
  const effectFrame = Math.max(0, Math.min(frame - startFrame, durationInFrames));

  // 使用 spring 或 interpolate 计算动画进度
  let animationProgress = 0;

  if (isInEffectRange || isAfterEffect) {
    if (easing === 'spring') {
      // 使用 spring 动画，确保流畅
      animationProgress = spring({
        frame: effectFrame,
        fps,
        config: springConfig,
        durationInFrames,
      });
    } else {
      // 使用 interpolate 配合优化的 Easing 函数
      const easingFunction = easing === 'linear'
        ? (x: number) => x
        : easing === 'easeOut'
        ? Easing.out(Easing.ease) // 使用更流畅的 ease 函数
        : Easing.inOut(Easing.ease);

      animationProgress = interpolate(
        effectFrame,
        [0, durationInFrames],
        [0, 1],
        {
          easing: easingFunction,
          extrapolateLeft: 'clamp',
          extrapolateRight: 'clamp',
        }
      );
    }

    // 动画完成后保持在最终状态
    if (isAfterEffect) {
      console.log('动效完成')
      animationProgress = 1;
    }
  }

  // 计算当前X位移 - 只处理入场效果
  let currentTranslateX = 0;

  if (isInEffectRange || isAfterEffect) {
    // 入场动效：从右侧滑入到正常位置，使用更流畅的插值
    currentTranslateX = Math.round(interpolate(
      animationProgress,
      [0, 1],
      [startOffset, 0],
      {
        extrapolateLeft: 'clamp',
        extrapolateRight: 'clamp',
        easing: Easing.out(Easing.ease) // 添加额外的缓动让位移更流畅
      }
    ));
  } else {
    // 动画开始前保持初始状态
    currentTranslateX = Math.round(startOffset);
  }


  // 当有 targetRef 时，将样式应用到目标元素上
  useEffect(() => {
    if (targetRef?.current) {
      const targetElement = targetRef.current;
      // 🎯 只有在动效时间范围内才应用样式

      if (debug) {
        console.log('⬅️ SlideLeft Debug:', {
          frame,
          startFrame,
          durationInFrames,
          effectFrame,
          isInEffectRange,
          isAfterEffect,
          animationProgress: animationProgress.toFixed(3),
          currentTranslateX: currentTranslateX.toFixed(3),
          easing,
          status: isInEffectRange ? 'ANIMATING' : isAfterEffect ? 'COMPLETED' : 'WAITING'
        });
      }

      if (isInEffectRange || isAfterEffect) {
        // 在动效范围内或动效完成后：应用样式
        const existingTransform = targetElement.style.transform || '';
        const slideTransform = `translateX(${currentTranslateX}%)`;

        // 检查是否存在 scaleX(-1) 并提取保留
        const hasScaleXNegative = existingTransform.includes('scaleX(-1)');
        const scaleXTransform = hasScaleXNegative ? 'scaleX(-1)' : '';

        // 构建最终的transform：滑动效果 + scaleX(-1)（如果存在）
        const finalTransform = scaleXTransform
          ? `${slideTransform} ${scaleXTransform}`
          : slideTransform;

        Object.assign(targetElement.style, {
          transform: finalTransform,
          transition: 'none',
          border: debug ? '2px solid red' : '',
        });
      }
    }
  }, [targetRef, currentTranslateX, debug, frame, startFrame, effectFrame, animationProgress, durationInFrames, isInEffectRange, isAfterEffect, easing]);

  // 如果有 targetRef，样式已经通过 useEffect 应用到目标元素，直接返回 null
  if (targetRef) {
    return null;
  }

  // 没有 targetRef 时，使用包装 div 的原有逻辑（保持向后兼容）
  const fallbackStyle: React.CSSProperties = {
    transform: `translateX(${currentTranslateX}%)`,
    transition: 'none', // 禁用CSS过渡，完全由帧控制
    border: debug ? '2px solid red' : 'none', // 调试时显示红色边框
    width: '100%',
    height: '100%',
    position: 'relative',
  };

  return (
    <div style={fallbackStyle}>
    </div>
  );
};

SlideLeft.key = 'slideLeft';
SlideLeft.showName = '向左滑动';
SlideLeft.description = '向左滑动';