import React, { useEffect } from 'react';
import { useCurrentFrame, spring, interpolate, useVideoConfig, Easing } from 'remotion';

export interface ZoomInProps {
  /** 动效开始帧 */
  startFrame: number;
  /** 动效持续帧数 */
  durationInFrames: number;
  /** 初始缩放比例，默认0.3 */
  startScale?: number;
  /** 动画缓动函数类型 */
  easing?: 'linear' | 'easeOut' | 'easeInOut' | 'spring';
  /** 是否启用调试模式 */
  debug?: boolean;
  /** 外部元素的ref，样式将应用到此元素上 */
  targetRef?: React.RefObject<HTMLElement>;
}

export const ZoomIn = ({
  startFrame,
  durationInFrames,
  startScale = 0.3,
  easing = 'easeOut',
  debug,
  targetRef
}: ZoomInProps) => {
  const frame = useCurrentFrame(); // 这是Sequence内部的相对帧数
  const { fps } = useVideoConfig();

  // 内部维护的 Spring 配置，针对缩放效果优化
  const springConfig = {
    mass: 1,
    damping: 20,
    stiffness: 200,
    overshootClamping: true,
  };

  // 判断是入场动效还是出场动效
  const isEnterEffect = startFrame === 0;
  const isExitEffect = startFrame > 0;

  // 计算动效的实际帧范围和当前帧
  let effectFrame = 0;
  let isInEffectRange = false;
  let isAfterEffect = false;

  if (isEnterEffect) {
    // 入场动效：从帧0开始
    isInEffectRange = frame >= 0 && frame < durationInFrames;
    isAfterEffect = frame >= durationInFrames;
    effectFrame = Math.max(0, Math.min(frame, durationInFrames));
  } else if (isExitEffect) {
    // 出场动效：从startFrame开始
    isInEffectRange = frame >= startFrame && frame < startFrame + durationInFrames;
    isAfterEffect = frame >= startFrame + durationInFrames;
    effectFrame = Math.max(0, Math.min(frame - startFrame, durationInFrames));
  }

  // 使用 spring 或 interpolate 计算动画进度
  let animationProgress = 0;

  if (isInEffectRange || isAfterEffect) {
    if (easing === 'spring') {
      // 使用 spring 动画，确保流畅
      animationProgress = spring({
        frame: effectFrame,
        fps,
        config: springConfig,
        durationInFrames,
      });
    } else {
      // 使用 interpolate 配合优化的 Easing 函数
      const easingFunction = easing === 'linear'
        ? (x: number) => x
        : easing === 'easeOut'
        ? Easing.out(Easing.ease) // 使用更流畅的 ease 函数
        : Easing.inOut(Easing.ease);

      animationProgress = interpolate(
        effectFrame,
        [0, durationInFrames],
        [0, 1],
        {
          easing: easingFunction,
          extrapolateLeft: 'clamp',
          extrapolateRight: 'clamp',
        }
      );
    }

    // 动画完成后保持在最终状态
    if (isAfterEffect) {
      animationProgress = 1;
    }
  }

  // 计算当前缩放比例 - 简化逻辑，专注于入场效果
  let currentScale = 1;

  if (isInEffectRange || isAfterEffect) {
    if (isEnterEffect) {
      // 入场动效：从小尺寸放大到正常尺寸，使用更流畅的插值
      currentScale = interpolate(
        animationProgress,
        [0, 1],
        [startScale, 1],
        {
          extrapolateLeft: 'clamp',
          extrapolateRight: 'clamp',
          easing: Easing.out(Easing.ease) // 添加额外的缓动让缩放更流畅
        }
      );
    } else if (isExitEffect) {
      // 出场动效：从正常尺寸缩小到小尺寸
      currentScale = interpolate(
        animationProgress,
        [0, 1],
        [1, startScale],
        {
          extrapolateLeft: 'clamp',
          extrapolateRight: 'clamp',
          easing: Easing.in(Easing.ease)
        }
      );
    }
  } else {
    // 动画开始前保持初始状态
    if (isEnterEffect) {
      currentScale = startScale;
    }
  }

  // 当有 targetRef 时，将样式应用到目标元素上
  useEffect(() => {
    if (targetRef?.current) {
      const targetElement = targetRef.current;
      // 🎯 只有在动效时间范围内才应用样式

      if (debug) {
        console.log('🔍 ZoomIn Debug:', {
          frame,
          startFrame,
          durationInFrames,
          effectFrame,
          isInEffectRange,
          isAfterEffect,
          isEnterEffect,
          isExitEffect,
          animationProgress: animationProgress.toFixed(3),
          currentScale: currentScale.toFixed(3),
          easing,
          status: isInEffectRange ? 'ANIMATING' : isAfterEffect ? 'COMPLETED' : 'WAITING'
        });
      }

      if (isInEffectRange || isAfterEffect) {
        // 在动效范围内或动效完成后：应用样式
        const existingTransform = targetElement.style.transform || '';
        const scaleTransform = `scale(${currentScale})`;

        // 检查是否存在 scaleX(-1) 并提取保留
        const hasScaleXNegative = existingTransform.includes('scaleX(-1)');
        const scaleXTransform = hasScaleXNegative ? 'scaleX(-1)' : '';

        // 构建最终的transform：缩放效果 + scaleX(-1)（如果存在）
        const finalTransform = scaleXTransform
          ? `${scaleTransform} ${scaleXTransform}`
          : scaleTransform;

        Object.assign(targetElement.style, {
          transform: finalTransform,
          transformOrigin: 'center center',
          transition: 'none',
          border: debug ? '2px solid green' : '',
        });
      }
    }
  }, [targetRef, currentScale, debug, frame, startFrame, effectFrame, animationProgress, durationInFrames, isInEffectRange, isAfterEffect, isEnterEffect, isExitEffect, easing]);

  // 如果有 targetRef，样式已经通过 useEffect 应用到目标元素，直接返回 null
  if (targetRef) {
    return null;
  }

  // 没有 targetRef 时，使用包装 div 的原有逻辑（保持向后兼容）
  const fallbackStyle: React.CSSProperties = {
    transform: `scale(${currentScale})`,
    transformOrigin: 'center center',
    transition: 'none', // 禁用CSS过渡，完全由帧控制
    border: debug ? '2px solid green' : 'none', // 调试时显示绿色边框
    width: '100%',
    height: '100%',
    position: 'relative',
  };
  
  return (
    <div style={fallbackStyle}>
    </div>
  );
}; 

ZoomIn.key = 'zoomIn';
ZoomIn.showName = '缩小';
ZoomIn.description = '缩小';