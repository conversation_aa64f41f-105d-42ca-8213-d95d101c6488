import React, { useEffect } from 'react';
import { useCurrentFrame, spring, interpolate, useVideoConfig, Easing } from 'remotion';

export interface FadeInProps {
  /** 动效开始帧 */
  startFrame: number;
  /** 动效持续帧数 */
  durationInFrames: number;
  /** 初始透明度，默认0.4 */
  startOpacity?: number;
  /** 动画缓动函数类型 */
  easing?: 'linear' | 'easeOut' | 'easeInOut' | 'spring';
  /** 是否启用调试模式 */
  debug?: boolean;
  /** 外部元素的ref，样式将应用到此元素上 */
  targetRef?: React.RefObject<HTMLElement>;
}

export const FadeIn = ({
  startFrame,
  durationInFrames,
  startOpacity = 0.4,
  easing = 'easeOut',
  debug,
  targetRef
}: FadeInProps) => {
  const frame = useCurrentFrame(); // 这是Sequence内部的相对帧数
  const { fps } = useVideoConfig();

  // 内部维护的 Spring 配置，针对透明度变化优化
  const springConfig = {
    mass: 1,
    damping: 20,
    stiffness: 200,
    overshootClamping: true,
  };

  // 判断是入场动效还是出场动效
  const isEnterEffect = startFrame === 0;
  const isExitEffect = startFrame > 0;

  // 计算动效的实际帧范围和当前帧
  let effectFrame = 0;
  let isInEffectRange = false;
  let isAfterEffect = false;

  if (isEnterEffect) {
    // 入场动效：从帧0开始
    isInEffectRange = frame >= 0 && frame < durationInFrames;
    isAfterEffect = frame >= durationInFrames;
    effectFrame = Math.max(0, Math.min(frame, durationInFrames));
  } else if (isExitEffect) {
    // 出场动效：从startFrame开始
    isInEffectRange = frame >= startFrame && frame < startFrame + durationInFrames;
    isAfterEffect = frame >= startFrame + durationInFrames;
    effectFrame = Math.max(0, Math.min(frame - startFrame, durationInFrames));
  }

  // 使用 spring 或 interpolate 计算动画进度
  let animationProgress = 0;

  if (isInEffectRange || isAfterEffect) {
    if (easing === 'spring') {
      // 使用 spring 动画，确保流畅
      animationProgress = spring({
        frame: effectFrame,
        fps,
        config: springConfig,
        durationInFrames,
      });
    } else {
      // 使用 interpolate 配合优化的 Easing 函数
      const easingFunction = easing === 'linear'
        ? (x: number) => x
        : easing === 'easeOut'
        ? Easing.out(Easing.ease) // 使用更流畅的 ease 函数
        : Easing.inOut(Easing.ease);

      animationProgress = interpolate(
        effectFrame,
        [0, durationInFrames],
        [0, 1],
        {
          easing: easingFunction,
          extrapolateLeft: 'clamp',
          extrapolateRight: 'clamp',
        }
      );
    }

    // 动画完成后保持在最终状态
    if (isAfterEffect) {
      animationProgress = 1;
    }
  }

  // 计算当前透明度 - 入场动效：从startOpacity渐变到1
  let currentOpacity = 1;

  if (isInEffectRange || isAfterEffect) {
    if (isEnterEffect) {
      // 入场动效：从初始透明度渐变到完全不透明
      currentOpacity = interpolate(
        animationProgress,
        [0, 1],
        [startOpacity, 1],
        {
          extrapolateLeft: 'clamp',
          extrapolateRight: 'clamp',
          easing: Easing.out(Easing.ease) // 添加额外的缓动让透明度变化更流畅
        }
      );
    } else if (isExitEffect) {
      // 出场动效：从完全不透明渐变到初始透明度
      currentOpacity = interpolate(
        animationProgress,
        [0, 1],
        [1, startOpacity],
        {
          extrapolateLeft: 'clamp',
          extrapolateRight: 'clamp',
          easing: Easing.in(Easing.ease)
        }
      );
    }
  } else {
    // 动画开始前保持初始状态
    if (isEnterEffect) {
      currentOpacity = startOpacity;
    }
  }

  // 当有 targetRef 时，将样式应用到目标元素上
  useEffect(() => {
    if (targetRef?.current) {
      const targetElement = targetRef.current;

      if (debug) {
        console.log('🌅 FadeIn Debug:', {
          frame,
          startFrame,
          durationInFrames,
          effectFrame,
          isInEffectRange,
          isAfterEffect,
          isEnterEffect,
          isExitEffect,
          animationProgress: animationProgress.toFixed(3),
          currentOpacity: currentOpacity.toFixed(6),
          easing,
          status: isInEffectRange ? 'ANIMATING' : isAfterEffect ? 'COMPLETED' : 'WAITING'
        });
      }

      // 应用动效样式（在动效期间和动效结束后都需要保持状态）
      if (isInEffectRange || isAfterEffect) {
        Object.assign(targetElement.style, {
          opacity: currentOpacity.toString(),
          transition: 'none',
          border: debug ? '2px solid cyan' : '',
        });
      }
    }
  }, [targetRef, currentOpacity, debug, frame, startFrame, effectFrame, animationProgress, durationInFrames, isInEffectRange, isAfterEffect, isEnterEffect, isExitEffect, easing]);


  // 如果有 targetRef，样式已经通过 useEffect 应用到目标元素，直接返回 null
  if (targetRef) {
    return null;
  }

  // 没有 targetRef 时，使用包装 div 的原有逻辑（保持向后兼容）
  const fallbackStyle: React.CSSProperties = {
    opacity: currentOpacity,
    transition: 'none', // 禁用CSS过渡，完全由帧控制
    border: debug ? '2px solid cyan' : 'none', // 调试时显示青色边框
    width: '100%',
    height: '100%',
    position: 'relative',
  };

  return (
    <div style={fallbackStyle}>
    </div>
  );
}; 

FadeIn.key = 'fadeIn';
FadeIn.showName = '渐入';
FadeIn.description = '渐入';