const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');
const ComponentParser = require('./component-parser');

class PreviewGenerator {
  constructor(srcPath) {
    this.srcPath = srcPath;
    this.parser = new ComponentParser(srcPath);
    this.previewDir = path.join(__dirname, '../previews');
    this.gifDir = path.join(this.previewDir, 'gifs');
    this.compositionsDir = path.join(this.previewDir, 'compositions');
    
    this.ensureDirectories();
  }

  // 确保目录存在
  ensureDirectories() {
    [this.previewDir, this.gifDir, this.compositionsDir].forEach(dir => {
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
      }
    });
  }

  // 生成单个组件的预览 Composition
  generateComponentComposition(component, config) {
    const { name, props, type } = component;
    const { sampleText, duration, dimensions } = config;

    // 根据组件类型生成不同的预览代码
    let componentJSX = '';
    let additionalProps = '';

    // 根据组件属性动态生成 props
    if (props.text) {
      additionalProps += `        text="${sampleText}"\n`;
    }
    if (props.style) {
      additionalProps += `        style={{\n          fontSize: 36,\n          fontWeight: 'bold',\n          color: '#fff',\n          textAlign: 'center'\n        }}\n`;
    }
    if (props.durationFrames) {
      additionalProps += `        durationFrames={${duration}}\n`;
    }
    if (props.speed && type === 'textLoop') {
      additionalProps += `        speed={1}\n`;
    }

    // 特殊组件类型的额外配置
    if (type === 'sticker') {
      additionalProps = `        style={{\n          width: 200,\n          height: 200\n        }}\n`;
    }

    componentJSX = `      <${name}\n${additionalProps}      />`;

    const compositionContent = `
import React from 'react';
import { Composition } from 'remotion';
import { ${name} } from '../../src';

// ${name} 预览组件
const ${name}Preview: React.FC = () => {
  return (
    <div style={{
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      width: '100%',
      height: '100%',
      backgroundColor: '#000',
      color: '#fff',
      padding: 20
    }}>
${componentJSX}
    </div>
  );
};

// 导出 Composition
export const ${name}Composition: React.FC = () => (
  <Composition
    id="${name}"
    component={${name}Preview}
    durationInFrames={${duration}}
    fps={30}
    width={${dimensions.width}}
    height={${dimensions.height}}
  />
);
`;

    const filePath = path.join(this.compositionsDir, `${name}.tsx`);
    fs.writeFileSync(filePath, compositionContent);
    console.log(`✅ 生成预览文件: ${name}.tsx`);
    
    return filePath;
  }

  // 生成 Remotion 配置文件
  generateRemotionConfig() {
    const configContent = `
import { Config } from '@remotion/cli/config';

// 设置渲染配置
Config.setImageFormat('jpeg');
Config.setPixelFormat('yuv420p');
Config.setCodec('h264');
Config.setConcurrency(1); // 降低并发以避免内存问题
Config.setQuality(8);

// GIF 特定配置
Config.setImageSequence(false);

export default {};
`;

    fs.writeFileSync(path.join(this.previewDir, 'remotion.config.ts'), configContent);
  }

  // 生成主索引文件
  generateMainIndex(componentList) {
    const imports = componentList.map(comp => 
      `import { ${comp.name}Composition } from './compositions/${comp.name}';`
    ).join('\n');

    const compositions = componentList.map(comp => 
      `      <${comp.name}Composition />`
    ).join('\n');

    const indexContent = `
import React from 'react';
import { registerRoot } from 'remotion';
${imports}

// Remotion 根组件
const RemotionRoot: React.FC = () => {
  return (
    <>
${compositions}
    </>
  );
};

registerRoot(RemotionRoot);
`;

    fs.writeFileSync(path.join(this.previewDir, 'index.tsx'), indexContent);
  }

  // 生成 package.json
  generatePackageJson() {
    const packageContent = {
      name: "remotion-previews",
      version: "1.0.0",
      description: "Component previews for remotion-wanqiang",
      scripts: {
        "render": "remotion render",
        "preview": "remotion preview",
        "render-all": "node ../scripts/render-all-gifs.js"
      },
      dependencies: {
        "remotion": "4.0.306",
        "react": "^18.0.0",
        "react-dom": "^18.0.0",
        "remotion-wanqiang": "file:.."
      },
      devDependencies: {
        "@types/react": "^18.0.0",
        "@types/react-dom": "^18.0.0",
        "typescript": "^5.0.0"
      }
    };

    fs.writeFileSync(
      path.join(this.previewDir, 'package.json'), 
      JSON.stringify(packageContent, null, 2)
    );
  }

  // 生成 TypeScript 配置
  generateTsConfig() {
    const tsConfigContent = {
      compilerOptions: {
        target: "es2018",
        lib: ["dom", "dom.iterable", "esnext"],
        allowJs: true,
        skipLibCheck: true,
        esModuleInterop: true,
        allowSyntheticDefaultImports: true,
        strict: true,
        forceConsistentCasingInFileNames: true,
        module: "esnext",
        moduleResolution: "node",
        resolveJsonModule: true,
        isolatedModules: true,
        noEmit: true,
        jsx: "react-jsx"
      },
      include: [
        "**/*"
      ]
    };

    fs.writeFileSync(
      path.join(this.previewDir, 'tsconfig.json'),
      JSON.stringify(tsConfigContent, null, 2)
    );
  }

  // 批量渲染 GIF
  async renderGifs(componentList, options = {}) {
    const { 
      concurrent = false, 
      quality = 8,
      scale = 1,
      frameRate = 30
    } = options;

    console.log('🎬 开始批量渲染 GIF...');
    console.log(`📊 共需要渲染 ${componentList.length} 个组件`);

    // 安装依赖（如果需要）
    try {
      console.log('📦 检查依赖...');
      execSync('npm install', { 
        cwd: this.previewDir,
        stdio: 'inherit'
      });
    } catch (error) {
      console.warn('⚠️ 依赖安装可能失败，继续渲染...');
    }

    const renderPromises = [];
    const renderResults = [];

    for (const [index, component] of componentList.entries()) {
      const renderTask = async () => {
        try {
          console.log(`📹 [${index + 1}/${componentList.length}] 渲染 ${component.name}...`);
          
          const outputPath = path.join(this.gifDir, `${component.name}.gif`);
          
          // 构建渲染命令
          const renderCommand = [
            'npx remotion render',
            component.name,
            `"${outputPath}"`,
            '--image-format=gif',
            `--quality=${quality}`,
            `--scale=${scale}`,
            `--frame-range=0-${component.config.duration - 1}`,
            '--overwrite'
          ].join(' ');

          console.log(`🔧 执行命令: ${renderCommand}`);
          
          execSync(renderCommand, { 
            cwd: this.previewDir,
            stdio: 'pipe'
          });
          
          console.log(`✅ ${component.name} 渲染完成`);
          return { name: component.name, success: true, path: outputPath };
        } catch (error) {
          console.error(`❌ ${component.name} 渲染失败:`, error.message);
          return { name: component.name, success: false, error: error.message };
        }
      };

      if (concurrent) {
        renderPromises.push(renderTask());
      } else {
        const result = await renderTask();
        renderResults.push(result);
      }
    }

    // 如果是并发模式，等待所有任务完成
    if (concurrent) {
      const results = await Promise.all(renderPromises);
      renderResults.push(...results);
    }

    return renderResults;
  }

  // 生成预览页面
  generatePreviewPage(componentList, renderResults) {
    const successfulRenders = renderResults.filter(r => r.success);
    
    let previewHTML = `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Remotion 万强组件库 - 预览</title>
    <style>
        body {
            font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .header {
            text-align: center;
            color: white;
            margin-bottom: 40px;
        }
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        .stats {
            display: flex;
            justify-content: center;
            gap: 30px;
            margin-bottom: 40px;
        }
        .stat-item {
            background: rgba(255,255,255,0.1);
            padding: 15px 25px;
            border-radius: 10px;
            color: white;
            text-align: center;
            backdrop-filter: blur(10px);
        }
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            display: block;
        }
        .components-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 20px;
            max-width: 1400px;
            margin: 0 auto;
        }
        .component-card {
            background: rgba(255,255,255,0.95);
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            transition: transform 0.3s ease;
        }
        .component-card:hover {
            transform: translateY(-5px);
        }
        .component-title {
            font-size: 1.2em;
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .component-category {
            background: #667eea;
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8em;
        }
        .gif-container {
            text-align: center;
            margin: 15px 0;
            background: #000;
            border-radius: 10px;
            overflow: hidden;
        }
        .gif-container img {
            width: 100%;
            height: auto;
            display: block;
        }
        .component-info {
            font-size: 0.9em;
            color: #666;
            margin-top: 10px;
        }
        .failed-render {
            background: rgba(244,67,54,0.1);
            border: 1px solid rgba(244,67,54,0.3);
        }
        .failed-render .component-title {
            color: #d32f2f;
        }
        .error-message {
            color: #d32f2f;
            font-size: 0.85em;
            margin-top: 10px;
            padding: 10px;
            background: rgba(244,67,54,0.05);
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🎬 Remotion 万强组件库</h1>
        <p>自动生成的组件预览</p>
    </div>
    
    <div class="stats">
        <div class="stat-item">
            <span class="stat-number">${componentList.length}</span>
            <span>总组件数</span>
        </div>
        <div class="stat-item">
            <span class="stat-number">${successfulRenders.length}</span>
            <span>成功渲染</span>
        </div>
        <div class="stat-item">
            <span class="stat-number">${renderResults.length - successfulRenders.length}</span>
            <span>渲染失败</span>
        </div>
    </div>

    <div class="components-grid">
`;

    // 为每个组件生成卡片
    renderResults.forEach(result => {
      const component = componentList.find(c => c.name === result.name);
      if (!component) return;

      const cardClass = result.success ? 'component-card' : 'component-card failed-render';
      const gifPath = result.success ? `./gifs/${result.name}.gif` : '';

      previewHTML += `
        <div class="${cardClass}">
            <div class="component-title">
                ${result.name}
                <span class="component-category">${component.category}</span>
            </div>
            ${result.success ? `
                <div class="gif-container">
                    <img src="${gifPath}" alt="${result.name} 预览" loading="lazy">
                </div>
            ` : `
                <div class="error-message">
                    渲染失败: ${result.error || '未知错误'}
                </div>
            `}
            <div class="component-info">
                <div><strong>类型:</strong> ${component.type}</div>
                <div><strong>动画:</strong> ${component.hasAnimation ? '是' : '否'}</div>
                ${component.description ? `<div><strong>描述:</strong> ${component.description}</div>` : ''}
            </div>
        </div>
      `;
    });

    previewHTML += `
    </div>
    <div style="text-align: center; margin-top: 40px; color: white;">
        <p>生成时间: ${new Date().toLocaleString('zh-CN')}</p>
    </div>
</body>
</html>
`;

    fs.writeFileSync(path.join(this.previewDir, 'index.html'), previewHTML);
    console.log(`🌐 预览页面已生成: ${path.join(this.previewDir, 'index.html')}`);
  }

  // 主执行函数
  async main(options = {}) {
    console.log('🚀 开始生成组件预览...');
    
    // 解析所有组件
    console.log('🔍 解析组件...');
    const components = this.parser.parseAllComponents();
    const config = this.parser.generateComponentConfig(components);
    
    console.log(`📊 找到 ${components.length} 个组件`);
    Object.keys(config).forEach(groupKey => {
      const group = config[groupKey];
      console.log(`  ${group.title}: ${group.components.length} 个组件`);
    });

    if (components.length === 0) {
      console.log('⚠️ 没有找到可渲染的组件');
      return;
    }

    // 生成必要文件
    console.log('📝 生成配置文件...');
    this.generateRemotionConfig();
    this.generatePackageJson();
    this.generateTsConfig();

    // 生成组件预览文件
    console.log('📄 生成组件预览文件...');
    const componentList = [];
    
    Object.keys(config).forEach(groupKey => {
      const group = config[groupKey];
      group.components.forEach(comp => {
        this.generateComponentComposition(comp, group);
        componentList.push({
          name: comp.name,
          category: group.title,
          type: groupKey,
          hasAnimation: comp.hasAnimation,
          description: comp.description,
          config: group
        });
      });
    });

    // 生成主索引文件
    this.generateMainIndex(componentList);

    // 渲染 GIF
    const renderResults = await this.renderGifs(componentList, options);

    // 生成预览页面
    this.generatePreviewPage(componentList, renderResults);

    console.log('🎉 批量生成完成！');
    console.log(`📂 输出目录: ${this.previewDir}`);
    console.log(`🌐 预览页面: ${path.join(this.previewDir, 'index.html')}`);
    
    return { componentList, renderResults };
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  const srcPath = path.join(__dirname, '../src');
  const generator = new PreviewGenerator(srcPath);
  
  // 解析命令行参数
  const args = process.argv.slice(2);
  const options = {
    concurrent: args.includes('--concurrent'),
    quality: parseInt(args.find(arg => arg.startsWith('--quality='))?.split('=')[1]) || 8,
    scale: parseFloat(args.find(arg => arg.startsWith('--scale='))?.split('=')[1]) || 1
  };

  generator.main(options).catch(console.error);
}

module.exports = PreviewGenerator; 