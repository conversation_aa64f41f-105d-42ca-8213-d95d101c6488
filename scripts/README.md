# 🎬 Remotion 万强组件库 - GIF 预览生成器

这是一个自动化工具，用于为 Remotion 万强组件库生成 GIF 预览图。

## 🚀 快速开始

### 1. 一键运行
```bash
# 生成所有组件的 GIF 预览
node scripts/run.js
```

### 2. 仅解析组件信息
```bash
# 查看所有可用组件
node scripts/run.js --parse-only
```

### 3. 预览将要执行的操作
```bash
# 干运行模式
node scripts/run.js --dry-run
```

## 📋 命令行选项

| 选项 | 说明 | 示例 |
|------|------|------|
| `--help` | 显示帮助信息 | `node scripts/run.js --help` |
| `--parse-only` | 仅解析组件，不渲染 GIF | `node scripts/run.js --parse-only` |
| `--concurrent` | 并发渲染（更快但占用更多资源） | `node scripts/run.js --concurrent` |
| `--quality=<1-10>` | 设置 GIF 质量（默认8） | `node scripts/run.js --quality=6` |
| `--scale=<0.1-2.0>` | 设置渲染缩放比例（默认1） | `node scripts/run.js --scale=0.5` |
| `--dry-run` | 预览操作，不实际执行 | `node scripts/run.js --dry-run` |

## 📁 输出结构

运行后会在项目根目录生成 `previews` 文件夹：

```
previews/
├── gifs/                    # 生成的 GIF 文件
│   ├── FadeInEffect.gif
│   ├── SlideInEffect.gif
│   └── ...
├── compositions/            # 组件预览源文件
│   ├── FadeInEffect.tsx
│   ├── SlideInEffect.tsx
│   └── ...
├── index.tsx               # Remotion 主入口
├── index.html              # 预览页面
├── package.json            # 依赖配置
├── tsconfig.json           # TypeScript 配置
└── remotion.config.ts      # Remotion 配置
```

## 🎯 支持的组件类型

| 组件类型 | 说明 | 示例时长 |
|----------|------|----------|
| 🎭 **文本入场特效** | FadeInEffect, SlideInEffect 等 | 4秒 |
| 🎪 **文本出场特效** | FadeOutEffect, SlideOutEffect 等 | 4秒 |
| 🔄 **文本循环特效** | MachineCodeEffect 等 | 6秒 |
| 💎 **花字组件** | MultiShadowText, DiamondText 等 | 3秒 |
| ⚡ **动态文本特效** | 各种动态效果 | 5秒 |
| 🎪 **贴纸组件** | 各种贴纸效果 | 3秒 |
| 🌟 **场景特效** | 入场和出场场景效果 | 4秒 |

## 🔧 高级用法

### 并发渲染（推荐用于强大的机器）
```bash
node scripts/run.js --concurrent --quality=8
```

### 低质量快速预览
```bash
node scripts/run.js --quality=4 --scale=0.5
```

### 高质量渲染
```bash
node scripts/run.js --quality=10 --scale=1.5
```

## 🎨 自定义配置

你可以修改 `scripts/component-parser.js` 中的配置来调整：

- **示例文本**：为不同类型组件设置不同的示例文字
- **动画时长**：调整各类型组件的动画持续时间
- **画面尺寸**：设置不同组件的渲染尺寸

```javascript
// 自定义示例文本
getSampleText(type) {
  const textMap = {
    'textIn': '你的自定义文本',
    'flower': '万强文字特效',
    // ...
  };
  return textMap[type] || '组件预览';
}
```

## 🐛 故障排除

### 常见问题

**1. 组件解析失败**
```bash
# 检查组件是否正确导出
node scripts/run.js --parse-only
```

**2. 渲染失败**
```bash
# 检查系统资源，尝试非并发模式
node scripts/run.js --quality=6
```

**3. 内存不足**
```bash
# 降低质量和缩放比例
node scripts/run.js --quality=4 --scale=0.5
```

### 系统要求

- **Node.js**: >= 16.0.0
- **内存**: 建议 8GB+ RAM
- **磁盘空间**: 预留 1GB+ 用于输出文件
- **FFmpeg**: 自动通过 Remotion 安装

## 📊 性能优化

### 渲染速度对比

| 模式 | 时间 | 资源占用 | 推荐场景 |
|------|------|----------|----------|
| 顺序渲染 | 慢 | 低 | 低配置机器 |
| 并发渲染 | 快 | 高 | 高配置机器 |

### 质量设置建议

| 质量等级 | 文件大小 | 清晰度 | 用途 |
|----------|----------|--------|------|
| 1-3 | 小 | 低 | 快速预览 |
| 4-6 | 中 | 中 | 一般预览 |
| 7-8 | 大 | 高 | 正式展示 |
| 9-10 | 很大 | 很高 | 高质量展示 |

## 🎉 预览页面功能

生成的 `previews/index.html` 包含：

- 📊 **统计信息**：组件总数、成功/失败渲染数量
- 🎬 **动态预览**：所有成功渲染的 GIF 预览
- 🏷️ **分类标签**：按组件类型分类显示
- 📱 **响应式设计**：支持各种屏幕尺寸
- 🎨 **美观界面**：现代化的渐变背景和卡片设计

## 🤝 贡献

如果你想扩展这个工具：

1. 修改 `component-parser.js` 添加新的组件类型支持
2. 调整 `generate-previews-enhanced.js` 的渲染逻辑
3. 优化 `run.js` 的命令行界面

## 📝 注意事项

- 首次运行可能需要较长时间来安装依赖
- 大型项目建议使用 `--concurrent` 选项提高速度  
- 生成的 GIF 文件可能较大，注意磁盘空间
- 如果遇到组件导入错误，请检查组件的导出方式是否正确 