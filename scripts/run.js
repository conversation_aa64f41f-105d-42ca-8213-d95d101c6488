#!/usr/bin/env node

const PreviewGenerator = require('./generate-previews-enhanced');
const path = require('path');

// 显示使用说明
function showUsage() {
  console.log(`
🎬 Remotion 万强组件库 - GIF 预览生成器
===========================================

用法:
  node scripts/run.js [选项]

选项:
  --help              显示此帮助信息
  --parse-only        仅解析组件，不渲染 GIF
  --concurrent        并发渲染（更快但占用更多资源）
  --quality=<1-10>    GIF 质量，默认为 8
  --scale=<0.1-2.0>   渲染缩放比例，默认为 1
  --dry-run           预览将要执行的操作，不实际运行

示例:
  node scripts/run.js                    # 默认渲染所有组件
  node scripts/run.js --concurrent       # 并发渲染，速度更快
  node scripts/run.js --quality=6        # 设置 GIF 质量为 6
  node scripts/run.js --parse-only       # 仅解析组件信息
  node scripts/run.js --dry-run          # 预览操作

输出:
  📂 previews/                 # 预览目录
  ├── gifs/                   # 生成的 GIF 文件
  ├── compositions/           # 组件预览文件
  ├── index.html             # 预览页面
  └── remotion.config.ts     # Remotion 配置

`);
}

// 解析命令行参数
function parseArgs() {
  const args = process.argv.slice(2);
  
  return {
    help: args.includes('--help') || args.includes('-h'),
    parseOnly: args.includes('--parse-only'),
    concurrent: args.includes('--concurrent'),
    dryRun: args.includes('--dry-run'),
    quality: parseInt(args.find(arg => arg.startsWith('--quality='))?.split('=')[1]) || 8,
    scale: parseFloat(args.find(arg => arg.startsWith('--scale='))?.split('=')[1]) || 1
  };
}

// 主函数
async function main() {
  const options = parseArgs();
  
  if (options.help) {
    showUsage();
    return;
  }
  
  console.log('🚀 Remotion 万强组件库 - GIF 预览生成器');
  console.log('==========================================\n');
  
  const srcPath = path.join(__dirname, '../src');
  const generator = new PreviewGenerator(srcPath);
  
  if (options.parseOnly) {
    console.log('🔍 仅解析组件信息...');
    const components = generator.parser.parseAllComponents();
    const config = generator.parser.generateComponentConfig(components);
    
    console.log(`\n📊 解析结果:`);
    console.log(`总共找到 ${components.length} 个组件\n`);
    
    Object.keys(config).forEach(groupKey => {
      const group = config[groupKey];
      console.log(`📁 ${group.title}:`);
      group.components.forEach(comp => {
        console.log(`  ├── ${comp.name} ${comp.hasAnimation ? '🎬' : '📄'}`);
      });
      console.log('');
    });
    
    return;
  }
  
  if (options.dryRun) {
    console.log('🔍 预览模式 - 将要执行的操作:');
    console.log(`  ✅ 解析所有组件`);
    console.log(`  ✅ 生成 Remotion 配置文件`);
    console.log(`  ✅ 生成组件预览文件`);
    console.log(`  ✅ 批量渲染 GIF (质量: ${options.quality}, 缩放: ${options.scale})`);
    console.log(`  ✅ 生成预览页面`);
    console.log(`  ${options.concurrent ? '⚡' : '🐌'} 渲染模式: ${options.concurrent ? '并发' : '顺序'}`);
    console.log('\n使用 --help 查看更多选项');
    return;
  }
  
  try {
    const result = await generator.main({
      concurrent: options.concurrent,
      quality: options.quality,
      scale: options.scale
    });
    
    console.log('\n✨ 生成完成统计:');
    console.log(`📊 总组件数: ${result.componentList.length}`);
    console.log(`✅ 成功渲染: ${result.renderResults.filter(r => r.success).length}`);
    console.log(`❌ 渲染失败: ${result.renderResults.filter(r => !r.success).length}`);
    
    if (result.renderResults.some(r => !r.success)) {
      console.log('\n❌ 失败的组件:');
      result.renderResults.filter(r => !r.success).forEach(r => {
        console.log(`  • ${r.name}: ${r.error}`);
      });
    }
    
    console.log(`\n🌐 预览页面: file://${path.resolve('./previews/index.html')}`);
    
  } catch (error) {
    console.error('❌ 生成过程中发生错误:', error.message);
    console.error('\n🔧 可能的解决方案:');
    console.error('  1. 检查 src 目录中的组件文件是否正确');
    console.error('  2. 确保所有依赖已正确安装');
    console.error('  3. 尝试使用 --parse-only 检查组件解析是否正常');
    console.error('  4. 检查系统内存是否充足');
    process.exit(1);
  }
}

// 运行主函数
main().catch(error => {
  console.error('💥 未处理的错误:', error);
  process.exit(1);
}); 