const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// 组件配置
const componentGroups = {
  FlowerTextComponents: {
    title: '花字组件',
    sampleText: '万强文字特效',
    duration: 90, // 3秒 @30fps
    dimensions: { width: 600, height: 200 }
  },
  TextLoopEffectsComponents: {
    title: '文本循环特效',
    sampleText: '循环动画效果',
    duration: 180, // 6秒 @30fps
    dimensions: { width: 600, height: 200 }
  },
  TextInEffectsComponents: {
    title: '文本入场特效',
    sampleText: '入场动画效果',
    duration: 120, // 4秒 @30fps
    dimensions: { width: 600, height: 200 }
  },
  TextOutEffectsComponents: {
    title: '文本出场特效',
    sampleText: '出场动画效果',
    duration: 120, // 4秒 @30fps
    dimensions: { width: 600, height: 200 }
  },
  DynamicSectionTextEffectsComponents: {
    title: '动态文本特效',
    sampleText: '动态文本效果',
    duration: 150, // 5秒 @30fps
    dimensions: { width: 600, height: 200 }
  },
  StickerComponents: {
    title: '贴纸组件',
    sampleText: '贴纸效果',
    duration: 90, // 3秒 @30fps
    dimensions: { width: 400, height: 400 }
  },
  SceneEffectsComponents: {
    title: '场景特效',
    sampleText: '场景动画',
    duration: 120, // 4秒 @30fps
    dimensions: { width: 800, height: 450 }
  }
};

// 创建预览目录
const previewDir = path.join(__dirname, '../previews');
const gifDir = path.join(previewDir, 'gifs');
const compositionsDir = path.join(previewDir, 'compositions');

if (!fs.existsSync(previewDir)) fs.mkdirSync(previewDir, { recursive: true });
if (!fs.existsSync(gifDir)) fs.mkdirSync(gifDir, { recursive: true });
if (!fs.existsSync(compositionsDir)) fs.mkdirSync(compositionsDir, { recursive: true });

// 生成单个组件的预览文件
function generateComponentPreview(componentName, groupName, config) {
  const previewContent = `
import React from 'react';
import { Composition } from 'remotion';
import { ${componentName} } from '../../src';

// 组件预览
const ${componentName}Preview = () => {
  return (
    <div style={{
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      width: '100%',
      height: '100%',
      backgroundColor: '#000',
      color: '#fff'
    }}>
      <${componentName}
        text="${config.sampleText}"
        style={{
          fontSize: 36,
          fontWeight: 'bold',
          color: '#fff',
          textAlign: 'center'
        }}
      />
    </div>
  );
};

// 导出 Composition
export default () => (
  <Composition
    id="${componentName}"
    component={${componentName}Preview}
    durationInFrames={${config.duration}}
    fps={30}
    width={${config.dimensions.width}}
    height={${config.dimensions.height}}
  />
);
`;

  const filePath = path.join(compositionsDir, `${componentName}.tsx`);
  fs.writeFileSync(filePath, previewContent);
  return filePath;
}

// 生成主要的 Remotion 配置文件
function generateRemotionConfig() {
  const configContent = `
import { Config } from '@remotion/cli/config';

Config.setImageFormat('jpeg');
Config.setPixelFormat('yuv420p');
Config.setCodec('h264');
Config.setConcurrency(2);
Config.setQuality(8);

export default {};
`;

  fs.writeFileSync(path.join(__dirname, '../remotion.config.ts'), configContent);
}

// 生成主索引文件
function generateMainIndex(componentList) {
  const imports = componentList.map(comp => 
    `import ${comp.name}Composition from './compositions/${comp.name}';`
  ).join('\n');

  const compositions = componentList.map(comp => 
    `      <${comp.name}Composition />`
  ).join('\n');

  const indexContent = `
import React from 'react';
import { registerRoot } from 'remotion';
${imports}

const RemotionRoot = () => {
  return (
    <>
${compositions}
    </>
  );
};

registerRoot(RemotionRoot);
`;

  fs.writeFileSync(path.join(previewDir, 'index.tsx'), indexContent);
}

// 批量渲染 GIF
async function renderGifs(componentList) {
  console.log('🎬 开始批量渲染 GIF...');
  
  for (const component of componentList) {
    try {
      console.log(`📹 渲染 ${component.name}...`);
      
      const outputPath = path.join(gifDir, `${component.name}.gif`);
      const command = `npx remotion render ${component.name} ${outputPath} --image-format=gif --quality=8`;
      
      execSync(command, { 
        cwd: path.join(__dirname, '../previews'),
        stdio: 'inherit' 
      });
      
      console.log(`✅ ${component.name} 渲染完成`);
    } catch (error) {
      console.error(`❌ ${component.name} 渲染失败:`, error.message);
    }
  }
}

// 主执行函数
async function main() {
  console.log('🚀 开始生成组件预览...');
  
  // 生成 Remotion 配置
  generateRemotionConfig();
  
  // 读取组件信息
  const srcIndexPath = path.join(__dirname, '../src/index.ts');
  const srcContent = fs.readFileSync(srcIndexPath, 'utf8');
  
  const componentList = [];
  
  // 解析每个组件组
  Object.keys(componentGroups).forEach(groupName => {
    const config = componentGroups[groupName];
    
    // 这里需要实际解析组件名称，暂时使用示例
    // 您需要根据实际的组件导出结构调整这部分
    const mockComponents = getMockComponentsForGroup(groupName);
    
    mockComponents.forEach(componentName => {
      console.log(`📝 生成 ${componentName} 预览文件...`);
      generateComponentPreview(componentName, groupName, config);
      componentList.push({ name: componentName, group: groupName });
    });
  });
  
  // 生成主索引文件
  generateMainIndex(componentList);
  
  // 渲染 GIF
  await renderGifs(componentList);
  
  console.log('🎉 批量生成完成！');
  console.log(`📂 GIF 文件保存在: ${gifDir}`);
}

// 临时函数：获取组件名称（需要根据实际情况调整）
function getMockComponentsForGroup(groupName) {
  const mockData = {
    TextInEffectsComponents: ['FadeInEffect', 'SlideInEffect', 'RotateScaleInEffect', 'StarJumpEffect'],
    TextLoopEffectsComponents: ['MachineCodeEffect'],
    FlowerTextComponents: ['MultiShadowText', 'DiamondText', 'SupremeGoldText'],
    // 其他组件组...
  };
  
  return mockData[groupName] || [];
}

// 运行主函数
if (require.main === module) {
  main().catch(console.error);
}

module.exports = { main, generateComponentPreview }; 