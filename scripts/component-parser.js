const fs = require('fs');
const path = require('path');

// 解析组件信息的工具函数
class ComponentParser {
  constructor(srcPath) {
    this.srcPath = srcPath;
    this.components = new Map();
  }

  // 解析指定目录下的所有组件
  parseDirectory(dirPath, category = '') {
    const fullPath = path.join(this.srcPath, dirPath);
    
    if (!fs.existsSync(fullPath)) {
      console.warn(`目录不存在: ${fullPath}`);
      return [];
    }

    const items = fs.readdirSync(fullPath);
    const components = [];

    for (const item of items) {
      const itemPath = path.join(fullPath, item);
      const stat = fs.statSync(itemPath);

      if (stat.isDirectory()) {
        // 递归解析子目录
        components.push(...this.parseDirectory(path.join(dirPath, item), category));
      } else if (item.endsWith('.tsx') && !item.startsWith('Base') && item !== 'index.tsx') {
        // 解析组件文件
        const componentInfo = this.parseComponentFile(itemPath, dirPath, category);
        if (componentInfo) {
          components.push(componentInfo);
        }
      }
    }

    return components;
  }

  // 解析单个组件文件
  parseComponentFile(filePath, relativePath, category) {
    try {
      const content = fs.readFileSync(filePath, 'utf8');
      const fileName = path.basename(filePath, '.tsx');
      
      // 检查是否是有效的 React 组件
      const hasDefaultExport = /export\s+default\s+/.test(content);
      const hasComponentDeclaration = new RegExp(`(const|function)\\s+${fileName}`, 'g').test(content);
      
      if (!hasDefaultExport && !hasComponentDeclaration) {
        return null;
      }

      // 提取组件的参数接口
      const propsPattern = /interface\s+(\w+Props)\s*{([^}]+)}/;
      const propsMatch = content.match(propsPattern);
      
      let props = { text: 'string', style: 'React.CSSProperties' };
      if (propsMatch) {
        // 简单解析 props 接口
        const propsContent = propsMatch[2];
        const propLines = propsContent.split('\n').map(line => line.trim()).filter(line => line);
        
        props = {};
        propLines.forEach(line => {
          const propMatch = line.match(/(\w+)\??\s*:\s*([^;]+);?/);
          if (propMatch) {
            props[propMatch[1]] = propMatch[2];
          }
        });
      }

      // 判断组件类型
      const componentType = this.determineComponentType(content, relativePath);
      
      return {
        name: fileName,
        filePath: path.relative(this.srcPath, filePath),
        category: category || this.getCategoryFromPath(relativePath),
        type: componentType,
        props: props,
        hasAnimation: this.hasAnimation(content),
        description: this.extractDescription(content)
      };
    } catch (error) {
      console.error(`解析组件文件失败 ${filePath}:`, error.message);
      return null;
    }
  }

  // 判断组件类型
  determineComponentType(content, relativePath) {
    if (relativePath.includes('textInEffects')) return 'textIn';
    if (relativePath.includes('textOutEffects')) return 'textOut';
    if (relativePath.includes('textLoopEffects')) return 'textLoop';
    if (relativePath.includes('flowers')) return 'flower';
    if (relativePath.includes('dynamic')) return 'dynamic';
    if (relativePath.includes('sticker')) return 'sticker';
    if (relativePath.includes('sence-effects')) return 'scene';
    if (relativePath.includes('global-effects')) return 'globalEffect';
    return 'other';
  }

  // 根据路径获取分类
  getCategoryFromPath(relativePath) {
    if (relativePath.includes('text/effects/textInEffects')) return '文本入场特效';
    if (relativePath.includes('text/effects/textOutEffects')) return '文本出场特效';
    if (relativePath.includes('text/effects/textLoopEffects')) return '文本循环特效';
    if (relativePath.includes('text/flowers')) return '花字组件';
    if (relativePath.includes('text/dynamic')) return '动态文本特效';
    if (relativePath.includes('sticker')) return '贴纸组件';
    if (relativePath.includes('sence-effects')) return '场景特效';
    if (relativePath.includes('global-effects')) return '全局特效';
    if (relativePath.includes('image')) return '图片组件';
    if (relativePath.includes('video')) return '视频组件';
    if (relativePath.includes('person-card')) return '个人卡片';
    return '其他组件';
  }

  // 检查是否包含动画
  hasAnimation(content) {
    const animationKeywords = [
      'useCurrentFrame',
      'interpolate',
      'spring',
      'Easing',
      'transform',
      'transition',
      'keyframes',
      '@keyframes'
    ];
    
    return animationKeywords.some(keyword => content.includes(keyword));
  }

  // 提取组件描述
  extractDescription(content) {
    // 尝试从注释中提取描述
    const commentPattern = /\/\*\*\s*\n\s*\*\s*(.+?)\s*\n/;
    const match = content.match(commentPattern);
    return match ? match[1] : '';
  }

  // 解析所有组件
  parseAllComponents() {
    const componentPaths = [
      'text/effects/textInEffects',
      'text/effects/textOutEffects', 
      'text/effects/textLoopEffects',
      'text/flowers/advanced-text',
      'text/flowers/decorative-text',
      'text/dynamic/sections',
      'sticker',
      'sence-effects/in',
      'sence-effects/out',
      'global-effects',
      'image',
      'video',
      'person-card'
    ];

    const allComponents = [];
    
    componentPaths.forEach(dirPath => {
      console.log(`🔍 解析目录: ${dirPath}`);
      const components = this.parseDirectory(dirPath);
      allComponents.push(...components);
      console.log(`   找到 ${components.length} 个组件`);
    });

    return allComponents;
  }

  // 生成组件配置
  generateComponentConfig(components) {
    const config = {};
    
    components.forEach(comp => {
      const groupKey = this.getGroupKey(comp.type);
      if (!config[groupKey]) {
        config[groupKey] = {
          title: comp.category,
          components: [],
          sampleText: this.getSampleText(comp.type),
          duration: this.getDuration(comp.type),
          dimensions: this.getDimensions(comp.type)
        };
      }
      
      config[groupKey].components.push({
        name: comp.name,
        description: comp.description,
        props: comp.props,
        hasAnimation: comp.hasAnimation
      });
    });

    return config;
  }

  // 获取组件组键名
  getGroupKey(type) {
    const typeMap = {
      'textIn': 'TextInEffectsComponents',
      'textOut': 'TextOutEffectsComponents',
      'textLoop': 'TextLoopEffectsComponents',
      'flower': 'FlowerTextComponents',
      'dynamic': 'DynamicSectionTextEffectsComponents',
      'sticker': 'StickerComponents',
      'scene': 'SceneEffectsComponents',
      'globalEffect': 'VideoEffectsComponents',
      'other': 'OtherComponents'
    };
    return typeMap[type] || 'OtherComponents';
  }

  // 获取示例文本
  getSampleText(type) {
    const textMap = {
      'textIn': '入场动画效果',
      'textOut': '出场动画效果', 
      'textLoop': '循环动画效果',
      'flower': '万强文字特效',
      'dynamic': '动态文本效果',
      'sticker': '贴纸效果',
      'scene': '场景动画',
      'globalEffect': '全局特效'
    };
    return textMap[type] || '组件预览';
  }

  // 获取动画时长
  getDuration(type) {
    const durationMap = {
      'textIn': 120,    // 4秒
      'textOut': 120,   // 4秒
      'textLoop': 180,  // 6秒
      'flower': 90,     // 3秒
      'dynamic': 150,   // 5秒
      'sticker': 90,    // 3秒
      'scene': 120,     // 4秒
      'globalEffect': 120
    };
    return durationMap[type] || 90;
  }

  // 获取画面尺寸
  getDimensions(type) {
    if (type === 'sticker') {
      return { width: 400, height: 400 };
    }
    if (type === 'scene') {
      return { width: 800, height: 450 };
    }
    return { width: 600, height: 200 };
  }
}

// 导出解析器
module.exports = ComponentParser;

// 如果直接运行此脚本
if (require.main === module) {
  const srcPath = path.join(__dirname, '../src');
  const parser = new ComponentParser(srcPath);
  
  console.log('🚀 开始解析组件...');
  const components = parser.parseAllComponents();
  const config = parser.generateComponentConfig(components);
  
  console.log(`\n📊 解析结果:`);
  console.log(`总共找到 ${components.length} 个组件`);
  
  Object.keys(config).forEach(groupKey => {
    const group = config[groupKey];
    console.log(`  ${group.title}: ${group.components.length} 个组件`);
  });
  
  // 保存配置到文件
  const configPath = path.join(__dirname, 'component-config.json');
  fs.writeFileSync(configPath, JSON.stringify({ components, config }, null, 2));
  console.log(`\n💾 配置已保存到: ${configPath}`);
} 