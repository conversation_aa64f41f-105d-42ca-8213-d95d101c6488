---
description: 
globs: 
alwaysApply: true
---
文字特效组件注意事项：
    包裹文本的元素只能用行内元素（包括其父元素），如inline、inline-flex
    参数定义：
        text: string | React.ReactNode[];
        style: React.CSSProperties;
        durationFrames?: number; // 入场和出场特效特有
        speed?: number; // 循环特效特有

花字组件注意事项：
    包裹文本的元素只能用行内元素（包括其父元素），如inline、inline-flex
    参数定义：        
        style: React.CSSProperties;
        text: string;
        className?: string;
        props: any; // 剩余参数