{"name": "remotion-wanqiang", "version": "0.0.17", "description": "A react library developed with dumi", "license": "MIT", "module": "dist/index.js", "types": "dist/index.d.ts", "files": ["dist"], "scripts": {"build": "father build", "build:watch": "father dev", "dev": "dumi dev", "docs:build": "dumi build", "docs:preview": "dumi preview", "doctor": "father doctor", "lint": "npm run lint:es && npm run lint:css", "lint:css": "stylelint \"{src,test}/**/*.{css,less}\"", "lint:es": "eslint \"{src,test}/**/*.{js,jsx,ts,tsx}\"", "prepare": "husky install && dumi setup", "prepublishOnly": "father doctor && npm run build", "start": "npm run dev", "preview:generate": "node scripts/run.js", "preview:parse": "node scripts/run.js --parse-only", "preview:fast": "node scripts/run.js --quality=4 --scale=0.5", "preview:hq": "node scripts/run.js --quality=10 --concurrent"}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "lint-staged": {"*.{md,json}": ["prettier --write --no-error-on-unmatched-pattern"], "*.{css,less}": ["stylelint --fix", "prettier --write"], "*.{js,jsx}": ["eslint --fix", "prettier --write"], "*.{ts,tsx}": ["eslint --fix", "prettier --parser=typescript --write"]}, "dependencies": {"@remotion/layout-utils": "4.0.314", "@remotion/player": "4.0.306", "remotion": "4.0.306"}, "devDependencies": {"@commitlint/cli": "^17.1.2", "@commitlint/config-conventional": "^17.1.0", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "@umijs/lint": "^4.0.0", "dumi": "^2.4.13", "eslint": "^8.23.0", "father": "^4.1.0", "husky": "^8.0.1", "lint-staged": "^13.0.3", "prettier": "^2.7.1", "prettier-plugin-organize-imports": "^3.0.0", "prettier-plugin-packagejson": "^2.2.18", "react": "^18.0.0", "react-dom": "^18.0.0", "stylelint": "^14.9.1"}, "peerDependencies": {"react": ">=16.9.0", "react-dom": ">=16.9.0"}, "publishConfig": {"access": "public"}, "authors": ["<EMAIL>"]}